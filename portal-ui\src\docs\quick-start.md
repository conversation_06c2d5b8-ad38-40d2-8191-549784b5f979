# 快速开始
> <font style="color:rgb(2, 8, 23);">前提条件：请先在[https://tiangongkaiwu.top/#/login](https://tiangongkaiwu.top/#/login)完成注册与登录</font>
>

---

> <font style="color:rgb(2, 8, 23);">一共有两种方法：利用我们预制的镜像快速发布任务和使用用户自定义打包的镜像发布任务</font>
>

## 1、用预制镜像发布任务
### 1.1第一步：新增部署任务<font style="background-color:#CEF5F7;"></font>
进入<font style="background-color:#E7E9E8;"> </font><font style="color:#2F8EF4;background-color:#E7E9E8;">新增部署任务 </font>界面：[https://tiangongkaiwu.top/#/console](https://tiangongkaiwu.top/#/console)

![](./imgs/start1.png)

### 1.2第二步：填写任务信息
#### 1.2.1选择资源
<font style="color:rgb(2, 8, 23);">根据您的需要筛选 GPU，包括所在区域、显存要求、CPU 核心数、内存大小等。</font>

![](./imgs/start2.png)

<font style="color:rgb(2, 8, 23);">选择 GPU 型号：</font>**<font style="color:rgb(2, 8, 23);">推荐选择不限区域——享受全国闲置算力资源 随取随用</font>**

<font style="color:rgb(2, 8, 23);">GPU 型号推荐配置：随便选</font>

1. **<font style="color:rgb(2, 8, 23);">显存完全达标</font>**<font style="color:rgb(2, 8, 23);">：</font>
+ <font style="color:rgb(2, 8, 23);">图片中所有型号单卡均提供</font>**<font style="color:rgb(2, 8, 23);"> 24GB 以上显存</font>**<font style="color:rgb(2, 8, 23);">，远超服务部署启动的要求，无需担心显存不足。</font>
2. **<font style="color:rgb(2, 8, 23);">性价比与灵活性</font>**<font style="color:rgb(2, 8, 23);">：</font>
+ **<font style="color:rgb(2, 8, 23);">1 卡配置</font>**<font style="color:rgb(2, 8, 23);">：适合短期测试或常规任务。</font>
+ **<font style="color:rgb(2, 8, 23);">库存充足</font>**<font style="color:rgb(2, 8, 23);">：1 卡版本库存多台，无需等待，快速部署。</font>
+ **<font style="color:rgb(2, 8, 23);">多卡选项备用</font>**<font style="color:rgb(2, 8, 23);">：若未来需要分布式训练或高并发任务，可随时切换 2 卡/4 卡。</font>
3. **<font style="color:rgb(2, 8, 23);">简化选择逻辑</font>**<font style="color:rgb(2, 8, 23);">：</font>
+ **<font style="color:rgb(2, 8, 23);">无特殊需求</font>**<font style="color:rgb(2, 8, 23);">：如果只是运行文档镜像中的常规任务（如模型推理、数据处理），单卡性能完全足够。</font>
+ **<font style="color:rgb(2, 8, 23);">兼容性无忧</font>**<font style="color:rgb(2, 8, 23);">：镜像仓库默认适配主流 GPU 型号（如 4090），无需额外调试。</font>

<font style="color:rgb(2, 8, 23);">操作建议</font>

+ **<font style="color:rgb(2, 8, 23);">随便选 直接勾选「4090（1 卡）」+「1 个节点」</font>**<font style="color:rgb(2, 8, 23);">，填写服务名称，快速完成部署。</font>
+ **<font style="color:rgb(2, 8, 23);">进阶场景</font>**<font style="color:rgb(2, 8, 23);">：仅当需要训练大模型（如 LLM、多模态）时，再考虑 2 卡/4 卡配置。</font>

**<font style="color:rgb(2, 8, 23);">图片中的配置均为“无坑选项”，闭眼选 1 卡即可开箱即用，成本可控，资源立即可用。</font>**

<font style="color:rgb(2, 8, 23);">点击选择某一型号 GPU 后，可配置任务需要的计算节点数量。至少选择 1 个节点，最多可选 20 个节点，建议先启动 1 个节点进行验证，运行稳定后可横向扩展至所需规模。</font>

<font style="color:rgb(2, 8, 23);">选择 GPU 和节点数量后，继续填写服务配置信息</font>

#### <font style="color:rgb(2, 8, 23);">1.2.2 定义服务配置信息</font>
<font style="color:rgb(2, 8, 23);">按照需求选择我们预制好的镜像，同时可以通过文档链接快速了解容器化部署对应服务的步骤。</font>

![](./imgs/start3.png)

<font style="color:rgb(2, 8, 23);">这里我们选择了Jupyter Lab的服务信息作为示例</font>

![](./imgs/start4.png)

<font style="color:rgb(2, 8, 23);">选择完成后，点击【部署服务】按钮，发布任务。</font>

### <font style="color:rgb(2, 8, 23);">1.3第三步：查看运行状态</font>
<font style="color:rgb(2, 8, 23);">发布任务后会自动跳转至任务详情界面，等待节点分配。</font>

![](./imgs/start5.png)

<font style="color:rgb(2, 8, 23);">节点分配完成后，点击【公开访问】中的任一链接均可正常访问该服务</font>

**<font style="color:rgb(2, 8, 23);">JupyterLab 建议只用一个节点</font>**<font style="color:rgb(2, 8, 23);">，不然可能导致异常</font>

<font style="color:rgb(2, 8, 23);">节点分配完成后，可以通过点击回传链接访问服务：</font>

![](./imgs/start6.png)

<font style="color:rgb(2, 8, 23);">进入 JupyterLab 服务：</font>

![](./imgs/start7.png)

## <font style="color:rgb(2, 8, 23);">2、使用用户自定义打包的镜像发布任务</font>
### 2.1第一步：新增部署任务
进入<font style="background-color:#E7E9E8;"> </font><font style="color:#2F8EF4;background-color:#E7E9E8;">新增部署任务 </font>界面：[https://tiangongkaiwu.top/#/console](https://tiangongkaiwu.top/#/console)

![](./imgs/start8.png)

### 2.2第二步：填写任务信息
#### 2.2.1选择资源
<font style="color:rgb(2, 8, 23);">根据您的需要筛选 GPU，包括所在区域、显存要求、CPU 核心数、内存大小等。</font>

![](./imgs/start9.png)

<font style="color:rgb(2, 8, 23);">选择 GPU 型号：</font>**<font style="color:rgb(2, 8, 23);">推荐选择不限区域——享受全国闲置算力资源 随取随用</font>**

<font style="color:rgb(2, 8, 23);">GPU 型号推荐配置：随便选</font>

1. **<font style="color:rgb(2, 8, 23);">显存完全达标</font>**<font style="color:rgb(2, 8, 23);">：</font>
+ <font style="color:rgb(2, 8, 23);">图片中所有型号单卡均提供</font>**<font style="color:rgb(2, 8, 23);"> 24GB 以上显存</font>**<font style="color:rgb(2, 8, 23);">，远超服务部署启动的要求，无需担心显存不足。</font>
2. **<font style="color:rgb(2, 8, 23);">性价比与灵活性</font>**<font style="color:rgb(2, 8, 23);">：</font>
+ **<font style="color:rgb(2, 8, 23);">1 卡配置</font>**<font style="color:rgb(2, 8, 23);">：适合短期测试或常规任务。</font>
+ **<font style="color:rgb(2, 8, 23);">库存充足</font>**<font style="color:rgb(2, 8, 23);">：1 卡版本库存多台，无需等待，快速部署。</font>
+ **<font style="color:rgb(2, 8, 23);">多卡选项备用</font>**<font style="color:rgb(2, 8, 23);">：若未来需要分布式训练或高并发任务，可随时切换 2 卡/4 卡。</font>
3. **<font style="color:rgb(2, 8, 23);">简化选择逻辑</font>**<font style="color:rgb(2, 8, 23);">：</font>
+ **<font style="color:rgb(2, 8, 23);">无特殊需求</font>**<font style="color:rgb(2, 8, 23);">：如果只是运行文档镜像中的常规任务（如模型推理、数据处理），单卡性能完全足够。</font>
+ **<font style="color:rgb(2, 8, 23);">兼容性无忧</font>**<font style="color:rgb(2, 8, 23);">：镜像仓库默认适配主流 GPU 型号（如 4090），无需额外调试。</font>

<font style="color:rgb(2, 8, 23);">操作建议</font>

+ **<font style="color:rgb(2, 8, 23);">随便选 直接勾选「4090（1 卡）」+「1 个节点」</font>**<font style="color:rgb(2, 8, 23);">，填写服务名称，快速完成部署。</font>
+ **<font style="color:rgb(2, 8, 23);">进阶场景</font>**<font style="color:rgb(2, 8, 23);">：仅当需要训练大模型（如 LLM、多模态）时，再考虑 2 卡/4 卡配置。</font>

**<font style="color:rgb(2, 8, 23);">图片中的配置均为“无坑选项”，闭眼选 1 卡即可开箱即用，成本可控，资源立即可用。</font>**

<font style="color:rgb(2, 8, 23);">点击选择某一型号 GPU 后，可配置任务需要的计算节点数量。至少选择 1 个节点，最多可选 20 个节点，建议先启动 1 个节点进行验证，运行稳定后可横向扩展至所需规模。</font>

<font style="color:rgb(2, 8, 23);">选择 GPU 和节点数量后，继续填写服务配置信息</font>

#### <font style="color:rgb(2, 8, 23);">2.2.2 定义服务配置信息</font>
<font style="color:rgb(2, 8, 23);">选择</font>**<font style="color:rgb(2, 8, 23);">服务配置——我的镜像</font>**

![](./imgs/start10.png)

<font style="color:rgb(2, 8, 23);">在镜像URL字段中填写完整的镜像地址及标签。支持以下两种方式：</font>

**<font style="color:rgb(2, 8, 23);">第一种：第三方公共镜像库</font>**<font style="color:rgb(2, 8, 23);">（如 Docker Hub、阿里云镜像库等）： 直接使用公开镜像地址，例如：</font>

```
swr.cn-north-4.myhuaweicloud.com/ddn-k8s/quay.io/jupyter/pytorch-notebook:cuda12-python-3.11.8
```

**<font style="color:rgb(2, 8, 23);">第二种：我们提供的私有镜像库</font>**<font style="color:rgb(2, 8, 23);">（需预先上传镜像）： 若需使用内部私有镜像仓库，请参考镜像仓库完成镜像推送后，刷新后在界面中选择内部仓库地址。</font>

<font style="color:rgb(2, 8, 23);">示例私有镜像地址库：</font><font style="color:#2F8EF4;background-color:#E7E9E8;">harbor.suanleme.cn/xiditgkw/qwen3:30b-a3b</font>

![](./imgs/start11.png)

<font style="color:rgb(2, 8, 23);">完成镜像 URL 填写后进行端口配置（这里以qwen3为例 端口号填写11434）</font>

![](./imgs/start12.png)

<font style="color:rgb(2, 8, 23);">完成上面的配置后点击部署服务发布任务</font>

![](./imgs/start13.png)

### <font style="color:rgb(2, 8, 23);">2.3 第三步：查看运行状态</font>
<font style="color:rgb(2, 8, 23);">发布任务后会自动跳转至任务详情界面，等待节点分配。</font>

![](./imgs/start14.png)

<font style="color:rgb(2, 8, 23);">节点分配完成后，点击【公开访问】中的任一链接均可正常访问该服务</font>

<font style="color:rgb(2, 8, 23);">节点分配完成后，可以通过点击回传链接访问服务：</font>

![](./imgs/start15.png)

<br><br>
<font style="color:rgba(178,178,178,1);">最后更新于: 2025/6/6 17:07</font>