# <font style="color:rgb(2, 8, 23);">容器化部署 StableDiffusion2.1-WebUI 应用</font>
## <font style="color:rgb(2, 8, 23);">1 部署步骤</font>
<font style="color:rgb(2, 8, 23);">我们提供了构建完毕的 Stable-Diffusion-WebUI 镜像，您可以直接部署使用。</font>

### <font style="color:rgb(2, 8, 23);">1.1 访问</font>[天工开物控制台](https://tiangongkaiwu.top/#/console)<font style="color:rgb(2, 8, 23);">，点击新增部署。</font>
![](./imgs/universal1.png)
### <font style="color:rgb(2, 8, 23);">1.2 基于自身需要进行配置，参考配置为单卡 4090 和 1 个节点（初次使用进行调试）。</font>
![](./imgs/universal2.png)

### <font style="color:rgb(2, 8, 23);">1.3 选择相应预制镜像</font>
![](./imgs/webui2-1v3.png)

### <font style="color:rgb(2, 8, 23);">1.4 点击部署服务，耐心等待节点拉取镜像并启动。</font>
![](./imgs/webui2-1v4.png)

### <font style="color:rgb(2, 8, 23);">1.5 节点启动后，你所在“任务详情页”中看到的内容可能如下：</font>
![](./imgs/webui2-1v5.png)

### <font style="color:rgb(2, 8, 23);">1.6 我们可以点击快捷访问下方“7860”端口的链接，测试 Gradio 运行情况</font>
<font style="color:rgb(2, 8, 23);">接下来填写 prompt，描述我们希望图片的内容。</font>

![](./imgs/webui2-1v6.png)

<font style="color:rgb(2, 8, 23);">最后点击生成按钮，接下来我们耐心稍等片刻，可以看到图片已经生成。</font>

![](./imgs/webui2-1v7.png)

## <font style="color:rgb(2, 8, 23);">2 构建镜像</font>
<font style="color:rgb(2, 8, 23);">如果您对如何构建该镜像感兴趣，可以继续查看接下来的教程。</font>

### <font style="color:rgb(2, 8, 23);">2.1 克隆项目</font>
<font style="color:rgb(2, 8, 23);">首先，我们需要在本地磁盘中新建一个文件夹，将 StableDiffusion-WebUI 项目克隆下来。 运行如下命令：</font>

```dockerfile
<NAME_EMAIL>:AUTOMATIC1111/stable-diffusion-webui.git
```

### <font style="color:rgb(2, 8, 23);">2.2 下载模型</font>
<font style="color:rgb(2, 8, 23);">我们需要 sd 的 2.1 版本模型，为此，我们可以选择到 HuggingFace 下载。</font>

<font style="color:rgb(2, 8, 23);">打开下面的 URL，点击页面中模型的下载按钮，耐心等待模型下载完毕。 </font>[<font style="color:#2F8EF4;">https://huggingface.co/stabilityai/stable-diffusion-2-1/tree/main</font>](https://huggingface.co/stabilityai/stable-diffusion-2-1/tree/main)

![](./imgs/webui2-1v8.png)

<font style="color:rgb(2, 8, 23);">下载完毕后，将其放入 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">stable-diffusion-webui\models\Stable-diffusion</font><font style="color:#2F8EF4;"> </font><font style="color:rgb(2, 8, 23);">目录下。</font>

### <font style="color:rgb(2, 8, 23);">2.3 修改源码</font>
<font style="color:rgb(2, 8, 23);">默认该版本 gradio 的 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">server_name</font><font style="color:rgb(2, 8, 23);"> 为 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">127.0.0.1</font><font style="color:rgb(2, 8, 23);"> ，这是一个本机可访问的地址，但无法提供给外界主机访问，因而我们需要修改其配置，而这一步最方便的办法就是直接修改其源码。</font>

<font style="color:rgb(2, 8, 23);">我们在根目录下搜索 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">initialize_util.py</font><font style="color:rgb(2, 8, 23);"> 这个文件，将 gradio_server_name 方法中的代码进行覆盖，使其返回内容如下。</font>

```dockerfile
def gradio_server_name():
    return "0.0.0.0"
```

### <font style="color:rgb(2, 8, 23);">2.4 编写 Dockerfile 文件</font>
<font style="color:rgb(2, 8, 23);">在</font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">stable-diffusion-webui</font><font style="color:rgb(2, 8, 23);">同级目录下新建一个文本文件，命名为</font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">Dockerfile</font><font style="color:rgb(2, 8, 23);">。</font>

<font style="color:rgb(2, 8, 23);">将下列内容粘贴到文件中：</font>

```dockerfile
FROM nvcr.io/nvidia/pytorch:24.08-py3

ENV venv_dir="-"

# 复制代码
COPY ./stable-diffusion-webui /app

# 强制卸载冲突 OpenCV 包
RUN pip uninstall -y opencv-python-headless opencv-contrib-python opencv-python || true

RUN sed -i 's/\r//g' /app/webui.sh && \
    sed -i 's/\r//g' /app/webui-user.sh && \
    chmod +x /app/webui.sh

RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        libgl1 \
        libsm6 \
        libxrender1 \
        libxext6 \
        ffmpeg \
        libgl1-mesa-glx && \
    rm -rf /var/lib/apt/lists/*

COPY CLIP-d50d76daa670286dd6cacf3bcd80b5e4823fc8e1.zip /app/clip.zip

RUN pip install /app/clip.zip --prefer-binary

WORKDIR /app

# 仅安装其他Python依赖
RUN pip install --no-cache-dir -r requirements.txt

CMD ["/bin/sh", "-c", "/app/webui.sh"]
```

### <font style="color:rgb(2, 8, 23);">2.5 构建镜像</font>
<font style="color:rgb(2, 8, 23);">运行如下命令：</font>

```shell
docker build -t sd-webui:0.1 .
```

<font style="color:rgb(2, 8, 23);">耐心等待镜像构建完毕即可。如果构建完毕后，运行时发现镜像缺少某些依赖，可下载好后通过命令复制到镜像中，重新执行构建。</font>

**<font style="color:rgb(103, 103, 108);"></font>**

<br><br>
<font style="color:rgba(178,178,178,1);">最后更新于: 2025/6/18 14:35</font>
