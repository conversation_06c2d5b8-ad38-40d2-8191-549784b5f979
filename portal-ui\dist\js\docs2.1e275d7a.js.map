{"version": 3, "file": "js/docs2.1e275d7a.js", "mappings": "wHACA,IAAIA,EAA6B,IAAIC,IAAI,aAErCC,EAAO,inHAAouHF,EAA6B,uxJAE5wH,c", "sources": ["webpack://portal-ui/./src/docs/cloud-storage.md"], "sourcesContent": ["// Imports\nvar ___HTML_LOADER_IMPORT_0___ = new URL(\"./imgs/cloud1.png\", import.meta.url);\n// Module\nvar code = \"<h1 id=\\\"云存储加速\\\"><font style=\\\"color:#020817\\\">云存储加速</font></h1> <h2 id=\\\"1-功能简介\\\"><strong><font style=\\\"color:#020817\\\">1. 功能简介</font></strong></h2> <p><font style=\\\"color:#020817\\\">本功能支持用户将 S3 兼容云存储（如阿里云 OSS、腾讯云 COS、AWS S3 等）挂载到平台，实现模型或数据的高效访问和加速。通过 JuiceFS 缓存机制，大幅提升模型加载和数据读取速度。</font></p> <h2 id=\\\"2-功能效果\\\"><strong><font style=\\\"color:#020817\\\">2. 功能效果</font></strong></h2> <blockquote> <p><font style=\\\"color:#67676c\\\">冷启动定义：模型服务从零实例状态（缩放到 0）接收请求到准备处理第一个请求的时间间隔，是影响部署响应能力、服务等级协议（SLA）和成本控制的关键因素。为了优化冷启动，我们将介绍以下策略：云存储加速，它通过提前将 S3 数据缓存到本地，从而提高性能。</font></p> </blockquote> <p><strong><font style=\\\"color:#020817\\\">性能提升显著</font></strong></p> <p><font style=\\\"color:#020817\\\">冷启动带来的挑战：</font></p> <ul> <li><font style=\\\"color:#020817\\\">用户体验：首次请求响应时间长，影响用户满意度</font></li> <li><font style=\\\"color:#020817\\\">成本控制：频繁冷启动导致资源浪费和成本增加</font></li> <li><font style=\\\"color:#020817\\\">服务可用性：冷启动时间过长可能导致服务超时</font></li> </ul> <p><font style=\\\"color:#020817\\\">S3 存储加速的优化策略：</font></p> <ol> <li><font style=\\\"color:#020817\\\">预取机制：利用 JuiceFS 预取功能，后台线程提前下载模型权重和数据</font></li> <li><font style=\\\"color:#020817\\\">分布式缓存：将模型权重缓存在分布式文件系统中，避免重复下载</font></li> <li><font style=\\\"color:#020817\\\">智能预热：对高频访问的模型进行预热处理</font></li> </ol> <table> <thead> <tr> <th><font style=\\\"color:#020817\\\">部署方式</font></th> <th><font style=\\\"color:#020817\\\">冷启动时间</font></th> <th><font style=\\\"color:#020817\\\">性能提升</font></th> </tr> </thead> <tbody><tr> <td><font style=\\\"color:#020817\\\">传统方式</font></td> <td><font style=\\\"color:#020817\\\">数分钟（如 Stable Diffusion XL）</font></td> <td></td> </tr> <tr> <td><font style=\\\"color:#020817\\\">S3 加速</font></td> <td><font style=\\\"color:#020817\\\">10 秒以内</font></td> <td><font style=\\\"color:#020817\\\">提升 90% 以上</font></td> </tr> </tbody></table> <p><strong><font style=\\\"color:#020817\\\">资源利用优化</font></strong></p> <ul> <li><font style=\\\"color:#020817\\\">读取加速：本地缓存机制提供接近本地磁盘的读取速度</font></li> <li><font style=\\\"color:#020817\\\">并行下载：多线程并行下载，充分利用网络带宽</font></li> <li><font style=\\\"color:#020817\\\">智能缓存：自动管理缓存空间，优先缓存高频访问数据</font></li> <li><font style=\\\"color:#020817\\\">按需加载：仅在首次访问时从云端拉取数据，避免重复下载</font></li> <li><font style=\\\"color:#020817\\\">空间复用：多个任务可共享同一份缓存数据，节省存储空间</font></li> </ul> <h2 id=\\\"3-应用场景\\\"><strong><font style=\\\"color:#020817\\\">3. 应用场景</font></strong></h2> <h3 id=\\\"31-模型版本管理与无停机更新\\\"><strong><font style=\\\"color:#020817\\\">3.1. 模型版本管理与无停机更新</font></strong></h3> <p><font style=\\\"color:#020817\\\">在生产环境中，AI 模型需要频繁更新迭代，传统方式需要重新构建和发布 Docker 镜像，过程繁琐且耗时。</font></p> <p><font style=\\\"color:#020817\\\">S3 存储加速解决方案：</font></p> <ul> <li><font style=\\\"color:#020817\\\">解耦模型与镜像：将模型文件存储在 S3 中，Docker 镜像只包含运行环境，实现模型与代码的分离</font></li> <li><font style=\\\"color:#020817\\\">快速模型切换：通过更新 S3 中的模型文件，无需重建镜像即可完成模型更新</font></li> <li><font style=\\\"color:#020817\\\">A/B 测试：可同时挂载多个模型版本，方便进行对比测试</font></li> </ul> <p><font style=\\\"color:#020817\\\">实际效果：</font></p> <ul> <li><font style=\\\"color:#020817\\\">模型更新时间：从数小时缩短至几分钟</font></li> <li><font style=\\\"color:#020817\\\">运维效率：提升 80% 以上</font></li> </ul> <h3 id=\\\"32-弹性扩容与负载均衡\\\"><strong><font style=\\\"color:#020817\\\">3.2. 弹性扩容与负载均衡</font></strong></h3> <p><font style=\\\"color:#020817\\\">在业务高峰期或突发流量场景下，需要快速扩容计算节点以应对负载增长。</font></p> <p><font style=\\\"color:#020817\\\">S3 存储加速解决方案：</font></p> <ul> <li><font style=\\\"color:#020817\\\">快速节点启动：新扩容的节点可直接使用缓存的模型数据，避免重复下载</font></li> <li><font style=\\\"color:#020817\\\">智能缓存预热：新节点启动时自动预热常用模型，减少首次访问延迟</font></li> <li><font style=\\\"color:#020817\\\">跨区域部署：支持在不同区域快速部署节点，提升服务覆盖范围</font></li> </ul> <p><font style=\\\"color:#020817\\\">实际效果：</font></p> <ul> <li><font style=\\\"color:#020817\\\">扩容时间：从传统的 10-30 分钟缩短至 1-3 分钟</font></li> <li><font style=\\\"color:#020817\\\">资源利用率：提升 60% 以上</font></li> <li><font style=\\\"color:#020817\\\">服务稳定性：显著提升，支持平滑扩缩容</font></li> </ul> <h2 id=\\\"4-操作流程一览\\\"><strong><font style=\\\"color:#020817\\\">4. 操作流程一览</font></strong></h2> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_0___ + \"\\\" alt=\\\"\\\"></p> <h2 id=\\\"5-操作流程\\\"><strong><font style=\\\"color:#020817\\\">5. 操作流程</font></strong></h2> <h3 id=\\\"51-存储加速配置\\\"><strong><font style=\\\"color:#020817\\\">5.1. 存储加速配置</font></strong></h3> <ol> <li><font style=\\\"color:#020817\\\">进入存储加速管理页面在左侧菜单点击&quot;云存储加速&quot;，进入管理页面。</font></li> <li><font style=\\\"color:#020817\\\">新增 S3 存储配置点击&quot;新增云存储配置&quot;按钮，选择云服务商，填写以下信息：</font></li> </ol> <ul> <li><font style=\\\"color:#020817\\\">配置名称</font></li> <li><font style=\\\"color:#020817\\\">AccessKey、SecretKey</font></li> <li><font style=\\\"color:#020817\\\">Endpoint</font></li> <li><font style=\\\"color:#020817\\\">Bucket 名称</font></li> <li><font style=\\\"color:#020817\\\">地域</font></li> <li><font style=\\\"color:#020817\\\">需要加速的目录</font></li> </ul> <ol start=\\\"3\\\"> <li><font style=\\\"color:#020817\\\">保存配置系统会自动检测配置是否可用。校验通过后方可保存。在列表中找到该配置，点击&quot;开始加速&quot;，选择加速区域，系统将自动初始化 JuiceFS 文件系统。并进行提前预热（这需要从云端把文件下载到本地，需要等待一定时间）</font></li> </ol> <hr> <h3 id=\\\"52-任务发布时挂载-s3-存储\\\"><strong><font style=\\\"color:#020817\\\">5.2. 任务发布时挂载 S3 存储</font></strong></h3> <ol> <li><font style=\\\"color:#020817\\\">选择 S3 存储桶在任务发布页面的&quot;存储配置&quot;区域，选择已配置并加速的 S3 存储桶（即使还在回源中，仍然可以提前挂载，不过挂载后仍然需要等待从云端拉取文件到本地）。</font></li> <li><font style=\\\"color:#020817\\\">指定容器内挂载路径为每个选定的 S3 存储桶填写容器内的挂载路径（如 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">/mnt/my_model_data</font><font style=\\\"color:#020817\\\">），路径需以 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">/</font><font style=\\\"color:#2f8ef4\\\"> </font><font style=\\\"color:#020817\\\">开头。</font></li> <li><font style=\\\"color:#020817\\\">管理已选存储和路径可随时删除或修改已选存储桶及其挂载路径。</font></li> <li><font style=\\\"color:#020817\\\">提交任务配置完成后提交任务，容器启动时会自动挂载所选 S3 存储。</font></li> <li><font style=\\\"color:#020817\\\">校验挂载目录等待容器启动完成后，可进入容器并验证挂载。查看挂载目录下的文件。</font></li> </ol> <hr> <h3 id=\\\"53-存储加速释放与管理\\\"><strong><font style=\\\"color:#020817\\\">5.3. 存储加速释放与管理</font></strong></h3> <ul> <li><font style=\\\"color:#020817\\\">手动释放：在存储加速管理页面可手动释放缓存，释放后 JuiceFS 文件系统和缓存会被清除，但配置仍保留。</font></li> <li><font style=\\\"color:#020817\\\">自动释放：若 15 天内无任务挂载该存储，加速服务会自动释放，配置数据保留，可随时重新激活。</font></li> </ul> <hr> <h2 id=\\\"6-状态说明\\\"><strong><font style=\\\"color:#020817\\\">6. 状态说明</font></strong></h2> <table> <thead> <tr> <th><font style=\\\"color:#020817\\\">事件名称</font></th> <th><font style=\\\"color:#020817\\\">事件描述</font></th> <th><font style=\\\"color:#020817\\\">状态变化</font></th> </tr> </thead> <tbody><tr> <td><font style=\\\"color:#020817\\\">创建配置</font></td> <td><font style=\\\"color:#020817\\\">用户创建新的 S3 存储配置</font></td> <td></td> </tr> <tr> <td><font style=\\\"color:#020817\\\">编辑配置</font></td> <td><font style=\\\"color:#020817\\\">用户修改现有 S3 存储配置</font></td> <td></td> </tr> <tr> <td><font style=\\\"color:#020817\\\">连接校验</font></td> <td><font style=\\\"color:#020817\\\">系统验证 S3 连接信息</font></td> <td></td> </tr> <tr> <td><font style=\\\"color:#020817\\\">开始加速</font></td> <td><font style=\\\"color:#020817\\\">系统建立 JuiceFS 文件系统、开始回源</font></td> <td><font style=\\\"color:#020817\\\">未加速 → 回源中</font></td> </tr> <tr> <td><font style=\\\"color:#020817\\\">回源完成</font></td> <td><font style=\\\"color:#020817\\\">系统完成数据回源同步</font></td> <td><font style=\\\"color:#020817\\\">回源中 → 加速中</font></td> </tr> <tr> <td><font style=\\\"color:#020817\\\">手动释放</font></td> <td><font style=\\\"color:#020817\\\">用户手动释放存储缓存</font></td> <td><font style=\\\"color:#020817\\\">加速中 → 未加速</font></td> </tr> <tr> <td><font style=\\\"color:#020817\\\">开始倒计时</font></td> <td><font style=\\\"color:#020817\\\">存储无任务挂载超过阈值</font></td> <td><font style=\\\"color:#020817\\\">加速中 → 即将释放</font></td> </tr> <tr> <td><font style=\\\"color:#020817\\\">倒计时结束</font></td> <td><font style=\\\"color:#020817\\\">倒计时达到 15 天</font></td> <td><font style=\\\"color:#020817\\\">即将释放 → 未加速</font></td> </tr> <tr> <td><font style=\\\"color:#020817\\\">重新挂载</font></td> <td><font style=\\\"color:#020817\\\">任务重新挂载存储</font></td> <td><font style=\\\"color:#020817\\\">即将释放 → 回源中</font></td> </tr> </tbody></table> <hr> <h2 id=\\\"7-常见操作\\\"><strong><font style=\\\"color:#020817\\\">7. 常见操作</font></strong></h2> <table> <thead> <tr> <th><font style=\\\"color:#020817\\\">状态</font></th> <th><font style=\\\"color:#020817\\\">可执行操作</font></th> </tr> </thead> <tbody><tr> <td><font style=\\\"color:#020817\\\">加速中</font></td> <td><font style=\\\"color:#020817\\\">释放存储缓存</font></td> </tr> <tr> <td><font style=\\\"color:#020817\\\">未加速</font></td> <td><font style=\\\"color:#020817\\\">编辑配置、开始同步、删除配置</font></td> </tr> <tr> <td><font style=\\\"color:#020817\\\">即将释放</font></td> <td><font style=\\\"color:#020817\\\">释放存储缓存</font></td> </tr> <tr> <td><font style=\\\"color:#020817\\\">同步中</font></td> <td><font style=\\\"color:#020817\\\">无</font></td> </tr> </tbody></table> <hr> <h2 id=\\\"8-注意事项\\\"><strong><font style=\\\"color:#020817\\\">8. 注意事项</font></strong></h2> <ul> <li><font style=\\\"color:#020817\\\">存储内容更新需手动点击&quot;回源上游&quot;按钮，不会自动同步。</font></li> <li><font style=\\\"color:#020817\\\">存储加速仅支持只读访问，适合模型或数据文件的高效读取。</font></li> <li><font style=\\\"color:#020817\\\">释放后的配置仍保留，可随时重新激活。</font></li> </ul> <p><strong><font style=\\\"color:#67676c\\\"></font></strong></p> <p><br><br> <font style=\\\"color:#b2b2b2\\\">最后更新于: 2025/6/19 09:36</font></p> \";\n// Exports\nexport default code;"], "names": ["___HTML_LOADER_IMPORT_0___", "URL", "code"], "sourceRoot": ""}