# 容器化部署 minicpm4
<font style="color:rgb(2, 8, 23);">本指南全面介绍了在天工开物平台上部署 minicpm4 大语言模型 API 服务的完整解决方案。该方案不仅提供了详实的部署流程，还提供了如何制作镜像的方案。</font>

> 此镜像提供了标准化的API 接口，让您能够便捷地通过 **API 调用方式**访问和使用所有功能。目前还不支持 **Web UI** 方式使用服务，需要您本地启动适配的 Web UI。
>

## **1.部署服务**
<font style="color:rgb(2, 8, 23);">点击这里 [<font style="color:rgb(0, 102, 204);">部署服务</font>](https://www.tiangongkaiwu.top/portal/#/console) ，登录后根据页面提示进行部署。选择合适的设备，在服务配置中输入镜像地址，部署服务，完成！</font>

### 1.1 访问天工开物[控制台](https://www.tiangongkaiwu.top/portal/#/console)，点击新增部署
![](./imgs/universal1.png)

### 1.2 选择设备
<font style="color:rgb(2, 8, 23);">基于自身需要进行配置，参考配置为单卡 4090 和 1 个节点（初次使用进行调试）。</font>

![](./imgs/universal2.png)

### 1.3 选择相应预制镜像
<font style="color:rgb(2, 8, 23);">选择 **minicpm4 预制镜像**，点击部署服务。</font>

![](./imgs/minicpm3.png)

### 1.4 耐心等待节点拉取镜像并启动
![](./imgs/minicpm4.png)

### 1.5 部署完成
<font style="color:rgb(2, 8, 23);">在部署完成页面，能看到一个公开访问链接。这个链接就是 Ollama 服务的 API 访问地址。</font>

<font style="color:rgb(2, 8, 23);">将这个 API 地址复制下来，就可以在任何支持 Ollama 协议的应用程序中使用。</font>

![](./imgs/minicpm5.png)

<font style="color:rgb(2, 8, 23);">在“常规”面板里可以看到公开访问的地址，此地址即为 Ollama 服务的 API 地址。 请耐心一点~~ 模型镜像会比较大，**minicpm4 镜像本身 20G+，打包之后大约 40G+，** 拉取镜像会需要一段时间。</font>

### 1.6 验证一下
<font style="color:rgb(2, 8, 23);">访问复制的链接，{快捷访问的地址} /api/tags，将链接复制到浏览器，就可以看到以下内容，说明模型已经部署并运行了。</font>

![](./imgs/minicpm6.png)

<font style="color:rgb(2, 8, 23);">如果需要在其他兼容 Ollama 的客户端使用时，需要提供的参数如下：</font>

+ 访问地址

<font style="color:rgb(2, 8, 23);">常规 -> 快捷访问中 11434 对应的链接。有的会需要在链接后面加上 /api</font>

+ ModelId

**minicpm4-8b**

+ 上下文长度

32k

+ 模型建议的其他参数（非必须，可以根据需要自行修改）

```dockerfile
{
    "repeat_penalty": 1,
    "temperature": 0.6,
    "top_k": 20,
    "top_p": 0.95
}
```

<font style="color:rgb(2, 8, 23);">使用第三方客户端时，可以按照下图填写内容</font>

![](./imgs/minicpm7.png)

## 2.模型速度测试
<font style="color:rgb(2, 8, 23);">minicpm4 部署完成了，速度怎么样呢？点击 [<font style="color:rgb(0, 102, 204);">LM Speed</font>](https://lmspeed.net/zh-CN) 测试一下速度吧~~~</font>

<font style="color:rgb(2, 8, 23);">如果 LM Speed 无法访问，多刷新几次就可以了😊</font>

**基础 URL 后面记得加 /v1**

![](./imgs/minicpm8.png)![](./imgs/minicpm9.png)

<br><br>
<font style="color:rgba(178,178,178,1);">最后更新于: 2025/7/3 15:25</font>

  


