<template>

  <div>

    <!-- 电脑端布局 -->
    <div v-if="!isMobile" class="desktop-layout">
      <!-- Banner Section -->
      <div class="banner-section">
        <div class="banner-container">
          <div class="big-box">
            <div class="img-box">
              <div class="show-box"
                   :style="{
                    transform: 'translateX(' + translate + ')',
                    transition: tsion ? 'all 0.5s' : 'none',
                  }">
                <div class="slide-item" v-for="(item, index) in bannerImages" :key="index">
                  <img :src="item.img" alt="" />
                  <div class="banner-content" :class="'pos-' + item.content.position">
                    <h2 class="banner-title" v-html="item.content.title"></h2>
                    <p class="banner-text">{{ item.content.text }}</p>
                    <div class="banner-actions">
                      <a v-if="!isLogin && item.content.secondaryLink"
                         href="#"
                         @click.prevent="navigateTo(item.content.secondaryLink)"
                         class="banner-button secondary-btn">
                        {{ item.content.secondaryBtnText }}
                      </a>
                      <a v-if="!isLogin && item.content.primaryLink"
                         href="#"
                         @click.prevent="navigateTo(item.content.primaryLink)"
                         class="banner-button primary-btn">
                        {{ item.content.primaryBtnText }}
                      </a>
                      <a v-if="item.content.thirdLink"
                         href="#"
                         @click.prevent="navigateTo(item.content.thirdLink)"
                         class="banner-button secondary-btn">
                        {{ item.content.thirdBtnText }}
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="arrowhead-box">
              <span @click="last" class="nav-arrow left">
                <img src="../../assets/images/index/right-arrow.png" alt="" class="arrow-icon rotated">
              </span>
              <span @click="next" class="nav-arrow right">
                <img src="../../assets/images/index/right-arrow.png" alt="" class="arrow-icon">
              </span>
            </div>
            <div class="swiper-pagination" ref="swiperPagination">
              <span v-for="(item, index) in bannerImages" :key="index" :class="{ active: translateX === index }"></span>
            </div>
          </div>
        </div>
      </div>

      <!-- 为您推荐 Section  -->
      <section class="section gpu-section">
        <div class="container">
          <div class="section-header">
            <h2 class="section-title">为您推荐</h2>
            <p class="section-description">
              专注于提供高性能、稳定可靠的 GPU 算力服务，兼具灵活配置与优质性价比。
            </p>
          </div>

          <div class="gpu-card-grid">
            <div
                v-for="(gpu, index) in gpus"
                :key="index"
                class="gpu-card"
                :class="{ 'recommended': gpu.recommended }"
                @click="navigateTo('/product')"
            >
              <div class="gpu-card-header">
                <h3 class="gpu-name">{{ gpu.name }}</h3>
                <span v-if="gpu.recommended" class="recommendation-tag">推荐</span>
                <span v-if="gpu.isNew" class="new-tag">NEW</span>
              </div>

              <div class="gpu-specs-pricing">
                <div class="specs-section">
                  <div class="spec-item">
                    <span class="spec-label">单精度:</span>
                    <span class="spec-value">{{ gpu.singlePrecision }} TFLOPS</span>
                  </div>
                  <div class="spec-item">
                    <span class="spec-label">半精度:</span>
                    <span class="spec-value">{{ gpu.halfPrecision }} Tensor TFL</span>
                  </div>
                </div>
                <div class="price-section">
                  <div class="gpu-pricing">
                    <span class="original-price" v-if="gpu.originalPrice">¥{{ gpu.originalPrice }}/时</span>
                    <span class="current-price">¥<span class="price-value">{{ gpu.price }}</span>/时</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <GpuComparison></GpuComparison>

      <!-- 核心优势 Section -->
      <section class="section services-section">
        <div class="container">
          <div class="section-header">
            <h2 class="section-title">核心优势</h2>
            <p class="section-description">
              专业铸造优秀,天工开物企业AI变革路上的好伙伴。
            </p>
          </div>

          <div class="services-grid">
            <div class="service-item" v-for="(service,index) in serviceList" :key="index">
              <div class="service-card">
                <i class="service-icon" :class="service.icon"></i>
                <h3 class="service-title">{{ service.title }}</h3>
                <div class="service-text"><p>{{service.desc}}</p></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Application Areas Section -->
      <section class="appsec-section">
        <div class="section-header">
          <h2 class="section-title">行业应用</h2>
          <p class="section-description">
            Applications
          </p>
        </div>

        <div class="appsec-container">
          <div class="appsec-grid">
            <!-- 第一行 -->
            <!-- 宽幅项目 (2x1) -->
            <div class="appsec-item appsec-wide">
              <div class="appsec-card" @mouseover="firstRowWide.hover = true" @mouseleave="firstRowWide.hover = false">
                <div class="appsec-image" :class="{ 'appsec-hover': firstRowWide.hover }">
                  <img :src="firstRowWide.image" :alt="firstRowWide.title">
                </div>
                <div class="appsec-cardtitle" :class="{ 'appsec-hover': firstRowWide.hover }">{{ firstRowWide.title }}</div>
              </div>
            </div>

            <!-- 两个高竖幅项目 (1x2) -->
            <div class="appsec-item appsec-tall" v-for="(app, index) in firstRowTallApps" :key="'tall-'+index">
              <div class="appsec-card" @mouseover="app.hover = true" @mouseleave="app.hover = false">
                <div class="appsec-image" :class="{ 'appsec-hover': app.hover }">
                  <img :src="app.image" :alt="app.title">
                </div>
                <div class="appsec-cardtitle" :class="{ 'appsec-hover': app.hover }">{{ app.title }}</div>
              </div>
            </div>

            <!-- 第二行 -->
            <!-- 两个小方形 (1x1) 放在第一行宽幅图片下方 -->
            <div class="appsec-item appsec-small" v-for="(app, index) in secondRowApps" :key="'small-'+index">
              <div class="appsec-card" @mouseover="app.hover = true" @mouseleave="app.hover = false">
                <div class="appsec-image" :class="{ 'appsec-hover': app.hover }">
                  <img :src="app.image" :alt="app.title">
                </div>
                <div class="appsec-cardtitle" :class="{ 'appsec-hover': app.hover }">{{ app.title }}</div>
              </div>
            </div>

            <!-- 第三行 -->
            <!-- 两个小方形 (1x1) -->
            <div class="appsec-item appsec-small" v-for="(app, index) in thirdRowSmallApps" :key="'third-small-'+index">
              <div class="appsec-card" @mouseover="app.hover = true" @mouseleave="app.hover = false">
                <div class="appsec-image" :class="{ 'appsec-hover': app.hover }">
                  <img :src="app.image" :alt="app.title">
                </div>
                <div class="appsec-cardtitle" :class="{ 'appsec-hover': app.hover }">{{ app.title }}</div>
              </div>
            </div>

            <!-- 一个宽幅项目 (2x1) -->
            <div class="appsec-item appsec-wide">
              <div class="appsec-card" @mouseover="thirdRowWide.hover = true" @mouseleave="thirdRowWide.hover = false">
                <div class="appsec-image" :class="{ 'appsec-hover': thirdRowWide.hover }">
                  <img :src="thirdRowWide.image" :alt="thirdRowWide.title">
                </div>
                <div class="appsec-cardtitle" :class="{ 'appsec-hover': thirdRowWide.hover }">{{ thirdRowWide.title }}</div>
              </div>
            </div>
          </div>
          <chat-ai/>
        </div>
      </section>

      <div class="recommendation-tag1">
        <div class="card1">
          <h1 class="banner-text1">为AI+千行百业，提供高性能算力服务</h1>
          <button class="consult-button1" @click="openContactModal">立即咨询</button>
        </div>
      </div>

      <transition name="fade">
        <div v-if="showContactModal" class="contact-modal-overlay" @click.self="closeContactModal">
          <div class="contact-modal">
            <button class="close-modal" @click="closeContactModal">
              &times;
            </button>
            <div class="contact-content">
              <div class="contact-item">
                <i class="am-icon-user"></i>
                <span>{{ contactInfo.name }}</span>
              </div>
              <div class="contact-item">
                <i class="am-icon-phone"></i>
                <span>{{ contactInfo.phone }}</span>
              </div>
            </div>
            <div class="contact-note">
              <p>欢迎随时来电咨询</p>
            </div>
          </div>
        </div>
      </transition>
    </div>

    <!-- 移动端布局 -->
    <div v-else class="mobile-layout">

      <div class="mobile-banner"
           @touchstart="handleTouchStart"
           @touchmove="handleTouchMove"
           @touchend="handleTouchEnd">
        <div class="mobile-banner-slider" :style="{ transform: `translateX(${-mobileCurrentSlide * 100}%)` }">
          <div class="mobile-slide" v-for="(item, index) in bannerImages" :key="index">
            <div class="mobile-slide-inner">
              <img :src="item.img" alt="" class="mobile-slide-img">
              <div class="mobile-banner-content" :class="'pos-' + item.content.position">
                <h2 class="mobile-banner-title" v-html="item.content.title"></h2>
                <p class="mobile-banner-text">{{ item.content.text }}</p>
                <div class="mobile-banner-actions">
                  <a v-if="!isLogin && item.content.secondaryLink"
                     href="#"
                     @click.prevent="navigateTo(item.content.secondaryLink)"
                     class="mobile-banner-button secondary-btn">
                    {{ item.content.secondaryBtnText }}
                  </a>
                  <a v-if="!isLogin && item.content.primaryLink"
                     href="#"
                     @click.prevent="navigateTo(item.content.primaryLink)"
                     class="mobile-banner-button primary-btn">
                    {{ item.content.primaryBtnText }}
                  </a>
                  <a v-if="item.content.thirdLink"
                     href="#"
                     @click.prevent="navigateTo(item.content.thirdLink)"
                     class="banner-button secondary-btn">
                    {{ item.content.thirdBtnText }}
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="mobile-banner-pagination">
    <span v-for="(item, index) in bannerImages"
          :key="index"
          :class="{ active: mobileCurrentSlide === index }"
          @click="goToSlide(index)"></span>
        </div>
      </div>

      <!-- 移动端 GPU 推荐 -->
      <section class="mobile-section mobile-gpu-section">
        <div class="mobile-section-header">
          <h2 class="mobile-section-title">为您推荐</h2>
          <p class="mobile-section-description">
            专注于提供高性能、稳定可靠的 GPU 算力服务
          </p>
        </div>

        <div class="mobile-gpu-list">
          <div class="mobile-gpu-card"
               v-for="(gpu, index) in gpus"
               :key="index"
               :class="{ 'recommended': gpu.recommended }"
               @click="navigateTo('/product')">
            <div class="mobile-gpu-header">
              <h3 class="mobile-gpu-name">{{ gpu.name }}</h3>
              <div class="mobile-gpu-tags">
                <span v-if="gpu.recommended" class="mobile-recommend-tag">推荐</span>
                <span v-if="gpu.isNew" class="mobile-new-tag">NEW</span>
              </div>
            </div>

            <div class="mobile-gpu-specs">
              <div class="mobile-spec-item">
                <span class="mobile-spec-label">单精度:</span>
                <span class="mobile-spec-value">{{ gpu.singlePrecision }} TFLOPS</span>
              </div>
              <div class="mobile-spec-item">
                <span class="mobile-spec-label">半精度:</span>
                <span class="mobile-spec-value">{{ gpu.halfPrecision }} Tensor TFL</span>
              </div>
            </div>

            <div class="mobile-gpu-price">
              <span class="mobile-original-price" v-if="gpu.originalPrice">¥{{ gpu.originalPrice }}/时</span>
              <span class="mobile-current-price">¥{{ gpu.price }}/时</span>
            </div>
          </div>
        </div>
      </section>

      <!-- 移动端 GPU 对比 -->
      <section class="mobile-section mobile-comparison-section">
        <div class="mobile-section-header">
          <h2 class="mobile-section-title">GPU性能对比</h2>
          <p class="mobile-section-description">
            专业GPU性能详细对比
          </p>
        </div>

        <div class="mobile-comparison-container">
          <div class="mobile-comparison-scroll">
            <table class="mobile-comparison-table">
              <thead>
              <tr>
                <th>GPU型号</th>
                <th v-for="gpu in comparisonGpus" :key="gpu.name">{{ gpu.name }}</th>
              </tr>
              </thead>
              <tbody>
              <tr>
                <td>架构</td>
                <td v-for="gpu in comparisonGpus" :key="gpu.name">{{ gpu.architecture }}</td>
              </tr>
              <tr>
                <td>FP16性能</td>
                <td v-for="gpu in comparisonGpus" :key="gpu.name">{{ gpu.fp16Performance }}</td>
              </tr>
              <tr>
                <td>FP32性能</td>
                <td v-for="gpu in comparisonGpus" :key="gpu.name">{{ gpu.fp32Performance }}</td>
              </tr>
              <tr>
                <td>显存</td>
                <td v-for="gpu in comparisonGpus" :key="gpu.name">{{ gpu.memory }}</td>
              </tr>
              <tr>
                <td>显存类型</td>
                <td v-for="gpu in comparisonGpus" :key="gpu.name">{{ gpu.memoryType }}</td>
              </tr>
              <tr>
                <td>带宽</td>
                <td v-for="gpu in comparisonGpus" :key="gpu.name">{{ gpu.bandwidth }}</td>
              </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>

      <!-- 移动端 核心优势 -->
      <section class="mobile-section mobile-services-section">
        <div class="mobile-section-header">
          <h2 class="mobile-section-title">核心优势</h2>
          <p class="mobile-section-description">
            专业铸造优秀,天工开物企业AI变革路上的好伙伴
          </p>
        </div>

        <div class="mobile-services-list">
          <div class="mobile-service-card" v-for="(service, index) in serviceList" :key="index">
            <div class="mobile-service-icon">
              <i :class="service.icon"></i>
            </div>
            <h3 class="mobile-service-title">{{ service.title }}</h3>
            <p class="mobile-service-desc">{{ service.desc }}</p>
          </div>
        </div>
      </section>

      <!-- 移动端 行业应用 -->
      <section class="mobile-section mobile-applications-section">
        <div class="mobile-section-header">
          <h2 class="mobile-section-title">行业应用</h2>
          <p class="mobile-section-description">
            Applications
          </p>
        </div>

        <div class="mobile-applications-grid">
          <div class="mobile-app-item" v-for="(app, index) in mobileApplications" :key="index">
            <div class="mobile-app-image">
              <img :src="app.image" :alt="app.title">
            </div>
            <h3 class="mobile-app-title">{{ app.title }}</h3>
          </div>
        </div>
      </section>

      <!-- 移动端 咨询按钮 -->
      <div class="mobile-consult-section">
        <h3 class="mobile-consult-title">为AI+千行百业，提供高性能算力服务</h3>
        <button class="mobile-consult-button" @click="openContactModal">立即咨询</button>
      </div>

      <!-- 移动端 联系弹窗 -->
      <transition name="mobile-fade">
        <div v-if="showContactModal" class="mobile-contact-overlay" @click.self="closeContactModal">
          <div class="mobile-contact-modal">
            <button class="mobile-close-modal" @click="closeContactModal">
              &times;
            </button>
            <div class="mobile-contact-content">
              <div class="mobile-contact-item">
                <i class="am-icon-user"></i>
                <span>{{ contactInfo.name }}</span>
              </div>
              <div class="mobile-contact-item">
                <i class="am-icon-phone"></i>
                <span>{{ contactInfo.phone }}</span>
              </div>
            </div>
            <div class="mobile-contact-note">
              <p>欢迎随时来电咨询</p>
            </div>
          </div>
        </div>
      </transition>
    </div>
    <Mider v-if="!isMobile"></Mider>
    <Footer></Footer>
    <chatAi></chatAi>
  </div>
</template>

<script>
// import Layout from "@/components/common/Layout";
import chatAi from "@/components/common/mider/chatAi";
import Mider from "@/components/common/mider/Mider";
import Footer from "@/components/common/footer/Footer";
import GpuComparison from "@/views/Index/GpuComparison";
import {postAnyData, getNotAuth, postNotAuth,getAnyData,postJsonData} from "@/api/login";


import {setToken} from "@/utils/auth";

export default {
  name: "IndexView",
  components: { chatAi,Footer, Mider, GpuComparison },
  computed: {
    translate() {
      return -this.translateX * 100 + "%";
    },
    isLogin() {
      return !! document.cookie.includes('Admin-Token');
    },
    mobileTranslateX() {
      return -this.mobileCurrentSlide * 100;
    },
    mobileApplications() {
      return [
        this.firstRowWide,
        ...this.firstRowTallApps,
        ...this.secondRowApps,
        ...this.thirdRowSmallApps,
        this.thirdRowWide
      ];
    }
  },
  data() {
    return {
      touchStartX: 0,
      touchEndX: 0,
      touchThreshold: 50, // 滑动阈值，单位px
      isMobile: false,
      mobileCurrentSlide: 0,
      showContactModal: false,
      contactInfo: {
        name: '王先生',
        phone: '13913283376'
      },
      tabIndex: 0,
      firstRowWide: {
        title: '工业制造',
        image: require("../../assets/images/index/gongyezhizao.webp"),
        hover: false,
      },
      firstRowTallApps: [
        {
          title: '自动驾驶',
          image: require("../../assets/images/index/zidongjiashi.webp"),
          hover: false,
        },
        {
          title: '智能交通',
          image: require("../../assets/images/index/zhinengjiaotong.webp"),
          hover: false,
        }
      ],
      secondRowApps: [
        {
          title: '智慧农业',
          image: require("../../assets/images/index/zhihuinongye.webp"),
          hover: false,
        },
        {
          title: '影视渲染',
          image: require("../../assets/images/index/yingshixuanran.webp"),
          hover: false,
        }
      ],
      thirdRowSmallApps: [
        {
          title: '医疗影像',
          image: require("../../assets/images/index/yiliaoyingxiang.webp"),
          hover: false,
        },
        {
          title: '金融风暴',
          image: require("../../assets/images/index/jinrongfengbao.webp"),
          hover: false,
        }
      ],
      thirdRowWide: {
        title: '能源科技',
        image: require("../../assets/images/index/nengyuankeji.webp"),
        hover: false,
      },
      bannerImages: [
        {
          img: require("/public/images/back1.webp"),
          content: {
            title: "天工开物",
            text: "构建AI应用周期服务的一站式算力云",
            position: "left",
            thirdLink: "/product",
            thirdBtnText: "立即购买"
          }
        },
        {
          img: require("/public/images/back2.webp"),
          content: {
            title: "专业AI训练平台",
            text: "为AI+千行百业，提供高性能算力服务",
            position: "left",
            secondaryLink: "/login",
            secondaryBtnText: "立即登录",
            primaryLink: '/register',
            primaryBtnText: "立即注册"
          }
        },
        {
          img: require("/public/images/back3.webp"),
          content: {
            title: "企业级GPU集群",
            text: "H100/H800/RTX 4090等高性能GPU随时可用，按需付费",
            position: "left",
          }
        }
      ],
      translateX: 0,
      tsion: true,
      tabList: [],
      swiperOptions: {
        loop: true,
        autoplay: {
          delay: 5000,
          disableOnInteraction: false,
        },
        pagination: {
          el: '.swiper-pagination',
          clickable: true
        },
        navigation: {
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev'
        }
      },
      serviceList: [
        { id: 1, icon: 'am-icon-shield', title: '数据安全', desc: '平台采取各种措施保证用户的数据安全，例如数据加密、防火墙、漏洞扫描和安全审计等措施，以防止数据泄露、篡改和丢失等风险。' },
        { id: 2, icon: 'am-icon-sliders', title: '部署灵活', desc: '租用GPU服务器具备比购买更高的灵活性，用户可以根据自己的需求随时调整租赁资源的配置及数量。' },
        { id: 3, icon: 'am-icon-server', title: '高可靠性', desc: '平台拥有完善的运维体系，采用丰富的数据备份、冗余技术以及定期检测等机制，保证租赁服务器的稳定性、易用性和安全性。' },
        { id: 4, icon: 'am-icon-rocket', title: '高处理性能', desc: '采用先进的GPU集群架构，平台能够大大提高计算速度和处理能力，可以在科学计算、深度学习等领域有更优秀的表现。' },
        { id: 5, icon: 'am-icon-credit-card', title: '低成本', desc: '购买GPU服务器需要投入较高的资金，而租赁可以让用户以较低的成本去获得相关基础设施，减轻了用户在预算上的负担。' },
        { id: 6, icon: 'am-icon-phone', title: '及时服务', desc: '提供365天 7*24小时技术支持及运维服务， 工程师现场及在线响应及电话支持。' }
      ],
      comparisonGpus: [
        {
          name: "A100",
          architecture: "Ampere",
          fp16Performance: "312 TFLOPS",
          fp32Performance: "19.5 TFLOPS",
          memory: "80 GB",
          memoryType: "HBM2",
          bandwidth: "2,039 GB/s"
        },
        {
          name: "A100",
          architecture: "Ampere",
          fp16Performance: "312 TFLOPS",
          fp32Performance: "19.5 TFLOPS",
          memory: "80 GB",
          memoryType: "HBM2",
          bandwidth: "2,039 GB/s"
        },
        {
          name: "A100",
          architecture: "Ampere",
          fp16Performance: "312 TFLOPS",
          fp32Performance: "19.5 TFLOPS",
          memory: "80 GB",
          memoryType: "HBM2",
          bandwidth: "2,039 GB/s"
        },
        {
          name: "V100",
          architecture: "Volta",
          fp16Performance: "125 TFLOPS",
          fp32Performance: "15.7 TFLOPS",
          memory: "32 GB",
          memoryType: "HBM2",
          bandwidth: "900 GB/s"
        },
        {
          name: "A6000",
          architecture: "Ampere",
          fp16Performance: "77.4 TFLOPS",
          fp32Performance: "38.7 TFLOPS",
          memory: "48 GB",
          memoryType: "GDDR6",
          bandwidth: "768 GB/s"
        },
        {
          name: "A5000",
          architecture: "Ampere",
          fp16Performance: "54.2 TFLOPS",
          fp32Performance: "27.8 TFLOPS",
          memory: "24 GB",
          memoryType: "GDDR6",
          bandwidth: "768 GB/s"
        },
        {
          name: "A4000",
          architecture: "Ampere",
          fp16Performance: "19.17 TFLOPS",
          fp32Performance: "19.17 TFLOPS",
          memory: "16 GB",
          memoryType: "GDDR6",
          bandwidth: "448 GB/s"
        }
      ],
      gpus: [
        {
          name: "A100 SXM4 / 80GB",
          singlePrecision: "156",
          halfPrecision: "19.5",
          originalPrice: "11.10",
          price: "6.66",
          recommended: true,
          isNew: false
        },
        {
          name: "RTX 3090 / 24GB",
          singlePrecision: "35.58",
          halfPrecision: "71.2",
          originalPrice: "2.30",
          price: "1.38",
          recommended: true,
          isNew: false
        },
        {
          name: "A100 PCIE / 80GB",
          singlePrecision: "156",
          halfPrecision: "19.5",
          originalPrice: "11.10",
          price: "6.66",
          recommended: true,
          isNew: false
        },
        {
          name: "RTX 4090 / 24GB",
          singlePrecision: "82.58",
          halfPrecision: "164.5",
          originalPrice: "2.97",
          price: "1.78",
          recommended: false,
          isNew: true
        },
        {
          name: "RTX 4090D / 24GB",
          singlePrecision: "73.54",
          halfPrecision: "147.1",
          originalPrice: "2.93",
          price: "1.76",
          recommended: false,
          isNew: false
        },
        {
          name: "RTX 3060 / 12GB",
          singlePrecision: "12.7",
          halfPrecision: "51.2",
          originalPrice: "1.00",
          price: "0.60",
          recommended: false,
          isNew: false
        },
        {
          name: "RTX A4000 / 16GB",
          singlePrecision: "19.17",
          halfPrecision: "76.7",
          originalPrice: "1.53",
          price: "0.92",
          recommended: false,
          isNew: false
        },
        {
          name: "Tesla P40 / 24GB",
          singlePrecision: "5.9",
          halfPrecision: "11.76",
          originalPrice: "1.35",
          price: "0.81",
          recommended: false,
          isNew: false
        },

      ],
      activeIndex: 0,
    };
  },
  created() {
    this.fetchRecommendations();
    const url = new URL(window.location.href);
    const token = url.searchParams.get('token');

    if (token) {
      const hasRefreshed = localStorage.getItem('hasRefreshedWithToken');
      // console.log('需要修改的参数为', url.origin + url.pathname)
      if (!hasRefreshed) {
        setToken(token);
        localStorage.setItem('hasRefreshedWithToken', 'true');
        // 直接修改 window.location（移除所有查询参数）
        // console.log('需要修改的参数为', url.origin + url.pathname)

      } else {
        window.location.href = url.origin + url.pathname;
        localStorage.setItem('hasRefreshedWithToken', 'false');
      }
    }
  },
  mounted() {
    this.checkIsMobile();
    window.addEventListener('resize', this.checkIsMobile);

    // Banner auto-play
    this.desktopInterval = setInterval(() => {
      this.next();
    }, 5000);

    this.mobileInterval = setInterval(() => {
      this.mobileNext();
    }, 5000);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.checkIsMobile);
    clearInterval(this.desktopInterval);
    clearInterval(this.mobileInterval);

  },
  methods: {

    async fetchRecommendations() {
      try {
        getNotAuth("/system/recommend/list").then(req =>{
          this.gpus = req.data.rows.map(item => ({
            ...item,
            isNew: item.isnew === 0,
            recommended: item.recommended === 0
          }))
          // this.gpus = req.data.rows
        })
      } catch (error) {
        console.error('获取GPU推荐列表失败:', error);
      }
    },

    handleTouchStart(e) {
      this.touchStartX = e.touches[0].clientX;
    },

    handleTouchMove(e) {
      this.touchEndX = e.touches[0].clientX;
    },

    handleTouchEnd() {
      if (!this.touchStartX || !this.touchEndX) return;

      const diff = this.touchStartX - this.touchEndX;

      // 向右滑动
      if (diff > this.touchThreshold) {
        this.mobileNext();
      }

      // 向左滑动
      if (diff < -this.touchThreshold) {
        this.mobilePrev();
      }

      // 重置触摸位置
      this.touchStartX = 0;
      this.touchEndX = 0;
    },

    mobilePrev() {
      this.mobileCurrentSlide = (this.mobileCurrentSlide - 1 + this.bannerImages.length) % this.bannerImages.length;
      this.resetMobileInterval();
    },

    mobileNext() {
      this.mobileCurrentSlide = (this.mobileCurrentSlide + 1) % this.bannerImages.length;
      this.resetMobileInterval();
    },

    resetMobileInterval() {
      clearInterval(this.mobileInterval);
      this.mobileInterval = setInterval(() => {
        this.mobileNext();
      }, 5000);
    },
    checkIsMobile() {
      this.isMobile = window.innerWidth <= 768;
    },
    openContactModal() {
      this.showContactModal = true;
    },
    closeContactModal() {
      this.showContactModal = false;
    },
    handleBannerAction(item) {
      if (item.content.link) {
        this.$router.push(item.content.link);
      }
      this.$ga.event('Banner', 'click', item.content.title);
    },
    navigateTo(path) {
      if (this.currentPath && this.currentPath !== path) {
        this.previousActivePath = this.currentPath;

        this.$nextTick(() => {
          const navLinks = document.querySelectorAll('.nav-link, .btn-login');
          navLinks.forEach(link => {
            if ((link.classList.contains('active') ||
                    (path === '/login' && link.classList.contains('btn-login'))) &&
                !link.classList.contains('active-exit')) {
              link.classList.add('active-exit');

              setTimeout(() => {
                link.classList.remove('active-exit');
              }, 300);
            }
          });

          this.currentPath = path;
        });
      } else {
        this.currentPath = path;
      }

      if (this.$route.path === path) {
        this.$nextTick(() => {
          window.scrollTo({
            top: 0,
            behavior: 'instant'
          });
          this.$router.go(0);
        });
      } else {
        this.$router.push(path);
        window.scrollTo({
          top: 0,
          behavior: 'instant'
        });
      }
    },
    last() {
      this.translateX = (this.translateX - 1 + this.bannerImages.length) % this.bannerImages.length;
      this.tsion = true;
    },
    next() {
      this.translateX = (this.translateX + 1) % this.bannerImages.length;
      this.tsion = true;
    },

    goToSlide(index) {
      this.mobileCurrentSlide = index;
      // 重置自动播放计时器
      clearInterval(this.mobileInterval);
      this.mobileInterval = setInterval(() => {
        this.mobileNext();
      }, 5000);
    },
    changeTab(index) {
      this.tabIndex = index;
    },
    useGpu(gpu) {
      console.log(`Selected GPU: ${gpu.name}`);
    },
    getCustomSolution() {
      console.log('Get Custom Solution');
    },
    contactUs() {
      console.log('Contact Us');
    },
    prevSlide() {
      this.activeIndex = (this.activeIndex > 0) ? this.activeIndex - 1 : this.slideshow.length - 1;
    },
    nextSlide() {
      this.activeIndex = (this.activeIndex < this.slideshow.length - 1) ? this.activeIndex + 1 : 0;
    },
    selectGpu(gpu) {
      console.log(`Selected GPU: ${gpu.name}`);
    },
  },
  watch: {
    translateX(newVal) {
      const dots = this.$refs.swiperPagination?.querySelectorAll("span");
      if (dots) {
        dots.forEach((dot, index) => {
          dot.classList.toggle("active", index === newVal);
        });
      }
    }
  }
};
</script>

<style scoped>
/* 通用样式 */
.section-header {
  text-align: center;
  margin-bottom: 40px;
}

.section-title {
  font-size: 28px;
  color: #333;
  position: relative;
  display: inline-block;
  margin-bottom: 15px;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background-color: #2196f3;
}

.section-description {
  color: #666;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* 电脑端样式 */
.desktop-layout {
  display: block;
}

/* Banner Section */
.banner-section {
  padding: 0;
}

.banner-container {
  width: 100%;
  max-width: 2560px;
  max-height: 1920px;
  margin: 0;
  position: relative;
}

.big-box {
  width: 100%;
  height: 93vh;
  overflow: hidden;
  position: relative;
  box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.img-box {
  height: 100%;
  width: 100%;
}

.show-box {
  display: flex;
  height: 100%;
  width: 100%;
  transition: all 0.5s;
}

.slide-item {
  position: relative;
  flex-shrink: 0;
  width: 100%;
  height: 100%;
}

.slide-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.banner-content {
  position: absolute;
  top: 40%;
  background-color: rgba(0, 0, 0, 0);
  padding: 40px;
  font-weight: 500;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0);
  max-width: 800px;
  color: #fdfcfc;
  z-index: 4;
}

.banner-title {
  font-size: 7vh;
  line-height: 1;
  margin-bottom: 0px;
  font-weight: 700;
}

.banner-text {
  font-size: 3vh;
  line-height: 3.5;
  margin-bottom: 0px;
  opacity: 0.9;
}

.banner-actions {
  display: flex;
  gap: 20px;
  z-index: 20;
}

.banner-button {
  padding: 10px 5px;
  border-radius: 0px;
  font-size: 1.5rem;
  font-weight:200;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(248, 245, 245, 0.2);
  text-decoration: none;
  text-align: center;
  display: inline-block;
  background-color: transparent;
  border: 2px solid white;
  color: white;
  position: relative;
  z-index: 3;
  pointer-events: auto;
  width: 150px;
}

.banner-button:hover {
  background-color: black;
}

.primary-btn {
  color: white;
  border: 1px solid white;
}

.primary-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.secondary-btn {
  color: white;
  background-color: #f67b3d;
  border-color: #f67b3d;
}

.secondary-btn:hover {
  background-color: #f67b3d;
}

.pos-left {
  left: 8%;
  transform: translateY(-50%);
  text-align: left;
  animation: slideInFromLeft 0.8s ease;
}

.pos-right {
  right: 10%;
  transform: translateY(-50%);
  text-align: left;
  animation: slideInFromRight 0.8s ease;
}

.pos-center {
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  animation: fadeIn 0.8s ease;
}

.arrowhead-box {
  position: absolute;
  top: 50%;
  width: 100%;
  display: flex;
  justify-content: space-between;
  transform: translateY(-50%);
  padding: 0 20px;
  z-index: 10;
  pointer-events: none;
}

.nav-arrow {
  display: flex;
  pointer-events: auto;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(0,0,0,0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-arrow:hover {
  background: rgba(0,0,0,0.5);
}

.arrow-icon {
  height: 24px;
  width: 24px;
  transition: transform 0.3s;
  filter: invert(1);
}

.rotated {
  transform: rotate(180deg);
}

.swiper-pagination {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
  z-index: 3;
}

.swiper-pagination span {
  width: 12px;
  height: 12px;
  background: rgba(255,255,255,0.5);
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
}

.swiper-pagination span.active {
  width: 30px;
  border-radius: 6px;
  background: #fff;
}

/* GPU Section */
.gpu-specs-pricing {
  display: flex;
  height: 8vh;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0px;
}

.specs-section {
  flex: 1;
}

.price-section {
  text-align: right;
  min-width: 120px;
}

.gpu-pricing {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.original-price {
  text-decoration: line-through;
  color: #999;
  font-size: 14px;
}

.current-price {
  font-size: 16px;
  color: #333;
}

.price-value {
  font-size: 24px;
  font-weight: 700;
  color: #2196f3;
}

/* GPU Card Grid */
.gpu-section {
  background-color: #f9f9f9;
  width: 100%;
  max-width: 2560px;
  max-height: 800px;
  margin: 0 auto;
  /*margin-bottom: -10vh;*/

}

.gpu-card-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  padding: 0 50px;
  margin-bottom: 40px;
}

.gpu-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  padding: 20px;
  display: flex;
  flex-direction: column;
  position: relative;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.gpu-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
  border: 1px solid #2196f3;
}

.gpu-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.gpu-name {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-left: 0px;
  margin-bottom: 15px;
}

.recommendation-tag {
  background-color: #2196f3;
  color: white;
  font-size: 12px;
  padding: 3px 8px;
  border-radius: 4px;
}

.new-tag {
  background-color: #ff4d4f;
  color: white;
  font-size: 12px;
  padding: 3px 8px;
  border-radius: 4px;
}

.spec-item {
  display: flex;
  margin-bottom: 8px;
  font-size: 14px;
}

.spec-label {
  color: #666;
  margin-right: 5px;
  flex-shrink: 0;
  width: 60px;
}

.spec-value {
  color: #333;
  font-weight: 500;
}

/* GPU 性能对比 Section */
.gpu-comparison-section {
  background-color: #f9f9f9;
  width: 100%;
  max-width: 2560px;
  margin: 0 auto;
  margin-bottom: -12vh;
}

.gpu-comparison-table {
  width: 100%;
  padding: 0px 50px;
  border-collapse: collapse;
  margin: 0;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  border-radius: 10px;
  overflow:hidden;
}

.gpu-comparison-table thead {
  background-color: #cddfec;
}

.gpu-comparison-table thead tr th {
  padding: 15px 20px;
  text-align: center;
  font-weight: 600;
  color: #2c3e50;
  font-size: 16px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.gpu-comparison-table tbody tr td {
  padding: 15px 20px;
  text-align: center;
  border-bottom: 1px solid #e0e5eb;
  transition: background-color 0.3s ease;
  font-size: 15px;
  color: #333;
}

.gpu-comparison-table tbody tr:nth-child(even) {
  background-color: #f9fafc;
}

.gpu-comparison-table tbody tr:hover {
  background-color: #f1f6fd;
}

/* Services Section */
.services-section {
  background-color: #f9f9f9;
  width: 100%;
  max-width: 2560px;
}

.services-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 0px 50px;
}

.service-item {
  width: 33.33%;
  padding: 0 11px;
}

.service-item:nth-child(-n+3) {
  margin-bottom: 40px;
}

.service-card {
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.05);
  padding: 25px;
  height: 100%;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: left;
}

.service-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.service-icon {
  font-size: 48px;
  margin-bottom: -30px;
  color: #2196f3;
  transition: all 0.3s ease;
}

.service-card:hover .service-icon {
  transform: scale(1.1);
}

.service-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
}

.service-text {
  font-size: 14px;
  color: #666;
  line-height: 2;
}

/* Application Areas Section */
.appsec-section {
  background-color: #f8f9fa;
  max-width: 2560px;
  max-height: 1000px;
  margin: 0 auto;
}

.appsec-container {
  margin: 0 auto;
  max-height: 1000px;
  width: 100%;
  padding: 0 50px;
}

.appsec-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-auto-rows: minmax(180px, auto);
  gap: 20px;
}

.appsec-item {
  position: relative;
  overflow: hidden;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.appsec-wide {
  grid-column: span 2;
  grid-row: span 1;
  height: 180px;
}

.appsec-tall {
  grid-column: span 1;
  grid-row: span 2;
  height: 380px;
}

.appsec-small {
  grid-column: span 1;
  grid-row: span 1;
  height: 180px;
}

.appsec-card {
  position: relative;
  height: 100%;
  width: 100%;
  cursor: pointer;
  overflow: hidden;
}

.appsec-image {
  height: 100%;
  width: 100%;
  transition: transform 0.5s ease;
}

.appsec-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.appsec-cardtitle {
  position: absolute;
  bottom: 20px;
  left: 20px;
  color: white;
  font-size: 18px;
  font-weight: bold;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6);
  z-index: 2;
  transition: transform 0.3s ease, font-size 0.3s ease;
}

.appsec-card::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
  z-index: 1;
}

.appsec-hover {
  transform: scale(1.05);
}

/* Recommendation Section */
.recommendation-tag1 {
  margin-top: 3vh;
  position: relative;
  width: 100%;
  height: 120px;
  background-image: url("../../assets/images/index/back3.png");
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card1 {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  max-width: 1200px;
  padding: 0 20px;
  z-index: 2;
}

.banner-text1 {
  color: white;
  font-size: 2rem;
  font-weight: bold;
  margin: 0;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.consult-button1 {
  background-color: white;
  color: #0d47a1;
  border: none;
  border-radius: 25px;
  padding: 10px 25px;
  font-size: 1.7rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.consult-button1:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  background-color: #0a69ff;
  color: white;
}

/* Contact Modal */
.contact-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.contact-modal {
  position: relative;
  background-color: white;
  padding: 30px;
  border-radius: 8px;
  width: 90%;
  max-width: 250px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  text-align: center;
}

.close-modal {
  position: absolute;
  top: 10px;
  right: 15px;
  font-size: 30px;
  background: none;
  border: none;
  cursor: pointer;
  color: #999;
}

.contact-content {
  margin-bottom: 20px;
}

.contact-item {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
  font-size: 18px;
}

.contact-item i {
  margin-right: 10px;
  color: #2196f3;
}

.contact-note {
  color: #666;
  font-size: 14px;
  line-height: 1.6;
}

/* Animations */
@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-50px) translateY(-50%);
  }
  to {
    opacity: 1;
    transform: translateX(0) translateY(-50%);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(50px) translateY(-50%);
  }
  to {
    opacity: 1;
    transform: translateX(0) translateY(-50%);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 移动端样式 */
.mobile-layout {
  display: none;
}

/* 移动端响应式设计 */
@media (max-width: 768px) {
  .desktop-layout {
    display: none;

  }
  .container, .banner-section, .services-grid, .appsec-container {
    max-width: 10%;
    padding-left: 15px;
    padding-right: 15px;
    overflow-x: hidden;
  }

  .mobile-layout {
    display: block;
    overflow-x: hidden;

    /*padding: 0 15px;*/
  }


    /* 修改移动端轮播图样式 */
  .mobile-banner {
    position: relative;
    width: 100%;
    height: 100%;
    min-height: 30vh;
    overflow: hidden;
    touch-action: pan-y;
    user-select: none;
    /*margin-bottom: -5vh;*/
  }

  .mobile-banner-slider {
    display: flex;
    height: 100%;
    transition: transform 0.5s ease;
  }

  .mobile-slide {
    flex: 0 0 100%;
    width: 100%;
    height: 70%;
    position: relative;
  }

  .mobile-slide-inner {
    width: 100%;
    height: 100%;
    position: relative;
  }

  .mobile-slide-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .mobile-banner-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 15px;
    /*background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);*/
    color: white;
    z-index: 2;
  }

  .mobile-banner-title {
    font-size: 1.5rem;
    margin-bottom: 8px;
    font-weight: bold;
    line-height: 1.3;
  }

  .mobile-banner-text {
    font-size: 0.9rem;
    margin-bottom: 12px;
    line-height: 1.4;
  }

  .mobile-banner-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
  }

  .mobile-banner-button {
    padding: 8px 12px;
    font-size: 14px;
    border-radius: 4px;
    text-decoration: none;
    font-weight: 500;
  }

  .mobile-banner-button.primary-btn {
    background-color: transparent;
    border: 1px solid white;
    color: white;
  }

  .mobile-banner-button.secondary-btn {
    background-color: #f67b3d;
    color: white;
    border: none;
  }

  .mobile-banner-pagination {
    position: absolute;
    bottom: 10px;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    gap: 8px;
    z-index: 3;
  }

  .mobile-banner-pagination span {
    display: block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: rgba(255,255,255,0.5);
    transition: all 0.3s ease;
    cursor: pointer;
  }

  .mobile-banner-pagination span.active {
    background-color: white;
    width: 20px;
    border-radius: 4px;
  }



  /* 移动端 GPU 推荐 */
  .mobile-section {
    padding: 30px 0;
  }

  .mobile-section-header {
    text-align: center;
    margin-bottom: 20px;
  }

  .mobile-section-title {
    font-size: 22px;
    color: #333;
    position: relative;
    display: inline-block;
    margin-bottom: 10px;
  }

  .mobile-section-title::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 2px;
    background-color: #2196f3;
  }

  .mobile-section-description {
    font-size: 14px;
    color: #666;
    max-width: 80%;
    margin: 0 auto;
  }

  .mobile-gpu-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
  }

  .mobile-gpu-card {
    background-color: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  }

  .mobile-gpu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
  }

  .mobile-gpu-name {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
  }

  .mobile-gpu-tags {
    display: flex;
    gap: 5px;
  }

  .mobile-recommend-tag {
    background-color: #2196f3;
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 3px;
  }

  .mobile-new-tag {
    background-color: #ff4d4f;
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 3px;
  }

  .mobile-gpu-specs {
    margin-bottom: 10px;
  }

  .mobile-spec-item {
    display: flex;
    margin-bottom: 5px;
    font-size: 13px;
  }

  .mobile-spec-label {
    color: #666;
    margin-right: 5px;
    width: 60px;
  }

  .mobile-spec-value {
    color: #333;
    font-weight: 500;
  }

  .mobile-gpu-price {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .mobile-original-price {
    font-size: 12px;
    color: #999;
    text-decoration: line-through;
  }

  .mobile-current-price {
    font-size: 16px;
    font-weight: bold;
    color: #2196f3;
  }

  /* 移动端 GPU 对比 */
  .mobile-comparison-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    margin: 0 -15px;
    padding: 0 15px;
  }

  .mobile-comparison-scroll {
    min-width: 600px;
  }

  .mobile-comparison-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 13px;
  }

  .mobile-comparison-table th,
  .mobile-comparison-table td {
    padding: 10px;
    text-align: center;
    border: 1px solid #eee;
  }

  .mobile-comparison-table thead {
    background-color: #f5f5f5;
  }

  .mobile-comparison-table th {
    font-weight: 600;
    color: #333;
  }

  /* 移动端 核心优势 */
  .mobile-services-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }

  .mobile-service-card {
    background-color: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    text-align: center;
  }

  .mobile-service-icon {
    font-size: 30px;
    color: #2196f3;
    margin-bottom: 10px;
  }

  .mobile-service-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
  }

  .mobile-service-desc {
    font-size: 12px;
    color: #666;
    line-height: 1.5;
  }

  /* 移动端 行业应用 */
  .mobile-applications-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }

  .mobile-app-item {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    height: 120px;
  }

  .mobile-app-image {
    width: 100%;
    height: 100%;
  }

  .mobile-app-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
  }

  .mobile-app-item:hover .mobile-app-image img {
    transform: scale(1.05);
  }

  .mobile-app-title {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 10px;
    background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);
    color: white;
    font-size: 14px;
    font-weight: 600;
    margin: 0;
  }

  /* 移动端 咨询按钮 */
  .mobile-consult-section {
    background-color: #2196f3;
    padding: 20px;
    text-align: center;
    margin: 30px -15px 0;
  }

  .mobile-consult-title {
    color: white;
    font-size: 18px;
    margin-bottom: 15px;
  }

  .mobile-consult-button {
    background-color: white;
    color: #2196f3;
    border: none;
    border-radius: 25px;
    padding: 10px 25px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .mobile-consult-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  }

  /* 移动端 联系弹窗 */
  .mobile-contact-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
  }

  .mobile-contact-modal {
    position: relative;
    background-color: white;
    border-radius: 10px;
    width: 80%;
    max-width: 300px;
    padding: 20px;
    text-align: center;
  }

  .mobile-close-modal {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 24px;
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
  }

  .mobile-contact-content {
    margin: 20px 0;
  }

  .mobile-contact-item {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    font-size: 16px;
  }

  .mobile-contact-item i {
    margin-right: 10px;
    color: #2196f3;
  }

  .mobile-contact-note {
    color: #666;
    font-size: 14px;
  }

  /* 移动端动画 */
  @keyframes mobile-fade {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .mobile-fade-enter-active, .mobile-fade-leave-active {
    transition: all 0.3s ease;
  }

  .mobile-fade-enter, .mobile-fade-leave-to {
    opacity: 0;
    transform: translateY(20px);
  }
}

/* 响应式设计 - 平板 */
@media (min-width: 769px) and (max-width: 1024px) {
  .gpu-card-grid {
    grid-template-columns: repeat(2, 1fr);
  }



  .services-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .appsec-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .appsec-wide {
    grid-column: span 2;
  }

  .appsec-tall {
    grid-column: span 1;
    grid-row: span 1;
    height: 180px;
  }
}



</style>
