"use strict";(self["webpackChunkportal_ui"]=self["webpackChunkportal_ui"]||[]).push([[5648],{5648:function(e,s,t){t.r(s),t.d(s,{default:function(){return n}});var i=function(){var e=this,s=e._self._c;return s("div",[e.visible?s("div",{staticClass:"modal-overlay"},[s("div",{staticClass:"modal-container"},[e._m(0),s("div",{staticClass:"modal-body"},[s("div",{staticClass:"section-title"},[e._v("计费方式")]),s("div",{staticClass:"billing-tabs"},e._l(e.billingOptions,(function(t,i){return s("div",{key:i,staticClass:"billing-tab",class:{active:e.selectedBillingMethod===t.value},on:{click:function(s){e.selectedBillingMethod=t.value}}},[e._v(" "+e._s(t.label)+" ")])})),0),s("div",{staticClass:"section-title"},[e._v("选择主机")]),s("div",{staticClass:"specs-example-table"},[e._m(1),s("div",{staticClass:"specs-example-row"},[s("div",{staticClass:"specs-example-cell"},[e._v(e._s(e.server.name))]),s("div",{staticClass:"specs-example-cell"},[e._v(e._s(e.server.graphicsCardNumber)+"卡")]),s("div",{staticClass:"specs-example-cell"},[e._v(e._s(e.server.videoMemory)+"GB")]),s("div",{staticClass:"specs-example-cell"},[e._v(e._s(e.server.gpuNuclearNumber)+"核")]),s("div",{staticClass:"specs-example-cell"},[e._v(e._s(e.server.internalMemory)+"GB")]),s("div",{staticClass:"specs-example-cell"},[e._v(e._s(e.server.systemDisk)+"GB")])]),s("div",{staticClass:"server-card-footer"},[s("div",{staticClass:"server-price"},[e._v("¥ "+e._s(e.server[e.selectedBillingMethod])),s("span",{staticClass:"spec-label"},[e._v(" "+e._s(e.priceUnit))])])])]),s("div",{staticClass:"section-title"},[e._v("实例规格")]),s("div",{staticClass:"specs-example-table"},[e._m(2),s("div",{staticClass:"specs-example-row"},[s("div",{staticClass:"specs-example-cell"},[e._v(e._s(e.server.name))]),s("div",{staticClass:"specs-example-cell"},[e._v(e._s(e.server.gpuNuclearNumber)+"核心")]),s("div",{staticClass:"specs-example-cell"},[e._v(e._s(e.server.internalMemory)+"GB")]),s("div",{staticClass:"specs-example-cell"},[e._v(e._s(e.server.systemDisk)+"GB")]),s("div",{staticClass:"specs-example-cell"},[e._v("免费"+e._s(e.server.dataDisk)+"GB SSD")])])]),s("div",{staticClass:"rental-duration"},[s("div",{staticClass:"duration-label"},[e._v("租用时长：")]),s("div",{staticClass:"duration-selector"},[s("select",{directives:[{name:"model",rawName:"v-model",value:e.selectedDuration,expression:"selectedDuration"}],staticClass:"duration-select",on:{change:function(s){var t=Array.prototype.filter.call(s.target.options,(function(e){return e.selected})).map((function(e){var s="_value"in e?e._value:e.value;return s}));e.selectedDuration=s.target.multiple?t:t[0]}}},e._l(e.currentDurationOptions,(function(t,i){return s("option",{key:i,domProps:{value:t.value}},[e._v(" "+e._s(t.label)+" ")])})),0)])]),s("div",{staticClass:"price-summary"},[s("div",{staticClass:"price-label"},[e._v("配置费用：")]),s("div",{staticClass:"price-value"},[e._v("¥ "+e._s(e.totalPrice)+" 元 ")])])]),s("div",{staticClass:"modal-footer"},[s("button",{staticClass:"cancel-button",on:{click:e.closeModal}},[e._v("取消")]),s("button",{staticClass:"confirm-button",on:{click:e.showConfirmDialog}},[e._v(" 立即租赁 ")])])])]):e._e(),e.showConfirmation?s("div",{staticClass:"confirm-overlay"},[s("div",{staticClass:"confirm-dialog"},[s("div",{staticClass:"confirm-message"},[e._v("是否确认订单？")]),s("div",{staticClass:"confirm-footer"},[s("button",{staticClass:"confirm-cancel",on:{click:function(s){e.showConfirmation=!1}}},[e._v("取消")]),s("button",{staticClass:"confirm-ok",on:{click:e.confirmOrder}},[e._v("确认")])])])]):e._e()])},l=[function(){var e=this,s=e._self._c;return s("div",{staticClass:"modal-header1"},[s("h2",[e._v("订单确认")])])},function(){var e=this,s=e._self._c;return s("div",{staticClass:"specs-example-row"},[s("div",{staticClass:"specs-example-cell"},[e._v("GPU型号")]),s("div",{staticClass:"specs-example-cell"},[e._v("GPU")]),s("div",{staticClass:"specs-example-cell"},[e._v("显存")]),s("div",{staticClass:"specs-example-cell"},[e._v("vCPU")]),s("div",{staticClass:"specs-example-cell"},[e._v("内存")]),s("div",{staticClass:"specs-example-cell"},[e._v("系统盘")])])},function(){var e=this,s=e._self._c;return s("div",{staticClass:"specs-example-row"},[s("div",{staticClass:"specs-example-cell"},[e._v("GPU型号")]),s("div",{staticClass:"specs-example-cell"},[e._v("vCPU")]),s("div",{staticClass:"specs-example-cell"},[e._v("内存")]),s("div",{staticClass:"specs-example-cell"},[e._v("系统盘")]),s("div",{staticClass:"specs-example-cell"},[e._v("数据盘")])])}],a={name:"OrderDetail",props:{visible:{type:Boolean,default:!0},server:{type:Object,default:()=>({})},selectedBillingMethod:{type:String}},data(){return{defaultDiskSize:50,selectedBillingMethod:"priceDay",selectedDuration:1,needExtraDisk:!1,showConfirmation:!1,billingOptions:[{label:"按量计费",value:"priceHour",unit:"/小时"},{label:"包日",value:"priceDay",unit:"/日"},{label:"包月",value:"priceMouth",unit:"/月"},{label:"包年",value:"priceYear",unit:"/年"}],durationOptionsHour:[{label:"1小时",value:1},{label:"2小时",value:2},{label:"4小时",value:4},{label:"8小时",value:8},{label:"12小时",value:12},{label:"24小时",value:24}],durationOptionsDay:[{label:"1天",value:1},{label:"2天",value:2},{label:"3天",value:3},{label:"4天",value:4},{label:"5天",value:5},{label:"6天",value:6}],durationOptionsWeek:[{label:"1月",value:1},{label:"2月",value:2},{label:"3月",value:3},{label:"4月",value:4},{label:"5月",value:5},{label:"6月",value:6},{label:"7月",value:7},{label:"8月",value:8}],durationOptionsMonth:[{label:"1年",value:1},{label:"2年",value:2},{label:"3年",value:3}]}},computed:{currentDurationOptions(){switch(this.selectedBillingMethod){case"priceHour":return this.totalPrice=this.server["priceHour"],this.durationOptionsHour;case"priceDay":return this.totalPrice=this.server["priceDay"],this.durationOptionsDay;case"priceMouth":return this.totalPrice=this.server["priceMouth"],this.durationOptionsWeek;case"priceYear":return this.totalPrice=this.server["priceYear"],this.durationOptionsMonth;default:return this.durationOptionsDay}},totalPrice(){const e=this.server[this.selectedBillingMethod]||0;return(e*this.selectedDuration).toFixed(2)},priceUnit(){switch(this.selectedBillingMethod){case"priceHour":return"/小时";case"priceDay":return"/日";case"priceMouth":return"/月";case"priceYear":return"/年";default:return""}},totalTime(){switch(this.selectedBillingMethod){case"priceHour":return"小时";case"priceDay":return"天";case"priceMouth":return"月";case"priceYear":return"年";default:return"小时"}}},watch:{selectedBillingMethod(){this.selectedDuration=this.currentDurationOptions[0]?.value||1},selectedDuration(){this.totalPrice=this.server[this.selectedBillingMethod]*this.selectedDuration},selectedDuration:{handler(e){this.$emit("time-updated",e+this.totalTime)},immediate:!0},totalPrice:{handler(e){this.$emit("price-updated",e)},immediate:!0}},methods:{closeModal(){this.$emit("close")},showConfirmDialog(){this.showConfirmation=!0},confirmOrder(){this.showConfirmation=!1,this.$emit("orderSubmitted");const e={serverId:this.server.id,serverName:this.server.name,billingMethod:this.selectedBillingMethod,duration:this.selectedDuration,needExtraDisk:this.needExtraDisk,price:this.totalPrice,specs:{gpuModel:this.server.name,vcpu:this.server.vcpu,systemDisk:this.server.systemDisk,cloudDisk:this.server.cloudDisk||0,memory:this.server.memory,videoMemory:this.server.videoMemory}};this.$emit("order-success",e),this.closeModal()}}},r=a,c=t(1001),o=(0,c.Z)(r,i,l,!1,null,"50b2ba60",null),n=o.exports}}]);
//# sourceMappingURL=5648.0d39ed99.js.map