// 引入 axios
import axios from "axios";

import Cookies from 'js-cookie'

let base = '/api';
// let base = ' http://114.215.252.21/prod-api';
// let base = ' https://test.tiangongkaiwu.top/prod-api';
   // let base = ' http://192.168.110.109:8080';
//let base = ' https://tiangongkaiwu.top/prod-api';

//传送json格式的Post请求55
export const postAnyData=(url,params)=>{
    return axios({
        headers: {
            'authorization': Cookies.get("Admin-Token"), // 必须设置
            'Content-Type': 'application/x-www-form-urlencoded' // 必须设置
        },
        method:'post',
        url:`${base}${url}`,
        data: params,
    })
}
export const postJsonData =(url,parmas)=>{
    return axios({
        headers:{
            'authorization': Cookies.get("Admin-Token"), // 必须设置
            'Content-Type': 'application/json;charset=UTF-8' // 必须设置
        },
        method:'post',
        url:`${base}${url}`,
        data:parmas
    })
}

export const postLogin=(url,params)=> {
    return axios({
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 必须设置
        },
        method: 'post',
        url: `${base}${url}`,
        data: params
    })
}
export const postNotAuth=(url,params)=> {
    return axios({
        headers: {
            'Content-Type': 'application/json;charset=UTF-8' // 必须设置
        },
        method: 'post',
        url: `${base}${url}`,
        data: params
    })
}
export const getNotAuth=(url,params)=> {
    return axios({
        headers: {
            'Content-Type': 'application/json;charset=UTF-8' // 必须设置
        },
        method: 'get',
        url: `${base}${url}`,
        params: params
    })
}

export const getAnyData=(url,params)=>{
    return axios({
        headers: {
            'authorization': Cookies.get("Admin-Token"), // 必须设置
            'Content-Type': 'application/x-www-form-urlencoded' // 必须设置
        },
        method:'get',
        url:`${base}${url}`,
        params: params
    })
}
