{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"Layout\", [_c(\"div\", {\n    staticClass: \"about-banner\"\n  }, [_c(\"div\", {\n    staticClass: \"about-banner-bg\",\n    staticStyle: {\n      background: \"url('images/earth.gif') center/cover\",\n      opacity: \"0.7\",\n      \"z-index\": \"1\",\n      position: \"absolute\",\n      top: \"0\",\n      left: \"0\",\n      right: \"0\",\n      bottom: \"0\"\n    }\n  }), _c(\"div\", {\n    staticClass: \"particles-container\"\n  }, _vm._l(50, function (n) {\n    return _c(\"div\", {\n      key: n,\n      staticClass: \"particle\",\n      style: _vm.getParticleStyle()\n    });\n  }), 0), _c(\"div\", {\n    staticClass: \"data-streams\"\n  }, _vm._l(8, function (n) {\n    return _c(\"div\", {\n      key: n,\n      staticClass: \"data-stream\"\n    });\n  }), 0), _c(\"div\", {\n    staticClass: \"banner-content\"\n  }, [_c(\"h1\", {\n    staticClass: \"banner-title\"\n  }, [_c(\"span\", {\n    staticClass: \"title-word\",\n    attrs: {\n      \"data-text\": \"承天工之智\"\n    }\n  }, [_vm._v(\"承天工之智\")]), _c(\"span\", {\n    staticClass: \"title-separator\"\n  }, [_vm._v(\"，\")]), _c(\"span\", {\n    staticClass: \"title-word\",\n    attrs: {\n      \"data-text\": \"启万物之能\"\n    }\n  }, [_vm._v(\"启万物之能\")])]), _c(\"div\", {\n    staticClass: \"banner-subtitle-container\"\n  }, [_c(\"p\", {\n    staticClass: \"banner-subtitle typing-effect\"\n  }, [_vm._v(\"我们相信\")]), _c(\"p\", {\n    staticClass: \"banner-subtitle typing-effect\",\n    staticStyle: {\n      \"animation-delay\": \"1s\"\n    }\n  }, [_vm._v(\"人类无需再围成一台机器\")]), _c(\"p\", {\n    staticClass: \"banner-subtitle typing-effect\",\n    staticStyle: {\n      \"animation-delay\": \"2s\"\n    }\n  }, [_vm._v(\"而是用智能连接彼此，释放算力的真正价值\")])])])]), _c(\"section\", {\n    staticClass: \"about-section\"\n  }, [_c(\"div\", {\n    staticClass: \"container\"\n  }, [_c(\"div\", {\n    staticClass: \"am-g\"\n  }, [_c(\"div\", {\n    staticClass: \"am-u-md-6\"\n  }, [_c(\"div\", {\n    staticClass: \"our-company-text\"\n  }, [_c(\"h1\", [_vm._v(\"关于我们\")]), _c(\"p\", {\n    staticStyle: {\n      \"font-size\": \"17px\"\n    }\n  }, [_vm._v(' 天工开物智能科技（苏州）有限公司，致力于打造面向企业级用户的高性能计算解决方案， 围绕\"高效调度、低门槛使用、专业保障\"的核心理念，为 AI、大模型、图形渲染、科研计算等场景提供灵活、稳定、弹性的算力支持。 ')]), _c(\"p\", {\n    staticStyle: {\n      \"font-size\": \"17px\"\n    }\n  }, [_vm._v(\" 我们基于全国分布式算力网络，自主构建智算调度平台，整合GPU资源与数据中心节点， 为企业提供从算力资源租用、模型部署优化到全流程运维服务的一站式专业方案。 \")]), _c(\"p\", {\n    staticStyle: {\n      \"font-size\": \"17px\"\n    }\n  }, [_vm._v(\" 在智能时代的浪潮中，天工开物致力于打造企业级专业算力服务平台， 以全国分布式高性能计算网络为基础，提供稳定、高效、灵活可控的算力解决方案。 \")])])]), _c(\"div\", {\n    staticClass: \"am-u-md-6\"\n  }, [_c(\"div\", {\n    staticClass: \"our-company-quote\"\n  }, [_c(\"div\", {\n    staticClass: \"our-company-img\"\n  }, [_c(\"img\", {\n    attrs: {\n      src: \"images/tgkw_about.jpg\",\n      alt: \"天工开物智能科技\",\n      loading: \"lazy\"\n    }\n  })])])])])])]), _c(\"section\", {\n    staticClass: \"our-mission\"\n  }, [_c(\"div\", {\n    staticClass: \"container\"\n  }, [_c(\"div\", {\n    staticClass: \"section--header\"\n  }, [_c(\"h2\", {\n    staticClass: \"section--title\"\n  }, [_vm._v(\"选择我们的理由\")])]), _c(\"div\", {\n    staticClass: \"am-g\"\n  }, [_c(\"div\", {\n    staticClass: \"am-u-sm-12 am-u-md-6 am-u-lg-3\"\n  }, [_c(\"div\", {\n    staticClass: \"our_mission--item\"\n  }, [_c(\"div\", {\n    staticClass: \"our_mission--item_media\"\n  }, [_c(\"i\", {\n    staticClass: \"am-icon-server\",\n    staticStyle: {\n      \"font-size\": \"48px\",\n      color: \"#1470FF\"\n    }\n  })]), _c(\"h4\", {\n    staticClass: \"our_mission--item_title\"\n  }, [_vm._v(\"企业级专业服务\")]), _c(\"div\", {\n    staticClass: \"our_mission--item_body\"\n  }, [_c(\"p\", [_vm._v(\"为AI、科研、图形渲染、工业仿真等场景，提供稳定高效的高性能计算支持\")])])])]), _c(\"div\", {\n    staticClass: \"am-u-sm-12 am-u-md-6 am-u-lg-3\"\n  }, [_c(\"div\", {\n    staticClass: \"our_mission--item\"\n  }, [_c(\"div\", {\n    staticClass: \"our_mission--item_media\"\n  }, [_c(\"i\", {\n    staticClass: \"am-icon-globe\",\n    staticStyle: {\n      \"font-size\": \"48px\",\n      color: \"#1470FF\"\n    }\n  })]), _c(\"h4\", {\n    staticClass: \"our_mission--item_title\"\n  }, [_vm._v(\"全国分布式节点布局\")]), _c(\"div\", {\n    staticClass: \"our_mission--item_body\"\n  }, [_c(\"p\", [_vm._v(\"多地部署，动态调度，资源灵活，负载均衡，响应迅速\")])])])]), _c(\"div\", {\n    staticClass: \"am-u-sm-12 am-u-md-6 am-u-lg-3\"\n  }, [_c(\"div\", {\n    staticClass: \"our_mission--item\"\n  }, [_c(\"div\", {\n    staticClass: \"our_mission--item_media\"\n  }, [_c(\"i\", {\n    staticClass: \"am-icon-cogs\",\n    staticStyle: {\n      \"font-size\": \"48px\",\n      color: \"#1470FF\"\n    }\n  })]), _c(\"h4\", {\n    staticClass: \"our_mission--item_title\"\n  }, [_vm._v(\"灵活弹性 + 高性价比\")]), _c(\"div\", {\n    staticClass: \"our_mission--item_body\"\n  }, [_c(\"p\", [_vm._v(\"自研调度平台，支持定制，与大客户深度合作\")])])])]), _c(\"div\", {\n    staticClass: \"am-u-sm-12 am-u-md-6 am-u-lg-3\"\n  }, [_c(\"div\", {\n    staticClass: \"our_mission--item\"\n  }, [_c(\"div\", {\n    staticClass: \"our_mission--item_media\"\n  }, [_c(\"i\", {\n    staticClass: \"am-icon-users\",\n    staticStyle: {\n      \"font-size\": \"48px\",\n      color: \"#1470FF\"\n    }\n  })]), _c(\"h4\", {\n    staticClass: \"our_mission--item_title\"\n  }, [_vm._v(\"更懂企业的算力伙伴\")]), _c(\"div\", {\n    staticClass: \"our_mission--item_body\"\n  }, [_c(\"p\", [_vm._v(\"从需求对接、技术支持到运维保障，全流程一对一服务\")])])])])])])]), _c(\"section\", {\n    staticClass: \"our-team\"\n  }, [_c(\"div\", {\n    staticClass: \"container\"\n  }, [_c(\"div\", {\n    staticClass: \"section--header\"\n  }, [_c(\"h2\", {\n    staticClass: \"section--title\"\n  }, [_vm._v(\"核心团队\")]), _c(\"p\", {\n    staticClass: \"section--description\"\n  }, [_vm._v(\" 核心团队来自知名AI云计算厂商、IDC运维专家与高校科研机构 \")])]), _c(\"div\", {\n    staticClass: \"am-g\"\n  }, [_c(\"div\", {\n    staticClass: \"am-u-sm-12 am-u-md-4\"\n  }, [_c(\"div\", {\n    staticClass: \"team-box\"\n  }, [_c(\"div\", {\n    staticClass: \"our-team-img\"\n  }, [_c(\"img\", {\n    attrs: {\n      src: \"images/techteam.png\",\n      alt: \"技术团队\",\n      loading: \"lazy\"\n    }\n  })]), _c(\"div\", {\n    staticClass: \"team_member--body\"\n  }, [_c(\"h4\", {\n    staticClass: \"team_member--name\"\n  }, [_vm._v(\"技术研发\")]), _c(\"span\", {\n    staticClass: \"team_member--position\"\n  }, [_vm._v(\"专业的研发团队，深耕AI算力调度与优化\")])])])]), _c(\"div\", {\n    staticClass: \"am-u-sm-12 am-u-md-4\"\n  }, [_c(\"div\", {\n    staticClass: \"team-box\"\n  }, [_c(\"div\", {\n    staticClass: \"our-team-img\"\n  }, [_c(\"img\", {\n    attrs: {\n      src: \"images/yunwei.png\",\n      alt: \"运维团队\",\n      loading: \"lazy\"\n    }\n  })]), _c(\"div\", {\n    staticClass: \"team_member--body\"\n  }, [_c(\"h4\", {\n    staticClass: \"team_member--name\"\n  }, [_vm._v(\"运维保障\")]), _c(\"span\", {\n    staticClass: \"team_member--position\"\n  }, [_vm._v(\"5x8小时专业运维，确保服务稳定可靠\")])])])]), _c(\"div\", {\n    staticClass: \"am-u-sm-12 am-u-md-4\"\n  }, [_c(\"div\", {\n    staticClass: \"team-box\"\n  }, [_c(\"div\", {\n    staticClass: \"our-team-img\"\n  }, [_c(\"img\", {\n    attrs: {\n      src: \"images/khjl.png\",\n      alt: \"客服团队\",\n      loading: \"lazy\"\n    }\n  })]), _c(\"div\", {\n    staticClass: \"team_member--body\"\n  }, [_c(\"h4\", {\n    staticClass: \"team_member--name\"\n  }, [_vm._v(\"客户服务\")]), _c(\"span\", {\n    staticClass: \"team_member--position\"\n  }, [_vm._v(\"专业客户经理，提供一对一贴心服务\")])])])])])])]), _c(\"section\", {\n    staticClass: \"contact-section\"\n  }, [_c(\"div\", {\n    staticClass: \"container\"\n  }, [_c(\"div\", {\n    staticClass: \"am-g\"\n  }, [_c(\"div\", {\n    staticClass: \"am-u-md-4\"\n  }, [_c(\"div\", {\n    staticClass: \"contact-item\"\n  }, [_c(\"i\", {\n    staticClass: \"am-icon-phone\"\n  }), _c(\"h4\", [_vm._v(\"联系电话\")]), _c(\"p\", [_vm._v(\"13913283376\")])])]), _c(\"div\", {\n    staticClass: \"am-u-md-4\"\n  }, [_c(\"div\", {\n    staticClass: \"contact-item\"\n  }, [_c(\"i\", {\n    staticClass: \"am-icon-envelope\"\n  }), _c(\"h4\", [_vm._v(\"官方公众号\")]), _c(\"p\", [_vm._v(\"昆山新质创新数字技术研究院\")])])]), _c(\"div\", {\n    staticClass: \"am-u-md-4\"\n  }, [_c(\"div\", {\n    staticClass: \"contact-item\"\n  }, [_c(\"i\", {\n    staticClass: \"am-icon-map-marker\"\n  }), _c(\"h4\", [_vm._v(\"公司地址\")]), _c(\"p\", [_vm._v(\"江苏省苏州市昆山市玉山镇祖冲之路1699号昆山工业技术研究院综合南楼1404\")])])])])])]), _c(\"section\", {\n    staticClass: \"cta-section\"\n  }, [_c(\"div\", {\n    staticClass: \"cta-content\"\n  }, [_c(\"h2\", [_vm._v(\"连接智算未来，让高性能计算像水电一样可得、可控、可负担\")]), _c(\"div\", {\n    staticClass: \"cta-buttons\"\n  }, [_c(\"button\", {\n    staticClass: \"am-btn am-btn-primary\",\n    on: {\n      click: _vm.startTrial\n    }\n  }, [_vm._v(\"立即开始\")])])])])]);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "staticStyle", "background", "opacity", "position", "top", "left", "right", "bottom", "_l", "n", "key", "style", "getParticleStyle", "attrs", "_v", "src", "alt", "loading", "color", "on", "click", "startTrial", "staticRenderFns", "_withStripped"], "sources": ["D:/code/frontend/BusinessWebisite_ui/portal-ui/src/views/About/AboutView.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"Layout\", [\n    _c(\"div\", { staticClass: \"about-banner\" }, [\n      _c(\"div\", {\n        staticClass: \"about-banner-bg\",\n        staticStyle: {\n          background: \"url('images/earth.gif') center/cover\",\n          opacity: \"0.7\",\n          \"z-index\": \"1\",\n          position: \"absolute\",\n          top: \"0\",\n          left: \"0\",\n          right: \"0\",\n          bottom: \"0\",\n        },\n      }),\n      _c(\n        \"div\",\n        { staticClass: \"particles-container\" },\n        _vm._l(50, function (n) {\n          return _c(\"div\", {\n            key: n,\n            staticClass: \"particle\",\n            style: _vm.getParticleStyle(),\n          })\n        }),\n        0\n      ),\n      _c(\n        \"div\",\n        { staticClass: \"data-streams\" },\n        _vm._l(8, function (n) {\n          return _c(\"div\", { key: n, staticClass: \"data-stream\" })\n        }),\n        0\n      ),\n      _c(\"div\", { staticClass: \"banner-content\" }, [\n        _c(\"h1\", { staticClass: \"banner-title\" }, [\n          _c(\n            \"span\",\n            { staticClass: \"title-word\", attrs: { \"data-text\": \"承天工之智\" } },\n            [_vm._v(\"承天工之智\")]\n          ),\n          _c(\"span\", { staticClass: \"title-separator\" }, [_vm._v(\"，\")]),\n          _c(\n            \"span\",\n            { staticClass: \"title-word\", attrs: { \"data-text\": \"启万物之能\" } },\n            [_vm._v(\"启万物之能\")]\n          ),\n        ]),\n        _c(\"div\", { staticClass: \"banner-subtitle-container\" }, [\n          _c(\"p\", { staticClass: \"banner-subtitle typing-effect\" }, [\n            _vm._v(\"我们相信\"),\n          ]),\n          _c(\n            \"p\",\n            {\n              staticClass: \"banner-subtitle typing-effect\",\n              staticStyle: { \"animation-delay\": \"1s\" },\n            },\n            [_vm._v(\"人类无需再围成一台机器\")]\n          ),\n          _c(\n            \"p\",\n            {\n              staticClass: \"banner-subtitle typing-effect\",\n              staticStyle: { \"animation-delay\": \"2s\" },\n            },\n            [_vm._v(\"而是用智能连接彼此，释放算力的真正价值\")]\n          ),\n        ]),\n      ]),\n    ]),\n    _c(\"section\", { staticClass: \"about-section\" }, [\n      _c(\"div\", { staticClass: \"container\" }, [\n        _c(\"div\", { staticClass: \"am-g\" }, [\n          _c(\"div\", { staticClass: \"am-u-md-6\" }, [\n            _c(\"div\", { staticClass: \"our-company-text\" }, [\n              _c(\"h1\", [_vm._v(\"关于我们\")]),\n              _c(\"p\", { staticStyle: { \"font-size\": \"17px\" } }, [\n                _vm._v(\n                  ' 天工开物智能科技（苏州）有限公司，致力于打造面向企业级用户的高性能计算解决方案， 围绕\"高效调度、低门槛使用、专业保障\"的核心理念，为 AI、大模型、图形渲染、科研计算等场景提供灵活、稳定、弹性的算力支持。 '\n                ),\n              ]),\n              _c(\"p\", { staticStyle: { \"font-size\": \"17px\" } }, [\n                _vm._v(\n                  \" 我们基于全国分布式算力网络，自主构建智算调度平台，整合GPU资源与数据中心节点， 为企业提供从算力资源租用、模型部署优化到全流程运维服务的一站式专业方案。 \"\n                ),\n              ]),\n              _c(\"p\", { staticStyle: { \"font-size\": \"17px\" } }, [\n                _vm._v(\n                  \" 在智能时代的浪潮中，天工开物致力于打造企业级专业算力服务平台， 以全国分布式高性能计算网络为基础，提供稳定、高效、灵活可控的算力解决方案。 \"\n                ),\n              ]),\n            ]),\n          ]),\n          _c(\"div\", { staticClass: \"am-u-md-6\" }, [\n            _c(\"div\", { staticClass: \"our-company-quote\" }, [\n              _c(\"div\", { staticClass: \"our-company-img\" }, [\n                _c(\"img\", {\n                  attrs: {\n                    src: \"images/tgkw_about.jpg\",\n                    alt: \"天工开物智能科技\",\n                    loading: \"lazy\",\n                  },\n                }),\n              ]),\n            ]),\n          ]),\n        ]),\n      ]),\n    ]),\n    _c(\"section\", { staticClass: \"our-mission\" }, [\n      _c(\"div\", { staticClass: \"container\" }, [\n        _c(\"div\", { staticClass: \"section--header\" }, [\n          _c(\"h2\", { staticClass: \"section--title\" }, [\n            _vm._v(\"选择我们的理由\"),\n          ]),\n        ]),\n        _c(\"div\", { staticClass: \"am-g\" }, [\n          _c(\"div\", { staticClass: \"am-u-sm-12 am-u-md-6 am-u-lg-3\" }, [\n            _c(\"div\", { staticClass: \"our_mission--item\" }, [\n              _c(\"div\", { staticClass: \"our_mission--item_media\" }, [\n                _c(\"i\", {\n                  staticClass: \"am-icon-server\",\n                  staticStyle: { \"font-size\": \"48px\", color: \"#1470FF\" },\n                }),\n              ]),\n              _c(\"h4\", { staticClass: \"our_mission--item_title\" }, [\n                _vm._v(\"企业级专业服务\"),\n              ]),\n              _c(\"div\", { staticClass: \"our_mission--item_body\" }, [\n                _c(\"p\", [\n                  _vm._v(\n                    \"为AI、科研、图形渲染、工业仿真等场景，提供稳定高效的高性能计算支持\"\n                  ),\n                ]),\n              ]),\n            ]),\n          ]),\n          _c(\"div\", { staticClass: \"am-u-sm-12 am-u-md-6 am-u-lg-3\" }, [\n            _c(\"div\", { staticClass: \"our_mission--item\" }, [\n              _c(\"div\", { staticClass: \"our_mission--item_media\" }, [\n                _c(\"i\", {\n                  staticClass: \"am-icon-globe\",\n                  staticStyle: { \"font-size\": \"48px\", color: \"#1470FF\" },\n                }),\n              ]),\n              _c(\"h4\", { staticClass: \"our_mission--item_title\" }, [\n                _vm._v(\"全国分布式节点布局\"),\n              ]),\n              _c(\"div\", { staticClass: \"our_mission--item_body\" }, [\n                _c(\"p\", [\n                  _vm._v(\"多地部署，动态调度，资源灵活，负载均衡，响应迅速\"),\n                ]),\n              ]),\n            ]),\n          ]),\n          _c(\"div\", { staticClass: \"am-u-sm-12 am-u-md-6 am-u-lg-3\" }, [\n            _c(\"div\", { staticClass: \"our_mission--item\" }, [\n              _c(\"div\", { staticClass: \"our_mission--item_media\" }, [\n                _c(\"i\", {\n                  staticClass: \"am-icon-cogs\",\n                  staticStyle: { \"font-size\": \"48px\", color: \"#1470FF\" },\n                }),\n              ]),\n              _c(\"h4\", { staticClass: \"our_mission--item_title\" }, [\n                _vm._v(\"灵活弹性 + 高性价比\"),\n              ]),\n              _c(\"div\", { staticClass: \"our_mission--item_body\" }, [\n                _c(\"p\", [_vm._v(\"自研调度平台，支持定制，与大客户深度合作\")]),\n              ]),\n            ]),\n          ]),\n          _c(\"div\", { staticClass: \"am-u-sm-12 am-u-md-6 am-u-lg-3\" }, [\n            _c(\"div\", { staticClass: \"our_mission--item\" }, [\n              _c(\"div\", { staticClass: \"our_mission--item_media\" }, [\n                _c(\"i\", {\n                  staticClass: \"am-icon-users\",\n                  staticStyle: { \"font-size\": \"48px\", color: \"#1470FF\" },\n                }),\n              ]),\n              _c(\"h4\", { staticClass: \"our_mission--item_title\" }, [\n                _vm._v(\"更懂企业的算力伙伴\"),\n              ]),\n              _c(\"div\", { staticClass: \"our_mission--item_body\" }, [\n                _c(\"p\", [\n                  _vm._v(\"从需求对接、技术支持到运维保障，全流程一对一服务\"),\n                ]),\n              ]),\n            ]),\n          ]),\n        ]),\n      ]),\n    ]),\n    _c(\"section\", { staticClass: \"our-team\" }, [\n      _c(\"div\", { staticClass: \"container\" }, [\n        _c(\"div\", { staticClass: \"section--header\" }, [\n          _c(\"h2\", { staticClass: \"section--title\" }, [_vm._v(\"核心团队\")]),\n          _c(\"p\", { staticClass: \"section--description\" }, [\n            _vm._v(\" 核心团队来自知名AI云计算厂商、IDC运维专家与高校科研机构 \"),\n          ]),\n        ]),\n        _c(\"div\", { staticClass: \"am-g\" }, [\n          _c(\"div\", { staticClass: \"am-u-sm-12 am-u-md-4\" }, [\n            _c(\"div\", { staticClass: \"team-box\" }, [\n              _c(\"div\", { staticClass: \"our-team-img\" }, [\n                _c(\"img\", {\n                  attrs: {\n                    src: \"images/techteam.png\",\n                    alt: \"技术团队\",\n                    loading: \"lazy\",\n                  },\n                }),\n              ]),\n              _c(\"div\", { staticClass: \"team_member--body\" }, [\n                _c(\"h4\", { staticClass: \"team_member--name\" }, [\n                  _vm._v(\"技术研发\"),\n                ]),\n                _c(\"span\", { staticClass: \"team_member--position\" }, [\n                  _vm._v(\"专业的研发团队，深耕AI算力调度与优化\"),\n                ]),\n              ]),\n            ]),\n          ]),\n          _c(\"div\", { staticClass: \"am-u-sm-12 am-u-md-4\" }, [\n            _c(\"div\", { staticClass: \"team-box\" }, [\n              _c(\"div\", { staticClass: \"our-team-img\" }, [\n                _c(\"img\", {\n                  attrs: {\n                    src: \"images/yunwei.png\",\n                    alt: \"运维团队\",\n                    loading: \"lazy\",\n                  },\n                }),\n              ]),\n              _c(\"div\", { staticClass: \"team_member--body\" }, [\n                _c(\"h4\", { staticClass: \"team_member--name\" }, [\n                  _vm._v(\"运维保障\"),\n                ]),\n                _c(\"span\", { staticClass: \"team_member--position\" }, [\n                  _vm._v(\"5x8小时专业运维，确保服务稳定可靠\"),\n                ]),\n              ]),\n            ]),\n          ]),\n          _c(\"div\", { staticClass: \"am-u-sm-12 am-u-md-4\" }, [\n            _c(\"div\", { staticClass: \"team-box\" }, [\n              _c(\"div\", { staticClass: \"our-team-img\" }, [\n                _c(\"img\", {\n                  attrs: {\n                    src: \"images/khjl.png\",\n                    alt: \"客服团队\",\n                    loading: \"lazy\",\n                  },\n                }),\n              ]),\n              _c(\"div\", { staticClass: \"team_member--body\" }, [\n                _c(\"h4\", { staticClass: \"team_member--name\" }, [\n                  _vm._v(\"客户服务\"),\n                ]),\n                _c(\"span\", { staticClass: \"team_member--position\" }, [\n                  _vm._v(\"专业客户经理，提供一对一贴心服务\"),\n                ]),\n              ]),\n            ]),\n          ]),\n        ]),\n      ]),\n    ]),\n    _c(\"section\", { staticClass: \"contact-section\" }, [\n      _c(\"div\", { staticClass: \"container\" }, [\n        _c(\"div\", { staticClass: \"am-g\" }, [\n          _c(\"div\", { staticClass: \"am-u-md-4\" }, [\n            _c(\"div\", { staticClass: \"contact-item\" }, [\n              _c(\"i\", { staticClass: \"am-icon-phone\" }),\n              _c(\"h4\", [_vm._v(\"联系电话\")]),\n              _c(\"p\", [_vm._v(\"13913283376\")]),\n            ]),\n          ]),\n          _c(\"div\", { staticClass: \"am-u-md-4\" }, [\n            _c(\"div\", { staticClass: \"contact-item\" }, [\n              _c(\"i\", { staticClass: \"am-icon-envelope\" }),\n              _c(\"h4\", [_vm._v(\"官方公众号\")]),\n              _c(\"p\", [_vm._v(\"昆山新质创新数字技术研究院\")]),\n            ]),\n          ]),\n          _c(\"div\", { staticClass: \"am-u-md-4\" }, [\n            _c(\"div\", { staticClass: \"contact-item\" }, [\n              _c(\"i\", { staticClass: \"am-icon-map-marker\" }),\n              _c(\"h4\", [_vm._v(\"公司地址\")]),\n              _c(\"p\", [\n                _vm._v(\n                  \"江苏省苏州市昆山市玉山镇祖冲之路1699号昆山工业技术研究院综合南楼1404\"\n                ),\n              ]),\n            ]),\n          ]),\n        ]),\n      ]),\n    ]),\n    _c(\"section\", { staticClass: \"cta-section\" }, [\n      _c(\"div\", { staticClass: \"cta-content\" }, [\n        _c(\"h2\", [\n          _vm._v(\"连接智算未来，让高性能计算像水电一样可得、可控、可负担\"),\n        ]),\n        _c(\"div\", { staticClass: \"cta-buttons\" }, [\n          _c(\n            \"button\",\n            {\n              staticClass: \"am-btn am-btn-primary\",\n              on: { click: _vm.startTrial },\n            },\n            [_vm._v(\"立即开始\")]\n          ),\n        ]),\n      ]),\n    ]),\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,QAAQ,EAAE,CAClBA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,iBAAiB;IAC9BC,WAAW,EAAE;MACXC,UAAU,EAAE,sCAAsC;MAClDC,OAAO,EAAE,KAAK;MACd,SAAS,EAAE,GAAG;MACdC,QAAQ,EAAE,UAAU;MACpBC,GAAG,EAAE,GAAG;MACRC,IAAI,EAAE,GAAG;MACTC,KAAK,EAAE,GAAG;MACVC,MAAM,EAAE;IACV;EACF,CAAC,CAAC,EACFV,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAsB,CAAC,EACtCH,GAAG,CAACY,EAAE,CAAC,EAAE,EAAE,UAAUC,CAAC,EAAE;IACtB,OAAOZ,EAAE,CAAC,KAAK,EAAE;MACfa,GAAG,EAAED,CAAC;MACNV,WAAW,EAAE,UAAU;MACvBY,KAAK,EAAEf,GAAG,CAACgB,gBAAgB;IAC7B,CAAC,CAAC;EACJ,CAAC,CAAC,EACF,CAAC,CACF,EACDf,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAe,CAAC,EAC/BH,GAAG,CAACY,EAAE,CAAC,CAAC,EAAE,UAAUC,CAAC,EAAE;IACrB,OAAOZ,EAAE,CAAC,KAAK,EAAE;MAAEa,GAAG,EAAED,CAAC;MAAEV,WAAW,EAAE;IAAc,CAAC,CAAC;EAC1D,CAAC,CAAC,EACF,CAAC,CACF,EACDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CF,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACxCF,EAAE,CACA,MAAM,EACN;IAAEE,WAAW,EAAE,YAAY;IAAEc,KAAK,EAAE;MAAE,WAAW,EAAE;IAAQ;EAAE,CAAC,EAC9D,CAACjB,GAAG,CAACkB,EAAE,CAAC,OAAO,CAAC,CAAC,CAClB,EACDjB,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAACH,GAAG,CAACkB,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAC7DjB,EAAE,CACA,MAAM,EACN;IAAEE,WAAW,EAAE,YAAY;IAAEc,KAAK,EAAE;MAAE,WAAW,EAAE;IAAQ;EAAE,CAAC,EAC9D,CAACjB,GAAG,CAACkB,EAAE,CAAC,OAAO,CAAC,CAAC,CAClB,CACF,CAAC,EACFjB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAA4B,CAAC,EAAE,CACtDF,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAgC,CAAC,EAAE,CACxDH,GAAG,CAACkB,EAAE,CAAC,MAAM,CAAC,CACf,CAAC,EACFjB,EAAE,CACA,GAAG,EACH;IACEE,WAAW,EAAE,+BAA+B;IAC5CC,WAAW,EAAE;MAAE,iBAAiB,EAAE;IAAK;EACzC,CAAC,EACD,CAACJ,GAAG,CAACkB,EAAE,CAAC,aAAa,CAAC,CAAC,CACxB,EACDjB,EAAE,CACA,GAAG,EACH;IACEE,WAAW,EAAE,+BAA+B;IAC5CC,WAAW,EAAE;MAAE,iBAAiB,EAAE;IAAK;EACzC,CAAC,EACD,CAACJ,GAAG,CAACkB,EAAE,CAAC,qBAAqB,CAAC,CAAC,CAChC,CACF,CAAC,CACH,CAAC,CACH,CAAC,EACFjB,EAAE,CAAC,SAAS,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC9CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAO,CAAC,EAAE,CACjCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,EAAE,CAC7CF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACkB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC1BjB,EAAE,CAAC,GAAG,EAAE;IAAEG,WAAW,EAAE;MAAE,WAAW,EAAE;IAAO;EAAE,CAAC,EAAE,CAChDJ,GAAG,CAACkB,EAAE,CACJ,2GAA2G,CAC5G,CACF,CAAC,EACFjB,EAAE,CAAC,GAAG,EAAE;IAAEG,WAAW,EAAE;MAAE,WAAW,EAAE;IAAO;EAAE,CAAC,EAAE,CAChDJ,GAAG,CAACkB,EAAE,CACJ,iFAAiF,CAClF,CACF,CAAC,EACFjB,EAAE,CAAC,GAAG,EAAE;IAAEG,WAAW,EAAE;MAAE,WAAW,EAAE;IAAO;EAAE,CAAC,EAAE,CAChDJ,GAAG,CAACkB,EAAE,CACJ,yEAAyE,CAC1E,CACF,CAAC,CACH,CAAC,CACH,CAAC,EACFjB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CF,EAAE,CAAC,KAAK,EAAE;IACRgB,KAAK,EAAE;MACLE,GAAG,EAAE,uBAAuB;MAC5BC,GAAG,EAAE,UAAU;MACfC,OAAO,EAAE;IACX;EACF,CAAC,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,EACFpB,EAAE,CAAC,SAAS,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CAC5CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CF,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC1CH,GAAG,CAACkB,EAAE,CAAC,SAAS,CAAC,CAClB,CAAC,CACH,CAAC,EACFjB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAO,CAAC,EAAE,CACjCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiC,CAAC,EAAE,CAC3DF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAA0B,CAAC,EAAE,CACpDF,EAAE,CAAC,GAAG,EAAE;IACNE,WAAW,EAAE,gBAAgB;IAC7BC,WAAW,EAAE;MAAE,WAAW,EAAE,MAAM;MAAEkB,KAAK,EAAE;IAAU;EACvD,CAAC,CAAC,CACH,CAAC,EACFrB,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAA0B,CAAC,EAAE,CACnDH,GAAG,CAACkB,EAAE,CAAC,SAAS,CAAC,CAClB,CAAC,EACFjB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAyB,CAAC,EAAE,CACnDF,EAAE,CAAC,GAAG,EAAE,CACND,GAAG,CAACkB,EAAE,CACJ,oCAAoC,CACrC,CACF,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,EACFjB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiC,CAAC,EAAE,CAC3DF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAA0B,CAAC,EAAE,CACpDF,EAAE,CAAC,GAAG,EAAE;IACNE,WAAW,EAAE,eAAe;IAC5BC,WAAW,EAAE;MAAE,WAAW,EAAE,MAAM;MAAEkB,KAAK,EAAE;IAAU;EACvD,CAAC,CAAC,CACH,CAAC,EACFrB,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAA0B,CAAC,EAAE,CACnDH,GAAG,CAACkB,EAAE,CAAC,WAAW,CAAC,CACpB,CAAC,EACFjB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAyB,CAAC,EAAE,CACnDF,EAAE,CAAC,GAAG,EAAE,CACND,GAAG,CAACkB,EAAE,CAAC,0BAA0B,CAAC,CACnC,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,EACFjB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiC,CAAC,EAAE,CAC3DF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAA0B,CAAC,EAAE,CACpDF,EAAE,CAAC,GAAG,EAAE;IACNE,WAAW,EAAE,cAAc;IAC3BC,WAAW,EAAE;MAAE,WAAW,EAAE,MAAM;MAAEkB,KAAK,EAAE;IAAU;EACvD,CAAC,CAAC,CACH,CAAC,EACFrB,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAA0B,CAAC,EAAE,CACnDH,GAAG,CAACkB,EAAE,CAAC,aAAa,CAAC,CACtB,CAAC,EACFjB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAyB,CAAC,EAAE,CACnDF,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACkB,EAAE,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAC1C,CAAC,CACH,CAAC,CACH,CAAC,EACFjB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiC,CAAC,EAAE,CAC3DF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAA0B,CAAC,EAAE,CACpDF,EAAE,CAAC,GAAG,EAAE;IACNE,WAAW,EAAE,eAAe;IAC5BC,WAAW,EAAE;MAAE,WAAW,EAAE,MAAM;MAAEkB,KAAK,EAAE;IAAU;EACvD,CAAC,CAAC,CACH,CAAC,EACFrB,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAA0B,CAAC,EAAE,CACnDH,GAAG,CAACkB,EAAE,CAAC,WAAW,CAAC,CACpB,CAAC,EACFjB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAyB,CAAC,EAAE,CACnDF,EAAE,CAAC,GAAG,EAAE,CACND,GAAG,CAACkB,EAAE,CAAC,0BAA0B,CAAC,CACnC,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,EACFjB,EAAE,CAAC,SAAS,EAAE;IAAEE,WAAW,EAAE;EAAW,CAAC,EAAE,CACzCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CF,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAACH,GAAG,CAACkB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC7DjB,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAuB,CAAC,EAAE,CAC/CH,GAAG,CAACkB,EAAE,CAAC,kCAAkC,CAAC,CAC3C,CAAC,CACH,CAAC,EACFjB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAO,CAAC,EAAE,CACjCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAuB,CAAC,EAAE,CACjDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAW,CAAC,EAAE,CACrCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,KAAK,EAAE;IACRgB,KAAK,EAAE;MACLE,GAAG,EAAE,qBAAqB;MAC1BC,GAAG,EAAE,MAAM;MACXC,OAAO,EAAE;IACX;EACF,CAAC,CAAC,CACH,CAAC,EACFpB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CF,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC7CH,GAAG,CAACkB,EAAE,CAAC,MAAM,CAAC,CACf,CAAC,EACFjB,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAwB,CAAC,EAAE,CACnDH,GAAG,CAACkB,EAAE,CAAC,qBAAqB,CAAC,CAC9B,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,EACFjB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAuB,CAAC,EAAE,CACjDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAW,CAAC,EAAE,CACrCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,KAAK,EAAE;IACRgB,KAAK,EAAE;MACLE,GAAG,EAAE,mBAAmB;MACxBC,GAAG,EAAE,MAAM;MACXC,OAAO,EAAE;IACX;EACF,CAAC,CAAC,CACH,CAAC,EACFpB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CF,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC7CH,GAAG,CAACkB,EAAE,CAAC,MAAM,CAAC,CACf,CAAC,EACFjB,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAwB,CAAC,EAAE,CACnDH,GAAG,CAACkB,EAAE,CAAC,oBAAoB,CAAC,CAC7B,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,EACFjB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAuB,CAAC,EAAE,CACjDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAW,CAAC,EAAE,CACrCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,KAAK,EAAE;IACRgB,KAAK,EAAE;MACLE,GAAG,EAAE,iBAAiB;MACtBC,GAAG,EAAE,MAAM;MACXC,OAAO,EAAE;IACX;EACF,CAAC,CAAC,CACH,CAAC,EACFpB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CF,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC7CH,GAAG,CAACkB,EAAE,CAAC,MAAM,CAAC,CACf,CAAC,EACFjB,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAwB,CAAC,EAAE,CACnDH,GAAG,CAACkB,EAAE,CAAC,kBAAkB,CAAC,CAC3B,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,EACFjB,EAAE,CAAC,SAAS,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAChDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAO,CAAC,EAAE,CACjCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,CAAC,EACzCF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACkB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC1BjB,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACkB,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC,CACjC,CAAC,CACH,CAAC,EACFjB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,CAAC,EAC5CF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACkB,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAC3BjB,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACkB,EAAE,CAAC,eAAe,CAAC,CAAC,CAAC,CACnC,CAAC,CACH,CAAC,EACFjB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAqB,CAAC,CAAC,EAC9CF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACkB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC1BjB,EAAE,CAAC,GAAG,EAAE,CACND,GAAG,CAACkB,EAAE,CACJ,wCAAwC,CACzC,CACF,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,EACFjB,EAAE,CAAC,SAAS,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CAC5CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CAAC,IAAI,EAAE,CACPD,GAAG,CAACkB,EAAE,CAAC,6BAA6B,CAAC,CACtC,CAAC,EACFjB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CACA,QAAQ,EACR;IACEE,WAAW,EAAE,uBAAuB;IACpCoB,EAAE,EAAE;MAAEC,KAAK,EAAExB,GAAG,CAACyB;IAAW;EAC9B,CAAC,EACD,CAACzB,GAAG,CAACkB,EAAE,CAAC,MAAM,CAAC,CAAC,CACjB,CACF,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC;AACJ,CAAC;AACD,IAAIQ,eAAe,GAAG,EAAE;AACxB3B,MAAM,CAAC4B,aAAa,GAAG,IAAI;AAE3B,SAAS5B,MAAM,EAAE2B,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}