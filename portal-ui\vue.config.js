module.exports = {
  // publicPath:'/portal',
  pages:{
    index:{
      entry:'src/main.js',
    }
  },
  lintOnSave: false, // 关闭语法检查

  devServer:{
    port:8087,
    proxy: {
      '/api': {
        target: 'http://192.168.110.109:8080', // 你的实际后端地址
        changeOrigin: true,
        pathRewrite: {
          '^/api': ''
        },
        secure: false // 如果使用https需要设为true
      }
    },
  },
  chainWebpack: config => {
    config.module
      .rule('markdown')
      .test(/\.md$/)
      .use('html-loader')
      .loader('html-loader')
      .end()
      .use('markdown-loader')
      .loader('markdown-loader')
      .end()
  }
}

