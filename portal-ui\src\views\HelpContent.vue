<template>
  <div class="doc-content">
    <div v-if="loading" class="loading">文档加载中...</div>
    <div v-else-if="error" class="error">文档加载失败: {{ error }}</div>
    <div v-else>
      <div v-html="markdownContent" ref="contentRef"></div>
      
      <!-- 上一页/下一页导航 -->
      <div class="page-navigation">
        <div class="prev-next-container">
          <router-link 
            v-if="prevPage" 
            :to="prevPage.path" 
            class="prev-page">
            <div class="nav-label">上一篇</div>
            <div class="nav-title">{{ prevPage.name }}</div>
          </router-link>
          <div v-else class="prev-page empty"></div>
          
          <router-link 
            v-if="nextPage" 
            :to="nextPage.path" 
            class="next-page">
            <div class="nav-label">下一篇</div>
            <div class="nav-title">{{ nextPage.name }}</div>
          </router-link>
          <div v-else class="next-page empty"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    doc: String,
    prevPage: Object,
    nextPage: Object
  },
  data() {
    return {
      markdownContent: '',
      loading: false,
      error: null
    };
  },
  watch: {
    doc: {
      immediate: true,
      async handler(doc) {
        this.loading = true;
        this.error = null;
        try {
          const module = await import(/* webpackChunkName: "docs" */ `../docs/${doc}.md`);
          this.markdownContent = module.default;
          this.$nextTick(() => {
            this.processContent();
            this.$emit('content-loaded');
          });
        } catch (e) {
          this.error = e.message;
          this.markdownContent = '<h1>文档加载失败</h1>';
        } finally {
          this.loading = false;
        }
      }
    }
  },
  methods: {
    slugify(text) {
      let slug = text
        .toLowerCase()
        .replace(/\s+/g, '-')           // 空格转为-
        .replace(/[^a-z0-9\-]+/g, '')   // 只保留小写字母、数字、短横线
        .replace(/\-\-+/g, '-')         // 多个-合并为一个
        .replace(/^-+/, '')              // 去除开头-
        .replace(/-+$/, '');             // 去除结尾-
      if (!slug || /^[0-9]+$/.test(slug)) {
        // 添加随机数确保唯一性
        slug = 'content-section-' + slug + '-' + Math.random().toString(36).substring(2, 11);
      }
      return slug;
    },
    processContent() {
      if (this.$refs.contentRef) {
        const headings = this.$refs.contentRef.querySelectorAll('h2, h3');
        headings.forEach(heading => {
          heading.id = this.slugify(heading.textContent);
        });
        
        const codeBlocks = this.$refs.contentRef.querySelectorAll('pre code');
        codeBlocks.forEach(block => {
          block.classList.add('hljs');
        });
        
        const links = this.$refs.contentRef.querySelectorAll('a[href^="#"]');
        links.forEach(link => {
          link.addEventListener('click', (e) => {
            e.preventDefault();
            const id = link.getAttribute('href').substring(1);
            const targetElement = document.getElementById(id);
            if (targetElement) {
              targetElement.scrollIntoView({ behavior: 'smooth' });
            }
          });
        });
      }
    }
  }
};
</script>

<style>
/* 注意：这里不使用scoped，以便样式应用到动态生成的内容 */
.doc-content h1 {
  font-size: 28px;
  margin-bottom: 20px;
  color: #333;
}

.doc-content h2 {
  font-size: 22px;
  margin: 30px 0 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
  color: #333;
}

.doc-content h3 {
  font-size: 18px;
  margin: 25px 0 15px;
  color: #333;
}

.doc-content p {
  margin: 15px 0;
  line-height: 1.6;
  color: #555;
}

.doc-content ul, .doc-content ol {
  padding-left: 25px;
  margin: 15px 0;
}

.doc-content li {
  margin-bottom: 8px;
  line-height: 1.6;
}

.doc-content code {
  background-color: #f5f5f5;
  padding: 2px 5px;
  border-radius: 3px;
  font-family: Consolas, Monaco, 'Andale Mono', monospace;
  color: #d63384;
}

.doc-content pre {
  background-color: #f8f8f8;
  padding: 15px;
  border-radius: 5px;
  overflow-x: auto;
  margin: 20px 0;
}

.doc-content pre code {
  background-color: transparent;
  padding: 0;
  color: #333;
  display: block;
}

.doc-content a {
  color: #1890ff;
  text-decoration: none;
}

.doc-content a:hover {
  text-decoration: underline;
}

.doc-content blockquote {
  border-left: 4px solid #1890ff !important;
  padding: 16px 20px 16px 50px !important;
  color: #555 !important;
  background: linear-gradient(135deg, #f0f8ff 0%, #e6f7ff 100%) !important;
  margin: 20px 0 !important;
  border-radius: 8px !important;
  font-family: inherit !important;
  position: relative !important;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1) !important;
  border: 1px solid rgba(24, 144, 255, 0.2) !important;
}

.doc-content blockquote::before {
  content: 'ℹ️';
  position: absolute;
  left: 16px;
  top: 16px;
  font-size: 16px;
  background: #1890ff;
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
}

.doc-content blockquote p {
  margin: 0 !important;
  line-height: 1.7 !important;
  font-size: 14px !important;
}

.doc-content blockquote p:not(:last-child) {
  margin-bottom: 12px !important;
}

/* 确保引用块内的font标签样式正常显示 */
.doc-content blockquote font {
  color: inherit !important;
}

/* 引用块内的链接样式 */
.doc-content blockquote a {
  color: #1890ff !important;
  text-decoration: none !important;
  font-weight: 500 !important;
}

.doc-content blockquote a:hover {
  text-decoration: underline !important;
  color: #40a9ff !important;
}

/* 引用块内的代码样式 */
.doc-content blockquote code {
  background-color: rgba(255, 255, 255, 0.8) !important;
  padding: 2px 6px !important;
  border-radius: 3px !important;
  border: 1px solid rgba(24, 144, 255, 0.2) !important;
  color: #1890ff !important;
  font-weight: 500 !important;
}

/* 引用块内的列表样式 */
.doc-content blockquote ul,
.doc-content blockquote ol {
  margin: 8px 0 !important;
  padding-left: 20px !important;
}

.doc-content blockquote li {
  margin-bottom: 4px !important;
  line-height: 1.6 !important;
}

.doc-content table {
  border-collapse: collapse;
  width: 100%;
  margin: 20px 0;
}

.doc-content table th, .doc-content table td {
  border: 1px solid #ddd;
  padding: 10px;
  text-align: left;
}

.doc-content table th {
  background-color: #f5f5f5;
}

.loading, .error {
  padding: 20px;
  text-align: center;
  font-size: 18px;
}

.error {
  color: red;
}

/* 上一页/下一页导航样式 */
.page-navigation {
  /* margin-top: 60px; */
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.prev-next-container {
  display: flex;
  justify-content: space-between;
}

.prev-page, .next-page {
  display: flex;
  flex-direction: column;
  width: 45%;
  padding: 15px;
  border: 1px solid #eee;
  border-radius: 5px;
  text-decoration: none;
  transition: all 0.3s;
}

.prev-page {
  text-align: left;
}

.next-page {
  text-align: right;
}

.prev-page:hover, .next-page:hover {
  border-color: #1890ff;
  background-color: #f0f8ff;
}

.nav-label {
  font-size: 12px;
  color: #999;
  margin-bottom: 5px;
}

.nav-title {
  font-size: 16px;
  font-weight: 500;
  color: #1890ff;
}

.empty {
  visibility: hidden;
}

.doc-content img {
  display: block;
  max-width: 100%;
  height: auto;
  margin: 24px auto;  /* 上下间距24px，左右自动居中 */
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  border-radius: 6px;
}

/* 引用块响应式样式 */
@media (max-width: 768px) {
  .doc-content blockquote {
    padding: 12px 16px 12px 40px !important;
    margin: 16px 0 !important;
    border-radius: 6px !important;
  }

  .doc-content blockquote::before {
    left: 12px !important;
    top: 12px !important;
    width: 20px !important;
    height: 20px !important;
    font-size: 12px !important;
  }

  .doc-content blockquote p {
    font-size: 13px !important;
    line-height: 1.6 !important;
  }
}

@media (max-width: 480px) {
  .doc-content blockquote {
    padding: 10px 12px 10px 36px !important;
    margin: 12px 0 !important;
  }

  .doc-content blockquote::before {
    left: 10px !important;
    top: 10px !important;
    width: 18px !important;
    height: 18px !important;
    font-size: 10px !important;
  }
}
</style>