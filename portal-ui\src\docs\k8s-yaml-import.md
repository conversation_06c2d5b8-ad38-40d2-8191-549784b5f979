# <font style="color:rgb(2, 8, 23);">K8S YAML 导入</font>
## <font style="color:rgb(2, 8, 23);">概述</font>
<font style="color:rgb(2, 8, 23);">K8S YAML 导入功能允许熟悉 K8S 的用户以自定义 Deployment 资源的 YAML 配置文件的方式，实现更高自由度的自定义配置。</font>

<font style="color:rgb(2, 8, 23);">例如：</font>

+ [<font style="color:#2F8EF4;">调整扩缩容和滚动更新策略</font>](https://kubernetes.io/zh-cn/docs/reference/kubernetes-api/workload-resources/deployment-v1/)
+ [<font style="color:#2F8EF4;">调整容器从启动到销毁整个生命周期的行为</font>](https://kubernetes.io/zh-cn/docs/reference/kubernetes-api/workload-resources/pod-v1/#PodSpec)
+ [<font style="color:#2F8EF4;">调整容器启动时采用的用户</font>](https://kubernetes.io/zh-cn/docs/reference/kubernetes-api/workload-resources/pod-v1/#PodSpec)
+ [<font style="color:#2F8EF4;">为容器配置启动命令和环境变量</font>](https://kubernetes.io/zh-cn/docs/reference/kubernetes-api/workload-resources/pod-v1/#PodSpec)
+ [<font style="color:#2F8EF4;">为容器挂载内存盘</font>](https://kubernetes.io/zh-cn/docs/reference/kubernetes-api/workload-resources/pod-v1/#PodSpec)

<font style="color:rgb(2, 8, 23);">关于 K8S Deployment 的简要介绍可见</font>[<font style="color:#2F8EF4;">Deployments</font>](https://kubernetes.io/zh-cn/docs/concepts/workloads/controllers/deployment/)<font style="color:rgb(2, 8, 23);">，详细功能介绍可见</font>[<font style="color:#2F8EF4;">Deployment</font>](https://kubernetes.io/zh-cn/docs/reference/kubernetes-api/workload-resources/deployment-v1/)<font style="color:rgb(2, 8, 23);">。</font>

## <font style="color:rgb(2, 8, 23);">限制</font>
<font style="color:rgb(2, 8, 23);">出于对集群安全性的考虑和业务逻辑的要求，目前不支持设置关于卡类型、数目、资源配额、容器镜像等信息（请通过 UI 界面或 API 接口进行设置），也不支持任何可能使得容器获取宿主机权限的配置，例如特权容器，访问主机网络，挂载 hostpath 等。如有相关需要，请联系我们。</font>

## <font style="color:rgb(2, 8, 23);">样例</font>
<font style="color:rgb(2, 8, 23);">这里给出几个典型应用场景下的 K8S Yaml 配置文件样例</font>

### <font style="color:rgb(2, 8, 23);">挂载 shm 内存盘</font>
<font style="color:rgb(2, 8, 23);">部分业务程序需要挂载内存盘作为进程间通信的方式，这里给出挂载内存盘的 yaml 文件样例</font>

```yaml
...... 
      containers:
        - image: harbor.suanleme.cn/xiditgkw/mem-allocator:v1    #选卡后默认配置内容
          name: d1749797718484-39886-container
          resources:
            limits:
              cpu: '14'
              memory: 63Gi
              nvidia.com/gpu: '1'
            requests:
              cpu: '7'
              memory: 32256Mi
          volumeMounts:        #共享内存关键配置
            - mountPath: /dev/shm
              name: shm-volume
      volumes:
        - emptyDir:
            medium: Memory
          name: shm-volume
```

### <font style="color:rgb(2, 8, 23);">实现容器内业务的优雅退出</font>
<font style="color:rgb(2, 8, 23);">部分业务程序处理单次请求可能需要数十分钟级别的较长时间，在服务缩容等场景中，如果不等待请求完成即退出程序，可能会对用户体验造成较大不良影响。</font>

<font style="color:rgb(2, 8, 23);">对于此类程序，务必在业务代码里面处理 SIGTERM 信号：</font>

+ <font style="color:rgb(2, 8, 23);">捕获 SIGTREM 信号</font>
+ <font style="color:rgb(2, 8, 23);">执行对应的清理逻辑</font>
+ <font style="color:rgb(2, 8, 23);">主动退出程序</font>

<font style="color:rgb(2, 8, 23);">并在 yaml 中配置适当的优雅退出宽限时间来实现。这里给出 yaml 文件样例。</font>

```yaml
......
      containers:
        - image: harbor.suanleme.cn/repository/imagename:v1    #选用的镜像
          lifecycle:            #（可选）回调函数退出时执行的逻辑
            preStop:
              exec:
                command:
                  - /bin/sh
                  - '-c'
                  - sleep 30
          name: d1749797718484-39886-container    #默认生成
          resources:        #选卡时确定此配置，不可修改
            limits:
              cpu: '14'
              memory: 63Gi
              nvidia.com/gpu: '1'
            requests:
              cpu: '7'
              memory: 32256Mi
      terminationGracePeriodSeconds: 200        #（关键）定义优雅退出最大时间
```

### <font style="color:rgb(2, 8, 23);">服务零中断滚动更新（待测试，需结合 S3）（待完善，暂时不公开）</font>
### <font style="color:rgb(2, 8, 23);">以 root 用户启动容器（待完善，暂时不公开）</font>
<font style="color:rgb(2, 8, 23);">部分业务容器可能希望以 root 身份启动，这与 K8S 的默认行为不同，可以通过自定义配置来进行设置。</font>

```plain
......
      containers:
        - image: harbor.suanleme.cn/repository/imagename:v1    #选用的镜像
          name: d1749797718484-39886-container    #默认生成
        securityContext:
          runAsUser: 0  #此处填写用户 UID，0 标识 root，也可定义其他可用 UID
          runAsGroup: 0 #（可选）：此处表示 root 组的 GID
```

### <font style="color:rgb(2, 8, 23);">关闭 istio 放开四层流量限制</font>
<font style="color:rgb(2, 8, 23);">这里是要解决什么场景下的什么问题？</font>

```plain
apiVersion: apps/v1
kind: Deployment
metadata:
  creationTimestamp: null
  labels:
    app: d06161816-mem-allocatorv1-186-11icjyhf
  name: d06161816-mem-allocatorv1-186-11icjyhf
  namespace: wp2upu39jptxiuhkfkm0nx2bdkp9elwo-186
spec:
  replicas: 1
  selector:
    matchLabels:
      app: d06161816-mem-allocatorv1-186-11icjyhf
  strategy: {}
  template:
    metadata:
      annotations:        #此项为放开四层流量的限制
        sidecar.istio.io/inject: 'false'
      creationTimestamp: null
      labels:
        app: d06161816-mem-allocatorv1-186-11icjyhf
    spec:
......
```

<br><br>
<font style="color:rgba(178,178,178,1);">最后更新于: 2025/6/19 17:06</font>
