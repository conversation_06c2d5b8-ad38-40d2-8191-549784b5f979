{"version": 3, "file": "js/6742.c4e51fc8.js", "mappings": "+JAAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,eAAe,CAAEJ,EAAIK,SAAUH,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,SAAS,CAACE,YAAY,iBAAiBE,MAAM,CAAE,OAAUN,EAAIO,gBAAiBC,GAAG,CAAC,MAAQR,EAAIS,gBAAgB,CAACP,EAAG,IAAI,CAACE,YAAY,cAAcF,EAAG,OAAO,CAACF,EAAIU,GAAG,UAAUR,EAAG,SAAS,CAACE,YAAY,aAAaE,MAAM,CAAE,OAAUN,EAAIW,YAAaH,GAAG,CAAC,MAAQR,EAAIY,YAAY,CAACV,EAAG,IAAI,CAACE,YAAY,cAAcF,EAAG,OAAO,CAACF,EAAIU,GAAG,YAAYV,EAAIa,KAAMb,EAAIK,WAAaL,EAAIO,gBAAkBP,EAAIW,YAAaT,EAAG,MAAM,CAACE,YAAY,UAAUI,GAAG,CAAC,MAAQR,EAAIc,kBAAkBd,EAAIa,KAAKX,EAAG,QAAQ,CAACa,IAAI,UAAUX,YAAY,UAAUE,MAAM,CACrrB,kBAAmBN,EAAIO,gBAAkBP,EAAIK,SAC7C,kBAAmBL,EAAIO,iBAAmBP,EAAIK,WAC7C,CAAEL,EAAIK,SAAUH,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,OAAO,CAACE,YAAY,iBAAiB,CAACJ,EAAIU,GAAG,UAAUR,EAAG,SAAS,CAACE,YAAY,YAAYI,GAAG,CAAC,MAAQR,EAAIgB,eAAe,CAACd,EAAG,IAAI,CAACE,YAAY,cAAc,CAACJ,EAAIU,GAAG,WAAWV,EAAIa,KAAKX,EAAG,MAAM,CAACE,YAAY,gBAAgBJ,EAAIiB,GAAIjB,EAAIkB,MAAM,SAASC,GAAU,OAAOjB,EAAG,MAAM,CAACkB,IAAID,EAASE,MAAMjB,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACJ,EAAIU,GAAGV,EAAIsB,GAAGH,EAASE,UAAUnB,EAAG,KAAK,CAACE,YAAY,aAAaJ,EAAIiB,GAAIE,EAASI,OAAO,SAASC,GAAM,OAAOtB,EAAG,KAAK,CAACkB,IAAII,EAAKC,KAAKrB,YAAY,aAAa,CAACF,EAAG,cAAc,CAACE,YAAY,YAAYE,MAAM,CAAE,mBAAoBN,EAAI0B,aAAaF,EAAKC,OAAQE,MAAM,CAAC,GAAKH,EAAKC,MAAMjB,GAAG,CAAC,MAAQR,EAAI4B,kBAAkB,CAAC5B,EAAIU,GAAG,IAAIV,EAAIsB,GAAGE,EAAKK,MAAM,QAAQ,EAAE,IAAG,IAAI,IAAG,KAAK3B,EAAG,OAAO,CAACa,IAAI,cAAcX,YAAY,eAAeE,MAAM,CACj2B,qBAAsBN,EAAIO,iBAAmBP,EAAIW,aAAeX,EAAIK,SACpE,gBAAiBL,EAAIO,iBAAmBP,EAAIW,YAAcX,EAAIK,WAC7D,CAACH,EAAG,cAAc,CAACyB,MAAM,CAAC,IAAM3B,EAAI8B,WAAW,YAAY9B,EAAI+B,cAAc,YAAY/B,EAAIgC,eAAexB,GAAG,CAAC,iBAAiBR,EAAIiC,aAAa,GAAG/B,EAAG,QAAQ,CAACE,YAAY,MAAME,MAAM,CAC1L,cAAeN,EAAIW,YAAcX,EAAIK,SACrC,cAAeL,EAAIW,aAAeX,EAAIK,WACrC,CAAEL,EAAIK,SAAUH,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,OAAO,CAACE,YAAY,aAAa,CAACJ,EAAIU,GAAG,UAAUR,EAAG,SAAS,CAACE,YAAY,YAAYI,GAAG,CAAC,MAAQR,EAAIkC,WAAW,CAAChC,EAAG,IAAI,CAACE,YAAY,cAAc,CAACJ,EAAIU,GAAG,WAAWV,EAAIa,KAAOb,EAAIK,SAA+DL,EAAIa,KAAzDX,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIU,GAAG,UAAmBR,EAAG,KAAK,CAACE,YAAY,YAAYJ,EAAIiB,GAAIjB,EAAImC,KAAK,SAASX,GAAM,OAAOtB,EAAG,KAAK,CAACkB,IAAII,EAAKY,GAAGhC,YAAY,WAAWE,MAAM,CAAE,cAA8B,IAAfkB,EAAKa,MAAa,OAAUb,EAAKY,KAAOpC,EAAIsC,cAAe,CAACpC,EAAG,IAAI,CAACE,YAAY,WAAWuB,MAAM,CAAC,KAAO,IAAMH,EAAKY,IAAI5B,GAAG,CAAC,MAAQ,SAAS+B,GAAgC,OAAxBA,EAAOC,iBAAwBxC,EAAIyC,eAAejB,EAAKY,GAAG,IAAI,CAACpC,EAAIU,GAAG,IAAIV,EAAIsB,GAAGE,EAAKkB,MAAM,QAAQ,IAAG,KAAKxC,EAAG,SAASA,EAAG,WAAW,EACruB,EACIyC,EAAkB,GCXlB5C,G,QAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,eAAe,CAAEJ,EAAI4C,QAAS1C,EAAG,MAAM,CAACE,YAAY,WAAW,CAACJ,EAAIU,GAAG,cAAeV,EAAI6C,MAAO3C,EAAG,MAAM,CAACE,YAAY,SAAS,CAACJ,EAAIU,GAAG,WAAWV,EAAIsB,GAAGtB,EAAI6C,UAAU3C,EAAG,MAAM,CAACA,EAAG,MAAM,CAACa,IAAI,aAAa+B,SAAS,CAAC,UAAY9C,EAAIsB,GAAGtB,EAAI+C,oBAAoB7C,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAAEJ,EAAIgD,SAAU9C,EAAG,cAAc,CAACE,YAAY,YAAYuB,MAAM,CAAC,GAAK3B,EAAIgD,SAASvB,OAAO,CAACvB,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIU,GAAG,SAASR,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIU,GAAGV,EAAIsB,GAAGtB,EAAIgD,SAASnB,WAAW3B,EAAG,MAAM,CAACE,YAAY,oBAAqBJ,EAAIiD,SAAU/C,EAAG,cAAc,CAACE,YAAY,YAAYuB,MAAM,CAAC,GAAK3B,EAAIiD,SAASxB,OAAO,CAACvB,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIU,GAAG,SAASR,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIU,GAAGV,EAAIsB,GAAGtB,EAAIiD,SAASpB,WAAW3B,EAAG,MAAM,CAACE,YAAY,qBAAqB,QACx7B,GACIuC,EAAkB,GCgCtB,GACAO,MAAA,CACAC,IAAAC,OACAJ,SAAAK,OACAJ,SAAAI,QAEAC,OACA,OACAP,gBAAA,GACAH,SAAA,EACAC,MAAA,KAEA,EACAU,MAAA,CACAJ,IAAA,CACAK,WAAA,EACA,cAAAL,GACA,KAAAP,SAAA,EACA,KAAAC,MAAA,KACA,IACA,MAAAY,QAAA,YAAAN,QACA,KAAAJ,gBAAAU,EAAAC,QACA,KAAAC,WAAA,KACA,KAAAC,iBACA,KAAAC,MAAA,oBAEA,OAAAC,GACA,KAAAjB,MAAAiB,EAAAC,QACA,KAAAhB,gBAAA,iBACA,SACA,KAAAH,SAAA,CACA,CACA,IAGAoB,QAAA,CACAC,QAAAvB,GACA,IAAAwB,EAAAxB,EACAyB,cACAC,QAAA,YACAA,QAAA,oBACAA,QAAA,cACAA,QAAA,UACAA,QAAA,UAKA,OAJAF,IAAA,WAAAG,KAAAH,KAEAA,EAAA,mBAAAA,EAAA,IAAAI,KAAAC,SAAAC,SAAA,IAAAC,UAAA,OAEAP,CACA,EACAN,iBACA,QAAAc,MAAAC,WAAA,CACA,MAAAC,EAAA,KAAAF,MAAAC,WAAAE,iBAAA,UACAD,EAAAE,SAAAC,IACAA,EAAA3C,GAAA,KAAA6B,QAAAc,EAAAC,YAAA,IAGA,MAAAC,EAAA,KAAAP,MAAAC,WAAAE,iBAAA,YACAI,EAAAH,SAAAI,IACAA,EAAAC,UAAAC,IAAA,WAGA,MAAAC,EAAA,KAAAX,MAAAC,WAAAE,iBAAA,gBACAQ,EAAAP,SAAAQ,IACAA,EAAAC,iBAAA,SAAAzB,IACAA,EAAAtB,iBACA,MAAAJ,EAAAkD,EAAAE,aAAA,QAAAf,UAAA,GACAgB,EAAAC,SAAAC,eAAAvD,GACAqD,GACAA,EAAAG,eAAA,CAAAC,SAAA,UACA,GACA,GAEA,CACA,IC5G2P,I,UCQvPC,GAAY,OACd,EACA,EACA,GACA,EACA,KACA,KACA,MAIF,EAAeA,EAAiB,Q,oBCkGhC,GACAC,WAAA,CAAAC,YAAA,EAAAC,OAAA,IAAAC,MAAAA,EAAAA,GACA5C,OACA,OACApC,KAAA,CACA,CAAAG,MAAA,SAAAE,MAAA,CACA,CAAAM,KAAA,OAAAJ,KAAA,iBACA,CAAAI,KAAA,OAAAJ,KAAA,qBACA,CAAAI,KAAA,OAAAJ,KAAA,mBAEA,CAAAJ,MAAA,OAAAE,MAAA,CACA,CAAAM,KAAA,OAAAJ,KAAA,gBACA,CAAAI,KAAA,UAAAJ,KAAA,uBACA,CAAAI,KAAA,OAAAJ,KAAA,sBACA,CAAAI,KAAA,cAAAJ,KAAA,yBACA,CAAAI,KAAA,QAAAJ,KAAA,yBAEA,CAAAJ,MAAA,OAAAE,MAAA,CACA,CAAAM,KAAA,yBAAAJ,KAAA,2BACA,CAAAI,KAAA,qBAAAJ,KAAA,qBACA,CAAAI,KAAA,gCAAAJ,KAAA,2BACA,CAAAI,KAAA,mBAAAJ,KAAA,qBACA,CAAAI,KAAA,2BAAAJ,KAAA,kBACA,CAAAI,KAAA,yBAAAJ,KAAA,oBACA,CAAAI,KAAA,gBAAAJ,KAAA,iBACA,CAAAI,KAAA,oCAAAJ,KAAA,6BACA,CAAAI,KAAA,oCAAAJ,KAAA,6BACA,CAAAI,KAAA,yCAAAJ,KAAA,mCACA,CAAAI,KAAA,iBAAAJ,KAAA,mBACA,CAAAI,KAAA,iBAAAJ,KAAA,kBACA,CAAAI,KAAA,kBAAAJ,KAAA,oBACA,CAAAI,KAAA,oCAAAJ,KAAA,4BACA,CAAAI,KAAA,yBAAAJ,KAAA,yBACA,CAAAI,KAAA,iBAAAJ,KAAA,kBACA,CAAAI,KAAA,eAAAJ,KAAA,gBACA,CAAAI,KAAA,eAAAJ,KAAA,gBACA,CAAAI,KAAA,wBAAAJ,KAAA,0BACA,CAAAI,KAAA,mBAAAJ,KAAA,uBAEA,CAAAJ,MAAA,QAAAE,MAAA,CACA,CAAAM,KAAA,WAAAJ,KAAA,wBACA,CAAAI,KAAA,SAAAJ,KAAA,kCAEA,CAAAJ,MAAA,OAAAE,MAAA,CACA,CAAAM,KAAA,OAAAJ,KAAA,wBACA,CAAAI,KAAA,OAAAJ,KAAA,0BAEA,CAAAJ,MAAA,KAAAE,MAAA,CACA,CAAAM,KAAA,YAAAJ,KAAA,4BAGAU,IAAA,GACAgE,SAAA,GACA7D,YAAA,KACA8D,kBAAA,EAEA/F,UAAA,EACAgG,YAAA,EACA9F,gBAAA,EACAI,YAAA,EAEA2F,sBAAA,EAEA,EACAC,SAAA,CACAzE,aACA,YAAA0E,OAAAC,OAAAtD,KAAA,SACA,EACAuD,cACA,YAAAF,OAAA/E,IACA,GAEAkF,UACA,KAAAC,eACA,KAAAC,iBACA,EACAC,UACA,KAAAC,kBAGA,MAAAC,EAAAC,aAAAC,QAAA,6BACAF,IACA,KAAAV,sBAAAa,SAAAH,EAAA,KAGA,KAAArD,WAAA,KACA,MAAAyD,EAAA,KAAA1C,MAAA0C,YACAA,GACAA,EAAA7B,iBAAA,cAAA8B,qBAGA,MAAAC,EAAA,KAAA5C,MAAA4C,QACAA,GACAA,EAAA/B,iBAAA,cAAAgC,qBAGA,KAAAC,8BAAA,IAGAC,OAAAlC,iBAAA,cAAAmC,cACA,KAAAb,iBACA,EACAc,gBACA,MAAAP,EAAA,KAAA1C,MAAA0C,YACAA,GACAA,EAAAQ,oBAAA,cAAAP,qBAGA,MAAAC,EAAA,KAAA5C,MAAA4C,QACAA,GACAA,EAAAM,oBAAA,cAAAL,qBAGAE,OAAAG,oBAAA,cAAAF,aACA,EACAnE,MAAA,CACA,gBACA,KAAAwD,kBACA,KAAApD,WAAA,KACAkE,YAAA,KACA,KAAAL,8BAAA,GACA,MAEA,GAEAxD,QAAA,CACAC,QAAAvB,GACA,IAAAwB,EAAAxB,EACAyB,cACAC,QAAA,YACAA,QAAA,oBACAA,QAAA,cACAA,QAAA,UACAA,QAAA,UAKA,OAJAF,IAAA,WAAAG,KAAAH,KAEAA,EAAA,eAAAA,EAAA,IAAA4D,KAAAC,OAEA7D,CACA,EACA0C,eACA,KAAAT,SAAA,GACA,KAAAjF,KAAA4D,SAAA3D,IACAA,EAAAI,MAAAuD,SAAAtD,IACA,KAAA2E,SAAA6B,KAAA,CACAnG,KAAAL,EAAAK,KACAJ,KAAAD,EAAAC,KACAN,SAAAA,EAAAE,OACA,GACA,GAEA,EACAU,cACA,MAAAkG,EAAA,KAAA9B,SAAA+B,WAAAC,GAAAA,EAAA1G,OAAA,KAAAiF,cACA,OAAAuB,EAAA,OAAA9B,SAAA8B,EAAA,OACA,EACAjG,cACA,MAAAiG,EAAA,KAAA9B,SAAA+B,WAAAC,GAAAA,EAAA1G,OAAA,KAAAiF,cACA,WAAAuB,GAAAA,EAAA,KAAA9B,SAAAiC,OAAA,OAAAjC,SAAA8B,EAAA,OACA,EACAhG,WACA,KAAA0B,WAAA,KACA,MAAAyD,EAAA,KAAA1C,MAAA0C,YACAiB,EAAAjB,EAAAkB,cAAA,gBACA,IAAAD,EAAA,OACA,MAAAzD,EAAAyD,EAAAxD,iBAAA,UACA,KAAA1C,IAAAoG,MAAAC,KAAA5D,GAAA6D,KAAAC,IAEA,MAAAC,EAAAD,EAAAtG,GACAA,EAAAuG,GAAA,KAAA1E,QAAAyE,EAAA1D,aAKA,OAHA2D,IACAD,EAAAtG,GAAAA,GAEA,CACAA,GAAAA,EACAM,KAAAgG,EAAA1D,YACA3C,MAAA8E,SAAAuB,EAAAE,QAAAnE,UAAA,IACA,IAEA,KAAAd,UAAA,KAAA0D,oBAAA,GAEA,EACAN,kBACA,MAAAL,EAAA,KAAAF,OAAA/E,KACA,UAAAN,KAAA,KAAAD,KACA,UAAAM,KAAAL,EAAAI,MACA,GAAAC,EAAAC,OAAAiF,EAEA,YADA,KAAAmC,iBAAArH,EAAAK,KAKA,EACAY,eAAAL,GACA,MAAA0G,EAAA,GACA,KAAA1C,kBAAA,EACA,KAAA9D,YAAAF,EACA,MAAAgF,EAAA,KAAA1C,MAAA0C,YACAiB,EAAAjB,EAAAkB,cAAA,gBACAS,EAAArD,SAAAC,eAAAvD,GACA,GAAA2G,EAAA,CACA,MAAAC,EAAAD,EAAAC,UAAAX,EAAAW,UACA5B,EAAA6B,UAAAD,EAAAF,CACA,CACAjB,YAAA,KACA,KAAAzB,kBAAA,IACA,IACA,EACAiB,sBACA,QAAAjB,iBAAA,OACA,MAAA0C,EAAA,GACA1B,EAAA,KAAA1C,MAAA0C,YACAiB,EAAAjB,EAAAkB,cAAA,gBACA,IAAAD,EAAA,OACA,MAAAzD,EAAAyD,EAAAxD,iBAAA,UACA,IAAAqE,EAAA,KACA,MAAAD,EAAA7B,EAAA6B,UACA,QAAAE,EAAAvE,EAAAwD,OAAA,EAAAe,GAAA,EAAAA,IAAA,CACA,MAAApE,EAAAH,EAAAuE,GACA,GAAApE,EAAAiE,UAAAF,GAAAG,EAAA,CACAC,EAAAnE,EAAA3C,GACA,KACA,CACA,CACA,KAAAE,YAAA4G,CACA,EACAxH,aAAAD,GACA,YAAA+E,OAAA/E,OAAAA,IACA,eAAA+E,OAAA/E,MAAA,gBAAA+E,OAAA/E,OAAA,KAAAP,KAAA,GAAAK,MAAA,GAAAE,OAAAA,CAEA,EAEAoF,kBACA,KAAAR,YAAAoB,OAAA2B,WACA,MAAAC,EAAA,KAAAhJ,SACA,KAAAA,SAAA,KAAAgG,aAAA,IAGAgD,IAAA,KAAAhJ,UACA,KAAAE,gBAAA,EACA,KAAAI,YAAA,IAGA0I,GAAA,KAAAhJ,WACA,KAAAE,gBAAA,EACA,KAAAI,YAAA,EAEA,EAEA+G,eACA4B,aAAA,KAAAC,aACA,KAAAA,YAAA1B,YAAA,KACA,KAAAhB,iBAAA,GACA,IACA,EAEApG,gBACA,KAAAF,gBAAA,KAAAA,eACA,KAAAA,gBAAA,KAAAI,aACA,KAAAA,YAAA,EAEA,EAEAC,YACA,KAAAD,YAAA,KAAAA,WACA,KAAAA,YAAA,KAAAJ,iBACA,KAAAA,gBAAA,EAEA,EAEAS,eACA,KAAAT,gBAAA,CACA,EAEA2B,WACA,KAAAvB,YAAA,CACA,EAEAG,iBACA,KAAAP,gBAAA,EACA,KAAAI,YAAA,CACA,EAEAiB,kBACA,KAAAvB,WACA,KAAAE,gBAAA,EAEA,EAEAgH,sBACA,MAAAD,EAAA,KAAA5C,MAAA4C,QACAA,IACA,KAAAhB,sBAAAgB,EAAA2B,UACAhC,aAAAuC,QAAA,4BAAAlC,EAAA2B,UAAAzE,YAEA,EAEAgD,+BACA,MAAAF,EAAA,KAAA5C,MAAA4C,QACAA,GAAA,KAAAhB,uBAAA,GACAmD,uBAAA,KACAnC,EAAA2B,UAAA,KAAA3C,qBAAA,GAGA,ICtawP,ICQpP,GAAY,OACd,EACAvG,EACA4C,GACA,EACA,KACA,WACA,MAIF,EAAe,EAAiB,O,sBCnBhC,IAAI8F,EAAM,CACT,0BAA2B,CAC1B,KACA,MAED,gBAAiB,CAChB,KACA,MAED,qBAAsB,CACrB,IACA,MAED,mBAAoB,CACnB,IACA,MAED,kBAAmB,CAClB,KACA,MAED,iBAAkB,CACjB,KACA,MAED,yBAA0B,CACzB,KACA,MAED,uBAAwB,CACvB,IACA,MAED,gBAAiB,CAChB,IACA,MAED,kBAAmB,CAClB,KACA,MAED,cAAe,CACd,KACA,MAED,qBAAsB,CACrB,KACA,MAED,oBAAqB,CACpB,KACA,MAED,uBAAwB,CACvB,KACA,MAED,wBAAyB,CACxB,KACA,KAED,mBAAoB,CACnB,KACA,KAED,uBAAwB,CACvB,KACA,MAED,cAAe,CACd,KACA,MAED,gBAAiB,CAChB,KACA,MAED,cAAe,CACd,KACA,KAED,yBAA0B,CACzB,KACA,MAED,mBAAoB,CACnB,KACA,MAED,8BAA+B,CAC9B,KACA,MAED,sBAAuB,CACtB,KACA,MAED,mBAAoB,CACnB,KACA,MAED,eAAgB,CACf,KACA,MAED,sBAAuB,CACtB,KACA,IAED,2BAA4B,CAC3B,KACA,MAED,2BAA4B,CAC3B,KACA,MAED,iCAAkC,CACjC,KACA,MAED,eAAgB,CACf,KACA,KAED,sBAAuB,CACtB,KACA,MAED,eAAgB,CACf,KACA,OAGF,SAASiB,EAAoBC,GAC5B,IAAIC,EAAoBC,EAAEpB,EAAKkB,GAC9B,OAAOG,QAAQC,UAAUC,MAAK,WAC7B,IAAIlG,EAAI,IAAImG,MAAM,uBAAyBN,EAAM,KAEjD,MADA7F,EAAEoG,KAAO,mBACHpG,CACP,IAGD,IAAIqG,EAAM1B,EAAIkB,GAAMvH,EAAK+H,EAAI,GAC7B,OAAOP,EAAoB9F,EAAEqG,EAAI,IAAIH,MAAK,WACzC,OAAOJ,EAAoBxH,EAC5B,GACD,CACAsH,EAAoBU,KAAO,WAAa,OAAO/G,OAAO+G,KAAK3B,EAAM,EACjEiB,EAAoBtH,GAAK,IACzBqB,EAAO4G,QAAUX,C", "sources": ["webpack://portal-ui/./src/views/HelpView.vue", "webpack://portal-ui/./src/views/HelpContent.vue", "webpack://portal-ui/src/views/HelpContent.vue", "webpack://portal-ui/./src/views/HelpContent.vue?cbaf", "webpack://portal-ui/./src/views/HelpContent.vue?7799", "webpack://portal-ui/src/views/HelpView.vue", "webpack://portal-ui/./src/views/HelpView.vue?9498", "webpack://portal-ui/./src/views/HelpView.vue?a84f", "webpack://portal-ui/./src/docs/ lazy ^\\.\\/.*\\.md$ chunkName: docs namespace object"], "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"help-layout\"},[(_vm.isMobile)?_c('div',{staticClass:\"mobile-controls\"},[_c('button',{staticClass:\"sidebar-toggle\",class:{ 'active': _vm.sidebarVisible },on:{\"click\":_vm.toggleSidebar}},[_c('i',{staticClass:\"icon-menu\"}),_c('span',[_vm._v(\"菜单\")])]),_c('button',{staticClass:\"toc-toggle\",class:{ 'active': _vm.tocVisible },on:{\"click\":_vm.toggleToc}},[_c('i',{staticClass:\"icon-list\"}),_c('span',[_vm._v(\"目录\")])])]):_vm._e(),(_vm.isMobile && (_vm.sidebarVisible || _vm.tocVisible))?_c('div',{staticClass:\"overlay\",on:{\"click\":_vm.closeAllPanels}}):_vm._e(),_c('aside',{ref:\"sidebar\",staticClass:\"sidebar\",class:{\n      'sidebar-hidden': !_vm.sidebarVisible && _vm.isMobile,\n      'sidebar-visible': _vm.sidebarVisible || !_vm.isMobile\n    }},[(_vm.isMobile)?_c('div',{staticClass:\"sidebar-header\"},[_c('span',{staticClass:\"sidebar-title\"},[_vm._v(\"帮助文档\")]),_c('button',{staticClass:\"close-btn\",on:{\"click\":_vm.closeSidebar}},[_c('i',{staticClass:\"icon-close\"},[_vm._v(\"×\")])])]):_vm._e(),_c('div',{staticClass:\"sidebar-menu\"},_vm._l((_vm.menu),function(category){return _c('div',{key:category.title,staticClass:\"menu-category\"},[_c('div',{staticClass:\"category-title\"},[_vm._v(_vm._s(category.title))]),_c('ul',{staticClass:\"menu-list\"},_vm._l((category.items),function(item){return _c('li',{key:item.path,staticClass:\"menu-item\"},[_c('router-link',{staticClass:\"menu-link\",class:{ 'menu-link-active': _vm.isMenuActive(item.path) },attrs:{\"to\":item.path},on:{\"click\":_vm.onMenuItemClick}},[_vm._v(\" \"+_vm._s(item.name)+\" \")])],1)}),0)])}),0)]),_c('main',{ref:\"mainContent\",staticClass:\"main-content\",class:{\n      'content-expanded': (!_vm.sidebarVisible || !_vm.tocVisible) && _vm.isMobile,\n      'content-full': !_vm.sidebarVisible && !_vm.tocVisible && _vm.isMobile\n    }},[_c('HelpContent',{attrs:{\"doc\":_vm.currentDoc,\"prev-page\":_vm.getPrevPage(),\"next-page\":_vm.getNextPage()},on:{\"content-loaded\":_vm.buildToc}})],1),_c('aside',{staticClass:\"toc\",class:{\n      'toc-hidden': !_vm.tocVisible && _vm.isMobile,\n      'toc-visible': _vm.tocVisible || !_vm.isMobile\n    }},[(_vm.isMobile)?_c('div',{staticClass:\"toc-header\"},[_c('span',{staticClass:\"toc-title\"},[_vm._v(\"文章导航\")]),_c('button',{staticClass:\"close-btn\",on:{\"click\":_vm.closeToc}},[_c('i',{staticClass:\"icon-close\"},[_vm._v(\"×\")])])]):_vm._e(),(!_vm.isMobile)?_c('div',{staticClass:\"toc-title\"},[_vm._v(\"文章导航\")]):_vm._e(),_c('ul',{staticClass:\"toc-list\"},_vm._l((_vm.toc),function(item){return _c('li',{key:item.id,staticClass:\"toc-item\",class:{ 'toc-item-h3': item.level === 3, 'active': item.id === _vm.activeTocId }},[_c('a',{staticClass:\"toc-link\",attrs:{\"href\":'#' + item.id},on:{\"click\":function($event){$event.preventDefault();return _vm.scrollToAnchor(item.id)}}},[_vm._v(\" \"+_vm._s(item.text)+\" \")])])}),0)]),_c('Mider'),_c('chatAi')],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"doc-content\"},[(_vm.loading)?_c('div',{staticClass:\"loading\"},[_vm._v(\"文档加载中...\")]):(_vm.error)?_c('div',{staticClass:\"error\"},[_vm._v(\"文档加载失败: \"+_vm._s(_vm.error))]):_c('div',[_c('div',{ref:\"contentRef\",domProps:{\"innerHTML\":_vm._s(_vm.markdownContent)}}),_c('div',{staticClass:\"page-navigation\"},[_c('div',{staticClass:\"prev-next-container\"},[(_vm.prevPage)?_c('router-link',{staticClass:\"prev-page\",attrs:{\"to\":_vm.prevPage.path}},[_c('div',{staticClass:\"nav-label\"},[_vm._v(\"上一篇\")]),_c('div',{staticClass:\"nav-title\"},[_vm._v(_vm._s(_vm.prevPage.name))])]):_c('div',{staticClass:\"prev-page empty\"}),(_vm.nextPage)?_c('router-link',{staticClass:\"next-page\",attrs:{\"to\":_vm.nextPage.path}},[_c('div',{staticClass:\"nav-label\"},[_vm._v(\"下一篇\")]),_c('div',{staticClass:\"nav-title\"},[_vm._v(_vm._s(_vm.nextPage.name))])]):_c('div',{staticClass:\"next-page empty\"})],1)])])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"doc-content\">\r\n    <div v-if=\"loading\" class=\"loading\">文档加载中...</div>\r\n    <div v-else-if=\"error\" class=\"error\">文档加载失败: {{ error }}</div>\r\n    <div v-else>\r\n      <div v-html=\"markdownContent\" ref=\"contentRef\"></div>\r\n      \r\n      <!-- 上一页/下一页导航 -->\r\n      <div class=\"page-navigation\">\r\n        <div class=\"prev-next-container\">\r\n          <router-link \r\n            v-if=\"prevPage\" \r\n            :to=\"prevPage.path\" \r\n            class=\"prev-page\">\r\n            <div class=\"nav-label\">上一篇</div>\r\n            <div class=\"nav-title\">{{ prevPage.name }}</div>\r\n          </router-link>\r\n          <div v-else class=\"prev-page empty\"></div>\r\n          \r\n          <router-link \r\n            v-if=\"nextPage\" \r\n            :to=\"nextPage.path\" \r\n            class=\"next-page\">\r\n            <div class=\"nav-label\">下一篇</div>\r\n            <div class=\"nav-title\">{{ nextPage.name }}</div>\r\n          </router-link>\r\n          <div v-else class=\"next-page empty\"></div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  props: {\r\n    doc: String,\r\n    prevPage: Object,\r\n    nextPage: Object\r\n  },\r\n  data() {\r\n    return {\r\n      markdownContent: '',\r\n      loading: false,\r\n      error: null\r\n    };\r\n  },\r\n  watch: {\r\n    doc: {\r\n      immediate: true,\r\n      async handler(doc) {\r\n        this.loading = true;\r\n        this.error = null;\r\n        try {\r\n          const module = await import(/* webpackChunkName: \"docs\" */ `../docs/${doc}.md`);\r\n          this.markdownContent = module.default;\r\n          this.$nextTick(() => {\r\n            this.processContent();\r\n            this.$emit('content-loaded');\r\n          });\r\n        } catch (e) {\r\n          this.error = e.message;\r\n          this.markdownContent = '<h1>文档加载失败</h1>';\r\n        } finally {\r\n          this.loading = false;\r\n        }\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    slugify(text) {\r\n      let slug = text\r\n        .toLowerCase()\r\n        .replace(/\\s+/g, '-')           // 空格转为-\r\n        .replace(/[^a-z0-9\\-]+/g, '')   // 只保留小写字母、数字、短横线\r\n        .replace(/\\-\\-+/g, '-')         // 多个-合并为一个\r\n        .replace(/^-+/, '')              // 去除开头-\r\n        .replace(/-+$/, '');             // 去除结尾-\r\n      if (!slug || /^[0-9]+$/.test(slug)) {\r\n        // 添加随机数确保唯一性\r\n        slug = 'content-section-' + slug + '-' + Math.random().toString(36).substring(2, 11);\r\n      }\r\n      return slug;\r\n    },\r\n    processContent() {\r\n      if (this.$refs.contentRef) {\r\n        const headings = this.$refs.contentRef.querySelectorAll('h2, h3');\r\n        headings.forEach(heading => {\r\n          heading.id = this.slugify(heading.textContent);\r\n        });\r\n        \r\n        const codeBlocks = this.$refs.contentRef.querySelectorAll('pre code');\r\n        codeBlocks.forEach(block => {\r\n          block.classList.add('hljs');\r\n        });\r\n        \r\n        const links = this.$refs.contentRef.querySelectorAll('a[href^=\"#\"]');\r\n        links.forEach(link => {\r\n          link.addEventListener('click', (e) => {\r\n            e.preventDefault();\r\n            const id = link.getAttribute('href').substring(1);\r\n            const targetElement = document.getElementById(id);\r\n            if (targetElement) {\r\n              targetElement.scrollIntoView({ behavior: 'smooth' });\r\n            }\r\n          });\r\n        });\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style>\r\n/* 注意：这里不使用scoped，以便样式应用到动态生成的内容 */\r\n.doc-content h1 {\r\n  font-size: 28px;\r\n  margin-bottom: 20px;\r\n  color: #333;\r\n}\r\n\r\n.doc-content h2 {\r\n  font-size: 22px;\r\n  margin: 30px 0 15px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #eee;\r\n  color: #333;\r\n}\r\n\r\n.doc-content h3 {\r\n  font-size: 18px;\r\n  margin: 25px 0 15px;\r\n  color: #333;\r\n}\r\n\r\n.doc-content p {\r\n  margin: 15px 0;\r\n  line-height: 1.6;\r\n  color: #555;\r\n}\r\n\r\n.doc-content ul, .doc-content ol {\r\n  padding-left: 25px;\r\n  margin: 15px 0;\r\n}\r\n\r\n.doc-content li {\r\n  margin-bottom: 8px;\r\n  line-height: 1.6;\r\n}\r\n\r\n.doc-content code {\r\n  background-color: #f5f5f5;\r\n  padding: 2px 5px;\r\n  border-radius: 3px;\r\n  font-family: Consolas, Monaco, 'Andale Mono', monospace;\r\n  color: #d63384;\r\n}\r\n\r\n.doc-content pre {\r\n  background-color: #f8f8f8;\r\n  padding: 15px;\r\n  border-radius: 5px;\r\n  overflow-x: auto;\r\n  margin: 20px 0;\r\n}\r\n\r\n.doc-content pre code {\r\n  background-color: transparent;\r\n  padding: 0;\r\n  color: #333;\r\n  display: block;\r\n}\r\n\r\n.doc-content a {\r\n  color: #1890ff;\r\n  text-decoration: none;\r\n}\r\n\r\n.doc-content a:hover {\r\n  text-decoration: underline;\r\n}\r\n\r\n.doc-content blockquote {\r\n  border-left: 4px solid #1890ff !important;\r\n  padding: 16px 20px 16px 50px !important;\r\n  color: #555 !important;\r\n  background: linear-gradient(135deg, #f0f8ff 0%, #e6f7ff 100%) !important;\r\n  margin: 20px 0 !important;\r\n  border-radius: 8px !important;\r\n  font-family: inherit !important;\r\n  position: relative !important;\r\n  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1) !important;\r\n  border: 1px solid rgba(24, 144, 255, 0.2) !important;\r\n}\r\n\r\n.doc-content blockquote::before {\r\n  content: 'ℹ️';\r\n  position: absolute;\r\n  left: 16px;\r\n  top: 16px;\r\n  font-size: 16px;\r\n  background: #1890ff;\r\n  color: white;\r\n  width: 24px;\r\n  height: 24px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 14px;\r\n  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);\r\n}\r\n\r\n.doc-content blockquote p {\r\n  margin: 0 !important;\r\n  line-height: 1.7 !important;\r\n  font-size: 14px !important;\r\n}\r\n\r\n.doc-content blockquote p:not(:last-child) {\r\n  margin-bottom: 12px !important;\r\n}\r\n\r\n/* 确保引用块内的font标签样式正常显示 */\r\n.doc-content blockquote font {\r\n  color: inherit !important;\r\n}\r\n\r\n/* 引用块内的链接样式 */\r\n.doc-content blockquote a {\r\n  color: #1890ff !important;\r\n  text-decoration: none !important;\r\n  font-weight: 500 !important;\r\n}\r\n\r\n.doc-content blockquote a:hover {\r\n  text-decoration: underline !important;\r\n  color: #40a9ff !important;\r\n}\r\n\r\n/* 引用块内的代码样式 */\r\n.doc-content blockquote code {\r\n  background-color: rgba(255, 255, 255, 0.8) !important;\r\n  padding: 2px 6px !important;\r\n  border-radius: 3px !important;\r\n  border: 1px solid rgba(24, 144, 255, 0.2) !important;\r\n  color: #1890ff !important;\r\n  font-weight: 500 !important;\r\n}\r\n\r\n/* 引用块内的列表样式 */\r\n.doc-content blockquote ul,\r\n.doc-content blockquote ol {\r\n  margin: 8px 0 !important;\r\n  padding-left: 20px !important;\r\n}\r\n\r\n.doc-content blockquote li {\r\n  margin-bottom: 4px !important;\r\n  line-height: 1.6 !important;\r\n}\r\n\r\n.doc-content table {\r\n  border-collapse: collapse;\r\n  width: 100%;\r\n  margin: 20px 0;\r\n}\r\n\r\n.doc-content table th, .doc-content table td {\r\n  border: 1px solid #ddd;\r\n  padding: 10px;\r\n  text-align: left;\r\n}\r\n\r\n.doc-content table th {\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n.loading, .error {\r\n  padding: 20px;\r\n  text-align: center;\r\n  font-size: 18px;\r\n}\r\n\r\n.error {\r\n  color: red;\r\n}\r\n\r\n/* 上一页/下一页导航样式 */\r\n.page-navigation {\r\n  /* margin-top: 60px; */\r\n  padding-top: 20px;\r\n  border-top: 1px solid #eee;\r\n}\r\n\r\n.prev-next-container {\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n\r\n.prev-page, .next-page {\r\n  display: flex;\r\n  flex-direction: column;\r\n  width: 45%;\r\n  padding: 15px;\r\n  border: 1px solid #eee;\r\n  border-radius: 5px;\r\n  text-decoration: none;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.prev-page {\r\n  text-align: left;\r\n}\r\n\r\n.next-page {\r\n  text-align: right;\r\n}\r\n\r\n.prev-page:hover, .next-page:hover {\r\n  border-color: #1890ff;\r\n  background-color: #f0f8ff;\r\n}\r\n\r\n.nav-label {\r\n  font-size: 12px;\r\n  color: #999;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.nav-title {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  color: #1890ff;\r\n}\r\n\r\n.empty {\r\n  visibility: hidden;\r\n}\r\n\r\n.doc-content img {\r\n  display: block;\r\n  max-width: 100%;\r\n  height: auto;\r\n  margin: 24px auto;  /* 上下间距24px，左右自动居中 */\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.04);\r\n  border-radius: 6px;\r\n}\r\n\r\n/* 引用块响应式样式 */\r\n@media (max-width: 768px) {\r\n  .doc-content blockquote {\r\n    padding: 12px 16px 12px 40px !important;\r\n    margin: 16px 0 !important;\r\n    border-radius: 6px !important;\r\n  }\r\n\r\n  .doc-content blockquote::before {\r\n    left: 12px !important;\r\n    top: 12px !important;\r\n    width: 20px !important;\r\n    height: 20px !important;\r\n    font-size: 12px !important;\r\n  }\r\n\r\n  .doc-content blockquote p {\r\n    font-size: 13px !important;\r\n    line-height: 1.6 !important;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .doc-content blockquote {\r\n    padding: 10px 12px 10px 36px !important;\r\n    margin: 12px 0 !important;\r\n  }\r\n\r\n  .doc-content blockquote::before {\r\n    left: 10px !important;\r\n    top: 10px !important;\r\n    width: 18px !important;\r\n    height: 18px !important;\r\n    font-size: 10px !important;\r\n  }\r\n}\r\n</style>", "import mod from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./HelpContent.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./HelpContent.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./HelpContent.vue?vue&type=template&id=63deeb16&\"\nimport script from \"./HelpContent.vue?vue&type=script&lang=js&\"\nexport * from \"./HelpContent.vue?vue&type=script&lang=js&\"\nimport style0 from \"./HelpContent.vue?vue&type=style&index=0&id=63deeb16&prod&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "<template>\r\n  <div class=\"help-layout\">\r\n    <!-- 移动端控制按钮 -->\r\n    <div class=\"mobile-controls\" v-if=\"isMobile\">\r\n      <button\r\n        class=\"sidebar-toggle\"\r\n        @click=\"toggleSidebar\"\r\n        :class=\"{ 'active': sidebarVisible }\"\r\n      >\r\n        <i class=\"icon-menu\"></i>\r\n        <span>菜单</span>\r\n      </button>\r\n      <button\r\n        class=\"toc-toggle\"\r\n        @click=\"toggleToc\"\r\n        :class=\"{ 'active': tocVisible }\"\r\n      >\r\n        <i class=\"icon-list\"></i>\r\n        <span>目录</span>\r\n      </button>\r\n    </div>\r\n\r\n    <!-- 遮罩层 -->\r\n    <div\r\n      class=\"overlay\"\r\n      v-if=\"isMobile && (sidebarVisible || tocVisible)\"\r\n      @click=\"closeAllPanels\"\r\n    ></div>\r\n\r\n    <!-- 左侧边栏 -->\r\n    <aside\r\n      ref=\"sidebar\"\r\n      class=\"sidebar\"\r\n      :class=\"{\r\n        'sidebar-hidden': !sidebarVisible && isMobile,\r\n        'sidebar-visible': sidebarVisible || !isMobile\r\n      }\"\r\n    >\r\n      <div class=\"sidebar-header\" v-if=\"isMobile\">\r\n        <span class=\"sidebar-title\">帮助文档</span>\r\n        <button class=\"close-btn\" @click=\"closeSidebar\">\r\n          <i class=\"icon-close\">×</i>\r\n        </button>\r\n      </div>\r\n      <div class=\"sidebar-menu\">\r\n        <div v-for=\"category in menu\" :key=\"category.title\" class=\"menu-category\">\r\n          <div class=\"category-title\">{{ category.title }}</div>\r\n          <ul class=\"menu-list\">\r\n            <li v-for=\"item in category.items\" :key=\"item.path\" class=\"menu-item\">\r\n              <router-link\r\n                :to=\"item.path\"\r\n                class=\"menu-link\"\r\n                :class=\"{ 'menu-link-active': isMenuActive(item.path) }\"\r\n                @click=\"onMenuItemClick\"\r\n              >\r\n                {{ item.name }}\r\n              </router-link>\r\n            </li>\r\n          </ul>\r\n        </div>\r\n      </div>\r\n    </aside>\r\n\r\n    <!-- 主内容区 -->\r\n    <main\r\n      class=\"main-content\"\r\n      ref=\"mainContent\"\r\n      :class=\"{\r\n        'content-expanded': (!sidebarVisible || !tocVisible) && isMobile,\r\n        'content-full': !sidebarVisible && !tocVisible && isMobile\r\n      }\"\r\n    >\r\n      <HelpContent\r\n        :doc=\"currentDoc\"\r\n        @content-loaded=\"buildToc\"\r\n        :prev-page=\"getPrevPage()\"\r\n        :next-page=\"getNextPage()\"\r\n      />\r\n    </main>\r\n\r\n    <!-- 右侧目录 -->\r\n    <aside\r\n      class=\"toc\"\r\n      :class=\"{\r\n        'toc-hidden': !tocVisible && isMobile,\r\n        'toc-visible': tocVisible || !isMobile\r\n      }\"\r\n    >\r\n      <div class=\"toc-header\" v-if=\"isMobile\">\r\n        <span class=\"toc-title\">文章导航</span>\r\n        <button class=\"close-btn\" @click=\"closeToc\">\r\n          <i class=\"icon-close\">×</i>\r\n        </button>\r\n      </div>\r\n      <div class=\"toc-title\" v-if=\"!isMobile\">文章导航</div>\r\n      <ul class=\"toc-list\">\r\n        <li v-for=\"item in toc\" :key=\"item.id\" class=\"toc-item\" :class=\"{ 'toc-item-h3': item.level === 3, 'active': item.id === activeTocId }\">\r\n          <a\r\n            :href=\"'#' + item.id\"\r\n            class=\"toc-link\"\r\n            @click.prevent=\"scrollToAnchor(item.id)\"\r\n          >\r\n            {{ item.text }}\r\n          </a>\r\n        </li>\r\n      </ul>\r\n    </aside>\r\n    <!-- 悬浮窗组件 -->\r\n    <Mider></Mider>\r\n    <chatAi></chatAi>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport HelpContent from './HelpContent';\r\nimport chatAi from \"@/components/common/mider/chatAi\";\r\nimport Mider from '@/components/common/mider/Mider';\r\nexport default {\r\n  components: { HelpContent, chatAi , Mider },\r\n  data() {\r\n    return {\r\n      menu: [\r\n        { title: '弹性部署服务', items: [\r\n          { name: '平台概要', path: '/help/summary' },\r\n          { name: '快速开始', path: '/help/quick-start' },\r\n          { name: '常见问题', path: '/help/qustion' }\r\n        ]},\r\n        { title: '功能介绍', items: [\r\n          { name: '镜像仓库', path: '/help/mirror' },\r\n          { name: 'GPU选型指南', path: '/help/gpu-selection' },\r\n          { name: '健康检查', path: '/help/health-check' },\r\n          { name: 'K8S YAML 导入', path: '/help/k8s-yaml-import' },\r\n          { name: '云存储加速', path: '/help/cloud-storage' }\r\n        ]},\r\n        { title: '最佳实践', items: [\r\n          { name: '弹性部署服务-Serverless 基础认识', path: '/help/deploy-serverless' },\r\n          { name: '容器化部署 Ollama+Qwen3', path: '/help/ollama-qwen' },\r\n          { name: '容器化部署 Ollama+Qwen3+Open WebUI', path: '/help/ollama-qwen-webui' },\r\n          { name: '容器化部署 JupyterLab', path: '/help/jupyter-lab' },\r\n          { name: '容器化部署 Flux.1-dev 文生图模型应用', path: '/help/flux-dev' },\r\n          { name: '容器化部署 FramePack 图生视频框架', path: '/help/frame-pack' },\r\n          { name: '容器化部署 Whisper', path: '/help/whisper' },\r\n          { name: '容器化部署 StableDiffusion1.5-WebUI 应用', path: '/help/stable-diffusion1.5' },\r\n          { name: '容器化部署 StableDiffusion2.1-WebUI 应用', path: '/help/stable-diffusion2.1' },\r\n          { name: '容器化部署 StableDiffusion3.5-large-文生图模型应用', path: '/help/stable-diffusion3.5-large' },\r\n          { name: '容器化部署 DailyHot', path: '/help/daily-hot' },\r\n          { name: '容器化部署 ACE-Step', path: '/help/ace-step' },\r\n          { name: '容器化部署 CosyVoice', path: '/help/cosy-voice' },\r\n          { name: '容器化部署 Flux.1 Kontext Dev 图片编辑模型应用', path: '/help/Flux.1-kontext-dev' },\r\n          { name: '容器化部署 HivisionIDPhotos', path: '/help/hivision-photos' },\r\n          { name: '容器化部署 minicpm4', path: '/help/minicpm4' },\r\n          { name: '容器化部署 minerU', path: '/help/mineru' },\r\n          { name: '容器化部署 FunASR', path: '/help/funasr' },\r\n          { name: '容器化部署 HunyuanPortrait', path: '/help/hunyuan-portrait' },\r\n          { name: '容器化部署 CodeFormer', path: '/help/code-former' },\r\n        ]},\r\n        { title: '账户与实名', items: [\r\n          { name: '手机号注册与登录', path: '/help/register-login' },\r\n          { name: '个人用户实名', path: '/help/personal-certification' }\r\n        ]},\r\n        { title: '服务协议', items: [\r\n          { name: '服务协议', path: '/help/user-agreement' },\r\n          { name: '隐私政策', path: '/help/privacy-policy' }\r\n        ]},\r\n        { title: '其他', items: [\r\n          { name: 'Docker 教程', path: '/help/docker-tutorial' }\r\n        ]}\r\n      ],\r\n      toc: [],\r\n      allPages: [],\r\n      activeTocId: null,\r\n      isAnchorClicking: false,\r\n      // 响应式状态\r\n      isMobile: false,\r\n      windowWidth: 0,\r\n      sidebarVisible: true,\r\n      tocVisible: true,\r\n      // 侧边栏滚动位置保存\r\n      sidebarScrollPosition: 0\r\n    };\r\n  },\r\n  computed: {\r\n    currentDoc() {\r\n      return this.$route.params.doc || 'summary';\r\n    },\r\n    currentPath() {\r\n      return this.$route.path;\r\n    }\r\n  },\r\n  created() {\r\n    this.flattenPages();\r\n    this.checkScreenSize();\r\n  },\r\n  mounted() {\r\n    this.updatePageTitle();\r\n\r\n    // 从 localStorage 恢复滚动位置\r\n    const savedScrollPosition = localStorage.getItem('helpSidebarScrollPosition');\r\n    if (savedScrollPosition) {\r\n      this.sidebarScrollPosition = parseInt(savedScrollPosition, 10);\r\n    }\r\n\r\n    this.$nextTick(() => {\r\n      const mainContent = this.$refs.mainContent;\r\n      if (mainContent) {\r\n        mainContent.addEventListener('scroll', this.handleContentScroll);\r\n      }\r\n\r\n      const sidebar = this.$refs.sidebar;\r\n      if (sidebar) {\r\n        sidebar.addEventListener('scroll', this.handleSidebarScroll);\r\n      }\r\n\r\n      this.restoreSidebarScrollPosition();\r\n    });\r\n\r\n    window.addEventListener('resize', this.handleResize);\r\n    this.checkScreenSize();\r\n  },\r\n  beforeDestroy() {\r\n    const mainContent = this.$refs.mainContent;\r\n    if (mainContent) {\r\n      mainContent.removeEventListener('scroll', this.handleContentScroll);\r\n    }\r\n\r\n    const sidebar = this.$refs.sidebar;\r\n    if (sidebar) {\r\n      sidebar.removeEventListener('scroll', this.handleSidebarScroll);\r\n    }\r\n\r\n    window.removeEventListener('resize', this.handleResize);\r\n  },\r\n  watch: {\r\n    '$route.path'() {\r\n      this.updatePageTitle();\r\n      this.$nextTick(() => {\r\n        setTimeout(() => {\r\n          this.restoreSidebarScrollPosition();\r\n        }, 50);\r\n      });\r\n    }\r\n  },\r\n  methods: {\r\n    slugify(text) {\r\n      let slug = text\r\n        .toLowerCase()\r\n        .replace(/\\s+/g, '-')           // 空格转为-\r\n        .replace(/[^a-z0-9\\-]+/g, '')   // 只保留小写字母、数字、短横线\r\n        .replace(/\\-\\-+/g, '-')         // 多个-合并为一个\r\n        .replace(/^-+/, '')              // 去除开头-\r\n        .replace(/-+$/, '');             // 去除结尾-\r\n      if (!slug || /^[0-9]+$/.test(slug)) {\r\n        // 添加时间戳确保唯一性\r\n        slug = 'toc-section-' + slug + '-' + Date.now();\r\n      }\r\n      return slug;\r\n    },\r\n    flattenPages() {\r\n      this.allPages = [];\r\n      this.menu.forEach(category => {\r\n        category.items.forEach(item => {\r\n          this.allPages.push({\r\n            name: item.name,\r\n            path: item.path,\r\n            category: category.title\r\n          });\r\n        });\r\n      });\r\n    },\r\n    getPrevPage() {\r\n      const currentIndex = this.allPages.findIndex(page => page.path === this.currentPath);\r\n      return currentIndex > 0 ? this.allPages[currentIndex - 1] : null;\r\n    },\r\n    getNextPage() {\r\n      const currentIndex = this.allPages.findIndex(page => page.path === this.currentPath);\r\n      return (currentIndex !== -1 && currentIndex < this.allPages.length - 1) ? this.allPages[currentIndex + 1] : null;\r\n    },\r\n    buildToc() {\r\n      this.$nextTick(() => {\r\n        const mainContent = this.$refs.mainContent;\r\n        const docContent = mainContent.querySelector('.doc-content');\r\n        if (!docContent) return;\r\n        const headings = docContent.querySelectorAll('h2, h3');\r\n        this.toc = Array.from(headings).map(h => {\r\n          // 如果元素已经有ID，使用现有的ID，否则生成新的\r\n          const existingId = h.id;\r\n          const id = existingId || this.slugify(h.textContent);\r\n          // 只有当元素没有ID时才设置ID\r\n          if (!existingId) {\r\n            h.id = id;\r\n          }\r\n          return {\r\n            id: id,\r\n            text: h.textContent,\r\n            level: parseInt(h.tagName.substring(1))\r\n          };\r\n        });\r\n        this.$nextTick(this.handleContentScroll);\r\n      });\r\n    },\r\n    updatePageTitle() {\r\n      const currentPath = this.$route.path;\r\n      for (const category of this.menu) {\r\n        for (const item of category.items) {\r\n          if (item.path === currentPath) {\r\n            this.currentPageTitle = item.name;\r\n            return;\r\n          }\r\n        }\r\n      }\r\n    },\r\n    scrollToAnchor(id) {\r\n      const OFFSET = 30;\r\n      this.isAnchorClicking = true;\r\n      this.activeTocId = id;\r\n      const mainContent = this.$refs.mainContent;\r\n      const docContent = mainContent.querySelector('.doc-content');\r\n      const element = document.getElementById(id);\r\n      if (element) {\r\n        const offsetTop = element.offsetTop - docContent.offsetTop;\r\n        mainContent.scrollTop = offsetTop - OFFSET;\r\n      }\r\n      setTimeout(() => {\r\n        this.isAnchorClicking = false;\r\n      }, 100);\r\n    },\r\n    handleContentScroll() {\r\n      if (this.isAnchorClicking) return;\r\n      const OFFSET = 30;\r\n      const mainContent = this.$refs.mainContent;\r\n      const docContent = mainContent.querySelector('.doc-content');\r\n      if (!docContent) return;\r\n      const headings = docContent.querySelectorAll('h2, h3');\r\n      let activeId = null;\r\n      const scrollTop = mainContent.scrollTop;\r\n      for (let i = headings.length - 1; i >= 0; i--) {\r\n        const heading = headings[i];\r\n        if (heading.offsetTop - OFFSET <= scrollTop) {\r\n          activeId = heading.id;\r\n          break;\r\n        }\r\n      }\r\n      this.activeTocId = activeId;\r\n    },\r\n    isMenuActive(path) {\r\n      if (this.$route.path === path) return true;\r\n      if ((this.$route.path === '/help' || this.$route.path === '/help/') && this.menu[0].items[0].path === path) return true;\r\n      return false;\r\n    },\r\n\r\n    checkScreenSize() {\r\n      this.windowWidth = window.innerWidth;\r\n      const wasMobile = this.isMobile;\r\n      this.isMobile = this.windowWidth <= 992;\r\n\r\n      // 如果从移动端切换到桌面端，显示所有面板\r\n      if (wasMobile && !this.isMobile) {\r\n        this.sidebarVisible = true;\r\n        this.tocVisible = true;\r\n      }\r\n      // 如果从桌面端切换到移动端，隐藏侧边栏\r\n      else if (!wasMobile && this.isMobile) {\r\n        this.sidebarVisible = false;\r\n        this.tocVisible = false;\r\n      }\r\n    },\r\n\r\n    handleResize() {\r\n      clearTimeout(this.resizeTimer);\r\n      this.resizeTimer = setTimeout(() => {\r\n        this.checkScreenSize();\r\n      }, 250);\r\n    },\r\n\r\n    toggleSidebar() {\r\n      this.sidebarVisible = !this.sidebarVisible;\r\n      if (this.sidebarVisible && this.tocVisible) {\r\n        this.tocVisible = false;\r\n      }\r\n    },\r\n\r\n    toggleToc() {\r\n      this.tocVisible = !this.tocVisible;\r\n      if (this.tocVisible && this.sidebarVisible) {\r\n        this.sidebarVisible = false;\r\n      }\r\n    },\r\n\r\n    closeSidebar() {\r\n      this.sidebarVisible = false;\r\n    },\r\n\r\n    closeToc() {\r\n      this.tocVisible = false;\r\n    },\r\n\r\n    closeAllPanels() {\r\n      this.sidebarVisible = false;\r\n      this.tocVisible = false;\r\n    },\r\n\r\n    onMenuItemClick() {\r\n      if (this.isMobile) {\r\n        this.sidebarVisible = false;\r\n      }\r\n    },\r\n\r\n    handleSidebarScroll() {\r\n      const sidebar = this.$refs.sidebar;\r\n      if (sidebar) {\r\n        this.sidebarScrollPosition = sidebar.scrollTop;\r\n        localStorage.setItem('helpSidebarScrollPosition', sidebar.scrollTop.toString());\r\n      }\r\n    },\r\n\r\n    restoreSidebarScrollPosition() {\r\n      const sidebar = this.$refs.sidebar;\r\n      if (sidebar && this.sidebarScrollPosition >= 0) {\r\n        requestAnimationFrame(() => {\r\n          sidebar.scrollTop = this.sidebarScrollPosition;\r\n        });\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.help-layout {\r\n  display: flex;\r\n  min-height: calc(100vh - 60px);\r\n  background-color: #fff;\r\n  height: calc(100vh - 60px);\r\n  position: relative;\r\n}\r\n\r\n/* 移动端控制按钮 */\r\n.mobile-controls {\r\n  position: fixed;\r\n  top: 70px;\r\n  left: 10px;\r\n  z-index: 1001;\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.sidebar-toggle,\r\n.toc-toggle {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n  padding: 8px 12px;\r\n  background: #1890ff;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\r\n}\r\n\r\n.sidebar-toggle:hover,\r\n.toc-toggle:hover {\r\n  background: #40a9ff;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.sidebar-toggle.active,\r\n.toc-toggle.active {\r\n  background: #096dd9;\r\n}\r\n\r\n.icon-menu,\r\n.icon-list {\r\n  font-size: 14px;\r\n  font-weight: bold;\r\n}\r\n\r\n.icon-menu::before {\r\n  content: '☰';\r\n}\r\n\r\n.icon-list::before {\r\n  content: '📋';\r\n}\r\n\r\n/* 遮罩层 */\r\n.overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  z-index: 999;\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n/* 侧边栏样式 */\r\n.sidebar {\r\n  width: 300px;\r\n  border-right: 1px solid #eee;\r\n  background-color: #f8f8f8;\r\n  overflow-y: auto;\r\n  height: 100%;\r\n  transition: transform 0.3s ease;\r\n  z-index: 1000;\r\n}\r\n\r\n.sidebar-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 15px 20px;\r\n  border-bottom: 1px solid #eee;\r\n}\r\n\r\n.sidebar-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.close-btn {\r\n  background: none;\r\n  border: none;\r\n  font-size: 18px;\r\n  cursor: pointer;\r\n  color: #666;\r\n  padding: 0;\r\n  width: 24px;\r\n  height: 24px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.close-btn:hover {\r\n  color: #1890ff;\r\n}\r\n\r\n.sidebar-menu { padding: 10px 0; }\r\n.menu-category { margin-bottom: 10px; margin-left: 30px; border-bottom: 1px solid #e7e7e7;}\r\n.category-title {\r\n  font-size: 16px;\r\n  font-weight: 900;\r\n  padding: 8px 20px;\r\n  color: #333;\r\n}\r\n.menu-list { list-style: none; padding: 0; margin: 0; }\r\n.menu-link {\r\n  display: block;\r\n  padding: 3px 20px 3px 30px;\r\n  color: #666;\r\n  text-decoration: none;\r\n  font-size: 15px;\r\n  transition: all 0.3s;\r\n}\r\n.menu-link:hover, .router-link-active {\r\n  color: #1890ff;\r\n  background-color: #e6f7ff;\r\n  border-right: 3px solid #1890ff;\r\n}\r\n/* 主内容区 */\r\n.main-content {\r\n  flex: 1;\r\n  padding: 30px 40px;\r\n  overflow-y: auto;\r\n  height: 100%;\r\n  transition: margin 0.3s ease;\r\n}\r\n\r\n.content-expanded {\r\n  margin-left: 0;\r\n  margin-right: 0;\r\n}\r\n\r\n.content-full {\r\n  margin-left: 0;\r\n  margin-right: 0;\r\n  padding: 20px;\r\n}\r\n\r\n/* 目录区域 */\r\n.toc {\r\n  width: 300px;\r\n  padding: 10px 20px 10px 20px;\r\n  border-left: 1px solid #eee;\r\n  background-color: #fff;\r\n  position: sticky;\r\n  top: 0;\r\n  max-height: 100vh;\r\n  overflow-y: auto;\r\n  transition: transform 0.3s ease;\r\n  z-index: 1000;\r\n}\r\n\r\n.toc-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 15px 0;\r\n  border-bottom: 1px solid #eee;\r\n}\r\n\r\n.toc-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n.toc-list { list-style: none; padding: 0; margin: 0; }\r\n.toc-item { margin-bottom: 0px; }\r\n.toc-item-h3 { padding-left: 15px; }\r\n.toc-link {\r\n  color: #666;\r\n  text-decoration: none;\r\n  font-size: 14px;\r\n  display: block;\r\n  transition: color 0.3s;\r\n}\r\n.toc-link:hover { color: #1890ff; }\r\n.toc-item.active > .toc-link {\r\n  color: #1890ff;\r\n  font-weight: bold;\r\n  background: #e6f7ff;\r\n  border-radius: 3px;\r\n}\r\n.menu-link-active {\r\n  color: #1890ff;\r\n  background-color: #e6f7ff;\r\n  border-right: 3px solid #1890ff;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 992px) {\r\n  .help-layout {\r\n    position: relative;\r\n  }\r\n\r\n  .mobile-controls {\r\n    display: flex;\r\n  }\r\n\r\n  .sidebar {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    height: 100vh;\r\n    z-index: 1000;\r\n    transform: translateX(-100%);\r\n  }\r\n\r\n  .sidebar-visible {\r\n    transform: translateX(0);\r\n  }\r\n\r\n  .sidebar-hidden {\r\n    transform: translateX(-100%);\r\n  }\r\n\r\n  .main-content {\r\n    width: 100%;\r\n    padding: 80px 20px 20px;\r\n  }\r\n\r\n  .toc {\r\n    position: fixed;\r\n    top: 0;\r\n    right: 0;\r\n    height: 100vh;\r\n    z-index: 1000;\r\n    transform: translateX(100%);\r\n  }\r\n\r\n  .toc-visible {\r\n    transform: translateX(0);\r\n  }\r\n\r\n  .toc-hidden {\r\n    transform: translateX(100%);\r\n  }\r\n}\r\n\r\n@media (min-width: 993px) {\r\n  .mobile-controls {\r\n    display: none;\r\n  }\r\n\r\n  .overlay {\r\n    display: none;\r\n  }\r\n\r\n  .sidebar-header,\r\n  .toc-header {\r\n    display: none;\r\n  }\r\n\r\n  .sidebar,\r\n  .toc {\r\n    position: static;\r\n    transform: none;\r\n  }\r\n}\r\n\r\n/* 平板适配 */\r\n@media (max-width: 1200px) and (min-width: 993px) {\r\n  .sidebar {\r\n    width: 250px;\r\n  }\r\n\r\n  .toc {\r\n    width: 250px;\r\n  }\r\n\r\n  .main-content {\r\n    padding: 20px 30px;\r\n  }\r\n}\r\n\r\n/* 小屏幕优化 */\r\n@media (max-width: 576px) {\r\n  .sidebar,\r\n  .toc {\r\n    width: 280px;\r\n  }\r\n\r\n  .main-content {\r\n    padding: 80px 15px 15px;\r\n  }\r\n\r\n  .mobile-controls {\r\n    left: 5px;\r\n    top: 65px;\r\n  }\r\n\r\n  .sidebar-toggle,\r\n  .toc-toggle {\r\n    padding: 6px 10px;\r\n    font-size: 11px;\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./HelpView.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./HelpView.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./HelpView.vue?vue&type=template&id=618f8321&scoped=true&\"\nimport script from \"./HelpView.vue?vue&type=script&lang=js&\"\nexport * from \"./HelpView.vue?vue&type=script&lang=js&\"\nimport style0 from \"./HelpView.vue?vue&type=style&index=0&id=618f8321&prod&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"618f8321\",\n  null\n  \n)\n\nexport default component.exports", "var map = {\n\t\"./Flux.1-kontext-dev.md\": [\n\t\t6141,\n\t\t4594\n\t],\n\t\"./ace-step.md\": [\n\t\t4670,\n\t\t3168\n\t],\n\t\"./cloud-storage.md\": [\n\t\t412,\n\t\t1912\n\t],\n\t\"./code-former.md\": [\n\t\t895,\n\t\t5061\n\t],\n\t\"./cosy-voice.md\": [\n\t\t7788,\n\t\t6202\n\t],\n\t\"./daily-hot.md\": [\n\t\t8723,\n\t\t9198\n\t],\n\t\"./deploy-serverless.md\": [\n\t\t7838,\n\t\t2719\n\t],\n\t\"./docker-tutorial.md\": [\n\t\t354,\n\t\t3469\n\t],\n\t\"./flux-dev.md\": [\n\t\t328,\n\t\t4956\n\t],\n\t\"./frame-pack.md\": [\n\t\t1493,\n\t\t4675\n\t],\n\t\"./funasr.md\": [\n\t\t7631,\n\t\t1447\n\t],\n\t\"./gpu-selection.md\": [\n\t\t2696,\n\t\t5838\n\t],\n\t\"./health-check.md\": [\n\t\t1063,\n\t\t2261\n\t],\n\t\"./hivision-photos.md\": [\n\t\t2478,\n\t\t9802\n\t],\n\t\"./hunyuan-portrait.md\": [\n\t\t3062,\n\t\t370\n\t],\n\t\"./jupyter-lab.md\": [\n\t\t2395,\n\t\t166\n\t],\n\t\"./k8s-yaml-import.md\": [\n\t\t5305,\n\t\t7231\n\t],\n\t\"./mineru.md\": [\n\t\t4633,\n\t\t2576\n\t],\n\t\"./minicpm4.md\": [\n\t\t5936,\n\t\t2091\n\t],\n\t\"./mirror.md\": [\n\t\t1628,\n\t\t371\n\t],\n\t\"./ollama-qwen-webui.md\": [\n\t\t9961,\n\t\t2543\n\t],\n\t\"./ollama-qwen.md\": [\n\t\t6585,\n\t\t8991\n\t],\n\t\"./personal-certification.md\": [\n\t\t2733,\n\t\t3959\n\t],\n\t\"./privacy-policy.md\": [\n\t\t3751,\n\t\t8295\n\t],\n\t\"./quick-start.md\": [\n\t\t1273,\n\t\t8265\n\t],\n\t\"./qustion.md\": [\n\t\t4226,\n\t\t7083\n\t],\n\t\"./register-login.md\": [\n\t\t4493,\n\t\t32\n\t],\n\t\"./stable-diffusion1.5.md\": [\n\t\t7507,\n\t\t6193\n\t],\n\t\"./stable-diffusion2.1.md\": [\n\t\t1362,\n\t\t8449\n\t],\n\t\"./stable-diffusion3.5-large.md\": [\n\t\t9045,\n\t\t9240\n\t],\n\t\"./summary.md\": [\n\t\t1782,\n\t\t762\n\t],\n\t\"./user-agreement.md\": [\n\t\t7553,\n\t\t8303\n\t],\n\t\"./whisper.md\": [\n\t\t3797,\n\t\t5042\n\t]\n};\nfunction webpackAsyncContext(req) {\n\tif(!__webpack_require__.o(map, req)) {\n\t\treturn Promise.resolve().then(function() {\n\t\t\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\t\t\te.code = 'MODULE_NOT_FOUND';\n\t\t\tthrow e;\n\t\t});\n\t}\n\n\tvar ids = map[req], id = ids[0];\n\treturn __webpack_require__.e(ids[1]).then(function() {\n\t\treturn __webpack_require__(id);\n\t});\n}\nwebpackAsyncContext.keys = function() { return Object.keys(map); };\nwebpackAsyncContext.id = 817;\nmodule.exports = webpackAsyncContext;"], "names": ["render", "_vm", "this", "_c", "_self", "staticClass", "isMobile", "class", "sidebarVisible", "on", "toggleSidebar", "_v", "tocVisible", "toggleToc", "_e", "closeAllPanels", "ref", "closeSidebar", "_l", "menu", "category", "key", "title", "_s", "items", "item", "path", "isMenuActive", "attrs", "onMenuItemClick", "name", "currentDoc", "getPrevPage", "getNextPage", "buildToc", "closeToc", "toc", "id", "level", "activeTocId", "$event", "preventDefault", "scrollToAnchor", "text", "staticRenderFns", "loading", "error", "domProps", "markdownContent", "prevPage", "nextPage", "props", "doc", "String", "Object", "data", "watch", "immediate", "module", "default", "$nextTick", "processContent", "$emit", "e", "message", "methods", "slugify", "slug", "toLowerCase", "replace", "test", "Math", "random", "toString", "substring", "$refs", "contentRef", "headings", "querySelectorAll", "for<PERSON>ach", "heading", "textContent", "codeBlocks", "block", "classList", "add", "links", "link", "addEventListener", "getAttribute", "targetElement", "document", "getElementById", "scrollIntoView", "behavior", "component", "components", "HelpContent", "chatAi", "<PERSON><PERSON>", "allPages", "isAnchorClicking", "windowWidth", "sidebarScrollPosition", "computed", "$route", "params", "currentPath", "created", "flattenPages", "checkScreenSize", "mounted", "updatePageTitle", "savedScrollPosition", "localStorage", "getItem", "parseInt", "mainContent", "handleContentScroll", "sidebar", "handleSidebarScroll", "restoreSidebarScrollPosition", "window", "handleResize", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "setTimeout", "Date", "now", "push", "currentIndex", "findIndex", "page", "length", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "querySelector", "Array", "from", "map", "h", "existingId", "tagName", "currentPageTitle", "OFFSET", "element", "offsetTop", "scrollTop", "activeId", "i", "innerWidth", "was<PERSON><PERSON><PERSON>", "clearTimeout", "resizeTimer", "setItem", "requestAnimationFrame", "webpackAsyncContext", "req", "__webpack_require__", "o", "Promise", "resolve", "then", "Error", "code", "ids", "keys", "exports"], "sourceRoot": ""}