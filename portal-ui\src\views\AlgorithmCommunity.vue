<template>
  <Layout>
    <div class="coming-soon-container">
      <h1 class="title">敬请期待</h1>
      <p class="subtitle">我们正在努力建设中，敬请期待更多精彩内容！</p>
    </div>
  </Layout>
</template>

<script>
import Layout from "@/components/common/Layout-header";

export default {
  name: "AlgorithmCommunity",
  components: { Layout }
};
</script>

<style scoped>
.coming-soon-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 70vh;
  text-align: center;
  background-color: #f9fafb;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 40px;
}

.title {
  font-size: 3rem;
  color: #1f2937;
  margin-bottom: 16px;
}

.subtitle {
  font-size: 1.2rem;
  color: #4b5563;
}
</style>
