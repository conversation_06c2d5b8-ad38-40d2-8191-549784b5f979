"use strict";(self["webpackChunkportal_ui"]=self["webpackChunkportal_ui"]||[]).push([[2091],{5936:function(o,t,p){p.r(t);var n=new URL(p(1404),p.b),i=new URL(p(4117),p.b),l=new URL(p(1480),p.b),e=new URL(p(2266),p.b),r=new URL(p(5727),p.b),c=new URL(p(1864),p.b),s=new URL(p(2786),p.b),f=new URL(p(5308),p.b),m=new URL(p(9386),p.b),g='<h1 id="容器化部署-minicpm4">容器化部署 minicpm4</h1> <p><font style="color:#020817">本指南全面介绍了在天工开物平台上部署 minicpm4 大语言模型 API 服务的完整解决方案。该方案不仅提供了详实的部署流程，还提供了如何制作镜像的方案。</font></p> <blockquote> <p>此镜像提供了标准化的API 接口，让您能够便捷地通过 <strong>API 调用方式</strong>访问和使用所有功能。目前还不支持 <strong>Web UI</strong> 方式使用服务，需要您本地启动适配的 Web UI。</p> </blockquote> <h2 id="1部署服务"><strong>1.部署服务</strong></h2> <p><font style="color:#020817">点击这里 <a href="https://www.tiangongkaiwu.top/portal/#/console"><font style="color:#06c">部署服务</font></a> ，登录后根据页面提示进行部署。选择合适的设备，在服务配置中输入镜像地址，部署服务，完成！</font></p> <h3 id="11-访问天工开物控制台，点击新增部署">1.1 访问天工开物<a href="https://www.tiangongkaiwu.top/portal/#/console">控制台</a>，点击新增部署</h3> <p><img src="'+n+'" alt=""></p> <h3 id="12-选择设备">1.2 选择设备</h3> <p><font style="color:#020817">基于自身需要进行配置，参考配置为单卡 4090 和 1 个节点（初次使用进行调试）。</font></p> <p><img src="'+i+'" alt=""></p> <h3 id="13-选择相应预制镜像">1.3 选择相应预制镜像</h3> <p><font style="color:#020817">选择 <strong>minicpm4 预制镜像</strong>，点击部署服务。</font></p> <p><img src="'+l+'" alt=""></p> <h3 id="14-耐心等待节点拉取镜像并启动">1.4 耐心等待节点拉取镜像并启动</h3> <p><img src="'+e+'" alt=""></p> <h3 id="15-部署完成">1.5 部署完成</h3> <p><font style="color:#020817">在部署完成页面，能看到一个公开访问链接。这个链接就是 Ollama 服务的 API 访问地址。</font></p> <p><font style="color:#020817">将这个 API 地址复制下来，就可以在任何支持 Ollama 协议的应用程序中使用。</font></p> <p><img src="'+r+'" alt=""></p> <p><font style="color:#020817">在“常规”面板里可以看到公开访问的地址，此地址即为 Ollama 服务的 API 地址。 请耐心一点~~ 模型镜像会比较大，<strong>minicpm4 镜像本身 20G+，打包之后大约 40G+，</strong> 拉取镜像会需要一段时间。</font></p> <h3 id="16-验证一下">1.6 验证一下</h3> <p><font style="color:#020817">访问复制的链接，{快捷访问的地址} /api/tags，将链接复制到浏览器，就可以看到以下内容，说明模型已经部署并运行了。</font></p> <p><img src="'+c+'" alt=""></p> <p><font style="color:#020817">如果需要在其他兼容 Ollama 的客户端使用时，需要提供的参数如下：</font></p> <ul> <li>访问地址</li> </ul> <p><font style="color:#020817">常规 -&gt; 快捷访问中 11434 对应的链接。有的会需要在链接后面加上 /api</font></p> <ul> <li>ModelId</li> </ul> <p><strong>minicpm4-8b</strong></p> <ul> <li>上下文长度</li> </ul> <p>32k</p> <ul> <li>模型建议的其他参数（非必须，可以根据需要自行修改）</li> </ul> <pre><code class="language-dockerfile">{\n    &quot;repeat_penalty&quot;: 1,\n    &quot;temperature&quot;: 0.6,\n    &quot;top_k&quot;: 20,\n    &quot;top_p&quot;: 0.95\n}\n</code></pre> <p><font style="color:#020817">使用第三方客户端时，可以按照下图填写内容</font></p> <p><img src="'+s+'" alt=""></p> <h2 id="2模型速度测试">2.模型速度测试</h2> <p><font style="color:#020817">minicpm4 部署完成了，速度怎么样呢？点击 <a href="https://lmspeed.net/zh-CN"><font style="color:#06c">LM Speed</font></a> 测试一下速度吧~~~</font></p> <p><font style="color:#020817">如果 LM Speed 无法访问，多刷新几次就可以了😊</font></p> <p><strong>基础 URL 后面记得加 /v1</strong></p> <p><img src="'+f+'" alt=""><img src="'+m+'" alt=""></p> <p><br><br> <font style="color:#b2b2b2">最后更新于: 2025/7/3 15:25</font></p> ';t["default"]=g},1480:function(o,t,p){o.exports=p.p+"img/minicpm3.e93105d2.png"},2266:function(o,t,p){o.exports=p.p+"img/minicpm4.4374d331.png"},5727:function(o,t,p){o.exports=p.p+"img/minicpm5.e866d085.png"},1864:function(o,t,p){o.exports=p.p+"img/minicpm6.71d398c3.png"},2786:function(o,t,p){o.exports=p.p+"img/minicpm7.65611133.png"},5308:function(o,t,p){o.exports=p.p+"img/minicpm8.5f5d8940.png"},9386:function(o,t,p){o.exports=p.p+"img/minicpm9.5ddf9c30.png"},1404:function(o,t,p){o.exports=p.p+"img/universal1.9a1b3f4b.png"},4117:function(o,t,p){o.exports=p.p+"img/universal2.4306636e.png"}}]);
//# sourceMappingURL=docs18.97d90bf2.js.map