# <font style="color:rgb(2, 8, 23);">云存储加速</font>
## **<font style="color:rgb(2, 8, 23);">1. 功能简介</font>**
<font style="color:rgb(2, 8, 23);">本功能支持用户将 S3 兼容云存储（如阿里云 OSS、腾讯云 COS、AWS S3 等）挂载到平台，实现模型或数据的高效访问和加速。通过 JuiceFS 缓存机制，大幅提升模型加载和数据读取速度。</font>

## **<font style="color:rgb(2, 8, 23);">2. 功能效果</font>**
> <font style="color:rgb(103, 103, 108);">冷启动定义：模型服务从零实例状态（缩放到 0）接收请求到准备处理第一个请求的时间间隔，是影响部署响应能力、服务等级协议（SLA）和成本控制的关键因素。为了优化冷启动，我们将介绍以下策略：云存储加速，它通过提前将 S3 数据缓存到本地，从而提高性能。</font>
>

**<font style="color:rgb(2, 8, 23);">性能提升显著</font>**

<font style="color:rgb(2, 8, 23);">冷启动带来的挑战：</font>

+ <font style="color:rgb(2, 8, 23);">用户体验：首次请求响应时间长，影响用户满意度</font>
+ <font style="color:rgb(2, 8, 23);">成本控制：频繁冷启动导致资源浪费和成本增加</font>
+ <font style="color:rgb(2, 8, 23);">服务可用性：冷启动时间过长可能导致服务超时</font>

<font style="color:rgb(2, 8, 23);">S3 存储加速的优化策略：</font>

1. <font style="color:rgb(2, 8, 23);">预取机制：利用 JuiceFS 预取功能，后台线程提前下载模型权重和数据</font>
2. <font style="color:rgb(2, 8, 23);">分布式缓存：将模型权重缓存在分布式文件系统中，避免重复下载</font>
3. <font style="color:rgb(2, 8, 23);">智能预热：对高频访问的模型进行预热处理</font>

| <font style="color:rgb(2, 8, 23);">部署方式</font> | <font style="color:rgb(2, 8, 23);">冷启动时间</font> | <font style="color:rgb(2, 8, 23);">性能提升</font> |
| --- | --- | --- |
| <font style="color:rgb(2, 8, 23);">传统方式</font> | <font style="color:rgb(2, 8, 23);">数分钟（如 Stable Diffusion XL）</font> | |
| <font style="color:rgb(2, 8, 23);">S3 加速</font> | <font style="color:rgb(2, 8, 23);">10 秒以内</font> | <font style="color:rgb(2, 8, 23);">提升 90% 以上</font> |


**<font style="color:rgb(2, 8, 23);">资源利用优化</font>**

+ <font style="color:rgb(2, 8, 23);">读取加速：本地缓存机制提供接近本地磁盘的读取速度</font>
+ <font style="color:rgb(2, 8, 23);">并行下载：多线程并行下载，充分利用网络带宽</font>
+ <font style="color:rgb(2, 8, 23);">智能缓存：自动管理缓存空间，优先缓存高频访问数据</font>
+ <font style="color:rgb(2, 8, 23);">按需加载：仅在首次访问时从云端拉取数据，避免重复下载</font>
+ <font style="color:rgb(2, 8, 23);">空间复用：多个任务可共享同一份缓存数据，节省存储空间</font>

## **<font style="color:rgb(2, 8, 23);">3. 应用场景</font>**
### **<font style="color:rgb(2, 8, 23);">3.1. 模型版本管理与无停机更新</font>**
<font style="color:rgb(2, 8, 23);">在生产环境中，AI 模型需要频繁更新迭代，传统方式需要重新构建和发布 Docker 镜像，过程繁琐且耗时。</font>

<font style="color:rgb(2, 8, 23);">S3 存储加速解决方案：</font>

+ <font style="color:rgb(2, 8, 23);">解耦模型与镜像：将模型文件存储在 S3 中，Docker 镜像只包含运行环境，实现模型与代码的分离</font>
+ <font style="color:rgb(2, 8, 23);">快速模型切换：通过更新 S3 中的模型文件，无需重建镜像即可完成模型更新</font>
+ <font style="color:rgb(2, 8, 23);">A/B 测试：可同时挂载多个模型版本，方便进行对比测试</font>

<font style="color:rgb(2, 8, 23);">实际效果：</font>

+ <font style="color:rgb(2, 8, 23);">模型更新时间：从数小时缩短至几分钟</font>
+ <font style="color:rgb(2, 8, 23);">运维效率：提升 80% 以上</font>

### **<font style="color:rgb(2, 8, 23);">3.2. 弹性扩容与负载均衡</font>**
<font style="color:rgb(2, 8, 23);">在业务高峰期或突发流量场景下，需要快速扩容计算节点以应对负载增长。</font>

<font style="color:rgb(2, 8, 23);">S3 存储加速解决方案：</font>

+ <font style="color:rgb(2, 8, 23);">快速节点启动：新扩容的节点可直接使用缓存的模型数据，避免重复下载</font>
+ <font style="color:rgb(2, 8, 23);">智能缓存预热：新节点启动时自动预热常用模型，减少首次访问延迟</font>
+ <font style="color:rgb(2, 8, 23);">跨区域部署：支持在不同区域快速部署节点，提升服务覆盖范围</font>

<font style="color:rgb(2, 8, 23);">实际效果：</font>

+ <font style="color:rgb(2, 8, 23);">扩容时间：从传统的 10-30 分钟缩短至 1-3 分钟</font>
+ <font style="color:rgb(2, 8, 23);">资源利用率：提升 60% 以上</font>
+ <font style="color:rgb(2, 8, 23);">服务稳定性：显著提升，支持平滑扩缩容</font>

## **<font style="color:rgb(2, 8, 23);">4. 操作流程一览</font>**
![](./imgs/cloud1.png)

## **<font style="color:rgb(2, 8, 23);">5. 操作流程</font>**
### **<font style="color:rgb(2, 8, 23);">5.1. 存储加速配置</font>**
1. <font style="color:rgb(2, 8, 23);">进入存储加速管理页面在左侧菜单点击"云存储加速"，进入管理页面。</font>
2. <font style="color:rgb(2, 8, 23);">新增 S3 存储配置点击"新增云存储配置"按钮，选择云服务商，填写以下信息：</font>
+ <font style="color:rgb(2, 8, 23);">配置名称</font>
+ <font style="color:rgb(2, 8, 23);">AccessKey、SecretKey</font>
+ <font style="color:rgb(2, 8, 23);">Endpoint</font>
+ <font style="color:rgb(2, 8, 23);">Bucket 名称</font>
+ <font style="color:rgb(2, 8, 23);">地域</font>
+ <font style="color:rgb(2, 8, 23);">需要加速的目录</font>
3. <font style="color:rgb(2, 8, 23);">保存配置系统会自动检测配置是否可用。校验通过后方可保存。在列表中找到该配置，点击"开始加速"，选择加速区域，系统将自动初始化 JuiceFS 文件系统。并进行提前预热（这需要从云端把文件下载到本地，需要等待一定时间）</font>

---

### **<font style="color:rgb(2, 8, 23);">5.2. 任务发布时挂载 S3 存储</font>**
1. <font style="color:rgb(2, 8, 23);">选择 S3 存储桶在任务发布页面的"存储配置"区域，选择已配置并加速的 S3 存储桶（即使还在回源中，仍然可以提前挂载，不过挂载后仍然需要等待从云端拉取文件到本地）。</font>
2. <font style="color:rgb(2, 8, 23);">指定容器内挂载路径为每个选定的 S3 存储桶填写容器内的挂载路径（如 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">/mnt/my_model_data</font><font style="color:rgb(2, 8, 23);">），路径需以 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">/</font><font style="color:#2F8EF4;"> </font><font style="color:rgb(2, 8, 23);">开头。</font>
3. <font style="color:rgb(2, 8, 23);">管理已选存储和路径可随时删除或修改已选存储桶及其挂载路径。</font>
4. <font style="color:rgb(2, 8, 23);">提交任务配置完成后提交任务，容器启动时会自动挂载所选 S3 存储。</font>
5. <font style="color:rgb(2, 8, 23);">校验挂载目录等待容器启动完成后，可进入容器并验证挂载。查看挂载目录下的文件。</font>

---

### **<font style="color:rgb(2, 8, 23);">5.3. 存储加速释放与管理</font>**
+ <font style="color:rgb(2, 8, 23);">手动释放：在存储加速管理页面可手动释放缓存，释放后 JuiceFS 文件系统和缓存会被清除，但配置仍保留。</font>
+ <font style="color:rgb(2, 8, 23);">自动释放：若 15 天内无任务挂载该存储，加速服务会自动释放，配置数据保留，可随时重新激活。</font>

---

## **<font style="color:rgb(2, 8, 23);">6. 状态说明</font>**
| <font style="color:rgb(2, 8, 23);">事件名称</font> | <font style="color:rgb(2, 8, 23);">事件描述</font> | <font style="color:rgb(2, 8, 23);">状态变化</font> |
| --- | --- | --- |
| <font style="color:rgb(2, 8, 23);">创建配置</font> | <font style="color:rgb(2, 8, 23);">用户创建新的 S3 存储配置</font> | |
| <font style="color:rgb(2, 8, 23);">编辑配置</font> | <font style="color:rgb(2, 8, 23);">用户修改现有 S3 存储配置</font> | |
| <font style="color:rgb(2, 8, 23);">连接校验</font> | <font style="color:rgb(2, 8, 23);">系统验证 S3 连接信息</font> | |
| <font style="color:rgb(2, 8, 23);">开始加速</font> | <font style="color:rgb(2, 8, 23);">系统建立 JuiceFS 文件系统、开始回源</font> | <font style="color:rgb(2, 8, 23);">未加速 → 回源中</font> |
| <font style="color:rgb(2, 8, 23);">回源完成</font> | <font style="color:rgb(2, 8, 23);">系统完成数据回源同步</font> | <font style="color:rgb(2, 8, 23);">回源中 → 加速中</font> |
| <font style="color:rgb(2, 8, 23);">手动释放</font> | <font style="color:rgb(2, 8, 23);">用户手动释放存储缓存</font> | <font style="color:rgb(2, 8, 23);">加速中 → 未加速</font> |
| <font style="color:rgb(2, 8, 23);">开始倒计时</font> | <font style="color:rgb(2, 8, 23);">存储无任务挂载超过阈值</font> | <font style="color:rgb(2, 8, 23);">加速中 → 即将释放</font> |
| <font style="color:rgb(2, 8, 23);">倒计时结束</font> | <font style="color:rgb(2, 8, 23);">倒计时达到 15 天</font> | <font style="color:rgb(2, 8, 23);">即将释放 → 未加速</font> |
| <font style="color:rgb(2, 8, 23);">重新挂载</font> | <font style="color:rgb(2, 8, 23);">任务重新挂载存储</font> | <font style="color:rgb(2, 8, 23);">即将释放 → 回源中</font> |


---

## **<font style="color:rgb(2, 8, 23);">7. 常见操作</font>**
| <font style="color:rgb(2, 8, 23);">状态</font> | <font style="color:rgb(2, 8, 23);">可执行操作</font> |
| --- | --- |
| <font style="color:rgb(2, 8, 23);">加速中</font> | <font style="color:rgb(2, 8, 23);">释放存储缓存</font> |
| <font style="color:rgb(2, 8, 23);">未加速</font> | <font style="color:rgb(2, 8, 23);">编辑配置、开始同步、删除配置</font> |
| <font style="color:rgb(2, 8, 23);">即将释放</font> | <font style="color:rgb(2, 8, 23);">释放存储缓存</font> |
| <font style="color:rgb(2, 8, 23);">同步中</font> | <font style="color:rgb(2, 8, 23);">无</font> |


---

## **<font style="color:rgb(2, 8, 23);">8. 注意事项</font>**
+ <font style="color:rgb(2, 8, 23);">存储内容更新需手动点击"回源上游"按钮，不会自动同步。</font>
+ <font style="color:rgb(2, 8, 23);">存储加速仅支持只读访问，适合模型或数据文件的高效读取。</font>
+ <font style="color:rgb(2, 8, 23);">释放后的配置仍保留，可随时重新激活。</font>

**<font style="color:rgb(103, 103, 108);"></font>**

<br><br>
<font style="color:rgba(178,178,178,1);">最后更新于: 2025/6/19 09:36</font>

