"use strict";(self["webpackChunkportal_ui"]=self["webpackChunkportal_ui"]||[]).push([[5042],{3797:function(o,t,n){n.r(t);var l=new URL(n(1404),n.b),r=new URL(n(4117),n.b),e=new URL(n(8957),n.b),f=new URL(n(8471),n.b),s=new URL(n(8159),n.b),c=new URL(n(1586),n.b),a=new URL(n(9043),n.b),i=new URL(n(7212),n.b),g=new URL(n(785),n.b),y=new URL(n(3756),n.b),p=new URL(n(7367),n.b),d=new URL(n(9844),n.b),h=new URL(n(5942),n.b),u='<h1 id="容器化部署-whisper"><font style="color:#020817">容器化部署 Whisper</font></h1> <p><font style="color:#020817">本指南详细阐述了在天工开物上部署 openai 开源的语音识别 Whisper 项目的解决方案。</font></p> <h2 id="1部署步骤"><font style="color:#020817">1.部署步骤</font></h2> <p><font style="color:#020817">我们在</font><strong><font style="color:#020817">服务配置——预制镜像</font></strong><font style="color:#020817">中提供了预构建的 Whisper 容器映像，旨在满足一般要求。您可以选择直接在天工开物上运行这些容器以执行任务。或者，您还可以通过使用我们资源管理——镜像仓库所提供的免费 Docker 镜像仓库服务，来方便管理您自身的 Docker 镜像。</font></p> <p><font style="color:#020817">下面是部署步骤：</font></p> <h3 id="11-访问天工开物控制台，点击新增部署。"><font style="color:#020817">1.1 访问</font><a href="https://tiangongkaiwu.top/#/console">天工开物控制台</a><font style="color:#020817">，点击新增部署。</font></h3> <p><img src="'+l+'" alt=""></p> <h3 id="12-基于自身需要进行配置，参考配置为单卡-4090（初次使用进行调试）。"><font style="color:#020817">1.2 基于自身需要进行配置，参考配置为单卡 4090（初次使用进行调试）。</font></h3> <p><img src="'+r+'" alt=""></p> <h3 id="13-选择服务配置中的预制镜像-选择我们打包好的-whisper-镜像快速开启服务"><font style="color:#020817">1.3 选择服务配置中的预制镜像 选择我们打包好的 Whisper 镜像快速开启服务</font></h3> <p><img src="'+e+'" alt=""></p> <h3 id="14-点击部署服务，耐心等待节点拉取镜像并启动-第一次访问会下载模型，所以需要稍等一会"><font style="color:#020817">1.4 点击部署服务，耐心等待节点拉取镜像并启动 第一次访问会下载模型，所以需要稍等一会</font></h3> <p><img src="'+f+'" alt=""></p> <h3 id="15-节点启动后，你所在公开访问中看到的内容可能如下："><font style="color:#020817">1.5 节点启动后，你所在“公开访问”中看到的内容可能如下：</font></h3> <p><img src="'+s+'" alt=""></p> <p><font style="color:#020817">通过查看节点列表——查看详情 通过容器信息</font><strong><font style="color:#020817">确定模型加载完成</font></strong><font style="color:#020817">即可正常进入服务</font></p> <p><img src="'+c+'" alt=""></p> <h3 id="16-我们可以点击9000端口的链接，测试-whisper-部署情况，系统会自动分配一个可公网访问的域名，接下来我们即可自由地使用-whisper-的服务"><font style="color:#020817">1.6 我们可以点击“9000”端口的链接，测试 whisper 部署情况，系统会自动分配一个可公网访问的域名，接下来我们即可自由地使用 whisper 的服务</font></h3> <h2 id="2使用教程"><font style="color:#020817">2.使用教程</font></h2> <p><font style="color:#020817">这个项目提供了 2 个 http 接口：</font></p> <p><font style="color:#020817">1./asr：语音识别接口，上传语音或者视频文件，输出文字。</font></p> <p><font style="color:#020817">2./detect-language：语言检测接口，上传语音或者视频文件，输出语言。</font></p> <h3 id="生产环境"><font style="color:#020817">生产环境</font></h3> <p><font style="color:#020817">以下是针对生产环境使用的说明，包含完整的请求命令、响应时间预估及返回结果示例：</font></p> <h4 id="211-语音识别接口-asr"><strong><font style="color:#020817">2.1.1 语音识别接口 (</font><strong><strong><font style="color:#020817">/asr</font></strong></strong><font style="color:#020817">)</font></strong></h4> <p><font style="color:#020817">请求命令（CURL）</font></p> <pre><code class="language-plain"># 英文音频/视频转文字  \ncurl -X POST &quot;http://api.example.com/asr?language=en&quot; \\  \n     -H &quot;Authorization: Bearer YOUR_API_KEY&quot; \\  # 若需认证  \n     -H &quot;Content-Type: multipart/form-data&quot; \\  \n     -F &quot;file=@audio_en.mp3&quot;  \n\n# 中文音频/视频转文字  \ncurl -X POST &quot;http://api.example.com/asr?language=zh&quot; \\  \n     -H &quot;Content-Type: multipart/form-data&quot; \\  \n     -F &quot;file=@video_zh.mp4&quot;\n</code></pre> <p><strong><font style="color:#020817">参数说明</font></strong></p> <ul> <li><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)">language</font><font style="color:#020817">：必填，指定音频语言（</font><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)">en</font><font style="color:#020817">为英文，</font><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)">zh</font><font style="color:#020817">为中文）。</font></li> <li><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)">file</font><font style="color:#020817">：必填，支持格式：</font><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)">.mp3</font><font style="color:#020817">, </font><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)">.wav</font><font style="color:#020817">, </font><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)">.mp4</font><font style="color:#020817">, </font><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)">.mov</font><font style="color:#020817">等。</font></li> </ul> <p><strong><font style="color:#020817">响应时间参考</font></strong></p> <table> <thead> <tr> <th><font style="color:#020817">文件大小</font></th> <th><font style="color:#020817">预估响应时间</font></th> </tr> </thead> <tbody><tr> <td><font style="color:#020817">&lt;10MB</font></td> <td><font style="color:#020817">3-8 秒</font></td> </tr> <tr> <td><font style="color:#020817">10MB-50MB</font></td> <td><font style="color:#020817">10-25 秒</font></td> </tr> <tr> <td><font style="color:#020817">&gt;50MB</font></td> <td><font style="color:#020817">异步处理（返回任务 ID）</font></td> </tr> </tbody></table> <p><strong><font style="color:#67676c">注</font></strong><font style="color:#67676c">：大文件建议分片上传或使用异步接口，同步请求超时时间默认为 30 秒。</font></p> <p><strong><font style="color:#020817">返回结果示例</font></strong></p> <pre><code class="language-plain">// 成功  \n{  \n  &quot;status&quot;: &quot;success&quot;,  \n  &quot;text&quot;: &quot;This is the transcribed text from your audio file.&quot;  \n}  \n\n// 失败（如语言参数错误）  \n{  \n  &quot;status&quot;: &quot;error&quot;,  \n  &quot;code&quot;: 400,  \n  &quot;message&quot;: &quot;Invalid language parameter. Supported: en, zh.&quot;  \n}\n</code></pre> <hr> <h4 id="212-语言检测接口-detect-language"><strong><font style="color:#020817">2.1.2 语言检测接口 (</font><strong><strong><font style="color:#020817">/detect-language</font></strong></strong><font style="color:#020817">)</font></strong></h4> <p><font style="color:#020817">请求命令（CURL）</font></p> <pre><code class="language-plain">curl -X POST &quot;http://api.example.com/detect-language&quot; \\  \n     -H &quot;Content-Type: multipart/form-data&quot; \\  \n     -F &quot;file=@unknown_audio.wav&quot;\n</code></pre> <p><font style="color:#020817">参数说明</font></p> <ul> <li><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)">file</font><font style="color:#020817">：必填，支持格式同上。</font></li> </ul> <p><font style="color:#020817">响应时间参考</font></p> <table> <thead> <tr> <th><font style="color:#020817">文件大小</font></th> <th><font style="color:#020817">预估响应时间</font></th> </tr> </thead> <tbody><tr> <td><font style="color:#020817">&lt;10MB</font></td> <td><font style="color:#020817">2-5 秒</font></td> </tr> <tr> <td><font style="color:#020817">10MB-50MB</font></td> <td><font style="color:#020817">5-10 秒</font></td> </tr> <tr> <td><font style="color:#020817">&gt;50MB</font></td> <td><font style="color:#020817">仅分析前 1 分钟内容</font></td> </tr> </tbody></table> <p><strong><font style="color:#67676c">注</font></strong><font style="color:#67676c">：大文件默认截取前 1 分钟内容进行检测以加速响应。</font></p> <p><strong><font style="color:#020817">返回结果示例</font></strong></p> <pre><code class="language-plain">// 成功  \n{  \n  &quot;status&quot;: &quot;success&quot;,  \n  &quot;language&quot;: &quot;fr&quot;,  \n  &quot;confidence&quot;: 0.92  // 检测置信度（0-1）  \n}  \n\n// 失败（如文件格式不支持）  \n{  \n  &quot;status&quot;: &quot;error&quot;,  \n  &quot;code&quot;: 415,  \n  &quot;message&quot;: &quot;Unsupported media type. Allowed: audio/*, video/*.&quot;  \n}\n</code></pre> <hr> <p><strong><font style="color:#020817">关键说明</font></strong></p> <ol> <li><strong><font style="color:#020817">认证</font></strong><font style="color:#020817">：若需 API 密钥，需在 Header 中添加 </font><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)">Authorization: Bearer YOUR_API_KEY</font><font style="color:#020817">。</font></li> <li><strong><font style="color:#020817">异步处理</font></strong><font style="color:#020817">：</font><ul> <li><font style="color:#020817">大文件可调用 </font><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)">/asr/async?language=en</font><font style="color:#020817"> 提交任务，返回 </font><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)">{&quot;task_id&quot;: &quot;123&quot;}</font><font style="color:#020817">。</font></li> <li><font style="color:#020817">通过 </font><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)">/tasks/123</font><font style="color:#020817"> 查询结果。</font></li> </ul> </li> <li><strong><font style="color:#020817">限速</font></strong><font style="color:#020817">：默认限制 10 请求/分钟/IP，生产环境需联系调整配额。</font></li> </ol> <p><font style="color:#020817">按需直接复制命令即可集成到脚本或应用程序中。</font></p> <h3 id="网页服务"><font style="color:#020817">网页服务</font></h3> <h4 id="221-英文音频转文字"><font style="color:#020817">2.2.1 英文音频转文字</font></h4> <p><font style="color:#020817">点击</font><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)">/asr：语音识别接口</font><font style="color:#020817">后点击页面右上角</font><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)">Try it out</font><font style="color:#020817"> 开始使用</font></p> <p><img src="'+a+'" alt=""></p> <p><font style="color:#020817">先用一个英文 mp3 音频看看效果，可以先照抄截图中的参数看看效果，后面会介绍每个参数的意思。</font></p> <p><img src="'+i+'" alt=""></p> <p><font style="color:#020817">稍等一会即可转换完成，在 response body 中可看到转换结果。</font></p> <p><img src="'+g+'" alt=""></p> <p><font style="color:#020817">下面是复制出来的文本，可以看到效果是非常好的：</font></p> <pre><code class="language-json">Wilbur and Orville Wright are the American inventors who made a small engine-powered flying machine.\nThey proved that flight without the aid of gas-filled balloons was possible.\nWilbur Wright was born in 1867 near Melville, Indiana.\nThis brother Orville was born four years later in Dayton, Ohio.\nAs they grew up, the Wright brothers experimented with mechanical things.\nLater, the Wright brothers began to design their own flying machine.\nThey used ideas they had developed from earlier experiments with a toy helicopter, Kites,\nthe printing machine, and bicycles.\nSoon they needed a place to test their ideas about flight.\nThe best place with best wind conditions seemed to be a piece of sandy land in North Carolina\nalong the coast of the Atlantic Ocean.\nIt was called Kill Devil Hill near the town of Kitty Hawk.\nThe Wright brothers did many tests with gliders at Kitty Hawk.\nWith these tests, they learned how to solve many problems.\nBy the autumn of 1903, Wilbur and Orville had designed and built an airplane powered\nby a gasoline engine.\nThe plane had wings twelve meters across.\nIt weighed about 340 kilograms, including the pilot.\nOn December 17, 1903, they made the world&#39;s first flight in a machine that was heavier\nthan air and powered by an engine.\nOrville flew the plane thirty-six meters.\nHe was in the air for twelve seconds.\nThe two brothers made three more flights that day.\nFor other men watched the Wright brothers&#39; first flights, one of the men took pictures.\nFew newspapers, however, noted the event.\nIt was almost five years before the Wright brothers became famous.\nIn 1908, Wilbur went to France.\nHe gave demonstration flights at heights of ninety meters.\nA French company agreed to begin making the Wright brothers flying machine.\nOrville made successful flights in the United States.\nAt the time, Wilbur was in France.\nThe United States War Department agreed to buy a Wright brothers plane.\nOrville and Orville suddenly became world heroes.\nBut the brothers were not seeking fame.\nThey returned to Dayton where they continued to improve their airplanes.\nThey taught many others how to fly.\nWilbur Wright died of typhoid fever in 1912.\nOrville Wright continued designing and inventing until he died many years later in 1948.\nToday, the Wright brothers&#39; first airplane is in the Air and Space Museum in Washington,\nDC.\nVisitors to the museum can look at the Wright brothers&#39; small plane.\nThen they can walk to another area and see space vehicles and a rock collected from the\nmoon.\nThe world has changed a lot since Wilbur and Orville Wright began the modern age of flight\nover one hundred years ago.\nI&#39;m John Russell.\n</code></pre> <h4 id="222-中文视频转文字"><font style="color:#020817">2.2.2 中文视频转文字</font></h4> <p><font style="color:#020817">与上面操作一样，只是选文件的时候选一个中文视频就可以了，然后提示词这里写上简体中文的要求（默认会输出繁体）</font></p> <p><img src="'+y+'" alt=""></p> <p><font style="color:#020817">下面是复制出来的文本，可以看到效果是非常好的：</font></p> <pre><code class="language-json">我们正站在一个计算力重新定义世界的时代\n算力，这个曾经只出现在服务器参数表上的词，正在悄无声息地进入每个人的生活\n你打开手机的一次语音助手唤醒，你看的一段实时翻译视频\n你在社交媒体刷到的一张 AI 合成图——它们背后，都有庞大算力在悄悄支撑\n但大多数人，并不知道这一切意味着什么\n有人说，算力是新时代的“水与电”\n它不是新闻，却支撑着所有新闻的传输；它不是观点，却加速了每一个观点的扩散\n每一次模型的训练、每一次搜索引擎的响应、每一帧超清视频的稳定播放，都是算力在负重前行\n而在过去的几年，算力不再只是“快与慢”的比较，它变成了谁拥有未来生产力的标尺\n国家之间在比拼数据中心的布局、芯片的迭代速度，城市之间在竞逐智能基础设施的落地率\n连一座山谷是否适合建 AI 工厂，背后考量的也不只是地理和气候，而是它能否撑起千万亿次的计算吞吐\n在这个时代，有人说数据是新的石油，但我们越来越清楚\n真正决定谁能炼出“智能”的，不只是数据的多寡，而是你有没有足够的算力\n这很冷酷，也很真实\n但也正因为如此，才更值得我们去关注\n当一切都在拼算力时，是否还有人为算力背后的能耗、伦理、地域不平衡发出一点声音\n当一个个模型被迅速部署、落地、取代时\n我们是否还能停下来想一想\n到底是我们在使用算力，还是某种不可见的秩序，正借由算力改写世界的运转方式\n没人能给出答案。我们只能继续看，继续记录\n</code></pre> <h4 id="223-语言检测"><font style="color:#020817">2.2.3 语言检测</font></h4> <p><font style="color:#020817">不识别文字，只检测一下是什么语言，大文件只会检查前 30 秒。</font></p> <p><img src="'+p+'" alt=""></p> <p><font style="color:#020817">结果展示：</font></p> <p><img src="'+d+'" alt=""></p> <h2 id="3参数解释"><font style="color:#020817">3.参数解释</font></h2> <h3 id="31-encode（编码预处理）"><strong><font style="color:#020817">3.1</font><strong><strong><font style="color:#020817"> </font></strong></strong><font style="color:#020817">encode（编码预处理）</font></strong></h3> <ul> <li><strong><font style="color:#020817">作用</font></strong><font style="color:#020817">：自动通过 </font><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)">ffmpeg</font><font style="color:#020817"> 对音视频文件进行预处理</font></li> <li><strong><font style="color:#020817">必填</font></strong><font style="color:#020817">：</font><font style="color:#020817">✅</font><font style="color:#020817"> 是（推荐始终设为 </font><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)">true</font><font style="color:#020817">）</font></li> <li><strong><font style="color:#020817">场景说明</font></strong><font style="color:#020817">：</font><ul> <li><strong><font style="color:#020817">true</font></strong><font style="color:#020817">：对非标准音频格式（如 MP4/MKV 中的音轨）提取为 PCM/WAV 格式</font></li> <li><strong><font style="color:#020817">false</font></strong><font style="color:#020817">：仅当输入为原始音频（如已解压的 WAV 文件）时使用</font></li> </ul> </li> <li><strong><font style="color:#020817">示例错误</font></strong><font style="color:#020817">：</font></li> </ul> <pre><code class="language-bash"># 若上传 MP4 但设为 false 会报错：\nError: Audio extraction failed - unsupported container format\n</code></pre> <h3 id="32-task（任务模式）"><strong><font style="color:#020817">3.2</font><strong><strong><font style="color:#020817"> </font></strong></strong><font style="color:#020817">task（任务模式）</font></strong></h3> <table> <thead> <tr> <th><font style="color:#020817">模式</font></th> <th><font style="color:#020817">功能说明</font></th> <th><font style="color:#020817">输出示例</font></th> </tr> </thead> <tbody><tr> <td><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)">transcribe</font></td> <td><font style="color:#020817">语音转文字（源语言→同语言文本）</font></td> <td><font style="color:#020817">中文音频 → 中文文本</font></td> </tr> <tr> <td><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)">translate</font></td> <td><font style="color:#020817">语音翻译（任何语言→英文文本）</font></td> <td><font style="color:#020817">中文音频 → 英文文本</font></td> </tr> </tbody></table> <ul> <li><strong><font style="color:#020817">注意</font></strong><font style="color:#020817">：</font><ul> <li><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)">translate</font><font style="color:#020817"> 仅支持输出英文，不可指定其他目标语言</font></li> <li><font style="color:#020817">教学场景建议优先用</font><font style="color:#2f8ef4"> </font><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)">transcribe</font><font style="color:#2f8ef4"> </font><font style="color:#020817">保留原语言语义</font></li> </ul> </li> </ul> <h3 id="33-language（源语言指定）"><strong><font style="color:#020817">3.3</font><strong><strong><font style="color:#020817"> </font></strong></strong><font style="color:#020817">language（源语言指定）</font></strong></h3> <ul> <li><strong><font style="color:#020817">作用</font></strong><font style="color:#020817">：声明输入音频的语言（ISO 639-1 代码，如</font><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)">ch</font><font style="color:#020817">/</font><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)">en</font><font style="color:#020817">）</font></li> <li><strong><font style="color:#020817">必填</font></strong><font style="color:#020817">：</font><font style="color:#020817">❌</font><font style="color:#020817"> 否（自动检测模式）</font></li> <li><strong><font style="color:#020817">使用策略</font></strong><font style="color:#020817">：</font></li> </ul> <table> <thead> <tr> <th><font style="color:#020817">场景</font></th> <th><font style="color:#020817">推荐操作</font></th> </tr> </thead> <tbody><tr> <td><font style="color:#020817">单一语言录音</font></td> <td><font style="color:#020817">留空（自动检测更准确）</font></td> </tr> <tr> <td><font style="color:#020817">混合语言学术会议</font></td> <td><font style="color:#020817">强制指定主语言（如 </font><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)">en</font><font style="color:#020817">）</font></td> </tr> </tbody></table> <ul> <li><strong><font style="color:#020817">错误示例</font></strong><font style="color:#020817">：</font></li> </ul> <pre><code class="language-python"># 中文音频指定 language=&#39;en&#39; 会导致：\n&quot;text&quot;: &quot;Yangyang, will you give me money...&quot;  # 拼音化乱码\n</code></pre> <h3 id="34-initial_prompt（上下文提示）"><strong><font style="color:#020817">3.4 </font><strong>i</strong><font style="color:#020817">nitial_prompt（上下文提示）</font></strong></h3> <ul> <li><strong><font style="color:#020817">作用</font></strong><font style="color:#020817">：提供领域关键词提升识别精度（类似 ChatGPT 的 system prompt）</font></li> <li><strong><font style="color:#020817">格式</font></strong><font style="color:#020817">：英文短语或术语列表（即使处理中文音频也需用英文填写）</font></li> <li><strong><font style="color:#020817">经典用例</font></strong><font style="color:#020817">：</font><ul> <li><font style="color:#020817">识别准确率提升 12-13%（针对专业术语）</font></li> <li><font style="color:#020817">支持动态更新词库（通过 </font><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)">X-KeyPool </font><font style="color:#020817">请求头注入）</font></li> </ul> </li> </ul> <pre><code class="language-json">{\n  &quot;initial_prompt&quot;: &quot;machine learning, convolutional neural networks, GPT-4&quot;\n}\n</code></pre> <h3 id="35-word_timestamps（时间戳控制）"><strong><font style="color:#020817">3.5 word_timestamps（时间戳控制）</font></strong></h3> <ul> <li><strong><font style="color:#020817">作用</font></strong><font style="color:#020817">：控制输出是否包含词级时间标注</font></li> <li><strong><font style="color:#020817">兼容性</font></strong><font style="color:#020817">：</font></li> </ul> <table> <thead> <tr> <th><font style="color:#020817">输出格式</font></th> <th><font style="color:#020817">时间戳表现</font></th> </tr> </thead> <tbody><tr> <td><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)">json</font></td> <td><font style="color:#020817">完整时间戳（精确到 10ms）</font></td> </tr> <tr> <td><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)">srt</font></td> <td><font style="color:#020817">句子级分段（自动聚合词级数据）</font></td> </tr> <tr> <td><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)">txt</font></td> <td><font style="color:#020817">不生效</font></td> </tr> </tbody></table> <h3 id="36-output（输出格式）"><strong><font style="color:#020817">3.6</font><strong><strong><font style="color:#020817"> </font></strong></strong><font style="color:#020817">output（输出格式）</font></strong></h3> <table> <thead> <tr> <th><font style="color:#020817">格式</font></th> <th><font style="color:#020817">适用场景</font></th> <th><font style="color:#020817">示例片段</font></th> </tr> </thead> <tbody><tr> <td><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)">txt</font></td> <td><font style="color:#020817">快速预览</font></td> <td><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)">Yangyang, will you give me...</font></td> </tr> <tr> <td><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)">srt</font></td> <td><font style="color:#020817">视频字幕嵌入</font></td> <td><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)">1↵00:00:02,140 → 00:00:04,320↵Yangyang, will you...</font></td> </tr> <tr> <td><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)">json</font></td> <td><font style="color:#020817">开发者分析（含置信度等元数据）</font></td> <td><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)">{&quot;text&quot;: &quot;...&quot;, &quot;words&quot;: [{&quot;word&quot;: &quot;Yangyang&quot;, &quot;start&quot;: 2.14, &quot;end&quot;: 2.87, &quot;confidence&quot;: 0.92}]}</font></td> </tr> <tr> <td><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)">vtt</font></td> <td><font style="color:#020817">流媒体兼容字幕</font></td> <td><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)">WEBVTT↵↵00:02.140 --&gt; 00:04.320↵Yangyang, will you...</font></td> </tr> </tbody></table> <h2 id="4项目介绍"><font style="color:#020817">4.项目介绍</font></h2> <p><font style="color:#020817">拥有 ChatGPT 语言模型的 OpenAI 公司，开源了 Whisper 自动语音识别系统，OpenAI 强调 Whisper 的语音识别能力已达到人类水准。</font></p> <p><font style="color:#020817">Whisper 是一个通用的语音识别模型，它使用了大量的多语言和多任务的监督数据来训练，能够在英语语音识别上达到接近人类水平的鲁棒性和准确性。Whisper 还可以进行多语言语音识别、语音翻译和语言识别等任务。Whisper 的架构是一个简单的端到端方法，采用了编码器 - 解码器的 Transformer 模型，将输入的音频转换为对应的文本序列，并根据特殊的标记来指定不同的任务。</font></p> <p><img src="'+h+'" alt=""></p> <p><font style="color:#020817">Whisper 是一个自动语音识别（ASR，Automatic Speech Recognition）系统，OpenAI 通过从网络上收集了 68 万小时的多语言（98 种语言）和多任务（multitask）监督数据对 Whisper 进行了训练。OpenAI 认为使用这样一个庞大而多样的数据集，可以提高对口音、背景噪音和技术术语的识别能力。除了可以用于语音识别，Whisper 还能实现多种语言的转录，以及将这些语言翻译成英语。OpenAI 开放模型和推理代码，希望开发者可以将 Whisper 作为建立有用的应用程序和进一步研究语音处理技术的基础。</font></p> <p><font style="color:#020817">项目地址：</font><a href="https://github.com/openai/whisper"><font style="color:#2f8ef4">https://github.com/openai/whisper</font></a></p> <h2 id="5天工开物打包好的-whisper-项目镜像的优势："><font style="color:#020817">5.天工开物打包好的 whisper 项目镜像的优势：</font></h2> <h3 id="51-本地转写的麻烦之处："><font style="color:#020817">5.1 本地转写的麻烦之处：</font></h3> <p><font style="color:#020817">笔记本上缺乏好的硬件：纯 CPU 模式跑 Whisper 速度非常慢，3 小时的音频可能需要十几个小时才能转写完毕。如果想用 GPU 加速，根据 Whisper 模型显存需求表格，官方的 Whisper-large 模型需要 10G 显存，普通的核显本实在是力不从心。后续的 Whisper cpp 项目 倒是大幅降低了内存需求，但这也引出了第二个问题。</font></p> <p><font style="color:#020817">环境部署复杂：OpenAI 开放的毕竟是项目代码，自己写代码适配的成本还是有点高。Whisper 的 GUI 客户端在 Mac 上不少（Whisper Transcription、MacWhisper..），Windows 上也有 Buzz，然而要找到一个支持 GPU 加速的客户端依然十分困难。</font></p> <h3 id="52-利用-openai-的-whisper-api-云端转写的问题："><font style="color:#020817">5.2 利用 OpenAI 的 Whisper API 云端转写的问题：</font></h3> <p><font style="color:#020817">利用 OpenAI 的 Whisper API 云端转写的优势在于不会受到本地机器性能的限制，且速度相对较快。但它存在两个问题：</font></p> <p><font style="color:#020817">项目处理流程复杂：OpenAI 的 Whisper API 限制单次请求的音频大小为 25Mb，而一节 3h 的音频通常都会有大几十 MB。这就需要对音频先做分段处理，再请求结果，最后合并结果。如果是 mp4 文件则还需要从中抽取音频文件，这个过程里没少踩坑。</font></p> <p><font style="color:#020817">成本问题：OpenAI 的 Whisper 模型 1min 收费 0.006 美元，1h 的音频按照 7.3 的汇率需要收费 2.7 元。坦白讲，Whisper 的 API 价格非常便宜了，几乎只是 Google Speech2Text API 的四分之一。但是，如果我们假设有 5 门课程，每堂课长 3 小时，每周有一次课，那么每个月的转写成本 = 5 x 3 x 4 x 2.7 = 162 元，这个价格还是有点肉疼。</font></p> <h3 id="53-天工开物打包好的-whisper-镜像的核心优势"><font style="color:#020817">5.3 天工开物打包好的 Whisper 镜像的核心优势</font></h3> <p><font style="color:#020817">针对传统音频转写场景的三大核心痛点，天工开物通过技术创新实现</font><strong><font style="color:#020817">&quot;资源重构、效率跃升、体验革新&quot;</font></strong><font style="color:#020817">，具体优势对比如下：</font></p> <h4 id="531-核显笔记本运行-whisper-large-的算力解放"><strong><font style="color:#020817">5.3.1 核显笔记本运行 Whisper-large 的算力解放</font></strong></h4> <p><strong><font style="color:#020817">▎痛点场景</font></strong></p> <p><font style="color:#020817">普通笔记本用 CPU 运行大型模型，3 小时音频需耗时 15 小时</font></p> <p><strong><font style="color:#020817">▎传统方案</font></strong></p> <ul> <li><font style="color:#020817">忍受超长等待或花费数万元升级显卡</font></li> </ul> <p><strong><font style="color:#020817">▎天工开物方案</font></strong></p> <ul> <li><strong><font style="color:#020817">智能路由</font></strong><font style="color:#020817">：自动接入算力池闲置 4090 显卡</font></li> <li><strong><font style="color:#020817">显存池化</font></strong><font style="color:#020817">：突破单卡物理限制，动态聚合多节点显存资源</font></li> <li><strong><font style="color:#020817">效能提升</font></strong><font style="color:#020817">：3 小时音频转写时间从 15 小时压缩至 55 分钟</font></li> </ul> <h4 id="532-本地-gpu-资源不足的柔性调度"><strong><font style="color:#020817">5.3.2 本地 GPU 资源不足的柔性调度</font></strong></h4> <p><strong><font style="color:#020817">▎痛点场景</font></strong></p> <p><font style="color:#020817">官方要求 10G 显存，普通设备无法满足</font></p> <p><strong><font style="color:#020817">▎传统方案</font></strong></p> <ul> <li><font style="color:#020817">被迫采购高配显卡（如 RTX 4090）</font></li> </ul> <p><strong><font style="color:#020817">▎天工开物方案</font></strong></p> <ul> <li><strong><font style="color:#020817">碎片化显存调用</font></strong><font style="color:#020817">：将多台设备显存组合为 10G 逻辑显卡</font></li> <li><strong><font style="color:#020817">分布式推理</font></strong><font style="color:#020817">：把 Whisper-large 模型拆解到多卡并行计算</font></li> <li><strong><font style="color:#020817">成本规避</font></strong><font style="color:#020817">：零硬件投入即可获得专业级计算能力</font></li> </ul> <h4 id="533-跨平台-gui-的终极统一方案"><strong><font style="color:#020817">5.3.3 跨平台 GUI 的终极统一方案</font></strong></h4> <p><strong><font style="color:#020817">▎痛点场景</font></strong></p> <p><font style="color:#020817">Windows/Mac 客户端功能割裂，且缺乏 GPU 加速支持</font></p> <p><strong><font style="color:#020817">▎传统方案</font></strong></p> <ul> <li><font style="color:#020817">在不同平台反复配置环境，手动处理兼容性问题</font></li> </ul> <p><strong><font style="color:#020817">▎天工开物方案</font></strong></p> <ul> <li><strong><font style="color:#020817">浏览器即工作站</font></strong><font style="color:#020817">：通过 Web 界面一键操作（上传/转写/导出）</font></li> <li><strong><font style="color:#020817">无缝衔接</font></strong><font style="color:#020817">：无论 Chromium 还是 Safari 内核，均可获得一致体验</font></li> </ul> <h3 id="54-技术价值提炼"><strong><font style="color:#020817">5.4 技术价值提炼</font></strong></h3> <table> <thead> <tr> <th><strong><font style="color:#020817">维度</font></strong></th> <th><strong><font style="color:#020817">传统方案</font></strong></th> <th><strong><font style="color:#020817">天工开物创新价值</font></strong></th> </tr> </thead> <tbody><tr> <td><strong><font style="color:#020817">硬件门槛</font></strong></td> <td><font style="color:#020817">被设备性能锁死生产力</font></td> <td><font style="color:#020817">算力资源&quot;无感穿透&quot;，让核显本拥有 A100 级能力</font></td> </tr> <tr> <td><strong><font style="color:#020817">部署成本</font></strong></td> <td><font style="color:#020817">动辄上万元的硬件投入</font></td> <td><font style="color:#020817">按需付费，单次任务最低 0.5 元起</font></td> </tr> <tr> <td><strong><font style="color:#020817">运维复杂度</font></strong></td> <td><font style="color:#020817">跨平台调试耗时 3 小时+/次</font></td> <td><font style="color:#020817">浏览器打开即用，全程无需技术背景</font></td> </tr> </tbody></table> <p><font style="color:#020817">通过将复杂的 GPU 资源调度、模型优化、跨平台兼容等底层技术封装为开箱即用的标准化服务，天工开物让专业级语音转写从</font><strong><font style="color:#020817">实验室特权</font></strong><font style="color:#020817">真正转变为</font><strong><font style="color:#020817">基础教育工具</font></strong><font style="color:#020817">。</font></p> <p><strong><font style="color:#67676c"></font></strong></p> <p><br><br> <font style="color:#b2b2b2">最后更新于: 2025/6/17 17:52</font></p> ';t["default"]=u},1404:function(o,t,n){o.exports=n.p+"img/universal1.9a1b3f4b.png"},4117:function(o,t,n){o.exports=n.p+"img/universal2.4306636e.png"},3756:function(o,t,n){o.exports=n.p+"img/whisper10.17232ca0.png"},7367:function(o,t,n){o.exports=n.p+"img/whisper11.5e6bdf9c.png"},9844:function(o,t,n){o.exports=n.p+"img/whisper12.413468d6.png"},5942:function(o,t,n){o.exports=n.p+"img/whisper13.46772dca.png"},8957:function(o,t,n){o.exports=n.p+"img/whisper3.78acbd41.png"},8471:function(o,t,n){o.exports=n.p+"img/whisper4.958bdcf5.png"},8159:function(o,t,n){o.exports=n.p+"img/whisper5.fca20726.png"},1586:function(o,t,n){o.exports=n.p+"img/whisper6.3819b839.png"},9043:function(o,t,n){o.exports=n.p+"img/whisper7.92d00110.png"},7212:function(o,t,n){o.exports=n.p+"img/whisper8.b4e0aa36.png"},785:function(o,t,n){o.exports=n.p+"img/whisper9.9fa5b2fb.png"}}]);
//# sourceMappingURL=docs32.e382623b.js.map