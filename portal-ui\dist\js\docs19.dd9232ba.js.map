{"version": 3, "file": "js/docs19.dd9232ba.js", "mappings": "wHACA,IAAIA,EAA6B,IAAIC,IAAI,aACrCC,EAA6B,IAAID,IAAI,YACrCE,EAA6B,IAAIF,IAAI,aACrCG,EAA6B,IAAIH,IAAI,aACrCI,EAA6B,IAAIJ,IAAI,aACrCK,EAA6B,IAAIL,IAAI,aACrCM,EAA6B,IAAIN,IAAI,aACrCO,EAA6B,IAAIP,IAAI,YACrCQ,EAA6B,IAAIR,IAAI,aACrCS,EAA6B,IAAIT,IAAI,aACrCU,EAA8B,IAAIV,IAAI,aAEtCW,EAAO,2rEAA0wEZ,EAA6B,6zCAAm3CE,EAA6B,uNAAmOC,EAA6B,2LAAuMC,EAA6B,6+cAAi8dC,EAA6B,4vCAA0yCC,EAA6B,uHAAiIC,EAA6B,wkBAAgmBC,EAA6B,sBAA4BC,EAA6B,2wBAAyyBC,EAA6B,itBAA2uBC,EAA8B,k9SAE92tB,c", "sources": ["webpack://portal-ui/./src/docs/mirror.md"], "sourcesContent": ["// Imports\nvar ___HTML_LOADER_IMPORT_0___ = new URL(\"./imgs/mirror1.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_1___ = new URL(\"./imgs/mirror2.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_2___ = new URL(\"./imgs/mirror3.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_3___ = new URL(\"./imgs/mirror4.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_4___ = new URL(\"./imgs/mirror5.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_5___ = new URL(\"./imgs/mirror6.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_6___ = new URL(\"./imgs/mirror7.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_7___ = new URL(\"./imgs/mirror8.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_8___ = new URL(\"./imgs/mirror9.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_9___ = new URL(\"./imgs/mirror10.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_10___ = new URL(\"./imgs/mirror11.png\", import.meta.url);\n// Module\nvar code = \"<h1 id=\\\"镜像仓库\\\"><font style=\\\"color:#020817\\\">镜像仓库</font></h1> <hr> <h2 id=\\\"1-服务选择建议\\\"><font style=\\\"color:#020817\\\">1. 服务选择建议</font></h2> <ol> <li><strong><font style=\\\"color:#020817\\\">阿里云容器镜像服务（ACR）</font></strong></li> </ol> <ul> <li><strong><font style=\\\"color:#020817\\\">功能特性</font></strong><font style=\\\"color:#020817\\\">：支持镜像托管、安全扫描、多地域分发，提供企业版与默认实例版，企业版适用于大规模多地域场景。</font></li> <li><strong><font style=\\\"color:#020817\\\">推荐场景</font></strong><font style=\\\"color:#020817\\\">：生产环境建议使用企业版，同时也提供免费版。</font></li> <li><strong><font style=\\\"color:#020817\\\">服务地址</font></strong><font style=\\\"color:#020817\\\">：</font><a href=\\\"https://www.aliyun.com/product/acr\\\"><font style=\\\"color:#2f8ef4\\\">https://www.aliyun.com/product/acr</font></a></li> </ul> <ol start=\\\"2\\\"> <li><strong><font style=\\\"color:#020817\\\">我们提供的暂时免费的镜像仓库</font></strong></li> </ol> <ul> <li><strong><font style=\\\"color:#020817\\\">适用场景</font></strong><font style=\\\"color:#020817\\\">：临时测试或小规模使用，需注意账号独立（与主账号密码不互通）。</font></li> </ul> <blockquote> <p><font style=\\\"color:#1f2937\\\">镜像仓库的账号密码和用户的账号密码不互通。</font></p> </blockquote> <h2 id=\\\"2-我们提供的暂时免费的镜像仓库使用指南\\\"><strong><font style=\\\"color:#020817\\\">2. 我们提供的暂时免费的镜像仓库</font></strong><font style=\\\"color:#020817\\\">使用指南</font></h2> <h3 id=\\\"21-步骤-1-登录天工开物算力镜像站\\\"><font style=\\\"color:#020817\\\">2.1 步骤 1: 登录天工开物算力镜像站</font></h3> <ol> <li><font style=\\\"color:#020817\\\">访问控制台的 </font><a href=\\\"https://tiangongkaiwu.top/#/console\\\">https://tiangongkaiwu.top/#/console</a> <font style=\\\"color:#020817\\\">页面</font></li> <li><font style=\\\"color:#020817\\\">点击&quot;访问凭证&quot;</font></li> <li><font style=\\\"color:#020817\\\">按照页面上的登录指引操作（运行命令-&gt;输入密码）</font></li> </ol> <p><strong><font style=\\\"color:#67676c\\\">凭证的核心作用：</font></strong></p> <ul> <li><strong><font style=\\\"color:#67676c\\\">身份验证</font></strong><font style=\\\"color:#67676c\\\"> </font><font style=\\\"color:#67676c\\\">凭证相当于平台访问的&quot;数字钥匙&quot;，用于验证用户身份，确保只有授权用户才能访问私有镜像库。</font></li> <li><strong><font style=\\\"color:#67676c\\\">权限控制</font></strong><font style=\\\"color:#67676c\\\"> </font><font style=\\\"color:#67676c\\\">通过凭证关联账户权限，控制用户对镜像资源的拉取（pull）、推送（push）等操作。</font></li> <li><strong><font style=\\\"color:#67676c\\\">安全通信</font></strong><font style=\\\"color:#67676c\\\"> </font><font style=\\\"color:#67676c\\\">加密客户端与镜像站之间的数据传输，防止敏感信息泄露。</font></li> </ul> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_0___ + \"\\\" alt=\\\"\\\"></p> <p><font style=\\\"color:#020817\\\">针对</font><font style=\\\"color:#020817\\\"> </font><strong><font style=\\\"color:#020817\\\">Windows</font></strong><font style=\\\"color:#020817\\\"> </font><font style=\\\"color:#020817\\\">和</font><font style=\\\"color:#020817\\\"> </font><strong><font style=\\\"color:#020817\\\">Mac</font></strong><font style=\\\"color:#020817\\\"> </font><font style=\\\"color:#020817\\\">系统的终端操作指南：</font></p> <h4 id=\\\"211-windows-系统操作步骤\\\"><strong><font style=\\\"color:#020817\\\">2.1.1 Windows 系统操作步骤</font></strong></h4> <p><strong><font style=\\\"color:#020817\\\">第一步：打开终端</font></strong><font style=\\\"color:#020817\\\"> 按下 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">Win + S</font><font style=\\\"color:#020817\\\"> 搜索 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">PowerShell</font><font style=\\\"color:#020817\\\"> 或 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">CMD</font><font style=\\\"color:#020817\\\">，右键选择</font><strong><font style=\\\"color:#020817\\\"> “以管理员身份运行”</font></strong><font style=\\\"color:#020817\\\">（避免权限问题）</font></p> <p><strong><font style=\\\"color:#020817\\\">第二步：执行登录命令</font></strong><font style=\\\"color:#020817\\\"> (复制凭证中的登录镜像仓库命令) </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">docker login harbor.suanleme.cn --username=XXX（换成用户所对应的仓库名称）</font><img src=\\\"\" + ___HTML_LOADER_IMPORT_1___ + \"\\\" alt=\\\"\\\"><font style=\\\"color:#020817\\\">系统会提示输入密码：</font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">Password:#输入你的仓库密码（输入时不会显示字符，直接盲输后按回车）</font><font style=\\\"color:#020817\\\"> </font><img src=\\\"\" + ___HTML_LOADER_IMPORT_2___ + \"\\\" alt=\\\"\\\"><font style=\\\"color:#020817\\\">成功提示：</font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">Login Succeeded</font><font style=\\\"color:#020817\\\"> </font><img src=\\\"\" + ___HTML_LOADER_IMPORT_3___ + \"\\\" alt=\\\"\\\"></p> <p><strong><font style=\\\"color:#020817\\\">常见问题：</font></strong></p> <p><font style=\\\"color:#020817\\\">如果提示 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">docker command not found</font><font style=\\\"color:#020817\\\">：需先安装</font><font style=\\\"color:#2f8ef4\\\"> </font><a href=\\\"https://www.docker.com/products/docker-desktop\\\"><font style=\\\"color:#2f8ef4\\\">Docker Desktop for Windows</font></a><font style=\\\"color:#2f8ef4\\\"> </font><font style=\\\"color:#020817\\\">并确保服务已启动。</font></p> <p><font style=\\\"color:#020817\\\">密码错误或过期：通过网页端</font><font style=\\\"color:#020817\\\"> </font><strong><font style=\\\"color:#020817\\\">&quot;重置密码&quot;</font></strong><font style=\\\"color:#020817\\\"> </font><font style=\\\"color:#020817\\\">后重试。</font></p> <hr> <h4 id=\\\"212-mac-系统操作步骤\\\"><strong><font style=\\\"color:#020817\\\">2.1.2 Mac 系统操作步骤</font></strong></h4> <p><strong><font style=\\\"color:#020817\\\">第一步：打开终端</font></strong><font style=\\\"color:#020817\\\"> 通过 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">Spotlight</font><font style=\\\"color:#020817\\\">（</font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">Command + Space</font><font style=\\\"color:#020817\\\">）搜索</font><font style=\\\"color:#2f8ef4\\\"> </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">终端</font><font style=\\\"color:#2f8ef4\\\"> </font><font style=\\\"color:#020817\\\">并打开</font></p> <p><strong><font style=\\\"color:#020817\\\">第二步：执行登录命令</font></strong><font style=\\\"color:#2f8ef4\\\"> </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">docker login harbor.suanleme.cn --username=XXX（换成用户所对应的仓库名称）</font><font style=\\\"color:#020817\\\">输入密码：</font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">Password: # 输入密码（无回显，输完直接按回车）</font><font style=\\\"color:#020817\\\"> 成功提示：</font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">Login Succeeded</font><font style=\\\"color:#2f8ef4\\\"> </font></p> <p><strong><font style=\\\"color:#020817\\\">常见问题：</font></strong><font style=\\\"color:#020817\\\"><br></font><font style=\\\"color:#020817\\\">如果提示权限不足：在命令前加 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">sudo</font><font style=\\\"color:#020817\\\">（需输入 Mac 用户密码）：</font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">sudo docker login harbor.suanleme.cn --username=XXX（换成用户所对应的仓库名称）</font><font style=\\\"color:#020817\\\"> Docker 未运行：需启动 </font><a href=\\\"https://www.docker.com/products/docker-desktop\\\"><font style=\\\"color:#2f8ef4\\\">Docker Desktop for Mac</font></a><font style=\\\"color:#2f8ef4\\\"> </font><font style=\\\"color:#020817\\\">并等待图标变绿。</font></p> <hr> <h4 id=\\\"213-密码重置指引\\\"><strong><font style=\\\"color:#020817\\\">2.1.3 密码重置指引</font></strong></h4> <ol> <li><font style=\\\"color:#020817\\\">访问镜像仓库控制台页面，点击</font><font style=\\\"color:#020817\\\"> </font><strong><font style=\\\"color:#020817\\\">&quot;重置密码&quot;</font></strong><font style=\\\"color:#020817\\\">。</font></li> <li><font style=\\\"color:#020817\\\">按提示通过邮箱或手机验证身份。</font></li> <li><font style=\\\"color:#020817\\\">设置新密码后，</font><strong><font style=\\\"color:#020817\\\">重新执行登录命令</font></strong><font style=\\\"color:#020817\\\">。</font></li> </ol> <hr> <h3 id=\\\"22-步骤-2-为镜像添加标签\\\"><font style=\\\"color:#020817\\\">2.2 步骤 2: 为镜像添加标签</font></h3> <h4 id=\\\"221-为何需要添加标签？\\\"><strong><font style=\\\"color:#020817\\\">2.2.1 为何需要添加标签？</font></strong></h4> <p><font style=\\\"color:#020817\\\">镜像标签（Tag）相当于镜像的&quot;地址 + 版本标识&quot;，目的是将本地镜像与远程仓库路径绑定。天工开物算力镜像站要求镜像名称必须包含完整的仓库地址和账户信息，否则无法识别推送目标。</font></p> <hr> <h4 id=\\\"222-完整操作步骤\\\"><strong><font style=\\\"color:#020817\\\">2.2.2 完整操作步骤</font></strong></h4> <h5 id=\\\"第一步：查看本地镜像列表\\\"><strong><font style=\\\"color:#020817\\\">第一步：查看本地镜像列表</font></strong></h5> <p><font style=\\\"color:#020817\\\">在终端执行以下命令，确认要推送的本地镜像名称和版本：</font></p> <h6 id=\\\"查看本地所有的镜像\\\"><strong><font style=\\\"color:#020817\\\">查看本地所有的镜像</font></strong></h6> <pre><code class=\\\"language-plain\\\">#查看本地所有的镜像\\ndocker images\\n</code></pre> <h6 id=\\\"非-root-用户使用\\\"><font style=\\\"color:#020817\\\">非 root 用户使用</font></h6> <pre><code class=\\\"language-plain\\\">#查看本地所有的镜像\\nsudo docker images\\n</code></pre> <p><font style=\\\"color:#020817\\\">输出示例：</font></p> <pre><code class=\\\"language-plain\\\">REPOSITORY    TAG       IMAGE ID       CREATED         SIZE\\nmy-image     latest    a1b2c3d4e5f6   2 hours ago     1.2GB\\n</code></pre> <h6 id=\\\"docker-镜像字段解析：\\\"><strong><font style=\\\"color:#020817\\\">Docker 镜像字段解析：</font></strong></h6> <p><strong><font style=\\\"color:#020817\\\">镜像仓库/名称 (REPOSITORY)</font></strong></p> <p><font style=\\\"color:#020817\\\">表示镜像的完整来源路径，由仓库地址、账户名和镜像名称组成。例如：</font></p> <ul> <li><font style=\\\"color:#020817\\\">公共仓库镜像：</font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">my-image</font><font style=\\\"color:#020817\\\">（默认从 Docker Hub 拉取，格式为</font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">仓库名/镜像名</font><font style=\\\"color:#020817\\\">）。</font></li> <li><font style=\\\"color:#020817\\\">私有仓库镜像：</font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">harbor.suanleme.cn/xiditgkw/my-app</font><font style=\\\"color:#020817\\\">（需包含仓库域名、账户名及镜像名）。</font></li> </ul> <p><strong><font style=\\\"color:#020817\\\">标签 (TAG)</font></strong></p> <p><font style=\\\"color:#020817\\\">用于标识镜像版本，支持自定义语义化命名规则：</font></p> <ul> <li><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">latest</font><font style=\\\"color:#52b788;background-color:rgba(142,150,170,.14)\\\"> </font><font style=\\\"color:#020817\\\">是默认标签，通常指向最新构建的镜像（生产环境慎用）。</font></li> <li><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">v1.2.0 </font><font style=\\\"color:#020817\\\">是推荐的自定义标签，可通过版本号区分不同功能阶段的镜像（如开发版、稳定版）。</font></li> </ul> <p><strong><font style=\\\"color:#020817\\\">镜像唯一 ID (IMAGE ID)</font></strong><font style=\\\"color:#020817\\\"> 由镜像内容生成的哈希值，是镜像的唯一标识符。实际显示为完整 64 位 ID 的前 12 位（例如 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">a1b2c3d4e5f</font><font style=\\\"color:#020817\\\">），可通过 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">docker inspect </font><font style=\\\"color:#020817\\\">命令查看完整 ID。</font></p> <p><strong><font style=\\\"color:#020817\\\">创建时间 (CREATED)</font></strong><font style=\\\"color:#020817\\\"> 记录镜像的构建时间，格式为可读的相对时间（如 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">2 weeks ago</font><font style=\\\"color:#020817\\\">）。此时间不随镜像更新而改变，可用于判断镜像版本的新旧程度。</font></p> <p><strong><font style=\\\"color:#020817\\\">镜像大小 (SIZE)</font></strong><font style=\\\"color:#020817\\\"> 表示镜像的虚拟存储空间，包含所有分层文件的总和。由于 Docker 采用分层存储机制，不同镜像可能共享基础层，因此实际磁盘占用通常小于各镜像 SIZE 的累加值（例如多个镜像共享同一 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">Ubuntu</font><font style=\\\"color:#020817\\\"> 基础层时）。</font></p> <h5 id=\\\"第二步：执行标签添加命令\\\"><strong><font style=\\\"color:#020817\\\">第二步：执行标签添加命令</font></strong></h5> <p><strong><font style=\\\"color:#020817\\\">命令格式</font></strong><font style=\\\"color:#020817\\\">：</font></p> <pre><code class=\\\"language-plain\\\">docker tag &lt;原镜像名称&gt;:&lt;原标签&gt; harbor.suanleme.cn/&lt;你的账户&gt;/&lt;镜像名称&gt;:&lt;自定义标签&gt;\\n</code></pre> <p><strong><font style=\\\"color:#020817\\\">参数解释</font></strong><font style=\\\"color:#020817\\\">：</font></p> <ul> <li><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">&lt;原镜像名称&gt;:&lt;原标签&gt;</font><font style=\\\"color:#020817\\\">：本地已有的镜像名称和标签（如 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">my-image:latest</font><font style=\\\"color:#020817\\\">）</font></li> <li><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">&lt;你的账户&gt;</font><font style=\\\"color:#020817\\\">：登录镜像仓库的账号（需在控制台 </font><strong><font style=\\\"color:#020817\\\">[镜像仓库 &gt; 访问凭证]</font></strong><font style=\\\"color:#020817\\\"> 中确认）</font></li> <li><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">&lt;镜像名称&gt;</font><font style=\\\"color:#020817\\\">：推送到仓库后的镜像名称（可自定义，建议与本地镜像同名）</font></li> <li><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">&lt;自定义标签&gt;</font><font style=\\\"color:#020817\\\">：镜像版本标识（如 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">v1.0</font><font style=\\\"color:#020817\\\">、</font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">prod</font><font style=\\\"color:#020817\\\">）</font></li> </ul> <hr> <h5 id=\\\"第三步：具体操作示例\\\"><strong><font style=\\\"color:#020817\\\">第三步：具体操作示例</font></strong></h5> <p><font style=\\\"color:#020817\\\">假设你的账户是 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">xiditgkw</font><font style=\\\"color:#020817\\\">，本地镜像为 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">my-image:latest</font><font style=\\\"color:#020817\\\">，目标标签设为 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">v1.0</font><font style=\\\"color:#020817\\\">：</font></p> <pre><code class=\\\"language-plain\\\">docker tag my-image:latest harbor.suanleme.cn/xiditgkw/my-image:v1.0\\n</code></pre> <p><font style=\\\"color:#020817\\\">验证是否成功：</font></p> <pre><code class=\\\"language-plain\\\">docker images\\n</code></pre> <p><font style=\\\"color:#020817\\\">输出中应出现新标签的镜像：</font></p> <pre><code class=\\\"language-plain\\\">REPOSITORY                            TAG       IMAGE ID       CREATED         SIZE\\nmy-image                              latest    a1b2c3d4e5f6   2 hours ago     1.2GB\\nharbor.suanleme.cn/xiditgkw/my-image v1.0      a1b2c3d4e5f6   2 hours ago     1.2GB\\n</code></pre> <hr> <h4 id=\\\"223-关键注意事项\\\"><strong><font style=\\\"color:#020817\\\">2.2.3 关键注意事项</font></strong></h4> <ol> <li><strong><font style=\\\"color:#020817\\\">账户名必须精确匹配</font></strong><font style=\\\"color:#020817\\\"> </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">&lt;你的账户&gt; </font><font style=\\\"color:#020817\\\">必须与镜像仓库控制台中显示的</font><strong><font style=\\\"color:#020817\\\">登录账号</font></strong><font style=\\\"color:#020817\\\">完全一致（区分大小写）</font></li> </ol> <ul> <li><font style=\\\"color:#020817\\\">可通过控制台</font><font style=\\\"color:#020817\\\"> </font><strong><font style=\\\"color:#020817\\\">[镜像仓库 &gt; 访问凭证]</font></strong><font style=\\\"color:#020817\\\"> </font><font style=\\\"color:#020817\\\">查看确认账号</font></li> </ul> <ol start=\\\"2\\\"> <li><strong><font style=\\\"color:#020817\\\">镜像层级结构规则</font></strong></li> </ol> <ul> <li><font style=\\\"color:#020817\\\">完整路径格式：</font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">harbor.suanleme.cn/&lt;账户&gt;/&lt;项目&gt;/&lt;镜像名&gt;:&lt;标签&gt;</font></li> </ul> <p><font style=\\\"color:#020817\\\">如果仓库有项目层级（如团队协作），需包含项目名：</font></p> <p><font style=\\\"color:#020817\\\">例如：</font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">docker tag my-image harbor.suanleme.cn/xiditgkw/project-a/my-image:v1.0</font></p> <ol start=\\\"3\\\"> <li><strong><font style=\\\"color:#020817\\\">标签命名建议</font></strong><font style=\\\"color:#020817\\\"> - 使用语义化版本（如 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">v1.0.2</font><font style=\\\"color:#020817\\\">）</font></li> </ol> <ul> <li><font style=\\\"color:#020817\\\">避免使用默认的</font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">latest</font><font style=\\\"color:#020817\\\">标签（易导致版本混乱）</font></li> <li><font style=\\\"color:#020817\\\">可包含环境标识（如</font><font style=\\\"color:#2f8ef4\\\"> </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">dev</font><font style=\\\"color:#020817\\\">、</font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">prod</font><font style=\\\"color:#020817\\\">）</font></li> </ul> <hr> <h4 id=\\\"224-常见问题排查\\\"><strong><font style=\\\"color:#020817\\\">2.2.4 常见问题排查</font></strong></h4> <ul> <li><strong><font style=\\\"color:#020817\\\">错误提示 &quot;Repository does not exist&quot;</font></strong><font style=\\\"color:#020817\\\"> </font><font style=\\\"color:#020817\\\">➠ 检查账户名是否拼写错误 ➠ 确认仓库中是否已手动创建对应项目（部分平台需先创建仓库目录）</font></li> <li><strong><font style=\\\"color:#020817\\\">镜像列表未显示新标签</font></strong><font style=\\\"color:#020817\\\"> ➠ 确保 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">docker tag</font><font style=\\\"color:#52b788;background-color:rgba(142,150,170,.14)\\\"> </font><font style=\\\"color:#020817\\\">命令参数顺序正确 ➠ 验证原镜像是否存在（通过 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">docker images</font><font style=\\\"color:#020817\\\">）</font></li> <li><strong><font style=\\\"color:#020817\\\">推送时提示权限不足</font></strong><font style=\\\"color:#020817\\\"> ➠ 重新执行 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">docker login</font><font style=\\\"color:#52b788;background-color:rgba(142,150,170,.14)\\\"> </font><font style=\\\"color:#020817\\\">确保登录状态 ➠ 检查账户是否有该仓库路径的写入权限</font></li> </ul> <hr> <h3 id=\\\"23-步骤-3-推送镜像\\\"><font style=\\\"color:#020817\\\">2.3 步骤 3: 推送镜像</font></h3> <p><font style=\\\"color:#020817\\\">标签添加完成后，使用以下命令推送镜像：</font></p> <pre><code class=\\\"language-plain\\\">docker push harbor.suanleme.cn/&lt;your-account&gt;/my-image:my-tag\\n</code></pre> <p><font style=\\\"color:#020817\\\">参数解释：</font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">docker push harbor.suanleme.cn/<your-account>/my-image:my-tag</your-account></font></p> <ul> <li><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">harbor.suanleme.cn</font><font style=\\\"color:#020817\\\"> 镜像仓库的域名地址，指向天工开物算力平台的私有镜像仓库存储服务。</font></li> <li><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\"><your-account></your-account></font><font style=\\\"color:#020817\\\"> 你的仓库账号（需替换为实际账号）。 ➠ 通过控制台 </font><strong><font style=\\\"color:#020817\\\">[镜像仓库 &gt; 访问凭证]</font></strong><font style=\\\"color:#020817\\\"> 查看确认账号名称（区分大小写）。</font></li> <li><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">my-image</font><font style=\\\"color:#2f8ef4\\\"> </font><font style=\\\"color:#020817\\\">推送到仓库后的镜像名称，支持以下两种形式：</font><ul> <li><strong><font style=\\\"color:#020817\\\">直接命名</font></strong><font style=\\\"color:#020817\\\">：与本地镜像同名（如 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">my-image</font><font style=\\\"color:#020817\\\">）</font></li> <li><strong><font style=\\\"color:#020817\\\">层级命名</font></strong><font style=\\\"color:#020817\\\">：包含项目/分类（如 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">project-a/my-image</font><font style=\\\"color:#020817\\\">，需提前在仓库创建对应目录）</font></li> </ul> </li> <li><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">my-tag</font><font style=\\\"color:#020817\\\"> 镜像的版本标签，用于标识不同版本或环境：</font><ul> <li><font style=\\\"color:#020817\\\">示例：</font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">v1.0</font><font style=\\\"color:#020817\\\">（语义化版本）、</font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">prod</font><font style=\\\"color:#020817\\\">（生产环境）、</font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">20251001</font><font style=\\\"color:#020817\\\">（日期版本）</font></li> <li><font style=\\\"color:#020817\\\">避免使用默认的 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">latest</font><font style=\\\"color:#2f8ef4\\\"> </font><font style=\\\"color:#020817\\\">标签（易导致版本混乱）。</font></li> </ul> </li> </ul> <p><font style=\\\"color:#020817\\\">在推送过程中，会显示上传进度。当推送成功后，可以在“镜像仓库”页面查看该镜像。如果镜像过大，那么推送所需时间可能会比较长。</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_4___ + \"\\\" alt=\\\"\\\"></p> <hr> <h2 id=\\\"3-阿里云容器镜像服务（acr）使用指南\\\"><strong><font style=\\\"color:#020817\\\">3. 阿里云容器镜像服务（ACR）使用指南</font></strong></h2> <h3 id=\\\"31-服务准备与实例选择\\\"><font style=\\\"color:#020817\\\">3.1 服务准备与实例选择</font></h3> <ol> <li><strong><font style=\\\"color:#020817\\\">实例类型选择</font></strong></li> </ol> <ul> <li><strong><font style=\\\"color:#020817\\\">企业版</font></strong><font style=\\\"color:#020817\\\">：适用于生产环境，支持多地域分发、安全扫描、镜像加签、全球同步加速、P2P 分发等功能，提供 99.95% SLA 保障。推荐选择标准版或高级版以支持交付链与大规模分发。</font></li> <li><strong><font style=\\\"color:#020817\\\">个人版</font></strong><font style=\\\"color:#020817\\\">：仅限测试或小规模使用，无 SLA 保障，功能受限（如不支持 Helm Chart 管理）。</font></li> </ul> <ol start=\\\"2\\\"> <li><strong><font style=\\\"color:#020817\\\">创建实例</font></strong></li> </ol> <ul> <li><strong><font style=\\\"color:#020817\\\">操作路径</font></strong><font style=\\\"color:#020817\\\">：登录</font><a href=\\\"https://www.aliyun.com/product/acr\\\"><font style=\\\"color:#2f8ef4\\\">容器镜像服务 ACR</font></a><font style=\\\"color:#020817\\\">，选择地域后创建实例，需配置实例名称、存储类型（默认 OSS 或自定义 Bucket）、安全扫描引擎（Trivy 或云安全引擎）及资源配额。</font></li> <li><strong><font style=\\\"color:#020817\\\">注意事项</font></strong><font style=\\\"color:#020817\\\">：</font><ul> <li><font style=\\\"color:#020817\\\">企业版需绑定 OSS 服务并设置 VPC 访问控制配额。</font></li> <li><font style=\\\"color:#020817\\\">命名空间与仓库数量受配额限制，建议按团队或项目划分命名空间。</font><img src=\\\"\" + ___HTML_LOADER_IMPORT_5___ + \"\\\" alt=\\\"\\\"><strong><font style=\\\"color:#020817\\\">企业版：</font></strong><font style=\\\"color:#020817\\\">容器镜像服务</font><img src=\\\"\" + ___HTML_LOADER_IMPORT_6___ + \"\\\" alt=\\\"\\\"></li> </ul> </li> </ul> <h3 id=\\\"32-镜像仓库配置\\\"><font style=\\\"color:#020817\\\">3.2 镜像仓库配置</font></h3> <p><strong><font style=\\\"color:#020817\\\">官方教程：阿里云【试用教程】在 Dockerfile 中使用构建打包镜像并运行</font></strong></p> <ol> <li><strong><font style=\\\"color:#020817\\\">命名空间与仓库创建</font></strong><ol> <li><strong><font style=\\\"color:#020817\\\">命名空间</font></strong><font style=\\\"color:#020817\\\">：逻辑隔离单元，建议按组织命名（如</font><font style=\\\"color:#2f8ef4\\\"> </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">dev-team</font><font style=\\\"color:#020817\\\">），一个账号最多创建 3 个（个人版）或更多（企业版）。</font> <img src=\\\"\" + ___HTML_LOADER_IMPORT_7___ + \"\\\" alt=\\\"\\\"><img src=\\\"\" + ___HTML_LOADER_IMPORT_8___ + \"\\\" alt=\\\"\\\"></li> <li><strong><font style=\\\"color:#020817\\\">镜像仓库</font></strong><font style=\\\"color:#020817\\\">：</font><font style=\\\"color:#020817\\\"> </font><strong><font style=\\\"color:#020817\\\">创建步骤</font></strong><font style=\\\"color:#020817\\\">：在控制台选择命名空间后，填写仓库名称、类型（私有/公开）、代码源（本地仓库或 Git 平台），并配置构建设置（自动构建、缓存策略）。</font></li> <li><strong><font style=\\\"color:#020817\\\">命名规范</font></strong><font style=\\\"color:#020817\\\">：需包含完整路径（如 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">registry.cn-hangzhou.aliyuncs.com/&lt;命名空间&gt;/&lt;仓库名&gt;:&lt;标签&gt;</font><font style=\\\"color:#020817\\\">），标签建议使用语义化版本（如</font><font style=\\\"color:#2f8ef4\\\"> </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">v1.0-prod</font><font style=\\\"color:#020817\\\">）。</font><img src=\\\"\" + ___HTML_LOADER_IMPORT_9___ + \"\\\" alt=\\\"\\\"></li> </ol> </li> <li><strong><font style=\\\"color:#020817\\\">访问控制</font></strong></li> </ol> <ul> <li><strong><font style=\\\"color:#020817\\\">公网/VPC 访问</font></strong><font style=\\\"color:#020817\\\">：企业版需在控制台开启公网入口并配置白名单，或通过专有网络实现内网加速。</font></li> <li><strong><font style=\\\"color:#020817\\\">权限管理</font></strong><font style=\\\"color:#020817\\\">：通过 RAM 子账号授权，区分命名空间管理员（全权限）、普通成员（读写）和只读用户。</font></li> </ul> <h3 id=\\\"33-镜像操作指南\\\"><font style=\\\"color:#020817\\\">3.3 镜像操作指南</font></h3> <ol> <li><strong><font style=\\\"color:#020817\\\">登录凭证配置</font></strong></li> </ol> <ul> <li><strong><font style=\\\"color:#020817\\\">凭证类型</font></strong><font style=\\\"color:#020817\\\">：支持阿里云账号 AccessKey 或临时密码，企业版需在控制台[访问凭证]页面设置固定密码。</font></li> </ul> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_10___ + \"\\\" alt=\\\"\\\"></p> <ul> <li><strong><font style=\\\"color:#020817\\\">登录命令</font></strong><font style=\\\"color:#020817\\\">：</font></li> </ul> <pre><code class=\\\"language-plain\\\">docker login --username=&lt;账号名&gt; registry.&lt;region&gt;.aliyuncs.com\\n</code></pre> <p><font style=\\\"color:#020817\\\">输入密码后显示 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">Login Succeeded</font><font style=\\\"color:#020817\\\"> 即为成功。</font></p> <ol start=\\\"2\\\"> <li><strong><font style=\\\"color:#020817\\\">镜像推送与拉取</font></strong></li> </ol> <ul> <li><strong><font style=\\\"color:#020817\\\">标签与推送</font></strong><font style=\\\"color:#020817\\\">：</font></li> </ul> <pre><code class=\\\"language-plain\\\">docker tag &lt;本地镜像&gt; registry.cn-hangzhou.aliyuncs.com/&lt;命名空间&gt;/&lt;仓库名&gt;:&lt;标签&gt;\\ndocker push registry.cn-hangzhou.aliyuncs.com/&lt;命名空间&gt;/&lt;仓库名&gt;:&lt;标签&gt;\\n</code></pre> <ul> <li><strong><font style=\\\"color:#020817\\\">拉取镜像</font></strong><font style=\\\"color:#020817\\\">：</font></li> </ul> <pre><code class=\\\"language-plain\\\">docker pull registry.cn-hangzhou.aliyuncs.com/&lt;命名空间&gt;/&lt;仓库名&gt;:&lt;标签&gt;\\n</code></pre> <ul> <li><strong><font style=\\\"color:#020817\\\">注意事项</font></strong><font style=\\\"color:#020817\\\">：企业版实例名称需替换为 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">企业版实例名称-region.cr.aliyuncs.com</font><font style=\\\"color:#020817\\\">。</font></li> </ul> <ol start=\\\"3\\\"> <li><strong><font style=\\\"color:#020817\\\">多架构镜像构建</font></strong></li> </ol> <ul> <li><strong><font style=\\\"color:#020817\\\">适用场景</font></strong><font style=\\\"color:#020817\\\">：需支持 x86、ARM 等不同架构。</font></li> <li><strong><font style=\\\"color:#020817\\\">操作步骤</font></strong><font style=\\\"color:#020817\\\">：</font> 1. <font style=\\\"color:#020817\\\">在代码仓库配置多架构构建规则（如 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">linux/amd64</font><font style=\\\"color:#020817\\\"> 和 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">linux/arm64</font><font style=\\\"color:#020817\\\">）。</font> 2. <font style=\\\"color:#020817\\\">触发构建后，同一标签将包含多架构镜像，客户端自动拉取适配版本。</font></li> </ul> <hr> <h3 id=\\\"34-高级功能与优化\\\"><font style=\\\"color:#020817\\\">3.4 高级功能与优化</font></h3> <ol> <li><strong><font style=\\\"color:#020817\\\">镜像加速器配置</font></strong></li> </ol> <ul> <li><strong><font style=\\\"color:#020817\\\">作用</font></strong><font style=\\\"color:#020817\\\">：加速 Docker Hub 官方镜像拉取，需在 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">/etc/docker/daemon.json </font><font style=\\\"color:#020817\\\">中添加加速器地址（如 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">https://&lt;加速器地址&gt;.mirror.aliyuncs.com</font><font style=\\\"color:#020817\\\">）并重启 Docker 服务。</font></li> <li><strong><font style=\\\"color:#020817\\\">限制</font></strong><font style=\\\"color:#020817\\\">：仅限阿里云公网产品使用，无法保证拉取最新版本，建议指定镜像版本。</font></li> </ul> <ol start=\\\"2\\\"> <li><strong><font style=\\\"color:#020817\\\">安全与合规</font></strong></li> </ol> <ul> <li><strong><font style=\\\"color:#020817\\\">安全扫描</font></strong><font style=\\\"color:#020817\\\">：企业版支持自动扫描漏洞（CVE、恶意代码），并生成修复建议。</font></li> <li><strong><font style=\\\"color:#020817\\\">镜像加签</font></strong><font style=\\\"color:#020817\\\">：通过密钥对镜像签名，防止篡改，可配置自动加签规则。</font></li> <li><strong><font style=\\\"color:#020817\\\">操作审计</font></strong><font style=\\\"color:#020817\\\">：记录所有镜像操作日志，便于追溯与合规审查。</font></li> </ul> <ol start=\\\"3\\\"> <li><strong><font style=\\\"color:#020817\\\">全球分发与同步</font></strong></li> </ol> <ul> <li><strong><font style=\\\"color:#020817\\\">多地域复制</font></strong><font style=\\\"color:#020817\\\">：企业版支持一键同步镜像至全球地域，通过内网专线降低延迟。</font></li> <li><strong><font style=\\\"color:#020817\\\">P2P 加速</font></strong><font style=\\\"color:#020817\\\">：千节点级分发时启用 P2P 技术，提升效率 4 倍以上。</font></li> </ul> <ol start=\\\"4\\\"> <li><strong><font style=\\\"color:#020817\\\">生命周期管理</font></strong></li> </ol> <ul> <li><strong><font style=\\\"color:#020817\\\">自动清理策略</font></strong><font style=\\\"color:#020817\\\">：设置保留规则（如保留最近 10 个版本），自动清理旧镜像释放存储空间。</font></li> <li><strong><font style=\\\"color:#020817\\\">交付链集成</font></strong><font style=\\\"color:#020817\\\">：高级版支持构建→扫描→加签→分发的自动化流水线，减少人工干预。</font></li> </ul> <hr> <h3 id=\\\"35-常见问题与排查\\\"><font style=\\\"color:#020817\\\">3.5 常见问题与排查</font></h3> <p><strong><font style=\\\"color:#020817\\\">问题现象：unauthorized或权限不足</font></strong></p> <ul> <li><strong><font style=\\\"color:#020817\\\">原因</font></strong><font style=\\\"color:#020817\\\">：Docker 客户端未携带有效身份凭证，或用户对目标仓库无操作权限。</font></li> <li><strong><font style=\\\"color:#020817\\\">解决方案</font></strong><font style=\\\"color:#020817\\\">：</font><ol> <li><font style=\\\"color:#020817\\\">执行 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">docker logout</font><font style=\\\"color:#020817\\\">退出当前登录状态，重新运行</font><font style=\\\"color:#2f8ef4\\\"> </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">docker login</font><font style=\\\"color:#2f8ef4\\\"> </font><font style=\\\"color:#020817\\\">输入账户密码。</font></li> <li><font style=\\\"color:#020817\\\">若使用私有仓库，需在登录时指定仓库地址（如</font><font style=\\\"color:#2f8ef4\\\"> </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">docker login harbor.example.com</font><font style=\\\"color:#020817\\\">）。</font></li> <li><font style=\\\"color:#020817\\\">检查账户是否被移出仓库权限组，联系管理员确认访问权限。</font></li> </ol> </li> </ul> <p><strong><font style=\\\"color:#020817\\\">问题现象：推送失败（Repository does not exist）</font></strong></p> <ul> <li><strong><font style=\\\"color:#020817\\\">原因</font></strong><font style=\\\"color:#020817\\\">：镜像名称中的命名空间或仓库路径不符合仓库规则，或目标仓库尚未创建。</font></li> <li><strong><font style=\\\"color:#020817\\\">解决方案</font></strong><font style=\\\"color:#020817\\\">：</font><ol> <li><font style=\\\"color:#020817\\\">镜像名称需严格遵循</font><font style=\\\"color:#2f8ef4\\\"> </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">仓库域名/命名空间/仓库名:标签</font><font style=\\\"color:#2f8ef4\\\"> </font><font style=\\\"color:#020817\\\">格式（如</font><font style=\\\"color:#2f8ef4\\\"> </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">harbor.com/dev-team/myapp:v1</font><font style=\\\"color:#020817\\\">）。</font></li> <li><font style=\\\"color:#020817\\\">通过仓库管理界面手动创建同名仓库目录（部分私有仓库要求先创建空仓库）。</font></li> <li><font style=\\\"color:#020817\\\">确认账户对目标仓库有</font><font style=\\\"color:#2f8ef4\\\"> </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">push</font><font style=\\\"color:#020817\\\">权限（权限不足时会伪装为“仓库不存在”错误）。</font></li> </ol> </li> </ul> <p><strong><font style=\\\"color:#020817\\\">问题现象：镜像拉取超时</font></strong></p> <ul> <li><strong><font style=\\\"color:#020817\\\">原因</font></strong><font style=\\\"color:#020817\\\">：网络连接不稳定或跨国访问公共仓库速度受限。</font></li> <li><strong><font style=\\\"color:#020817\\\">解决方案</font></strong><font style=\\\"color:#020817\\\">：</font><ol> <li><strong><font style=\\\"color:#020817\\\">配置镜像加速器</font></strong><font style=\\\"color:#020817\\\">：</font><ul> <li><font style=\\\"color:#020817\\\">修改 Docker 配置文件 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">/etc/docker/daemon.json</font><font style=\\\"color:#020817\\\">，添加国内镜像源（如阿里云、腾讯云镜像加速地址）。</font></li> <li><font style=\\\"color:#020817\\\">执行 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">systemctl reload docker</font><font style=\\\"color:#020817\\\"> 重启服务生效。</font></li> </ul> </li> <li><strong><font style=\\\"color:#020817\\\">启用企业版加速功能</font></strong><font style=\\\"color:#020817\\\">：</font><ul> <li><font style=\\\"color:#020817\\\">私有化部署仓库（如 Harbor）可开启 P2P 分发或全球节点同步功能。</font></li> </ul> </li> <li><font style=\\\"color:#020817\\\">临时切换网络环境测试（如从公司内网切换至公网）。</font></li> </ol> </li> </ul> <p><strong><font style=\\\"color:#020817\\\">问题现象：安全扫描报错</font></strong></p> <ul> <li><strong><font style=\\\"color:#020817\\\">原因</font></strong><font style=\\\"color:#020817\\\">：镜像包含高危漏洞或依赖项版本过低。</font></li> <li><strong><font style=\\\"color:#020817\\\">解决方案</font></strong><font style=\\\"color:#020817\\\">：</font><ol> <li><font style=\\\"color:#020817\\\">使用</font><font style=\\\"color:#2f8ef4\\\"> </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">docker scan 镜像名</font><font style=\\\"color:#020817\\\"> 运行安全扫描，查看具体漏洞详情。</font></li> <li><font style=\\\"color:#020817\\\">升级 Dockerfile 中 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">FROM</font><font style=\\\"color:#020817\\\">指定的基础镜像版本（如 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">FROM alpine:3.19</font><font style=\\\"color:#020817\\\">替代 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">alpine:3.10</font><font style=\\\"color:#020817\\\">）。</font></li> <li><font style=\\\"color:#020817\\\">使用阿里云容器镜像服务提供的「安全镜像」，自动集成漏洞修复补丁。</font></li> <li><font style=\\\"color:#020817\\\">在 CI/CD 流程中集成 Trivy 或 Clair 漏洞扫描工具，阻断不安全镜像流入生产环境。</font></li> </ol> </li> </ul> <hr> <h3 id=\\\"36-最佳实践建议\\\"><font style=\\\"color:#020817\\\">3.6 最佳实践建议</font></h3> <ol> <li><strong><font style=\\\"color:#020817\\\">生产环境规范</font></strong><font style=\\\"color:#020817\\\">：</font></li> </ol> <ul> <li><font style=\\\"color:#020817\\\">使用企业版并启用安全扫描、加签与访问控制。</font></li> <li><font style=\\\"color:#020817\\\">避免使用</font><font style=\\\"color:#2f8ef4\\\"> </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">latest</font><font style=\\\"color:#020817\\\"> 标签，采用语义化版本管理。</font></li> </ul> <ol start=\\\"2\\\"> <li><strong><font style=\\\"color:#020817\\\">性能优化</font></strong><font style=\\\"color:#020817\\\">：</font></li> </ol> <ul> <li><font style=\\\"color:#020817\\\">内网推送使用 VPC 域名（如 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">registry-vpc.cn-hangzhou.aliyuncs.com</font><font style=\\\"color:#020817\\\">）以提升速度。</font></li> <li><font style=\\\"color:#020817\\\">大镜像（如 AI 模型）启用按需加载功能，减少启动时间 60%。</font></li> </ul> <p><font style=\\\"color:#020817\\\">通过以上步骤，可高效利用 ACR 实现镜像全生命周期管理，兼顾安全性与运维效率。如需进一步探索 API 集成或自定义认证，可参考</font><a href=\\\"https://help.aliyun.com/zh/acr\\\"><font style=\\\"color:#2f8ef4\\\">ACR 官方文档</font></a><font style=\\\"color:#020817\\\">。</font></p> <p><br><br> <font style=\\\"color:#b2b2b2\\\">最后更新于: 2025/6/9 17:00</font></p> \";\n// Exports\nexport default code;"], "names": ["___HTML_LOADER_IMPORT_0___", "URL", "___HTML_LOADER_IMPORT_1___", "___HTML_LOADER_IMPORT_2___", "___HTML_LOADER_IMPORT_3___", "___HTML_LOADER_IMPORT_4___", "___HTML_LOADER_IMPORT_5___", "___HTML_LOADER_IMPORT_6___", "___HTML_LOADER_IMPORT_7___", "___HTML_LOADER_IMPORT_8___", "___HTML_LOADER_IMPORT_9___", "___HTML_LOADER_IMPORT_10___", "code"], "sourceRoot": ""}