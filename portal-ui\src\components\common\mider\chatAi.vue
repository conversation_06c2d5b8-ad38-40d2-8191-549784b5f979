<template>
    <div >
<!--        问题轮播-->
        <!-- 悬浮客服容器 -->
        <div class="chat-container">
            <!-- 问题轮播区 -->
            <div class="question-carousel"
                 @mouseenter="pauseCarousel"
                 @mouseleave="resumeCarousel">
                <transition-group name="slide" tag="div" class="carousel-wrapper">
                    <div v-for="(question, index) in questions"
                         :key="question"
                         class="question-item"
                         v-show="currentQuestionIndex === index"
                         @click="sendCarouselQuestion(question)"
                         @mouseenter="witde(index)"
                    >
                        {{ question }}
                    </div>
                </transition-group>
            </div>

            <!-- 原有悬浮按钮 -->
            <div class="chat-icon"
                 :class="{ 'chat-icon-active': showChat }"
                 @click="toggleChat">
                <i class="fas fa-comment"></i>
            </div>
        </div>

        <!-- 聊天窗口 -->
        <div class="chat-window" v-show="showChat">
            <div class="chat-header">
                <div class="chat-title">
                    <i class="fas fa-robot"></i>
                    <span>智能客服</span>
                </div>
                <div class="chat-controls">
                    <i class="fas fa-times" @click="toggleChat"></i>
                </div>
            </div>

            <div class="chat-messages" ref="messagesContainer">
                <div
                        v-for="(message, index) in messages"
                        :key="index"
                        :class="['message', message.type]"
                >
                    <div class="avatar" v-if="message.type === 'bot'">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="message-content">
                        <div class="message-text" v-html="formatMessage(message.text)"></div>
                        <div class="message-time">{{ formatTime(message.time) }}</div>
                    </div>
                </div>
                <div class="typing-indicator" v-if="loading">
                    <div class="dot"></div>
                    <div class="dot"></div>
                    <div class="dot"></div>
                </div>
            </div>

            <div class="chat-input">
                <input
                        type="text"
                        v-model="userInput"
                        placeholder="请输入您的问题..."
                        @keyup.enter="sendMessage"
                        :disabled="loading"
                />
                <button @click="sendMessage" :disabled="loading || !userInput.trim()">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
    </div>
</template>

<script>
    export default {
        name: 'chatAi',

        data() {
            return {
                showChat: false,
                userInput: '',
                messages: [
                    {
                        type: 'bot',
                        text: '您好！我是智能客服助手，有什么可以帮您？',
                        time: new Date()
                    }
                ],
                loading: false,
                historyMessages:[],
                questions: [
                    "如何租赁GPU算力？",
                    "支持哪些支付方式？",
                    "如何查看订单状态？"
                ],
                currentQuestionIndex: 0,
                carouselTimer: null,
                carouselInterval: 3000,
                isPaused: false
            }
        },
        beforeDestroy() {
            this.clearCarousel()
        },
        mounted() {
            this.startCarousel();
            // 导入 Font Awesome 图标库
            if (!document.getElementById('font-awesome')) {
                const link = document.createElement('link');
                link.id = 'font-awesome';
                link.rel = 'stylesheet';
                link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css';
                document.head.appendChild(link);
            }
        },

        methods: {
            witde(index){
                this.currentQuestionIndex = index;
                this.pauseCarousel();
            },
            startCarousel() {
                let that = this
                this.clearCarousel();
                this.carouselTimer = setInterval(() => {
                    if (!that.isPaused) {
                        this.currentQuestionIndex =
                            (this.currentQuestionIndex + 1) % this.questions.length
                        console.log("数据", this.currentQuestionIndex)
                        console.log("ispasued",that.isPaused)
                    }
                }, this.carouselInterval)
            },
            pauseCarousel() {
                this.isPaused = true;
            },
            resumeCarousel() {
                this.isPaused = false;
            },
            clearCarousel() {
                if (this.carouselTimer) {
                    clearInterval(this.carouselTimer);
                    this.carouselTimer = null;
                }
            },
            // 点击轮播问题自动提问
            sendCarouselQuestion(question) {
                this.userInput = question;
                this.sendMessage();
            },
            toggleChat() {
                this.showChat = !this.showChat;

                if (this.showChat) {
                    this.$nextTick(() => {
                        this.scrollToBottom();
                    });
                }
            },

            async sendMessage() {
                if (!this.userInput.trim() || this.loading) return;

                // 添加用户消息
                this.messages.push({
                    type: 'user',
                    text: this.userInput,
                    time: new Date()
                });

                const userQuestion = this.userInput;
                this.userInput = '';
                this.loading = true;
                //添加历史记录
                this.historyMessages.push({
                    role:'user',
                    content:userQuestion,
                })

                // 构造请求体
                const requestBody = {
                    model: 'Qwen/QwQ-32B',
                    messages: [{role:"system",content: "你是天工开物算力租赁网站的客服，当我问到算力租赁相关问题的时候再进行回答，如果我的问题与算力无关则回答“小天也不知道怎么办了呢，不如我们来聊聊算力相关的话题吧！”,如果问题涉及到你是谁或者你好的这种礼貌性踊跃，可以进行礼貌性的回复"},...this.historyMessages], // 携带上下文历史
                    stream: true,
                    options: {
                        presence_penalty: 1.2,  // 重复内容惩罚（0-2）
                        frequency_penalty: 1.5, // 高频词惩罚（0-2）
                        // repeat_last_n: 64,      // 检查重复的上下文长度
                        seed: 12345             // 固定随机种子
                    }
                };
                // 滚动到底部
                this.$nextTick(() => {
                    this.scrollToBottom();
                });

                try {
                    // 调用后端API获取回复
                    // 替换为你的实际API
                    const response = await fetch('https://api.siliconflow.cn/v1/chat/completions', {
                        method: 'POST',
                        headers: {
                            Authorization: 'Bearer sk-yxjtnfarjndtcwbjwmscdejrgtlpaaebnfdxcfnhhuanbqty',
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(requestBody)
                    });

                    // 处理流式数据
                    const reader = response.body.getReader();
                    const decoder = new TextDecoder();
                    const aiResponseIndex = this.messages.push({
                        type: 'bot',
                        text: '',
                        time:new Date()
                    }) - 1;
                    while (true) {
                        const { done, value } = await reader.read();
                        if (done) break;

                        // 解析流式数据块（可能包含多个JSON对象）
                        const chunk = decoder.decode(value);
                        const lines = chunk.split('\n').filter(line => line.trim());

                        for (const line of lines) {
                            try {
                                const jsonString = line.slice(6).trim();
                                if (jsonString === "" || jsonString === "[DONE]") continue;

                                let data = JSON.parse(jsonString)
                                if (data.choices) {
                                    if (data.choices[0].delta.reasoning_content!=null){
                                        continue
                                    }
                                    if (data.choices[0].delta.content == '\n\n'){
                                        continue
                                    }
                                    this.messages[aiResponseIndex].text += data.choices[0].delta.content;
                                }
                            } catch (e) {
                            }
                        }
                    }
                    this.historyMessages.push({
                        role:"assistant",
                        content:this.messages[aiResponseIndex].text
                    })

                    // 添加机器人回复
                    // this.messages.push({
                    //     type: 'bot',
                    //     text: response,
                    //     time: new Date()
                    // });
                } catch (error) {

                    // 添加错误消息
                    this.messages.push({
                        type: 'bot',
                        text: '抱歉，系统暂时无法响应，请稍后再试。',
                        time: new Date()
                    });
                } finally {
                    this.loading = false;

                    // 滚动到底部
                    this.$nextTick(() => {
                        this.scrollToBottom();
                    });
                }
            },

            // 模拟API调用，实际使用时替换为真实API
            async callChatAPI(message) {
                // 模拟网络延迟
                await new Promise(resolve => setTimeout(resolve, 1000));

                // TODO: 替换为实际的API调用
                // const response = await fetch('YOUR_API_ENDPOINT', {
                //   method: 'POST',
                //   headers: {
                //     'Content-Type': 'application/json',
                //   },
                //   body: JSON.stringify({ message }),
                // });
                // return await response.json();

                // 模拟返回数据
                return `感谢您的提问: "${message}"。这是一个模拟回复，请替换为真实API调用。`;
            },

            scrollToBottom() {
                const container = this.$refs.messagesContainer;
                container.scrollTop = container.scrollHeight;
            },

            formatTime(date) {
                return new Date(date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
            },

            formatMessage(text) {
                // 处理文本中的链接、表情等
                return text
                    .replace(/\n/g, '<br>')
                    .replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank">$1</a>');
            }
        }
    }
</script>

<style scoped>
    /* 修正后的轮播样式 */
    .chat-container {
        position: fixed;
        bottom: 20px;
        right: 20px;
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        gap: 10px;
        z-index: 1001;
    }

    .question-carousel {
        left: 20px;
        padding-bottom: 85px;
        padding-top: 20px;
        color: white;
        min-width: 150px;
        text-align: left;
        cursor: pointer;
        overflow: hidden;
        position: relative;
        height: 60px; /* 固定高度避免跳动 */
    }

    .carousel-wrapper {
        position: relative;
        height: 100%;
    }

    .question-item {
        position: absolute;
        border-radius: 20px 20px 20px 20px;
        background-color: black;
        width: 100%;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        padding: 10px 10px;
        font-size: 14px;
        opacity: 1;
        transition: all 0.5s ease;
    }

    .question-item:hover {
        color: #4286f4;
    }

    /* 过渡动画修正 */
    .slide-enter-active,
    .slide-leave-active {
        transition: all 0.5s ease;
    }
    .slide-enter-from {
        opacity: 0;
        transform: translateY(20px) translateY(-50%);
    }
    .slide-leave-to {
        opacity: 0;
        transform: translateY(-20px) translateY(-50%);
    }
    .slide-enter-to,
    .slide-leave-from {
        opacity: 1;
        transform: translateY(0) translateY(-50%);
    }
    .chat-icon {
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 60px;
        height: 60px;
        background-color: blue;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        transition: all 0.3s ease;
        z-index: 1001;
    }

    .chat-icon i {
        color: white;
        font-size: 24px;
    }

    .chat-icon:hover, .chat-icon-active {
        background-color: #3367d6;
        transform: scale(1.05);
    }

    .chat-window {
        position: fixed;
        bottom: 90px;
        right: 20px;
        width: 350px;
        height: 500px;
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 5px 40px rgba(0, 0, 0, 0.16);
        display: flex;
        flex-direction: column;
        overflow: hidden;
        z-index: 1002;
    }

    .chat-header {
        padding: 15px;
        background-color: #4286f4;
        color: white;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .chat-title {
        display: flex;
        align-items: center;
        gap: 10px;
        font-weight: bold;
    }

    .chat-controls i {
        cursor: pointer;
        font-size: 18px;
    }

    .chat-messages {
        flex: 1;
        padding: 15px;
        overflow-y: auto;
        background-color: #f5f5f5;
    }

    .message {
        display: flex;
        margin-bottom: 15px;
        align-items: flex-start;
    }

    .message.user {
        flex-direction: row-reverse;
    }

    .avatar {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        background-color: #4286f4;
        display: flex;
        justify-content: center;
        align-items: center;
        color: white;
        margin-right: 10px;
    }

    .message-content {
        max-width: 70%;
    }

    .message-text {
        padding: 10px 15px;
        border-radius: 18px;
        margin-bottom: 5px;
        word-break: break-word;
    }

    .message.bot .message-text {
        background-color: white;
        border: 1px solid #e0e0e0;
    }

    .message.user .message-text {
        background-color: #4286f4;
        color: white;
        text-align: right;
    }

    .message-time {
        font-size: 12px;
        color: #999;
        margin-left: 10px;
    }

    .message.user .message-time {
        text-align: right;
    }

    .typing-indicator {
        display: flex;
        padding: 10px 15px;
        background-color: white;
        border-radius: 18px;
        border: 1px solid #e0e0e0;
        width: fit-content;
        margin-bottom: 15px;
    }

    .dot {
        width: 8px;
        height: 8px;
        background-color: #999;
        border-radius: 50%;
        margin: 0 2px;
        animation: bounce 1.5s infinite;
    }

    .dot:nth-child(2) {
        animation-delay: 0.2s;
    }

    .dot:nth-child(3) {
        animation-delay: 0.4s;
    }

    @keyframes bounce {
        0%, 60%, 100% {
            transform: translateY(0);
        }
        30% {
            transform: translateY(-4px);
        }
    }

    .chat-input {
        padding: 15px;
        display: flex;
        border-top: 1px solid #e0e0e0;
        background-color: white;
    }

    .chat-input input {
        flex: 1;
        padding: 10px 15px;
        border: 1px solid #e0e0e0;
        border-radius: 20px;
        font-size: 14px;
        outline: none;
    }

    .chat-input button {
        margin-left: 10px;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #4286f4;
        color: white;
        border: none;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .chat-input button:disabled {
        background-color: #b3c9f4;
        cursor: not-allowed;
    }

    .chat-input button i {
        font-size: 16px;
    }

    /* 移动端适配 */
    @media (max-width: 480px) {
        .chat-window {
            width: 100%;
            height: 100%;
            bottom: 0;
            right: 0;
            border-radius: 0;
        }

        .chat-icon {
            bottom: 15px;
            right: 15px;
        }
    }
</style>