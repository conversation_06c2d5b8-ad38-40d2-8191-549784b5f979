<template>
  <div class="modal-overlay" v-if="visible">
    <div class="modal-container">
      <div class="modal-header1">
        <h2>订单确认</h2>
      </div>

      <div class="modal-body">
        <!-- 计费方式 -->
        <div class="section-title">计费方式</div>
        <div class="billing-tabs">
          <div
              v-for="(option, index) in billingOptions"
              :key="index"
              class="billing-tab"
              :class="{ 'active': selectedBillingMethod === option.value }"
              @click="selectedBillingMethod = option.value"
          >
            {{ option.label }}
          </div>
        </div>

        <div class="section-title">选择主机</div>
        <div class="specs-example-table">
          <div class="specs-example-row">
            <div class="specs-example-cell">GPU型号</div>
            <div class="specs-example-cell">GPU</div>
            <div class="specs-example-cell">显存</div>
            <div class="specs-example-cell">vCPU</div>
            <div class="specs-example-cell">内存</div>
            <div class="specs-example-cell">系统盘</div>
          </div>
          <div class="specs-example-row">
            <div class="specs-example-cell">{{ server.name }}</div>
            <div class="specs-example-cell">{{ server.graphicsCardNumber }}卡</div>
            <div class="specs-example-cell">{{ server.videoMemory }}GB</div>
            <div class="specs-example-cell">{{ server.gpuNuclearNumber }}核</div>
            <div class="specs-example-cell">{{ server.internalMemory }}GB</div>
            <div class="specs-example-cell">{{ server.systemDisk }}GB</div>
          </div>
          <div class="server-card-footer">
            <div class="server-price">¥ {{ server[selectedBillingMethod] }}<span class="spec-label"> {{ priceUnit }}</span></div>
          </div>
        </div>

        <!-- 服务器配置规格展示 -->
        <div class="section-title">实例规格</div>
        <div class="specs-example-table">
          <div class="specs-example-row">
            <div class="specs-example-cell">GPU型号</div>
            <div class="specs-example-cell">vCPU</div>
            <div class="specs-example-cell">内存</div>
            <div class="specs-example-cell">系统盘</div>
            <div class="specs-example-cell">数据盘</div>
          </div>
          <div class="specs-example-row">
            <div class="specs-example-cell">{{ server.name }}</div>
            <div class="specs-example-cell">{{ server.gpuNuclearNumber }}核心</div>
            <div class="specs-example-cell">{{ server.internalMemory }}GB</div>
            <div class="specs-example-cell">{{ server.systemDisk }}GB</div>
            <div class="specs-example-cell">免费{{ server.dataDisk }}GB SSD</div>
          </div>
        </div>

        <!-- 租用时长 -->
        <div class="rental-duration">
          <div class="duration-label">租用时长：</div>
          <div class="duration-selector">
            <select v-model="selectedDuration"  class="duration-select">
              <option
                  v-for="(option, index) in currentDurationOptions"
                  :key="index"
                  :value="option.value"
              >
                {{ option.label }}
              </option>
            </select>
          </div>
          <div class="duration-hint" v-if="selectedBillingMethod === 'priceHour'"></div>
          <div class="duration-hint" v-else-if="selectedBillingMethod === 'priceDay'"></div>
          <div class="duration-hint" v-else-if="selectedBillingMethod === 'priceWeek'"></div>
          <div class="duration-hint" v-else></div>
        </div>

        <!-- 配置费用 -->
        <div class="price-summary">
          <div class="price-label">配置费用：</div>
          <div class="price-value">¥ {{ totalPrice }} 元 </div>
          <div class="details-link" @click="showPriceDetails = true">费用明细</div>
        </div>
      </div>

      <div class="modal-footer">
        <button class="cancel-button" @click="closeModal">取消</button>
        <button
            class="confirm-button"
            @click="showConfirmDialog"
        >
          立即租赁
        </button>
      </div>
    </div>
  </div>

</template>

<script>
export default {
  name: 'OrderDetail',
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    server: {
      type: Object,
      default: () => ({})
    },
    selectedBillingMethod: {
      type:String
    }
  },
  data() {
    return {
      defaultDiskSize: 50,
      selectedBillingMethod: 'priceDay',
      selectedDuration: 1,
      needExtraDisk: false,
      showPriceDetails: false,
      showConfirmation: false, // 控制二次确认弹窗显示
      billingOptions: [
        { label: '按量计费', value: 'priceHour', unit: '/小时' },
        { label: '包日', value: 'priceDay', unit: '/日' },
        { label: '包月', value: 'priceMouth', unit: '/月' },
        { label: '包年', value: 'priceYear', unit: '/年' }
      ],
      durationOptionsHour: [
        { label: '1小时', value: 1 },
        { label: '2小时', value: 2 },
        { label: '4小时', value: 4 },
        { label: '8小时', value: 8 },
        { label: '12小时', value: 12 },
        { label: '24小时', value: 24 }
      ],
      durationOptionsDay: [
        { label: '1天', value: 1 },
        { label: '2天', value: 2 },
        { label: '3天', value: 3 },
        { label: '4天', value: 4 },
        { label: '5天', value: 5 },
        { label: '6天', value: 6 }
      ],
      durationOptionsWeek: [
        { label: '1月', value: 1 },
        { label: '2月', value: 2 },
        { label: '3月', value: 3 },
        { label: '4月', value: 4 },
        { label: '5月', value: 5 },
        { label: '6月', value: 6 },
        { label: '7月', value: 7 },
        { label: '8月', value: 8 }
      ],
      durationOptionsMonth: [
        { label: '1年', value: 1 },
        { label: '2年', value: 2 },
        { label: '3年', value: 3 }
      ]
    };
  },
  created() {
  },
  computed: {
    currentDurationOptions() {
      switch (this.selectedBillingMethod) {
        case 'priceHour':
          this.totalPrice = this.server['priceHour']
          return this.durationOptionsHour;
        case 'priceDay':
          this.totalPrice = this.server['priceDay']
          return this.durationOptionsDay;
        case 'priceMouth':
          this.totalPrice = this.server['priceMouth']
          return this.durationOptionsWeek;
        case 'priceYear':
          this.totalPrice = this.server['priceYear']
          return this.durationOptionsMonth;
        default:
          return this.durationOptionsDay;
      }
    },
    totalPrice() {
      const price = this.server[this.selectedBillingMethod] || 0;
      return (price * this.selectedDuration).toFixed(2);
    },
    priceUnit() {
      switch (this.selectedBillingMethod) {
        case 'priceHour': return '/小时';
        case 'priceDay': return '/日';
        case 'priceMouth': return '/月';
        case 'priceYear': return '/年';
        default: return '';
      }
    },
    totalTime(){
      switch (this.selectedBillingMethod) {
        case 'priceHour':
          return '小时';
        case 'priceDay':
          return '天'
        case 'priceMouth':
          return '月'
        case 'priceYear':
          return '年'
        default:
          return '小时'
      }
    },
  },
  watch: {
    selectedBillingMethod() {
      this.selectedDuration = this.currentDurationOptions[0]?.value || 1;
    },
    selectedDuration(){
      this.totalPrice =this.server[this.selectedBillingMethod] * this.selectedDuration
    },
    selectedDuration:{
      handler(newval){
        this.$emit('time-updated',newval+this.totalTime)
      },
      immediate:true
    },
    totalPrice:{
      handler(newval){
        this.$emit('price-updated',newval);
      },
      immediate:true
    },
  },
  methods: {
    closeModal() {
      this.$emit('close');
    },
    selectServer() {
      // 保留方法，但不需要实际操作，因为只有一个服务器
    },
    showConfirmDialog() {
      this.showConfirmation = true;
    },
    confirmOrder() {
      this.showConfirmation = false;
      this.$emit("orderSubmitted");

      const order = {
        serverId: this.server.id,
        serverName: this.server.name,
        billingMethod: this.selectedBillingMethod,
        duration: this.selectedDuration,
        needExtraDisk: this.needExtraDisk,
        price: this.totalPrice,
        specs: {
          gpuModel: this.server.name,
          vcpu: this.server.vcpu,
          systemDisk: this.server.systemDisk,
          cloudDisk: this.server.cloudDisk || 0,
          memory: this.server.memory,
          videoMemory: this.server.videoMemory
        }
      };

      this.$emit('order-success', order);
      this.closeModal();
    }
  }
};
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 1020px;
  max-width: 95%;
  max-height: 90vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.modal-header1 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.modal-header1 h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.close-button {
  background: none;
  width: 30px;
  height: 55px;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #999;
  transition: color 0.2s;
}

.close-button:hover {
  color: #333;
}

.modal-body {
  margin-top: -4vh;
  padding: 20px 24px;
  flex-grow: 1;
}

.section-title {
  font-size: 15px;
  font-weight: 600;
  color: #333;
  margin-top: 24px;
  margin-bottom: 12px;
}

.billing-tabs {
  display: flex;
  background-color: #f7f7f9;
  border-radius: 8px;
  font-size: 1.8vh;
  padding: 4px;
  width:50vh;
  margin-bottom: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.billing-tab {
  flex: 1;
  padding: 12px 16px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  color: #666;
  border-radius: 6px;
}

.billing-tab:hover {
  color: #2196f3;
  background-color: rgba(99, 102, 241, 0.05);
}

.billing-tab.active {
  color: #fff;
  background-color: #2196f3;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
}

.server-card {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 20px;
  transition: all 0.3s;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.server-card:hover {
  border-color: #2196f3;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.15);
  transform: translateY(-2px);
}

.server-card-header {
  padding: 16px;
  background-color: #f9f9f9;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #e8e8e8;
}

.server-radio {
  margin-right: 12px;
  width: 18px;
  height: 18px;
  accent-color: #2196f3;
}

.server-name {
  font-weight: 600;
  font-size: 15px;
  color: #333;
}

.server-card-body {
  padding: 16px;
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  background-color: #fff;
}

.server-spec {
  display: flex;
  align-items: center;
  flex: 1 0 18%;
  min-width: 120px;
}

.spec-label {
  color: #666;
  margin-right: 8px;
  font-size: 14px;
}

.spec-value {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.server-card-footer {
  padding: 12px 16px;
  height: 6vh;
  background-color: #f9f9f9;
  border-top: 1px solid #e8e8e8;
  display: flex;
  justify-content: flex-end;
}

.server-price {
  font-weight: 600;
  color: #f43f5e;
  font-size: 16px;
}

.specs-example {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.specs-example-table {
  width: 100%;
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid #eaeaea;
}

.specs-example-row {
  display: flex;
}

.specs-example-row:first-child {
  background-color: #f3f4f6;
  font-weight: 500;
}

.specs-example-row:last-child {
  background-color: #fff;
}

.specs-example-cell {
  padding: 10px 12px;
  flex: 1;
  border-right: 1px solid #eaeaea;
  font-size: 13px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.specs-example-cell:last-child {
  border-right: none;
}

.rental-duration {
  margin-top: 2vh;
  display: flex;
  margin-left: 2vh;
  align-items: center;
  margin-bottom: -2vh;
  flex-wrap: wrap;
  gap: 10px;
}

.duration-label {
  font-weight: 500;
  color: #333;
  margin-right: 12px;
}

.duration-selector {
  position: relative;
}

.duration-select {
  padding: 10px 32px 10px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  appearance: none;
  background-color: white;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 120px;
}

.duration-select:hover {
  border-color: #2196f3;
}

.duration-select:focus {
  outline: none;
  border-color: #2196f3;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.15);
}

.duration-selector::after {
  content: "▼";
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
  pointer-events: none;
  font-size: 10px;
}

.duration-hint {
  font-size: 12px;
  color: #666;
  margin-left: 8px;
}

.price-summary {
  display: flex;
  align-items: center;
  margin-top: 24px;
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.price-label {
  font-weight: 500;
  color: #333;
  height: 4vh;
  margin-right: 8px;
}

.price-value {
  font-weight: 700;
  color: #f43f5e;
  font-size: 18px;
  margin-right: auto;
}

.details-link {
  color: #2196f3;
  cursor: pointer;
  font-size: 14px;
  text-decoration: underline;
}

.details-link:hover {
  color: #2196f3;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding: 16px 24px;
  border-top: 1px solid #f0f0f0;
  gap: 12px;
}

.cancel-button {
  padding: 10px 20px;
  border: 1px solid #ddd;
  background-color: white;
  color: #666;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
}

.cancel-button:hover {
  background-color: #f3f4f6;
  border-color: #ccc;
}

.confirm-button {
  padding: 10px 24px;
  border: none;
  background-color: #2196f3;
  color: white;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
  box-shadow: 0 2px 4px rgba(99, 102, 241, 0.2);
}

.confirm-button:hover {
  background-color: #2196f3;
  box-shadow: 0 4px 8px rgba(99, 102, 241, 0.3);
}

/* 二次确认弹窗样式 */
.confirm-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

.confirm-dialog {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 400px;
  max-width: 95%;
  overflow: hidden;
}

.confirm-header {
  padding: 16px 24px;
  background-color: #f7f7f9;
  border-bottom: 1px solid #eee;
}

.confirm-header h3 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.confirm-body {
  padding: 20px 24px;
}

.confirm-body p {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #333;
}

.confirm-details {
  background-color: #f9f9f9;
  padding: 12px;
  border-radius: 6px;
  font-size: 13px;
  line-height: 1.6;
}

.confirm-details div {
  margin-bottom: 6px;
}

.confirm-details div:last-child {
  margin-bottom: 0;
}

.confirm-footer {
  display: flex;
  justify-content: flex-end;
  padding: 16px 24px;
  border-top: 1px solid #eee;
  gap: 12px;
}

.confirm-cancel {
  padding: 8px 16px;
  border: 1px solid #ddd;
  background-color: white;
  color: #666;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.confirm-cancel:hover {
  background-color: #f3f4f6;
  border-color: #ccc;
}

.confirm-ok {
  padding: 8px 16px;
  border: none;
  background-color: #2196f3;
  color: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.confirm-ok:hover {
  background-color: #2196f3;
}
</style>

<template>
  <div>
    <div class="modal-overlay" v-if="visible">
      <div class="modal-container">
        <div class="modal-header1">
          <h2>订单确认</h2>
        </div>

        <div class="modal-body">
          <!-- 计费方式 -->
          <div class="section-title">计费方式</div>
          <div class="billing-tabs">
            <div
                v-for="(option, index) in billingOptions"
                :key="index"
                class="billing-tab"
                :class="{ 'active': selectedBillingMethod === option.value }"
                @click="selectedBillingMethod = option.value"
            >
              {{ option.label }}
            </div>
          </div>

          <div class="section-title">选择主机</div>
          <div class="specs-example-table">
            <div class="specs-example-row">
              <div class="specs-example-cell">GPU型号</div>
              <div class="specs-example-cell">GPU</div>
              <div class="specs-example-cell">显存</div>
              <div class="specs-example-cell">vCPU</div>
              <div class="specs-example-cell">内存</div>
              <div class="specs-example-cell">系统盘</div>
            </div>
            <div class="specs-example-row">
              <div class="specs-example-cell">{{ server.name }}</div>
              <div class="specs-example-cell">{{ server.graphicsCardNumber }}卡</div>
              <div class="specs-example-cell">{{ server.videoMemory }}GB</div>
              <div class="specs-example-cell">{{ server.gpuNuclearNumber }}核</div>
              <div class="specs-example-cell">{{ server.internalMemory }}GB</div>
              <div class="specs-example-cell">{{ server.systemDisk }}GB</div>
            </div>
            <div class="server-card-footer">
              <div class="server-price">¥ {{ server[selectedBillingMethod] }}<span class="spec-label"> {{ priceUnit }}</span></div>
            </div>
          </div>

          <!-- 服务器配置规格展示 -->
          <div class="section-title">实例规格</div>
          <div class="specs-example-table">
            <div class="specs-example-row">
              <div class="specs-example-cell">GPU型号</div>
              <div class="specs-example-cell">vCPU</div>
              <div class="specs-example-cell">内存</div>
              <div class="specs-example-cell">系统盘</div>
              <div class="specs-example-cell">数据盘</div>
            </div>
            <div class="specs-example-row">
              <div class="specs-example-cell">{{ server.name }}</div>
              <div class="specs-example-cell">{{ server.gpuNuclearNumber }}核心</div>
              <div class="specs-example-cell">{{ server.internalMemory }}GB</div>
              <div class="specs-example-cell">{{ server.systemDisk }}GB</div>
              <div class="specs-example-cell">免费{{ server.dataDisk }}GB SSD</div>
            </div>
          </div>

          <!-- 租用时长 -->
          <div class="rental-duration">
            <div class="duration-label">租用时长：</div>
            <div class="duration-selector">
              <select v-model="selectedDuration"  class="duration-select">
                <option
                    v-for="(option, index) in currentDurationOptions"
                    :key="index"
                    :value="option.value"
                >
                  {{ option.label }}
                </option>
              </select>
            </div>
          </div>

          <!-- 配置费用 -->
          <div class="price-summary">
            <div class="price-label">配置费用：</div>
            <div class="price-value">¥ {{ totalPrice }} 元 </div>
          </div>
        </div>

        <div class="modal-footer">
          <button class="cancel-button" @click="closeModal">取消</button>
          <button
              class="confirm-button"
              @click="showConfirmDialog"
          >
            立即租赁
          </button>
        </div>
      </div>
    </div>

    <!-- 简化的二次确认弹窗 -->
    <div class="confirm-overlay" v-if="showConfirmation">
      <div class="confirm-dialog">
        <div class="confirm-message">是否确认订单？</div>
        <div class="confirm-footer">
          <button class="confirm-cancel" @click="showConfirmation = false">取消</button>
          <button class="confirm-ok" @click="confirmOrder">确认</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'OrderDetail',
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    server: {
      type: Object,
      default: () => ({})
    },
    selectedBillingMethod: {
      type:String
    }
  },
  data() {
    return {
      defaultDiskSize: 50,
      selectedBillingMethod: 'priceDay',
      selectedDuration: 1,
      needExtraDisk: false,
      showConfirmation: false,
      billingOptions: [
        { label: '按量计费', value: 'priceHour', unit: '/小时' },
        { label: '包日', value: 'priceDay', unit: '/日' },
        { label: '包月', value: 'priceMouth', unit: '/月' },
        { label: '包年', value: 'priceYear', unit: '/年' }
      ],
      durationOptionsHour: [
        { label: '1小时', value: 1 },
        { label: '2小时', value: 2 },
        { label: '4小时', value: 4 },
        { label: '8小时', value: 8 },
        { label: '12小时', value: 12 },
        { label: '24小时', value: 24 }
      ],
      durationOptionsDay: [
        { label: '1天', value: 1 },
        { label: '2天', value: 2 },
        { label: '3天', value: 3 },
        { label: '4天', value: 4 },
        { label: '5天', value: 5 },
        { label: '6天', value: 6 }
      ],
      durationOptionsWeek: [
        { label: '1月', value: 1 },
        { label: '2月', value: 2 },
        { label: '3月', value: 3 },
        { label: '4月', value: 4 },
        { label: '5月', value: 5 },
        { label: '6月', value: 6 },
        { label: '7月', value: 7 },
        { label: '8月', value: 8 }
      ],
      durationOptionsMonth: [
        { label: '1年', value: 1 },
        { label: '2年', value: 2 },
        { label: '3年', value: 3 }
      ]
    };
  },
  computed: {
    currentDurationOptions() {
      switch (this.selectedBillingMethod) {
        case 'priceHour':
          this.totalPrice = this.server['priceHour']
          return this.durationOptionsHour;
        case 'priceDay':
          this.totalPrice = this.server['priceDay']
          return this.durationOptionsDay;
        case 'priceMouth':
          this.totalPrice = this.server['priceMouth']
          return this.durationOptionsWeek;
        case 'priceYear':
          this.totalPrice = this.server['priceYear']
          return this.durationOptionsMonth;
        default:
          return this.durationOptionsDay;
      }
    },
    totalPrice() {
      const price = this.server[this.selectedBillingMethod] || 0;
      return (price * this.selectedDuration).toFixed(2);
    },
    priceUnit() {
      switch (this.selectedBillingMethod) {
        case 'priceHour': return '/小时';
        case 'priceDay': return '/日';
        case 'priceMouth': return '/月';
        case 'priceYear': return '/年';
        default: return '';
      }
    },
    totalTime(){
      switch (this.selectedBillingMethod) {
        case 'priceHour':
          return '小时';
        case 'priceDay':
          return '天'
        case 'priceMouth':
          return '月'
        case 'priceYear':
          return '年'
        default:
          return '小时'
      }
    },
  },
  watch: {
    selectedBillingMethod() {
      this.selectedDuration = this.currentDurationOptions[0]?.value || 1;
    },
    selectedDuration(){
      this.totalPrice =this.server[this.selectedBillingMethod] * this.selectedDuration
    },
    selectedDuration:{
      handler(newval){
        this.$emit('time-updated',newval+this.totalTime)
      },
      immediate:true
    },
    totalPrice:{
      handler(newval){
        this.$emit('price-updated',newval);
      },
      immediate:true
    },
  },
  methods: {
    closeModal() {
      this.$emit('close');
    },
    showConfirmDialog() {
      this.showConfirmation = true;
    },
    confirmOrder() {
      this.showConfirmation = false;
      this.$emit("orderSubmitted");

      const order = {
        serverId: this.server.id,
        serverName: this.server.name,
        billingMethod: this.selectedBillingMethod,
        duration: this.selectedDuration,
        needExtraDisk: this.needExtraDisk,
        price: this.totalPrice,
        specs: {
          gpuModel: this.server.name,
          vcpu: this.server.vcpu,
          systemDisk: this.server.systemDisk,
          cloudDisk: this.server.cloudDisk || 0,
          memory: this.server.memory,
          videoMemory: this.server.videoMemory
        }
      };

      this.$emit('order-success', order);
      this.closeModal();
    }
  }
};
</script>

<style scoped>
.modal-overlay {
  /*margin-left: 200px;*/
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 1020px;
  max-width: 95%;
  max-height: 90vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.modal-header1 {
  display: flex;
  /*height: 5vh;*/
  justify-content: space-between;
  align-items: center;
  padding: 10px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.modal-header1 h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.close-button {
  background: none;
  width: 30px;
  height: 55px;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #999;
  transition: color 0.2s;
}

.close-button:hover {
  color: #333;
}

.modal-body {
  margin-top: -4vh;
  padding: 20px 24px;
  flex-grow: 1;
}

.section-title {
  font-size: 15px;
  font-weight: 600;
  color: #333;
  margin-top: 24px;
  margin-bottom: 12px;
}

/* 优化的计费方式选项卡样式 */
.billing-tabs {
  display: flex;
  background-color: #f7f7f9;
  border-radius: 8px;
  font-size: 1.8vh;
  padding: 4px;
  width:50vh;
  /*higth:10px;*/
  margin-bottom: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.billing-tab {
  flex: 1;
  padding: 12px 16px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  color: #666;
  border-radius: 6px;
}

.billing-tab:hover {
  color: #2196f3;
  background-color: rgba(99, 102, 241, 0.05);
}

.billing-tab.active {
  color: #fff;
  background-color: #2196f3;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
}

.billing-description {
  font-size: 12px;
  color: #666;
  margin-top: 8px;
  margin-bottom: 20px;
  line-height: 1.5;
}

/* 服务器卡片样式 */
.server-card {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 20px;
  transition: all 0.3s;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.server-card:hover {
  border-color: #2196f3;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.15);
  transform: translateY(-2px);
}

.server-card-header {
  padding: 16px;
  background-color: #f9f9f9;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #e8e8e8;
}

.server-radio {
  margin-right: 12px;
  width: 18px;
  height: 18px;
  accent-color: #2196f3;
}

.server-name {
  font-weight: 600;
  font-size: 15px;
  color: #333;
}

.server-card-body {
  padding: 16px;
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  background-color: #fff;
}

.server-spec {
  display: flex;
  align-items: center;
  flex: 1 0 18%;
  min-width: 120px;
}

.spec-label {
  color: #666;
  margin-right: 8px;
  font-size: 14px;
}

.spec-value {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.server-card-footer {
  padding: 12px 16px;
  height: 6vh;
  background-color: #f9f9f9;
  border-top: 1px solid #e8e8e8;
  display: flex;
  justify-content: flex-end;
}

.server-price {
  font-weight: 600;
  color: #f43f5e;
  font-size: 16px;
}

/* 数据盘选择样式 */
.disk-options {
  display: flex;
  gap: 24px;
  margin-bottom: 20px;
}

.disk-option {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  color: #333;
}

.disk-option input[type="checkbox"] {
  margin-right: 8px;
  width: 16px;
  height: 16px;
  accent-color: #2196f3;
}

/* 规格展示表格样式 */
.specs-example {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.specs-example-title {
  font-weight: 600;
  margin-bottom: 12px;
  color: #333;
  font-size: 14px;
}

.specs-example-table {
  width: 100%;
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid #eaeaea;
}

.specs-example-row {
  display: flex;
}

.specs-example-row:first-child {
  background-color: #f3f4f6;
  font-weight: 500;
}

.specs-example-row:last-child {
  background-color: #fff;
}

.specs-example-cell {
  padding: 10px 12px;
  flex: 1;
  border-right: 1px solid #eaeaea;
  font-size: 13px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.specs-example-cell:last-child {
  border-right: none;
}

/* 租用时长样式 */
.rental-duration {
  margin-top: 2vh;
  display: flex;
  margin-left: 2vh;
  align-items: center;
  margin-bottom: -2vh;
  /*margin-bottom: 24px;*/
  flex-wrap: wrap;
  gap: 10px;
}

.duration-label {
  font-weight: 500;
  color: #333;
  margin-right: 12px;
}

.duration-selector {
  position: relative;
}

.duration-select {
  padding: 10px 32px 10px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  appearance: none;
  background-color: white;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 120px;
}

.duration-select:hover {
  border-color: #2196f3;
}

.duration-select:focus {
  outline: none;
  border-color: #2196f3;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.15);
}

.duration-selector::after {
  content: "▼";
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
  pointer-events: none;
  font-size: 10px;
}

.duration-hint {
  font-size: 12px;
  color: #666;
  margin-left: 8px;
}

/* 价格总结样式 */
.price-summary {
  display: flex;
  align-items: center;
  margin-top: 24px;
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.price-label {
  font-weight: 500;
  color: #333;
  height: 4vh;
  margin-right: 8px;
}

.price-value {
  font-weight: 700;
  color: #f43f5e;
  font-size: 18px;
  margin-right: auto;
}

.details-link {
  color: #2196f3;
  cursor: pointer;
  font-size: 14px;
  text-decoration: underline;
}

.details-link:hover {
  color: #2196f3;
}

/* 底部按钮样式 */
.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding: 16px 24px;
  border-top: 1px solid #f0f0f0;
  gap: 12px;
}

.cancel-button {
  padding: 10px 20px;
  border: 1px solid #ddd;
  background-color: white;
  color: #666;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
}

.cancel-button:hover {
  background-color: #f3f4f6;
  border-color: #ccc;
}

.confirm-button {
  padding: 10px 24px;
  border: none;
  background-color: #2196f3;
  color: white;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
  box-shadow: 0 2px 4px rgba(99, 102, 241, 0.2);
}

.confirm-button:hover {
  background-color: #2196f3;
  box-shadow: 0 4px 8px rgba(99, 102, 241, 0.3);
}

/* 简化后的二次确认弹窗样式 */
.confirm-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

.confirm-dialog {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 300px;
  padding: 20px;
}

.confirm-message {
  margin-bottom: 20px;
  text-align: center;
  font-size: 16px;
}

.confirm-footer {
  display: flex;
  justify-content: center;
  gap: 16px;
}

.confirm-cancel {
  padding: 8px 16px;
  border: 1px solid #ddd;
  background-color: white;
  color: #666;
  width: 100px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.confirm-cancel:hover {
  background-color: #f3f4f6;
  border-color: #ccc;
}

.confirm-ok {
  padding: 8px 16px;
  border: none;
  background-color: #2196f3;
  color: white;
  border-radius: 4px;
  width: 100px;

  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.confirm-ok:hover {
  background-color: #2196f3;
}
</style>