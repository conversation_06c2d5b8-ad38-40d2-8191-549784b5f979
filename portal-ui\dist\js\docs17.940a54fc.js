"use strict";(self["webpackChunkportal_ui"]=self["webpackChunkportal_ui"]||[]).push([[2576],{4633:function(t,o,n){n.r(o);var e=new URL(n(1404),n.b),r=new URL(n(4117),n.b),u=new URL(n(4181),n.b),s=new URL(n(4040),n.b),a=new URL(n(6752),n.b),f='<h1 id="容器化部署-mineru">容器化部署 minerU</h1> <blockquote> <p>此镜像提供了标准化的<strong>API 接口</strong>，让您能够便捷地通过 <strong>API 调用方式</strong>访问和使用所有功能。</p> </blockquote> <p><font style="color:#020817">本指南详细阐述了在天工开物平台上，高效部署与使用 minerU API 项目的技术方案。minerU 是一款将 PDF 转化为机器可读格式的工具（如 markdown、json），可以很方便地输出为任意格式。</font></p> <h2 id="1在天工开物上运行-mineru-api">1.在天工开物上运行 minerU API</h2> <p><font style="color:#020817">天工开物平台提供预构建的 minerU 容器镜像，用户无需本地复杂环境配置，可快速完成部署并启用服务。以下是详细部署步骤：</font></p> <h3 id="11-创建部署服务">1.1 创建部署服务</h3> <p><font style="color:#020817">登录<a href="https://tiangongkaiwu.top/portal/#/console"><font style="color:#06c">天工开物控制台</font></a>，在控制台首页点击“弹性部署服务”进入管理页面。</font></p> <p><img src="'+e+'" alt=""></p> <h3 id="12-选择-gpu-型号">1.2 选择 GPU 型号</h3> <p><font style="color:#020817">根据实际需求选择 GPU 型号：</font></p> <p><font style="color:#020817">初次使用或调试阶段，推荐配置单张 NVIDIA RTX 4090 GPU</font></p> <p><img src="'+r+'" alt=""></p> <h3 id="13-选择预制镜像">1.3 选择预制镜像</h3> <p><font style="color:#020817">在“服务配置”模块切换至“预制服务”选项卡，选择 minerU 官方镜像。</font></p> <p><img src="'+u+'" alt=""></p> <h3 id="14-部署并访问服务">1.4 部署并访问服务</h3> <p><font style="color:#020817">点击“部署服务”，平台将自动拉取镜像并启动容器。</font></p> <p><img src="'+s+'" alt=""></p> <p><font style="color:#020817">部署完成后，在“快捷访问”中复制端口为 8000 的公网访问链接，后续是通过该地址调用 API 服务。</font></p> <p><font style="color:#020817">地址后面加上 /docs 进入 API 接口文档</font></p> <p><img src="'+a+'" alt=""></p> <h2 id="2快速上手-api-使用说明">2.快速上手—— API 使用说明</h2> <h3 id="21-接口功能">2.1 接口功能</h3> <p><font style="color:#020817"><font style="color:#2f8ef4;background-color:#eff0f0">POST /pdf_parse </font>HTTP 方法为 POST，路由路径为 <font style="color:#2f8ef4;background-color:#eff0f0">/pdf_parse</font>。</font></p> <p><font style="color:#020817"><font style="color:#2f8ef4;background-color:#eff0f0">Parse PDF file</font> 核心功能：解析 PDF 文件，将其内容转换为 JSON 和 Markdown 格式。</font></p> <h3 id="22-功能详细说明">2.2 功能详细说明</h3> <p><font style="color:#020817">核心过程： 将 PDF 解析为 JSON 和 Markdown，输出到指定目录。</font></p> <p><font style="color:#020817">参数说明：</font></p> <ul> <li><font style="color:#2f8ef4;background-color:#eff0f0">pdf_file</font> (必传) PDF 文件二进制数据（通过表单上传）。</li> <li><font style="color:#2f8ef4;background-color:#eff0f0">parse_method</font> (可选) 解析模式，可选 <font style="color:#2f8ef4;background-color:#eff0f0">auto</font>（自动）、<font style="color:#2f8ef4;background-color:#eff0f0">ocr</font>（光学识别）、<font style="color:#2f8ef4;background-color:#eff0f0">txt</font>（文本提取）。默认 <font style="color:#2f8ef4;background-color:#eff0f0">auto</font>，若效果不佳建议尝试 <font style="color:#2f8ef4;background-color:#eff0f0">ocr</font>。</li> <li><font style="color:#2f8ef4;background-color:#eff0f0">model_json_path</font> (可选) 自定义解析模型路径。若为空，使用内置模型。注：模型需与 PDF 文件匹配。</li> <li><font style="color:#2f8ef4;background-color:#eff0f0">is_json_md_dump</font> (可选) 是否输出 JSON/MD 文件，默认 <font style="color:#2f8ef4;background-color:#eff0f0">true</font>（生成）。</li> <li><font style="color:#2f8ef4;background-color:#eff0f0">output_dir</font> (可选) 输出目录，默认为 <font style="color:#2f8ef4;background-color:#eff0f0">output</font>。系统会按 PDF 文件名创建子目录存放结果。</li> </ul> <p><font style="color:#020817">输出规则：</font></p> <p><font style="color:#020817">生成 3 个阶段性 JSON 文件（不同解析阶段）</font></p> <p><font style="color:#020817">生成 1 个最终 Markdown 文件（<font style="color:#2f8ef4;background-color:#eff0f0">.md</font>）</font></p> <h3 id="23-接口参数（请求部分）">2.3 接口参数（请求部分）</h3> <h4 id="231-query-参数（url-参数）">2.3.1 Query 参数（URL 参数）</h4> <table> <thead> <tr> <th><strong>参数名</strong></th> <th><strong>类型</strong></th> <th><strong>位置</strong></th> <th><strong>默认值</strong></th> <th><strong>说明</strong></th> </tr> </thead> <tbody><tr> <td>parse_method</td> <td>string</td> <td>query</td> <td>auto</td> <td>解析模式（auto/ocr/txt）</td> </tr> <tr> <td>model_json_path</td> <td>string</td> <td>query</td> <td>model_json_path</td> <td>自定义模型文件路径</td> </tr> <tr> <td>is_json_md_dump</td> <td>boolean</td> <td>query</td> <td>TRUE</td> <td>是否输出 JSON/MD 文件</td> </tr> <tr> <td>output_dir</td> <td>string</td> <td>query</td> <td>output</td> <td>结果输出目录</td> </tr> </tbody></table> <h4 id="232-body-参数（表单数据）">2.3.2 Body 参数（表单数据）</h4> <table> <thead> <tr> <th><strong>参数名</strong></th> <th><strong>类型</strong></th> <th><strong>必传</strong></th> <th><strong>示例值</strong></th> <th><strong>说明</strong></th> </tr> </thead> <tbody><tr> <td>pdf_file</td> <td>binary</td> <td>是</td> <td>Happy-LLM.pdf</td> <td>PDF 文件二进制数据</td> </tr> </tbody></table> <p><font style="color:#020817"><strong>注</strong>：需使用 <font style="color:#2f8ef4;background-color:#eff0f0">multipart/form-data</font> 格式上传文件。</font></p> <h3 id="24-完整-api-使用示例">2.4 完整 API 使用示例</h3> <pre><code class="language-python">import os\nimport json\nimport requests\nimport logging\nfrom typing import Optional\nlogging.basicConfig(\n    level=logging.INFO,\n    format=&#39;%(asctime)s - %(name)s - %(levelname)s - %(message)s&#39;\n)\nlogger = logging.getLogger(&quot;PDFParser&quot;)\ndef pdf_parse_main(\n    pdf_path: str,  # 这里修改了语法错误\n    parse_method: str = &#39;auto&#39;,\n    model_json_path: Optional[str] = None,\n    is_json_md_dump: bool = True,\n    output_dir: Optional[str] = None,\n    api_base_url: str = &quot;你的公网访问地址&quot;\n):\n    &quot;&quot;&quot;\n    通过远程 API 执行 PDF 转换到 JSON、MD 的过程\n    Args:\n        pdf_path (str): PDF 文件的完整路径\n        parse_method (str): 解析方法，支持 &#39;auto&#39;、&#39;ocr&#39;、&#39;txt&#39; 三种模式，默认为 &#39;auto&#39;\n                           - auto: 自动选择最佳解析方式\n                           - ocr: 使用 OCR 光学字符识别\n                           - txt: 提取文本内容\n        model_json_path (str): 预训练模型数据文件路径（可选）\n        is_json_md_dump (bool): 是否将解析结果保存为 JSON 和 Markdown 文件，默认为 True\n        output_dir (str): 输出目录路径，如果为 None 则使用 PDF 文件同级目录\n        api_base_url (str): API 服务的基础 URL 地址\n    Returns:\n        dict: 包含解析结果的字典，如果失败则返回 None\n    Raises:\n        FileNotFoundError: 当 PDF 文件不存在时抛出\n        requests.RequestException: 当 API 调用失败时抛出\n    &quot;&quot;&quot;\n    try:\n        # 检查 PDF 文件是否存在\n        if not os.path.exists(pdf_path):\n            logger.error(f&quot;PDF 文件不存在：{pdf_path}&quot;)\n            raise FileNotFoundError(f&quot;PDF 文件不存在：{pdf_path}&quot;)\n        # 准备输出目录结构\n        pdf_name = os.path.basename(pdf_path).rsplit(&quot;.&quot;, 1)[0]  # 使用 rsplit 处理多个点的情况\n        if output_dir:\n            output_path = os.path.join(output_dir, pdf_name)\n        else:\n            # 如果没有指定输出目录，使用 PDF 文件所在目录\n            pdf_path_parent = os.path.dirname(pdf_path)\n            output_path = os.path.join(pdf_path_parent, pdf_name)\n        # 创建输出目录\n        os.makedirs(output_path, exist_ok=True)\n        output_image_path = os.path.join(output_path, &#39;images&#39;)\n        os.makedirs(output_image_path, exist_ok=True)\n        logger.info(f&quot;开始处理 PDF 文件：{pdf_path}&quot;)\n        logger.info(f&quot;使用解析方法：{parse_method}&quot;)\n        logger.info(f&quot;输出目录：{output_path}&quot;)\n        # 准备 API 请求\n        api_url = f&quot;{api_base_url}/pdf_parse&quot;  # 修正了 API 端点 URL\n        # 准备请求参数 - 使用 params 传递查询参数\n        params = {\n            &quot;parse_method&quot;: parse_method,\n            &quot;is_json_md_dump&quot;: str(is_json_md_dump).lower(),\n            &quot;output_dir&quot;: output_dir if output_dir else &quot;output&quot;\n        }\n        # 如果提供了模型 JSON 路径，添加到请求中\n        if model_json_path and os.path.exists(model_json_path):\n            params[&quot;model_json_path&quot;] = model_json_path\n            logger.info(f&quot;使用模型文件：{model_json_path}&quot;)\n        # 准备文件上传\n        files = {&#39;pdf_file&#39;: open(pdf_path, &#39;rb&#39;)}\n        logger.info(&quot;开始调用远程 API 进行 PDF 解析...&quot;)\n        # 发送 API 请求\n        try:\n            response = requests.post(\n                api_url,\n                params=params,  # 使用 params 传递查询参数\n                files=files,\n                timeout=300  # 设置 5 分钟超时，PDF 解析可能需要较长时间\n            )\n            # 检查响应状态\n            response.raise_for_status()\n        except requests.exceptions.Timeout:\n            logger.error(&quot;API 请求超时，PDF 文件可能过大或服务繁忙&quot;)\n            return None\n        except requests.exceptions.ConnectionError:\n            logger.error(f&quot;无法连接到 API 服务：{api_base_url}&quot;)\n            return None\n        except requests.exceptions.HTTPError as e:\n            logger.error(f&quot;API 请求失败：HTTP {response.status_code} - {response.text}&quot;)\n            return None\n        finally:\n            # 确保关闭文件\n            if &#39;pdf_file&#39; in files:\n                files[&#39;pdf_file&#39;].close()\n        # 解析 API 响应\n        try:\n            result_data = response.json()\n            logger.info(&quot;API 调用成功，开始处理返回结果&quot;)\n        except json.JSONDecodeError:\n            logger.error(&quot;API 返回的数据格式不正确，无法解析 JSON&quot;)\n            return None\n        # 保存解析结果到本地文件\n        if is_json_md_dump and _save_results_to_local(result_data, output_path, pdf_name):\n            logger.info(f&quot;解析结果已保存到：{output_path}&quot;)\n        logger.info(&quot;PDF 解析完成&quot;)\n        return result_data\n    except FileNotFoundError as e:\n        logger.error(f&quot;文件错误：{e}&quot;)\n        return None\n    except requests.RequestException as e:\n        logger.error(f&quot;网络请求错误：{e}&quot;)\n        return None\n    except Exception as e:\n        logger.exception(f&quot;处理 PDF 时发生未知错误：{e}&quot;)\n        return None\ndef _save_results_to_local(result_data: dict, output_path: str, pdf_name: str) -&gt; bool:\n    &quot;&quot;&quot;\n    将 API 返回的结果保存到本地文件\n    Args:\n        result_data (dict): API 返回的解析结果数据\n        output_path (str): 输出目录路径\n        pdf_name (str): PDF 文件名（不含扩展名）\n    Returns:\n        bool: 保存成功返回 True，失败返回 False\n    &quot;&quot;&quot;\n    try:\n        # 保存模型数据 JSON 文件\n        if &#39;model_list&#39; in result_data:\n            model_json_path = os.path.join(output_path, f&quot;{pdf_name}_model.json&quot;)\n            with open(model_json_path, &#39;w&#39;, encoding=&#39;utf-8&#39;) as f:\n                json.dump(result_data[&#39;model_list&#39;], f, ensure_ascii=False, indent=4)\n            logger.info(f&quot;模型数据已保存：{model_json_path}&quot;)\n        # 保存内容列表 JSON 文件\n        if &#39;content_list&#39; in result_data:\n            content_json_path = os.path.join(output_path, f&quot;{pdf_name}_content_list.json&quot;)\n            with open(content_json_path, &#39;w&#39;, encoding=&#39;utf-8&#39;) as f:\n                json.dump(result_data[&#39;content_list&#39;], f, ensure_ascii=False, indent=4)\n            logger.info(f&quot;内容列表已保存：{content_json_path}&quot;)\n        # 保存 Markdown 文件\n        if &#39;markdown_content&#39; in result_data:\n            md_path = os.path.join(output_path, f&quot;{pdf_name}.md&quot;)\n            with open(md_path, &#39;w&#39;, encoding=&#39;utf-8&#39;) as f:\n                f.write(result_data[&#39;markdown_content&#39;])\n            logger.info(f&quot;Markdown 文件已保存：{md_path}&quot;)\n        # 保存图片文件（如果有）\n        if &#39;images&#39; in result_data:\n            images_dir = os.path.join(output_path, &#39;images&#39;)\n            _save_images(result_data[&#39;images&#39;], images_dir)\n        return True\n    except Exception as e:\n        logger.error(f&quot;保存结果文件时发生错误：{e}&quot;)\n        return False\ndef _save_images(images_data: dict, images_dir: str) -&gt; None:\n    &quot;&quot;&quot;\n    保存解析过程中提取的图片文件\n    Args:\n        images_data (dict): 包含图片数据的字典\n        images_dir (str): 图片保存目录\n    &quot;&quot;&quot;\n    try:\n        os.makedirs(images_dir, exist_ok=True)\n        for image_name, image_content in images_data.items():\n            image_path = os.path.join(images_dir, image_name)\n            # 如果图片内容是 base64 编码，需要先解码\n            if isinstance(image_content, str):\n                import base64\n                try:\n                    image_content = base64.b64decode(image_content)\n                except base64.binascii.Error:\n                    logger.warning(f&quot;图片 {image_name} 的 Base64 格式无效&quot;)\n                    continue\n            # 确保二进制数据写入\n            with open(image_path, &#39;wb&#39;) as f:\n                if isinstance(image_content, bytes):\n                    f.write(image_content)\n                else:\n                    logger.error(f&quot;图片 {image_name} 的内容不是有效的二进制数据&quot;)\n            logger.info(f&quot;图片已保存：{image_path}&quot;)\n    except Exception as e:\n        logger.error(f&quot;保存图片时发生错误：{e}&quot;)\ndef batch_pdf_parse(\n    pdf_directory: str,\n    parse_method: str = &#39;auto&#39;,\n    output_dir: Optional[str] = None,\n    api_base_url: str = &quot;你的公网访问地址&quot;\n) -&gt; dict:\n    &quot;&quot;&quot;\n    批量处理目录中的所有 PDF 文件\n    Args:\n        pdf_directory (str): 包含 PDF 文件的目录路径\n        parse_method (str): 解析方法，默认为&#39;auto&#39;\n        output_dir (str): 输出目录，如果为 None 则在每个 PDF 同级目录创建\n        api_base_url (str): API 服务地址\n    Returns:\n        dict: 包含处理结果统计的字典\n    &quot;&quot;&quot;\n    if not os.path.exists(pdf_directory):\n        logger.error(f&quot;目录不存在：{pdf_directory}&quot;)\n        return {&quot;success&quot;: 0, &quot;failed&quot;: 0, &quot;total&quot;: 0}\n    # 获取目录中所有 PDF 文件\n    pdf_files = [f for f in os.listdir(pdf_directory) if f.lower().endswith(&#39;.pdf&#39;)]\n    if not pdf_files:\n        logger.warning(f&quot;目录中没有找到 PDF 文件：{pdf_directory}&quot;)\n        return {&quot;success&quot;: 0, &quot;failed&quot;: 0, &quot;total&quot;: 0}\n    success_count = 0\n    failed_count = 0\n    total_count = len(pdf_files)\n    logger.info(f&quot;开始批量处理，共找到 {total_count} 个 PDF 文件&quot;)\n    for i, pdf_file in enumerate(pdf_files, 1):\n        pdf_path = os.path.join(pdf_directory, pdf_file)\n        logger.info(f&quot;正在处理第 {i}/{total_count} 个文件：{pdf_file}&quot;)\n        try:\n            result = pdf_parse_main(\n                pdf_path=pdf_path,\n                parse_method=parse_method,\n                output_dir=output_dir,\n                api_base_url=api_base_url\n            )\n            if result is not None:\n                success_count += 1\n                logger.info(f&quot;文件处理成功：{pdf_file}&quot;)\n            else:\n                failed_count += 1\n                logger.error(f&quot;文件处理失败：{pdf_file}&quot;)\n        except Exception as e:\n            failed_count += 1\n            logger.error(f&quot;处理文件时发生异常 {pdf_file}: {str(e)}&quot;)\n    result_summary = {\n        &quot;success&quot;: success_count,\n        &quot;failed&quot;: failed_count,\n        &quot;total&quot;: total_count\n    }\n    logger.info(f&quot;批量处理完成：成功 {success_count}/{total_count}, 失败 {failed_count}/{total_count}&quot;)\n    return result_summary\nif __name__ == &#39;__main__&#39;:\n    # 单个文件处理示例\n    pdf_path = &quot;book.pdf&quot;  # 这里是你的 PDF 文件名\n    # 检查示例文件是否存在\n    if os.path.exists(pdf_path):\n        logger.info(&quot;开始处理示例 PDF 文件&quot;)\n        result = pdf_parse_main(\n            pdf_path=pdf_path,\n            parse_method=&quot;auto&quot;,  # 使用自动解析模式\n            output_dir=&quot;./output&quot;,  # 指定输出目录\n            is_json_md_dump=True  # 保存 JSON 和 MD 文件\n        )\n        if result:\n            logger.info(&quot;PDF 处理完成，结果已保存到 output 目录&quot;)\n            print(f&quot;处理结果：{result}&quot;)\n        else:\n            logger.error(&quot;PDF 处理失败&quot;)\n    else:\n        logger.warning(f&quot;示例文件不存在：{pdf_path}&quot;)\n        logger.info(&quot;请将 PDF 文件放在当前目录下，或修改 pdf_path 变量&quot;)\n    # 批量处理示例（注释掉，需要时取消注释）\n    # batch_result = batch_pdf_parse(\n    #     pdf_directory=&quot;./pdf_files&quot;,\n    #     parse_method=&quot;auto&quot;,\n    #     output_dir=&quot;./batch_output&quot;\n    # )\n    # logger.info(f&quot;批量处理结果：{batch_result}&quot;)\n</code></pre> <p><font style="color:#020817"><em>提示：上述代码展示了完整的处理流程，包括:</em></font></p> <ul> <li>支持多种解析方式 (auto/ocr/txt)</li> <li>自动创建输出目录结构</li> <li>保存模型结果、内容列表和 Markdown 输出</li> <li>异常处理和日志记录</li> </ul> <p><br><br> <font style="color:#b2b2b2">最后更新于: 2025/7/4 10:51</font></p> ';o["default"]=f},4181:function(t,o,n){t.exports=n.p+"img/mineru3.868e040e.png"},4040:function(t,o,n){t.exports=n.p+"img/mineru4.880ccb33.png"},6752:function(t,o,n){t.exports=n.p+"img/mineru5.3c4ca070.png"},1404:function(t,o,n){t.exports=n.p+"img/universal1.9a1b3f4b.png"},4117:function(t,o,n){t.exports=n.p+"img/universal2.4306636e.png"}}]);
//# sourceMappingURL=docs17.940a54fc.js.map