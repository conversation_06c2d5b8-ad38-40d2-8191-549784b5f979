<template>
  <div class="footer">
    <div class="footer-content">
      <!-- First column -->
      <div class="footer-column">
        <div class="footer-title">售前咨询热线</div>
        <div class="footer-phone">13913283376</div>
        <div class="footer-links">
          <a @click="navigateTo('/index')"><div class="footer-link">首页</div></a>
          <a @click="navigateTo('/product')"><div class="footer-link">AI算力市场</div></a>

<!--          <div class="footer-link">AI算力市场</div>-->

        </div>
      </div>

      <!-- Second column -->
      <div class="footer-column">
        <div class="footer-title">支持与服务</div>
        <div class="footer-links">
          <div class="footer-link">联系我们</div>
          <a @click="navigateTo('/help')"><div class="footer-link">帮助文档</div></a>
          <div class="footer-link">公告</div>
          <div class="footer-link">提交建议</div>
        </div>
      </div>

<!--      &lt;!&ndash; Third column &ndash;&gt;-->
<!--      <div class="footer-column">-->
<!--        <div class="footer-title">账户管理</div>-->
<!--        <div class="footer-links">-->
<!--          <div class="footer-link">控制台</div>-->
<!--          <div class="footer-link">账号管理</div>-->
<!--          <div class="footer-link">充值付款</div>-->
<!--          <div class="footer-link">线下款 / 电汇</div>-->
<!--          <div class="footer-link">索取发票</div>-->
<!--          <div class="footer-link">合规性</div>-->
<!--        </div>-->
<!--      </div>-->

      <!-- Fourth column -->
      <div class="footer-column">
        <div class="footer-title">关注天工开物</div>
        <div class="footer-links">
          <div class="footer-link">关注天工开物</div>
          <div class="footer-link">天工开物公众号</div>
          <div class="footer-link">天工开物微博</div>
          <div class="footer-link">天工开物支持与服务</div>
        </div>
      </div>

      <!-- Fifth column -->
      <div class="footer-column">
        <div class="footer-title">联系专属客服</div>
        <div class="footer-qrcode">
          <img src="@/assets/images/footer/wechat.jpg" alt="联系专属客服二维码" />
        </div>
      </div>

      <!-- Sixth column -->
      <div class="footer-column">
        <div class="footer-title">官方公众号</div>
        <div class="footer-qrcode">
          <img src="@/assets/images/footer/wechat.jpg" alt="官方公众号二维码" />
        </div>
      </div>
    </div>

    <!-- Bottom footer links -->
    <div class="footer-bottom">

      <div class="footer-copyright">
        <a href="https://beian.miit.gov.cn" target="_blank" style="color: inherit; text-decoration: none;">
          苏ICP备2025171841号-1
        </a>
           天工开物智能科技（苏州）有限公司 ALL RIGHTS RESERVED. </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "Footer",
  data() {
    return {
      // Data can be added here if needed
    }
  },
  methods: {
    navigateTo(path) {
      // 记录当前活动路径作为上一个活动路径
      if (this.currentPath && this.currentPath !== path) {
        this.previousActivePath = this.currentPath;

        // 为当前活动链接和登录按钮添加 active-exit 类
        this.$nextTick(() => {
          const navLinks = document.querySelectorAll('.nav-link, .btn-login');
          navLinks.forEach(link => {
            if ((link.classList.contains('active') ||
                    (path === '/login' && link.classList.contains('btn-login'))) &&
                !link.classList.contains('active-exit')) {
              link.classList.add('active-exit');

              // 等待动画完成后移除 active-exit 类
              setTimeout(() => {
                link.classList.remove('active-exit');
              }, 300); // 匹配你的 CSS transition 持续时间 (0.3s)
            }
          });

          // 更新当前路径
          this.currentPath = path;
        });
      } else {
        this.currentPath = path;
      }

      // 如果当前路径与目标路径相同，则重新加载页面
      if (this.$route.path === path) {
        this.$nextTick(() => {
          window.scrollTo({
            top: 0,
            behavior: 'instant' // 使用即时滚动而不是平滑滚动
          });
          this.$router.go(0); // 刷新当前页面
        });
      } else {
        // 不同路径，正常导航并滚动到顶部
        this.$router.push(path);
        window.scrollTo({
          top: 0,
          behavior: 'instant'
        });
      }
    }
  }
}
</script>

<style scoped>
.footer {
  max-width: 2560px;
  width: 100%;
  background-color: #424242;
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  color: white;
  padding: 0;
  margin: 0px;
}

.footer-content {
  display: flex;
  flex-wrap: wrap;
  max-width: 2560px;
  margin: 0 40px;
  padding: 20px 0;
}

.footer-column {
  flex: 1;
  min-width: 150px;
  padding: 0 15px;
  margin-bottom: 20px;
}

.footer-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  color: white;
}

.footer-phone {
  font-size: 16px;
  margin-bottom: 15px;
}

.footer-links {
  display: flex;
  flex-direction: column;
}

.footer-link {
  margin-bottom: 10px;
  cursor: pointer;
  color: white;
  font-size: 14px;
  transition: color 0.2s;
}

.footer-link:hover {
  color: #1890ff;
}

.footer-qrcode {
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.footer-qrcode img {
  max-width: 100%;
  max-height: 100%;
  margin-left: -18%;
}

.footer-bottom {
  border-top: 1px solid #e8e8e8;
  padding: 20px 0;
  text-align: center;
  max-width: 2560px;
  margin: 0 40px;
}

.footer-bottom-links {
  margin-bottom: 10px;
  text-align: left;
}

.footer-bottom-link {
  color: white;
  margin: 0 10px;
  font-size: 14px;
  text-decoration: none;
  transition: color 0.2s;
}

.footer-bottom-link:hover {
  color: #1890ff;
  text-decoration: underline;
}

.footer-copyright, .footer-license {
  font-size: 15px;
  text-align: center;
  color: white;
  margin-left: 10px;
}
</style>