{"version": 3, "file": "js/docs8.5542fa90.js", "mappings": "wHACA,IAAIA,EAA6B,IAAIC,IAAI,aACrCC,EAA6B,IAAID,IAAI,aACrCE,EAA6B,IAAIF,IAAI,aACrCG,EAA6B,IAAIH,IAAI,aACrCI,EAA6B,IAAIJ,IAAI,aACrCK,EAA6B,IAAIL,IAAI,aACrCM,EAA6B,IAAIN,IAAI,aACrCO,EAA6B,IAAIP,IAAI,aACrCQ,EAA6B,IAAIR,IAAI,aACrCS,EAA6B,IAAIT,IAAI,aACrCU,EAA8B,IAAIV,IAAI,aACtCW,EAA8B,IAAIX,IAAI,aACtCY,EAA8B,IAAIZ,IAAI,aACtCa,EAA8B,IAAIb,IAAI,aACtCc,EAA8B,IAAId,IAAI,aACtCe,EAA8B,IAAIf,IAAI,aAEtCgB,EAAO,kuBAA2vBjB,EAA6B,uYAA6ZE,EAA6B,4KAAsLC,EAA6B,wGAAkHC,EAA6B,kIAA4IC,EAA6B,4IAAsJC,EAA6B,8WAA0XC,EAA6B,iJAAyJC,EAA6B,yuBAAqvBC,EAA6B,ksBAA4tBC,EAA6B,oNAAgOC,EAA8B,4uBAA0wBC,EAA8B,gPAA4PC,EAA8B,4SAA4TC,EAA8B,+vJAAm3JC,EAA8B,2sCAAyvCC,EAA8B,07LAErzX,c", "sources": ["webpack://portal-ui/./src/docs/flux-dev.md"], "sourcesContent": ["// Imports\nvar ___HTML_LOADER_IMPORT_0___ = new URL(\"./imgs/flux_dev1.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_1___ = new URL(\"./imgs/universal1.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_2___ = new URL(\"./imgs/universal2.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_3___ = new URL(\"./imgs/flux_dev4.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_4___ = new URL(\"./imgs/flux_dev5.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_5___ = new URL(\"./imgs/flux_dev6.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_6___ = new URL(\"./imgs/flux_dev7.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_7___ = new URL(\"./imgs/flux_dev8.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_8___ = new URL(\"./imgs/flux_dev9.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_9___ = new URL(\"./imgs/flux_dev10.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_10___ = new URL(\"./imgs/flux_dev11.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_11___ = new URL(\"./imgs/flux_dev12.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_12___ = new URL(\"./imgs/flux_dev13.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_13___ = new URL(\"./imgs/flux_dev14.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_14___ = new URL(\"./imgs/flux_dev15.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_15___ = new URL(\"./imgs/flux_dev16.png\", import.meta.url);\n// Module\nvar code = \"<h1 id=\\\"容器化部署-flux1-dev-文生图模型应用\\\"><font style=\\\"color:#020817\\\">容器化部署 Flux.1-dev 文生图模型应用</font></h1> <p><font style=\\\"color:#020817\\\">本指南详细阐述了在天工开物上部署 Flux.1-dev 模型应用的解决方案。</font></p> <p><font style=\\\"color:#1f2937\\\">鉴于 Serverless 类型的平台具备多节点特性，从专业角度出发，强烈不建议将 Web UI 用于生产环境。在生产场景中，使用 API 是更为适宜的选择。此镜像已集成封装好的 API，以便于您便捷使用。</font></p> <h2 id=\\\"1-开源案例\\\"><font style=\\\"color:#020817\\\">1 开源案例</font></h2> <p><font style=\\\"color:#020817\\\">我们基于本教程开源了一套前端 Flux.1-dev 文生图服务网站解决方案。</font></p> <p><font style=\\\"color:#020817\\\">具有完整的 Docker&amp;Serverless 化部署方案，您可以参考使用。</font></p> <p><font style=\\\"color:#020817\\\">项目地址：</font><a href=\\\"https://github.com/slmnb-lab/FluxEz\\\"><font style=\\\"color:#2f8ef4\\\">https://github.com/slmnb-lab/FluxEz</font></a></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_0___ + \"\\\" alt=\\\"\\\"></p> <h2 id=\\\"2-部署步骤\\\"><font style=\\\"color:#020817\\\">2 部署步骤</font></h2> <p><font style=\\\"color:#020817\\\">我们提供了构建完毕的 Flux 镜像可以直接部署使用。</font></p> <p><font style=\\\"color:#020817\\\">下面是部署步骤：</font></p> <h3 id=\\\"21-访问天工开物控制台，点击新增部署。\\\"><font style=\\\"color:#020817\\\">2.1 访问</font><a href=\\\"https://tiangongkaiwu.top/#/console\\\">天工开物控制台</a><font style=\\\"color:#020817\\\">，点击新增部署。</font></h3> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_1___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"22-基于自身需要进行配置，参考配置为单卡-4090-和-1-个节点（初次使用进行调试）。\\\"><font style=\\\"color:#020817\\\">2.2 基于自身需要进行配置，参考配置为单卡 4090 和 1 个节点（初次使用进行调试）。</font></h3> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_2___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"23-选择相应预制镜像\\\"><font style=\\\"color:#020817\\\">2.3 选择相应预制镜像</font></h3> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_3___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"24-点击部署服务，耐心等待节点拉取镜像并启动。\\\"><font style=\\\"color:#020817\\\">2.4 点击部署服务，耐心等待节点拉取镜像并启动。</font></h3> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_4___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"25-节点启动后，你所在任务详情页中看到的内容可能如下：\\\"><font style=\\\"color:#020817\\\">2.5 节点启动后，你所在“任务详情页”中看到的内容可能如下：</font></h3> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_5___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"26-我们可以点击快速访问下方8188端口的链接，测试-comfyui-部署情况\\\"><font style=\\\"color:#020817\\\">2.6 我们可以点击快速访问下方“8188”端口的链接，测试 comfyui 部署情况</font></h3> <p><font style=\\\"color:#020817\\\">系统会自动分配一个可公网访问的域名，点击 8188 端口的链接。接下来我们即可自由地通过使用工作流在 comfyui 中使用 Flux 模型进行图像生成。</font></p> <p>我们进入 8188 端口的 web ui 界面，选择左侧的“工作流“菜单，找到名为”flux_dev_t5fp8.json“的工作流文件，鼠标点击。</p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_6___ + \"\\\" alt=\\\"\\\"></p> <p><font style=\\\"color:#020817\\\">随后，我们可以看到工作流已经被正常载入 ComfyUI 了。接下来，我们找到 prompt（提示词）的填写节点，输入我们想要生成的图像描述文本。</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_7___ + \"\\\" alt=\\\"\\\"></p> <p><font style=\\\"color:#020817\\\">参考的 prompt 如下（FLUX 模型对英文支持较好）：</font></p> <p><font style=\\\"color:#67676c\\\"> best quality, a cute anime boy and girl, sunshine, soft features, sitting together on a swing, blue and white outfits, flowers in their hair, blue eyes, blush, gentle smile, long hair (girl), short tousled hair (boy), barefoot, looking at viewer, full body, light particles, pale skin, floral background, off shoulder dress (girl), open shirt with vest (boy), collarbones, blue roses, vines and plants, romantic atmosphere, blue and white theme, ethereal lighting, soft breeze, whimsical mood </font></p> <p><font style=\\\"color:#020817\\\">稍等片刻，即可在后方的“保存图像”节点看到基于我们提示词生成的图片。右键点击图片，选择“Save Image”即可保存图片。</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_8___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"27-通过-api-的形式来调用-comfyui-进行图像生成\\\"><font style=\\\"color:#020817\\\">2.7 通过 API 的形式来调用 comfyui 进行图像生成</font></h3> <p><font style=\\\"color:#020817\\\">我们不推荐直接使用 comfyui 的默认 API 接口，因为多节点时需自行解决无状态问题。更推荐的做法是通过我们暴露的 3000 端口进行请求，其通过 </font><a href=\\\"https://github.com/SaladTechnologies/comfyui-api?tab=readme-ov-file\\\"><font style=\\\"color:#2f8ef4\\\">comfyui-api</font></a><font style=\\\"color:#020817\\\"> 进行包装，支持默认的同步生图请求和 webhook 实现。</font></p> <p><font style=\\\"color:#020817\\\">以下以 POSTMAN 为例，简要描述如何向 3000 端口发送图片生成的 API 请求：</font></p> <h4 id=\\\"271-保存页面工作流\\\"><font style=\\\"color:#020817\\\">2.7.1 保存页面工作流</font></h4> <p><font style=\\\"color:#020817\\\">点击”导航栏——工作流——导出（API）“菜单，浏览器会自动下载一个 json 文件。</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_9___ + \"\\\" alt=\\\"\\\"></p> <h4 id=\\\"272-打开-postman，新建一个-post-请求\\\"><font style=\\\"color:#020817\\\">2.7.2 打开 POSTMAN，新建一个 POST 请求</font></h4> <p><font style=\\\"color:#020817\\\">新建一个 POST 请求，并命名为”prompt“，如下图所示：</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_10___ + \"\\\" alt=\\\"\\\"></p> <h4 id=\\\"273-完善请求信息\\\"><font style=\\\"color:#020817\\\">2.7.3 完善请求信息</font></h4> <p><font style=\\\"color:#020817\\\">需要完善的信息如下：</font></p> <ul> <li><strong><font style=\\\"color:#020817\\\">请求的 URL</font></strong></li> </ul> <p><font style=\\\"color:#020817\\\">在 3000 端口的回传链接后加上“/prompt”的路径，保证其格式类似于</font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\"><a href=\\\"https://xxx/prompt\\\">https://xxx/prompt</a></font><font style=\\\"color:#020817\\\">。</font></p> <ul> <li><strong><font style=\\\"color:#020817\\\">将请求体参数格式设置为 raw 和 json</font></strong></li> </ul> <p><font style=\\\"color:#020817\\\">如图。</font></p> <ul> <li><strong><font style=\\\"color:#020817\\\">设置参数内容基本格式</font></strong></li> </ul> <p><font style=\\\"color:#020817\\\">如图。</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_11___ + \"\\\" alt=\\\"\\\"></p> <h4 id=\\\"274-将我们下载好的工作流-json-文件粘贴为参数中prompt字段的值\\\"><font style=\\\"color:#020817\\\">2.7.4 将我们下载好的工作流 json 文件粘贴为参数中prompt字段的值</font></h4> <p><font style=\\\"color:#020817\\\">如下图所示，我们将鼠标移动至 prompt 字段的冒号后，粘贴工作流的内容。</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_12___ + \"\\\" alt=\\\"\\\"></p> <h4 id=\\\"275-发送请求\\\"><font style=\\\"color:#020817\\\">2.7.5 发送请求</font></h4> <p><font style=\\\"color:#020817\\\">返回结果如下所示，</font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">images</font><font style=\\\"color:#020817\\\">字段包含一个字符数组，其中的元素即为生成图片的 base64 编码。</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_13___ + \"\\\" alt=\\\"\\\"></p> <h2 id=\\\"3-构建-comfyui-镜像\\\"><font style=\\\"color:#020817\\\">3 构建 comfyui 镜像</font></h2> <blockquote> <p><font style=\\\"color:#67676c\\\">温馨提示，如果你只希望使用我们默认的镜像，那么下面的内容您无需关注。</font></p> </blockquote> <p><font style=\\\"color:#020817\\\">如果您希望构建自定义的镜像，而非仅仅使用我们提供的预设 Flux 镜像，以下是一个很好的参考。您可根据自身需要，添加需要的模型和插件。</font></p> <h3 id=\\\"31-下载模型文件\\\"><font style=\\\"color:#020817\\\">3.1 下载模型文件</font></h3> <p><font style=\\\"color:#020817\\\">我们所需的模型文件共有四个，下载链接分别如下，分别将其下载至本地：</font></p> <ul> <li><a href=\\\"https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/clip_l.safetensors?download=true\\\"><font style=\\\"color:#2f8ef4\\\">https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/clip_l.safetensors?download=true</font></a></li> <li><a href=\\\"https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/t5xxl_fp16.safetensors?download=true\\\"><font style=\\\"color:#2f8ef4\\\">https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/t5xxl_fp16.safetensors?download=true</font></a></li> <li><a href=\\\"https://huggingface.co/black-forest-labs/FLUX.1-schnell/resolve/main/ae.safetensors?download=true\\\"><font style=\\\"color:#2f8ef4\\\">https://huggingface.co/black-forest-labs/FLUX.1-schnell/resolve/main/ae.safetensors?download=true</font></a></li> <li><a href=\\\"https://huggingface.co/black-forest-labs/FLUX.1-dev/resolve/main/flux1-dev.safetensors\\\"><font style=\\\"color:#2f8ef4\\\">https://huggingface.co/black-forest-labs/FLUX.1-dev/resolve/main/flux1-dev.safetensors</font></a></li> </ul> <p><font style=\\\"color:#020817\\\">注意，如果你的显存低于 32GB，建议将上述文件中的 t5xxl_fp16.safetensors 替换为下面的低配版模型 t5xxl_fp8_e4m3n.safetensors（后续工作流中对应修改一下模型名即可），下面的案例中我们将使用低配版模型作为示例：</font></p> <p><a href=\\\"https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/t5xxl_fp8_e4m3fn.safetensors?download=true\\\"><font style=\\\"color:#2f8ef4\\\">https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/t5xxl_fp8_e4m3fn.safetensors?download=true</font></a></p> <h3 id=\\\"32-创建-dockerfile-文件\\\"><font style=\\\"color:#020817\\\">3.2 创建 Dockerfile 文件</font></h3> <p><font style=\\\"color:#020817\\\">一个合适的 docker 基础镜像能帮助我们节省大量的时间，同时还能大大减少我们构建出错的概率。这里我们选择</font><a href=\\\"https://github.com/SaladTechnologies/comfyui-api\\\"><font style=\\\"color:#2f8ef4\\\">comfyui-api</font></a><font style=\\\"color:#020817\\\">开源库的官方镜像作为我们的基础镜像，其预装了 comfyui 及 comfyui-api 以及基础的运行环境依赖。</font></p> <p><font style=\\\"color:#020817\\\">我们最终的需要 Dockerfile 文件内容如下，请新建一个名为 Dockerfile 的文件（注意无后缀），通过任意编辑器打开，将下面的内容复制进去。</font></p> <pre><code class=\\\"language-dockerfile\\\"># 使用 comfyui-api 基础镜像\\nFROM ghcr.io/saladtechnologies/comfyui-api:comfy0.3.29-api1.8.3-torch2.6.0-cuda12.4-runtime\\n\\n# 设置环境变量\\nENV COMFYUI_PORT=8188 \\\\\\n    MODEL_DIR=/opt/ComfyUI/models \\\\\\n    BASE=&quot;&quot;\\n\\n# 4. 预创建模型目录结构\\nRUN mkdir -p ${MODEL_DIR}/{loras,vaes,text_encoders,diffusion_models}\\n\\n# 5. 复制模型文件\\nCOPY diffusion_models/*.safetensors ${MODEL_DIR}/diffusion_models/\\nCOPY vae/*.safetensors ${MODEL_DIR}/vae/\\nCOPY text_encoders/*.safetensors ${MODEL_DIR}/text_encoders/\\n\\n# 6. 暴露端口\\nEXPOSE ${COMFYUI_PORT}\\n</code></pre> <h3 id=\\\"33-创建目录\\\"><font style=\\\"color:#020817\\\">3.3 创建目录</font></h3> <p><font style=\\\"color:#020817\\\">创建目录是为了便于我们指定路径，请按照下面的路径放置上述的文件。</font></p> <p><font style=\\\"color:#020817\\\">comfyUI/</font></p> <p><font style=\\\"color:#020817\\\">└── Dockerfile</font></p> <p><font style=\\\"color:#020817\\\">├── diffusion_models/</font></p> <p><font style=\\\"color:#020817\\\">│ └── flux1-dev.safetensors</font></p> <p><font style=\\\"color:#020817\\\">├── text_encoders/</font></p> <p><font style=\\\"color:#020817\\\">│ ├── clip_l.safetensors</font></p> <p><font style=\\\"color:#020817\\\">│ └── t5xxl_fp8_e4m3fn.safetensors</font></p> <p><font style=\\\"color:#020817\\\">├── vae/</font></p> <p><font style=\\\"color:#020817\\\">│ └── ae.safetensors</font></p> <h3 id=\\\"34-执行构建\\\"><font style=\\\"color:#020817\\\">3.4 执行构建</font></h3> <p><font style=\\\"color:#020817\\\">进入 comfyuiUI 目录，打开控制台，执行如下命令（可根据需要自行修改标签和镜像名）：</font></p> <pre><code class=\\\"language-dockerfile\\\">docker build -t comfyui-flux:0.1 .\\n</code></pre> <p><font style=\\\"color:#020817\\\">耐心等待构建完毕，最终生成的镜像体积约 42GB。</font></p> <h3 id=\\\"35-本地测试（推荐）\\\"><font style=\\\"color:#020817\\\">3.5 本地测试（推荐）</font></h3> <p><font style=\\\"color:#020817\\\">虽然即使不经过本地测试也可以直接上传使用，但本地测试可以更快的预览镜像构建效果，还能提前排除可能的一些问题，避免构建过程中的错误在上传远程仓库后才发现。</font></p> <p><font style=\\\"color:#020817\\\">我们可以在任意位置打开控制台，键入以下指令：</font></p> <pre><code class=\\\"language-dockerfile\\\">docker run --rm --gpus all -p 3000:3000 comfyui-flux:0.1\\n</code></pre> <p><font style=\\\"color:#020817\\\">当容器运行完毕后，，我们可以通过 API 来判断其是否正常工作。</font></p> <h4 id=\\\"351-获取-api-文档\\\"><font style=\\\"color:#020817\\\">3.5.1 获取 API 文档</font></h4> <p><font style=\\\"color:#020817\\\">我们可以打开浏览器，网址栏输入</font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\"><a href=\\\"http://localhost:3000/docs\\\">http://localhost:3000/docs</a></font><font style=\\\"color:#020817\\\">，即可打开默认的 comfyui-api 基于 swagger 的文档页面，如下图所示：</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_14___ + \"\\\" alt=\\\"\\\"></p> <p><font style=\\\"color:#020817\\\">其中介绍了我们可以使用的 4 个常用方法，可自行了解详情。</font></p> <h4 id=\\\"352-测试生图接口\\\"><font style=\\\"color:#020817\\\">3.5.2 测试生图接口</font></h4> <p><font style=\\\"color:#020817\\\">接下来我们需要测试镜像容器的生图功能——当然也是最重要的功能。这一步推荐使用 PostMan 这类 API 测试工具，以下将以 PostMan 作为示例：</font></p> <h5 id=\\\"3521-创建请求\\\"><font style=\\\"color:#020817\\\">******* 创建请求</font></h5> <p><font style=\\\"color:#020817\\\">新建一个请求，将其请求方法设置为 POST，并键入</font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\"><a href=\\\"http://localhost:3000/prompt\\\">http://localhost:3000/prompt</a></font><font style=\\\"color:#020817\\\">作为请求的 URL。</font></p> <h5 id=\\\"3522-选择参数\\\"><font style=\\\"color:#020817\\\">******* 选择参数</font></h5> <p><font style=\\\"color:#020817\\\">找到下方的</font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">body</font><font style=\\\"color:#020817\\\">栏，点击</font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">raw</font><font style=\\\"color:#020817\\\">一项，并选择右侧的类型为</font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">JSON</font><font style=\\\"color:#020817\\\">。在下方键入我们的 JSON 参数。</font></p> <p><font style=\\\"color:#020817\\\">我们所需的参数如下，prompt 对应的是 comfyui 的工作流内容（API 形式）如果有自定义需要，我们也可以修改其中的参数值。</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_15___ + \"\\\" alt=\\\"\\\"></p> <pre><code class=\\\"language-json\\\">{\\n  &quot;prompt&quot;:{\\n    &quot;8&quot;: {\\n      &quot;inputs&quot;: {\\n        &quot;samples&quot;: [\\n          &quot;40&quot;,\\n          0\\n        ],\\n        &quot;vae&quot;: [\\n          &quot;10&quot;,\\n          0\\n        ]\\n      },\\n      &quot;class_type&quot;: &quot;VAEDecode&quot;,\\n      &quot;_meta&quot;: {\\n        &quot;title&quot;: &quot;VAE解码&quot;\\n      }\\n    },\\n    &quot;10&quot;: {\\n      &quot;inputs&quot;: {\\n        &quot;vae_name&quot;: &quot;ae.safetensors&quot;\\n      },\\n      &quot;class_type&quot;: &quot;VAELoader&quot;,\\n      &quot;_meta&quot;: {\\n        &quot;title&quot;: &quot;加载VAE&quot;\\n      }\\n    },\\n    &quot;11&quot;: {\\n      &quot;inputs&quot;: {\\n        &quot;clip_name1&quot;: &quot;t5xxl_fp8_e4m3fn.safetensors&quot;,\\n        &quot;clip_name2&quot;: &quot;clip_l.safetensors&quot;,\\n        &quot;type&quot;: &quot;flux&quot;,\\n        &quot;device&quot;: &quot;default&quot;\\n      },\\n      &quot;class_type&quot;: &quot;DualCLIPLoader&quot;,\\n      &quot;_meta&quot;: {\\n        &quot;title&quot;: &quot;双CLIP加载器&quot;\\n      }\\n    },\\n    &quot;17&quot;: {\\n      &quot;inputs&quot;: {\\n        &quot;scheduler&quot;: &quot;normal&quot;,\\n        &quot;steps&quot;: 25,\\n        &quot;denoise&quot;: 1,\\n        &quot;model&quot;: [\\n          &quot;46&quot;,\\n          0\\n        ]\\n      },\\n      &quot;class_type&quot;: &quot;BasicScheduler&quot;,\\n      &quot;_meta&quot;: {\\n        &quot;title&quot;: &quot;基本调度器&quot;\\n      }\\n    },\\n    &quot;38&quot;: {\\n      &quot;inputs&quot;: {\\n        &quot;model&quot;: [\\n          &quot;46&quot;,\\n          0\\n        ],\\n        &quot;conditioning&quot;: [\\n          &quot;42&quot;,\\n          0\\n        ]\\n      },\\n      &quot;class_type&quot;: &quot;BasicGuider&quot;,\\n      &quot;_meta&quot;: {\\n        &quot;title&quot;: &quot;基本引导器&quot;\\n      }\\n    },\\n    &quot;39&quot;: {\\n      &quot;inputs&quot;: {\\n        &quot;filename_prefix&quot;: &quot;FluxEz&quot;,\\n        &quot;images&quot;: [\\n          &quot;8&quot;,\\n          0\\n        ]\\n      },\\n      &quot;class_type&quot;: &quot;SaveImage&quot;,\\n      &quot;_meta&quot;: {\\n        &quot;title&quot;: &quot;保存图像&quot;\\n      }\\n    },\\n    &quot;40&quot;: {\\n      &quot;inputs&quot;: {\\n        &quot;noise&quot;: [\\n          &quot;45&quot;,\\n          0\\n        ],\\n        &quot;guider&quot;: [\\n          &quot;38&quot;,\\n          0\\n        ],\\n        &quot;sampler&quot;: [\\n          &quot;47&quot;,\\n          0\\n        ],\\n        &quot;sigmas&quot;: [\\n          &quot;17&quot;,\\n          0\\n        ],\\n        &quot;latent_image&quot;: [\\n          &quot;44&quot;,\\n          0\\n        ]\\n      },\\n      &quot;class_type&quot;: &quot;SamplerCustomAdvanced&quot;,\\n      &quot;_meta&quot;: {\\n        &quot;title&quot;: &quot;自定义采样器（高级）&quot;\\n      }\\n    },\\n    &quot;42&quot;: {\\n      &quot;inputs&quot;: {\\n        &quot;guidance&quot;: 3.5,\\n        &quot;conditioning&quot;: [\\n          &quot;43&quot;,\\n          0\\n        ]\\n      },\\n      &quot;class_type&quot;: &quot;FluxGuidance&quot;,\\n      &quot;_meta&quot;: {\\n        &quot;title&quot;: &quot;Flux引导&quot;\\n      }\\n    },\\n    &quot;43&quot;: {\\n      &quot;inputs&quot;: {\\n        &quot;text&quot;: &quot;beautiful photography of a gonger haired artist with Lots of Colorful coloursplashes in face and pn her hands, she is natural, having her hair in a casual bun, looking happily into camera, cinematic,&quot;,\\n        &quot;clip&quot;: [\\n          &quot;11&quot;,\\n          0\\n        ]\\n      },\\n      &quot;class_type&quot;: &quot;CLIPTextEncode&quot;,\\n      &quot;_meta&quot;: {\\n        &quot;title&quot;: &quot;CLIP文本编码&quot;\\n      }\\n    },\\n    &quot;44&quot;: {\\n      &quot;inputs&quot;: {\\n        &quot;width&quot;: 1024,\\n        &quot;height&quot;: 1024,\\n        &quot;batch_size&quot;: 1\\n      },\\n      &quot;class_type&quot;: &quot;EmptySD3LatentImage&quot;,\\n      &quot;_meta&quot;: {\\n        &quot;title&quot;: &quot;空Latent图像（SD3）&quot;\\n      }\\n    },\\n    &quot;45&quot;: {\\n      &quot;inputs&quot;: {\\n        &quot;noise_seed&quot;: 454905699352480\\n      },\\n      &quot;class_type&quot;: &quot;RandomNoise&quot;,\\n      &quot;_meta&quot;: {\\n        &quot;title&quot;: &quot;随机噪波&quot;\\n      }\\n    },\\n    &quot;46&quot;: {\\n      &quot;inputs&quot;: {\\n        &quot;max_shift&quot;: 1.15,\\n        &quot;base_shift&quot;: 0.5,\\n        &quot;width&quot;: 1024,\\n        &quot;height&quot;: 1024,\\n        &quot;model&quot;: [\\n          &quot;48&quot;,\\n          0\\n        ]\\n      },\\n      &quot;class_type&quot;: &quot;ModelSamplingFlux&quot;,\\n      &quot;_meta&quot;: {\\n        &quot;title&quot;: &quot;采样算法（Flux）&quot;\\n      }\\n    },\\n    &quot;47&quot;: {\\n      &quot;inputs&quot;: {\\n        &quot;sampler_name&quot;: &quot;euler&quot;\\n      },\\n      &quot;class_type&quot;: &quot;KSamplerSelect&quot;,\\n      &quot;_meta&quot;: {\\n        &quot;title&quot;: &quot;K采样器选择&quot;\\n      }\\n    },\\n    &quot;48&quot;: {\\n      &quot;inputs&quot;: {\\n        &quot;unet_name&quot;: &quot;flux1-dev.safetensors&quot;,\\n        &quot;weight_dtype&quot;: &quot;default&quot;\\n      },\\n      &quot;class_type&quot;: &quot;UNETLoader&quot;,\\n      &quot;_meta&quot;: {\\n        &quot;title&quot;: &quot;UNet加载器&quot;\\n      }\\n    }\\n  }\\n}\\n</code></pre> <h5 id=\\\"3523-发送请求\\\"><font style=\\\"color:#020817\\\">3.5.2.3 发送请求</font></h5> <p><font style=\\\"color:#020817\\\">请求返回的结果应该如下：</font></p> <pre><code class=\\\"language-dockerfile\\\">{\\n    &quot;id&quot;: &quot;************************************&quot;,\\n    &quot;prompt&quot;://你的工作流参数,\\n    &quot;images&quot;: []//返回的图片数组,\\n}\\n</code></pre> <h3 id=\\\"36-推送镜像到平台\\\"><font style=\\\"color:#020817\\\">3.6 推送镜像到平台</font></h3> <p><font style=\\\"color:#020817\\\">将上一步中自定义的镜像上传到我们的镜像仓库服务中。请参考文档镜像仓库。</font></p> <p><strong><font style=\\\"color:#67676c\\\"></font></strong></p> <p><br><br> <font style=\\\"color:#b2b2b2\\\">最后更新于: 2025/6/16 14:36</font></p> \";\n// Exports\nexport default code;"], "names": ["___HTML_LOADER_IMPORT_0___", "URL", "___HTML_LOADER_IMPORT_1___", "___HTML_LOADER_IMPORT_2___", "___HTML_LOADER_IMPORT_3___", "___HTML_LOADER_IMPORT_4___", "___HTML_LOADER_IMPORT_5___", "___HTML_LOADER_IMPORT_6___", "___HTML_LOADER_IMPORT_7___", "___HTML_LOADER_IMPORT_8___", "___HTML_LOADER_IMPORT_9___", "___HTML_LOADER_IMPORT_10___", "___HTML_LOADER_IMPORT_11___", "___HTML_LOADER_IMPORT_12___", "___HTML_LOADER_IMPORT_13___", "___HTML_LOADER_IMPORT_14___", "___HTML_LOADER_IMPORT_15___", "code"], "sourceRoot": ""}