# <font style="color:rgb(2, 8, 23);">容器化部署 StableDiffusion1.5-WebUI 应用</font>
## <font style="color:rgb(2, 8, 23);">1 部署步骤</font>
<font style="color:rgb(2, 8, 23);">我们提供了构建完毕的 Stable-Diffusion-WebUI 镜像，您可以直接部署使用。</font>

### <font style="color:rgb(2, 8, 23);">1.1 访问</font>[天工开物控制台](https://tiangongkaiwu.top/#/console)<font style="color:rgb(2, 8, 23);">，点击新增部署。</font>
![](./imgs/universal1.png)

### <font style="color:rgb(2, 8, 23);">1.2 基于自身需要进行配置，参考配置为单卡 4090 和 1 个节点（初次使用进行调试）。</font>
![](./imgs/universal2.png)

### <font style="color:rgb(2, 8, 23);">1.3 选择相应预制镜像</font>
![](./imgs/webui1-5v3.png)

### <font style="color:rgb(2, 8, 23);">1.4 点击部署服务，耐心等待节点拉取镜像并启动。</font>
![](./imgs/webui1-5v4.png)

### <font style="color:rgb(2, 8, 23);">1.5 节点启动后，你所在“任务详情页”中看到的内容可能如下：</font>
![](./imgs/webui1-5v5.png)

### <font style="color:rgb(2, 8, 23);">1.6 我们可以点击快捷访问下方“7860”端口的链接，测试 Gradio 运行情况</font>
<font style="color:rgb(2, 8, 23);">接下来填写 prompt，描述我们希望图片的内容。</font>

![](./imgs/webui1-5v6.png)

<font style="color:rgb(2, 8, 23);">最后点击生成按钮，接下来我们耐心稍等片刻，可以看到图片已经生成。</font>

![](./imgs/webui1-5v7.png)

<br><br>
<font style="color:rgba(178,178,178,1);">最后更新于: 2025/6/18 11:29</font>
