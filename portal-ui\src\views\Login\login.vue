<template>
  <div class="login-page">
    <SlideNotification
        v-if="showNotification"
        :message="notificationMessage"
        :type="notificationType"
        :duration="3000"
        :minHeight= minHeight
        @close="showNotification = false"
    />

    <div class="left-side">
      <backgroundlogin />

<!--      <div class="logo-container">-->
<!--        <div class="logo-area">-->
<!--          <img src="../assets/images/logo2.png" alt="算力租赁">-->
<!--        </div>-->
<!--      </div>-->

<!--      <div class="headline-container">-->
<!--        <h2 class="headline">天工开物 AI算力云</h2>-->
<!--        <p class="subheadline">-->
<!--          专门面向AI 2.0 时代的创新平台。利用顶尖的技术驱动，为用户的大模型开发、训练、运行、应用提供完整的工具链。-->
<!--        </p>-->
<!--      </div>-->
    </div>

    <div class="right-side">
      <div class="login-form-container">
        <h3>欢迎来到 天工开物</h3>

        <!-- Login tabs -->
        <div class="login-tabs">
          <div
              :class="['tab-item', activeTab === 'phone' ? 'active' : '']"
              @click="activeTab = 'phone'"
          >
            手机号登录
          </div>
          <div
              :class="['tab-item', activeTab === 'account' ? 'active' : '']"
              @click="activeTab = 'account'"
          >
            账号登录
          </div>
        </div>

        <!-- Login form container with fixed height -->
        <div class="form-container">
          <!-- Phone login form -->
          <div v-if="activeTab === 'phone'" class="login-form">
            <p class="form-note">   </p>

            <div class="input-group" :class="{ 'error': errors.phone }">
              <input
                  type="text"
                  v-model="phoneForm.phone"
                  placeholder="请输入手机号"
                  @blur="validatePhone"
              />
              <div class="error-container">
                <div v-if="errors.phone" class="error-message">{{ errors.phone }}</div>
              </div>
            </div>

            <div class="input-group verification-code" :class="{ 'error': errors.code }">
              <div class="code-input-container">
                <input
                    type="text"
                    v-model="phoneForm.code"
                    placeholder="请输入验证码"
                    @blur="validateCodegeshi"
                />
                <button
                    class="get-code-btn-inline"
                    @click="getVerificationCode"
                    :disabled="!phoneForm.phone || errors.phone || codeSent"
                >
                  {{ codeSent ? `${countdown}秒后重试` : '获取验证码' }}
                </button>
              </div>
              <div class="error-container">
                <div v-if="errors.code" class="error-message">{{ errors.code }}</div>
              </div>
            </div>

            <div class="agreement-text">
              登录视为您已阅读并同意天工开物
              <router-link to="/help/user-agreement" >服务条款</router-link> 和<router-link to="/help/privacy-policy" >隐私政策</router-link>
            </div>

            <button
                class="login-btn"
                @click="phoneLogin"
                :disabled="!phoneForm.phone || !phoneForm.code"
            >
              登录
            </button>
            <div class="login-link">
              <a href="#" @click="navigateTo('/register')">立即注册</a>
              <span class="divider">|</span>
              <a href="#" @click="navigateTo('/forgetpass')">忘记密码</a>
            </div>
          </div>

          <!-- Account login form -->
          <div v-if="activeTab === 'account'" class="login-form">
            <p class="form-note">手机号即为登录账号</p>
            <div class="input-group" :class="{ 'error': errors.username }">
              <input
                  type="text"
                  v-model="accountForm.username"
                  placeholder="请输入登录账号"
                  @blur="validateUsername"
              />
              <div class="error-container">
                <div v-if="errors.username" class="error-message">{{ errors.username }}</div>
              </div>
            </div>

            <div class="input-group" :class="{ 'error': errors.password }">
              <div class="password-input-container">
                <input
                    :type="passwordVisible ? 'text' : 'password'"
                    v-model="accountForm.password"
                    placeholder="请输入登录密码"
                    @blur="validatePassword"
                />
                <span class="password-toggle" @click="togglePasswordVisibility">
                  <i :class="['eye-icon', passwordVisible ? 'visible' : '']"></i>
                </span>
              </div>
              <div class="error-container">
                <div v-if="errors.password" class="error-message">{{ errors.password }}</div>
              </div>
            </div>

            <div class="agreement-text">
              登录视为您已阅读并同意天工开物 
              <router-link to="/help/user-agreement" >服务条款</router-link> 和<router-link to="/help/privacy-policy" >隐私政策</router-link>
            </div>

            <button
                class="login-btn"
                @click="accountLogin"
                :disabled="!accountForm.username || !accountForm.password"
            >
              登录
            </button>
            <div class="login-link">
              <a href="#" @click="navigateTo('/register')">立即注册</a>
              <span class="divider">|</span>
              <a href="#" @click="navigateTo('/forgetpass')">忘记密码</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import {postAnyData, getAnyData, postLogin, postJsonData} from "@/api/login";
import { getToken, setToken, removeToken } from '@/utils/auth'
import SlideNotification from '@/components/common/header/SlideNotification.vue';
import Cookies from 'js-cookie'
import backgroundlogin from '@/views/Login/backgroundlogin.vue';

export default {
  name: "login",
  components: {
    SlideNotification,
    backgroundlogin
  },
  data() {
    return {
      activeTab: 'phone', // 默认选中手机号登录
      phoneForm: {
        phone: '',
        code: ''
      },
      accountForm: {
        username: '',
        password: ''
      },
      passwordVisible: false,
      errors: {
        phone: '',
        code: '',
        username: '',
        password: ''
      },
      codeSent: false,
      countdown: 60,
      timer: null,
      showNotification: false,
      notificationMessage: '',
      notificationType: 'success',
      minHeight: '50px'
    }
  },
  created() {
    // 页面创建时检查本地存储中的计时器状态
    this.checkCountdownState();
    this.$emit('hiden-layout')
  },
  activated() {
    this.$emit('hiden-layout', true); // 从缓存返回时再次隐藏
  },
  methods: {
    showNotificationMessage(message, type = 'info') {
      this.notificationMessage = message;
      this.notificationType = type;
      this.showNotification = true;
    },
    //生成公私钥对
    async generateKeyPair() {
      try {
        this.error = null;
        // 生成密钥对
        const keyPair = await crypto.subtle.generateKey(
                {
                  name: 'RSA-OAEP',
                  modulusLength: 2048,
                  publicExponent: new Uint8Array([1, 0, 1]),
                  hash: { name: 'SHA-256' }
                },
                true,
                ['encrypt', 'decrypt']
        );

        // 导出公钥
        const exportedPublicKey = await crypto.subtle.exportKey('spki', keyPair.publicKey);
        const publicKeyPem = btoa(String.fromCharCode(...new Uint8Array(exportedPublicKey)));

        // 导出私钥
        const exportedPrivateKey = await crypto.subtle.exportKey('pkcs8', keyPair.privateKey);
        const privateKeyPem = btoa(String.fromCharCode(...new Uint8Array(exportedPrivateKey)));

        const key = { publicKeyPem, privateKeyPem };
        Cookies.set('publicKey-B',publicKeyPem)
        Cookies.set('privateKey-B',privateKeyPem)
      } catch (err) {
        this.error = '密钥对生成失败，请确保在安全上下文（HTTPS）中运行';
      } finally {
        this.isLoading = false;
      }
    },

    checkCountdownState() {
      // 从localStorage获取倒计时信息
      const storedPhone = localStorage.getItem('verificationPhone');
      const expireTime = localStorage.getItem('verificationExpireTime');

      if (storedPhone && expireTime) {
        const now = new Date().getTime();
        const timeLeft = Math.ceil((parseInt(expireTime) - now) / 1000);

        // 如果倒计时还没结束
        if (timeLeft > 0) {
          this.codeSent = true;
          this.countdown = timeLeft;
          this.startCountdown();

          // 如果当前输入的手机号与存储的手机号一致，应用倒计时限制
          if (this.phoneForm.phone === storedPhone) {
            this.codeSent = true;
          }
        } else {
          // 倒计时已结束，清除存储
          this.clearCountdownStorage();
        }
      }
    },

    clearCountdownStorage() {
      localStorage.removeItem('verificationPhone');
      localStorage.removeItem('verificationExpireTime');
      this.codeSent = false;
      this.countdown = 60;
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
    },

    validatePhone() {
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!this.phoneForm.phone) {
        this.errors.phone = '请输入手机号';
      } else if (!phoneRegex.test(this.phoneForm.phone)) {
        this.errors.phone = '请输入有效的手机号';
      } else {
        this.errors.phone = '';

        // 检查该手机号是否处于冷却期
        const storedPhone = localStorage.getItem('verificationPhone');
        const expireTime = localStorage.getItem('verificationExpireTime');

        if (storedPhone === this.phoneForm.phone && expireTime) {
          const now = new Date().getTime();
          const timeLeft = Math.ceil((parseInt(expireTime) - now) / 1000);

          if (timeLeft > 0) {
            this.codeSent = true;
            this.countdown = timeLeft;
            // 如果没有定时器，则重新启动
            if (!this.timer) {
              this.startCountdown();
            }
          }
        }
      }
    },

    async validateCodegeshi() {
      // 清空错误
      this.errors.code = '';

      // 前端基础验证（保持原有长度判断）
      if (!this.phoneForm.code) {
        this.errors.code = '请输入验证码';
        return false;
      }
      if (this.phoneForm.code.length !== 4 || !/^\d+$/.test(this.phoneForm.code)) {
        this.errors.code = '验证码必须为4位数字';
        return false;
      }
    },
    startCountdown() {
      // 清除可能存在的旧定时器
      if (this.timer) {
        clearInterval(this.timer);
      }

      // 使用固定的时间间隔
      this.timer = setInterval(() => {
        if (this.countdown <= 1) {
          clearInterval(this.timer);
          this.timer = null;
          this.codeSent = false;
          this.countdown = 60;
          // 清除localStorage中的记录
          this.clearCountdownStorage();
        } else {
          this.countdown--;
          // 更新localStorage中的过期时间（可选，保持同步）
          const expireTime = new Date().getTime() + (this.countdown * 1000);
          localStorage.setItem('verificationExpireTime', expireTime.toString());
        }
      }, 1000);
    },
    navigateTo(path) {
      // 如果当前路径与目标路径相同，则重新加载页面
      if (this.$route.path === path) {
        // 先跳转到一个临时路由（如果有的话）或者重新加载页面
        this.$nextTick(() => {
          window.scrollTo({
            top: 0,
            behavior: 'instant' // 使用即时滚动而不是平滑滚动
          });
          this.$router.go(0); // 刷新当前页面
        });
      } else {
        // 不同路径，正常导航并滚动到顶部
        this.$router.push(path);
        window.scrollTo({
          top: 0,
          behavior: 'instant'
        });
      }
      this.currentPath = path;
    },

    validateUsername() {
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!this.accountForm.username) {
        this.errors.username = '请输入登录账号';
      } else if (!phoneRegex.test(this.accountForm.username)) {
        this.errors.username = '请输入有效的登录账号';
      } else {
        this.errors.username = '';
      }
    },

    validatePassword() {
      if (!this.accountForm.password) {
        this.errors.password = '请输入登录密码';
      } else if (this.accountForm.password.length < 8) {
        this.errors.password = '密码长度至少为8位';
      } else {
        this.errors.password = '';
      }
    },


    getVerificationCode() {
      // 先验证手机号格式
      this.validatePhone();
      if (this.errors.phone) return;
      // 显示发送中状态
      this.codeSent = true;
      this.isSendingCode = true;
      // 调用发送验证码接口
      postLogin("/auth/sendCode", { phone: this.phoneForm.phone}).then(res => {
        if (res.data.code === 200) {
          // 存储验证码发送时间和手机号到localStorage
          const now = new Date().getTime();
          const expireTime = now + (60 * 1000); // 当前时间 + 60秒

          localStorage.setItem('verificationPhone', this.phoneForm.phone);
          localStorage.setItem('verificationExpireTime', expireTime.toString());

          this.showNotificationMessage('验证码已发送，可能会有延迟，请耐心等待！', 'success');
          this.startCountdown();
        } else {
          this.errors.code = res.data.msg || '验证码发送失败';
          this.codeSent = false; // 发送失败时可重新发送
          // this.showNotificationMessage(res.data.msg || '验证码发送失败', 'error');
        }
      })
    },
    validateCode() {
      // 清空错误
      this.errors.code = '';

      // 前端基础验证（保持原有长度判断）
      if (!this.phoneForm.code) {
        this.errors.code = '请输入验证码';
        return false;
      }
      if (this.phoneForm.code.length !== 4 || !/^\d+$/.test(this.phoneForm.code)) {
        this.errors.code = '验证码必须为4位数字';
        return false;
      }

      try {
        // 新增接口验证（参数与发送接口一致）
        postAnyData("/auth/verifyCode", {
          phone: this.phoneForm.phone, // 必须携带手机号
          code: this.phoneForm.code
        }).then(res =>{
          if (res.data.code == 200) {
            postLogin("/auth/codeLogin", {
              phone: this.phoneForm.phone, // 必须携带手机号
              code: this.phoneForm.code
            }).then(res =>{
              if (res.data.code == 200) {
                setToken(res.data.token);
                this.$emit("refresh-header")
                this.$router.push('/index');
              } else if (res.data.code !== 200) {
                this.errors.code = res.data.msg || '验证码错误';
                // this.showNotificationMessage(res.data.msg || '验证码错误', 'error');
                return false;
              }
            })
            return true;
          } else if (res.data.code !== 200) {
            this.errors.code = res.data.msg || '验证码错误';
            // this.showNotificationMessage(res.data.msg || '验证码错误', 'error');
            return false;
          }
        })
        return true;
      } catch (error) {
        this.errors.code = '网络异常，请稍后重试';
        // this.showNotificationMessage('网络异常，请稍后重试', 'error');
        return false;
      }
    },

    phoneLogin() {
      this.validatePhone();
      this.validateCode();
      if (this.errors.phone || this.errors.code) {
        // 如果有错误，显示提示信息
        const errorMessage = this.errors.phone || this.errors.code || '请正确填写手机号和验证码';
        // this.showNotificationMessage(errorMessage, 'error');
        return; // 如果表单无效，直接返回，不发送请求
      }
      this.generateKeyPair()
      this.$emit("refresh-header")
    },
    accountLogin() {
      this.validateUsername();
      this.validatePassword();

      // 表单验证失败处理
      if (this.errors.username || this.errors.password) {
        const errorMessage = this.errors.username || this.errors.password || '请正确填写用户名和密码';
        // this.showNotificationMessage(errorMessage, 'error');
        return;
      }

      // 显示加载状态
      const loading = this.$loading ? this.$loading({
        lock: true,
        text: '登录中...',
        spinner: 'el-icon-loading',
      }) : null;

      // 调用登录API
      postLogin("/auth/login", this.accountForm)
          .then(res => {
            // 关闭加载状态
            if (loading) loading.close();

            if (res.data && res.data.code == 200) {
              // 登录成功
              setToken(res.data.token);
              this.showNotificationMessage('登录成功', 'success');
              this.$emit("refresh-header")
              this.$router.push('/index');
              this.generateKeyPair()
            } else {
              // 登录失败，服务器返回了错误码
              this.errors.password = res.data.msg;
              // this.showNotificationMessage(res.data.msg || '登录失败，请检查账号和密码', 'error');
            }
          })
          .catch(error => {
            // 关闭加载状态
            if (loading) loading.close();

            // 处理网络错误或其他异常
            this.errors.password = '网络异常，请稍后重试';
            // this.showNotificationMessage('网络异常，请稍后重试', 'error');
          });
    },

    togglePasswordVisibility() {
      this.passwordVisible = !this.passwordVisible;
    }
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer);
    }
    this.$emit('hiden-layout')
  }
}
</script>

<style scoped>
/* Full page layout */
.login-page {
  display: flex;
  min-height: 100vh;
  overflow: hidden;
}

/* Left side styling */
.left-side {
  flex: 1;
  position: relative;
  background: linear-gradient(135deg, #cdb3e5, #5127d5);
  display: flex;
  flex-direction: column;
  padding: 0rem;
  color: #030303;
}

.logo-container {
  z-index: 2;
  padding: 1rem 0;
}

.logo {
  font-size: 15px;
  font-weight: bold;
  margin: 0;
  color: white;
}

.logo-subtitle {
  font-size: 14px;
  margin: 0;
}

.headline-container {
  margin-top: 10px;
  text-align: left;
  z-index: 2;
  max-width: 80%;
  margin-left: 100px;
}

.headline {
  font-size: 25px;
  font-weight: 500;
  margin-bottom: 1rem;
}

.subheadline {
  font-size: 14px;
  line-height: 1.6;
  opacity: 0.9;
  margin-bottom: 1rem
}

/* Animated background */
.animated-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  opacity: 0.4;
}

.hex-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.hex {
  position: absolute;
  width: 100px;
  height: 110px;
  background: rgba(255, 255, 255, 0.15);
  clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
}

.hex1 {
  top: 20%;
  left: 10%;
  transform: scale(1.5);
  animation: float 8s infinite ease-in-out;
}

.hex2 {
  top: 60%;
  left: 20%;
  transform: scale(1.2);
  animation: float 7s infinite ease-in-out reverse;
}

.hex3 {
  top: 30%;
  left: 50%;
  transform: scale(1.3);
  animation: float 10s infinite ease-in-out 1s;
}

.hex4 {
  top: 70%;
  left: 70%;
  transform: scale(1.1);
  animation: float 6s infinite ease-in-out 2s;
}

.hex5 {
  top: 40%;
  left: 80%;
  transform: scale(1.4);
  animation: float 9s infinite ease-in-out 3s;
}

.floating-cube {
  position: absolute;
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.2);
  top: 25%;
  left: 30%;
  animation: float 8s infinite ease-in-out, rotate 15s infinite linear;
}

.floating-sphere {
  position: absolute;
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.15);
  top: 50%;
  left: 40%;
  animation: float 10s infinite ease-in-out reverse;
}

.floating-diamond {
  position: absolute;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  transform: rotate(45deg);
  top: 65%;
  left: 60%;
  animation: float 7s infinite ease-in-out 2s;
}

.ripple-effect {
  position: absolute;
  width: 200px;
  height: 200px;
  border-radius: 50%;
  border: 3px solid rgba(255, 255, 255, 0.1);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation: ripple 6s infinite linear;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(20px);
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes ripple {
  0% {
    width: 0;
    height: 0;
    opacity: 0.8;
  }
  100% {
    width: 300px;
    height: 300px;
    opacity: 0;
  }
}

/* Right side styling */
.right-side {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f8f9fa;
  padding: 2rem;
}

.login-form-container {
  background-color: white;
  border-radius: 8px;
  padding: 2rem;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.login-form-container h3 {
  font-size: 24px;
  font-weight: 500;
  margin-bottom: 20px;
  text-align: center;
  color: #333;
}

.login-tabs {
  display: flex;
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 20px;
  /*height: 70px;*/
}

.tab-item {
  flex: 1;
  padding: 10px 0;
  text-align: center;
  cursor: pointer;
  position: relative;
  color: #666;
  transition: color 0.3s;
}

.tab-item.active {
  color: #4169E1;
  font-weight: 500;
}

.tab-item.active:after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #4169E1;
}

/* 表单容器，定义固定高度 */
.form-container {
  min-height: 300px;
  position: relative;
  height: 330px;
}

.login-form {
  margin-top: 20px;
  width: 100%;
}

.form-note {
  color: #999;
  font-size: 12px;
  margin-bottom: 15px;
}

.input-group {
  margin-bottom: 20px;
  position: relative;
}

.input-group input {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s;
}

.input-group input:focus {
  outline: none;
  border-color: #6a26cd;
}
/*错误输入框变红*/
.input-group.error input {
  outline: none;
  border: 2px solid #ff4d4f; /* 红色边框 */
  /*background-color: #fff1f0; !* 淡红色背景 *!*/
}


/* 错误信息容器，固定高度 */
.error-container {
  min-height: 10px;
  display: block;
}

/* 验证码输入框与按钮在同一行 */
.verification-code .code-input-container {
  display: flex;
  gap: 10px;
}

.verification-code input {
  flex: 1;
  margin-right: 0; /* 移除原有的右边距 */
}

.error-container {
  order: 3; /* 将错误容器放在最下方 */
  width: 100%;
  margin-top: 4px;
}

.get-code-btn-inline {
  flex-shrink: 0;
  width: 130px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  background-color: #2196f3;
  color: #ffffff;
}

/* 密码输入容器，确保图标垂直居中 */
.password-input-container {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

input[type="password"]::-ms-reveal,
input[type="password"]::-webkit-credentials-auto-fill-button,
input[type="password"]::-webkit-clear-button {
  display: none !important;
  pointer-events: none;
}

.eye-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path><circle cx="12" cy="12" r="3"></circle></svg>');
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0.5;
}

.eye-icon.visible {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path><circle cx="12" cy="12" r="3"></circle><line x1="1" y1="1" x2="23" y2="23"></line></svg>');
}

.agreement-text {
  font-size: 12px;
  color: #999;
  margin-bottom: 20px;
}

.agreement-text a {
  color: #4169E1;
  text-decoration: none;
}

.login-btn {
  width: 100%;
  padding: 12px 0;
  background-color: #2196f3;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.login-btn:hover:not(:disabled) {
  background-color: #4169E1;
}

.login-btn:disabled {
  background-color: #2196f3;
  cursor: not-allowed;
}
/*错误提示词提示*/
.error-message {
  color: #f44336;
  font-size: 12px;
  margin-top: 0px;
  /*margin-left: 5px;*/
  max-height: 10px;
}

/* 人机验证弹窗样式 */
.captcha-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.captcha-container {
  background-color: white;
  border-radius: 8px;
  width: 350px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.captcha-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #eee;
}

.captcha-header h4 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.close-btn {
  font-size: 24px;
  cursor: pointer;
  color: #999;
}

.captcha-content {
  padding: 20px;
}

.captcha-content p {
  margin-top: 0;
  margin-bottom: 20px;
  color: #666;
}

.logo-area {
  flex: 0 0 auto;
  margin-right: 10px;
}

.logo-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  cursor: pointer;
}

.logo-area img {
  height: 30px;
  max-width: 100%;
}

.logo-text {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-left: 10px;
}

/* 调整提示文字大小 */
.input-group input::placeholder {
  font-size: 12px;
}

/* 独立验证码按钮样式 */
.get-code-btn-standalone {
  margin-top: 10px;
  padding: 10px 15px;
  background-color: #2196f3;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.get-code-btn-standalone:hover:not(:disabled) {
  background-color: #5a20b0;
}

.get-code-btn-standalone:disabled {
  background-color: #9254de;
  cursor: not-allowed;
}

/* Login link */
.login-link {
  margin-top: 15px;
  text-align: center;
  font-size: 14px;
}

.login-link a {
  color: #4169E1;
  text-decoration: none;
}
.divider {
  margin: 0 10px;
  color: #ddd;
}
@media screen and (max-width: 768px) {
  .left-side {
    display: none; /* 在手机端隐藏左侧背景 */
  }

  .right-side {
    flex: 1 0 100%; /* 让右侧占据全部宽度 */
    padding: 1rem; /* 减少内边距以适应小屏幕 */
  }

  .login-form-container {
    max-width: 100%; /* 让登录表单占据全部可用宽度 */
    box-shadow: none; /* 移除阴影以节省空间 */
    padding: 1.5rem; /* 调整内边距 */
  }

}
</style>
