{"version": 3, "file": "js/docs18.97d90bf2.js", "mappings": "yHACA,IAAIA,EAA6B,IAAIC,IAAI,aACrCC,EAA6B,IAAID,IAAI,aACrCE,EAA6B,IAAIF,IAAI,aACrCG,EAA6B,IAAIH,IAAI,aACrCI,EAA6B,IAAIJ,IAAI,aACrCK,EAA6B,IAAIL,IAAI,aACrCM,EAA6B,IAAIN,IAAI,aACrCO,EAA6B,IAAIP,IAAI,aACrCQ,EAA6B,IAAIR,IAAI,aAErCS,EAAO,4qBAA+rBV,EAA6B,kJAA4JE,EAA6B,yJAAmKC,EAA6B,+EAAuFC,EAA6B,4OAAwPC,EAA6B,2UAAuVC,EAA6B,ymBAAunBC,EAA6B,4UAA4VC,EAA6B,sBAA4BC,EAA6B,0FAEn6F,c", "sources": ["webpack://portal-ui/./src/docs/minicpm4.md"], "sourcesContent": ["// Imports\nvar ___HTML_LOADER_IMPORT_0___ = new URL(\"./imgs/universal1.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_1___ = new URL(\"./imgs/universal2.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_2___ = new URL(\"./imgs/minicpm3.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_3___ = new URL(\"./imgs/minicpm4.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_4___ = new URL(\"./imgs/minicpm5.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_5___ = new URL(\"./imgs/minicpm6.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_6___ = new URL(\"./imgs/minicpm7.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_7___ = new URL(\"./imgs/minicpm8.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_8___ = new URL(\"./imgs/minicpm9.png\", import.meta.url);\n// Module\nvar code = \"<h1 id=\\\"容器化部署-minicpm4\\\">容器化部署 minicpm4</h1> <p><font style=\\\"color:#020817\\\">本指南全面介绍了在天工开物平台上部署 minicpm4 大语言模型 API 服务的完整解决方案。该方案不仅提供了详实的部署流程，还提供了如何制作镜像的方案。</font></p> <blockquote> <p>此镜像提供了标准化的API 接口，让您能够便捷地通过 <strong>API 调用方式</strong>访问和使用所有功能。目前还不支持 <strong>Web UI</strong> 方式使用服务，需要您本地启动适配的 Web UI。</p> </blockquote> <h2 id=\\\"1部署服务\\\"><strong>1.部署服务</strong></h2> <p><font style=\\\"color:#020817\\\">点击这里 <a href=\\\"https://www.tiangongkaiwu.top/portal/#/console\\\"><font style=\\\"color:#06c\\\">部署服务</font></a> ，登录后根据页面提示进行部署。选择合适的设备，在服务配置中输入镜像地址，部署服务，完成！</font></p> <h3 id=\\\"11-访问天工开物控制台，点击新增部署\\\">1.1 访问天工开物<a href=\\\"https://www.tiangongkaiwu.top/portal/#/console\\\">控制台</a>，点击新增部署</h3> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_0___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"12-选择设备\\\">1.2 选择设备</h3> <p><font style=\\\"color:#020817\\\">基于自身需要进行配置，参考配置为单卡 4090 和 1 个节点（初次使用进行调试）。</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_1___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"13-选择相应预制镜像\\\">1.3 选择相应预制镜像</h3> <p><font style=\\\"color:#020817\\\">选择 <strong>minicpm4 预制镜像</strong>，点击部署服务。</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_2___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"14-耐心等待节点拉取镜像并启动\\\">1.4 耐心等待节点拉取镜像并启动</h3> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_3___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"15-部署完成\\\">1.5 部署完成</h3> <p><font style=\\\"color:#020817\\\">在部署完成页面，能看到一个公开访问链接。这个链接就是 Ollama 服务的 API 访问地址。</font></p> <p><font style=\\\"color:#020817\\\">将这个 API 地址复制下来，就可以在任何支持 Ollama 协议的应用程序中使用。</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_4___ + \"\\\" alt=\\\"\\\"></p> <p><font style=\\\"color:#020817\\\">在“常规”面板里可以看到公开访问的地址，此地址即为 Ollama 服务的 API 地址。 请耐心一点~~ 模型镜像会比较大，<strong>minicpm4 镜像本身 20G+，打包之后大约 40G+，</strong> 拉取镜像会需要一段时间。</font></p> <h3 id=\\\"16-验证一下\\\">1.6 验证一下</h3> <p><font style=\\\"color:#020817\\\">访问复制的链接，{快捷访问的地址} /api/tags，将链接复制到浏览器，就可以看到以下内容，说明模型已经部署并运行了。</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_5___ + \"\\\" alt=\\\"\\\"></p> <p><font style=\\\"color:#020817\\\">如果需要在其他兼容 Ollama 的客户端使用时，需要提供的参数如下：</font></p> <ul> <li>访问地址</li> </ul> <p><font style=\\\"color:#020817\\\">常规 -&gt; 快捷访问中 11434 对应的链接。有的会需要在链接后面加上 /api</font></p> <ul> <li>ModelId</li> </ul> <p><strong>minicpm4-8b</strong></p> <ul> <li>上下文长度</li> </ul> <p>32k</p> <ul> <li>模型建议的其他参数（非必须，可以根据需要自行修改）</li> </ul> <pre><code class=\\\"language-dockerfile\\\">{\\n    &quot;repeat_penalty&quot;: 1,\\n    &quot;temperature&quot;: 0.6,\\n    &quot;top_k&quot;: 20,\\n    &quot;top_p&quot;: 0.95\\n}\\n</code></pre> <p><font style=\\\"color:#020817\\\">使用第三方客户端时，可以按照下图填写内容</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_6___ + \"\\\" alt=\\\"\\\"></p> <h2 id=\\\"2模型速度测试\\\">2.模型速度测试</h2> <p><font style=\\\"color:#020817\\\">minicpm4 部署完成了，速度怎么样呢？点击 <a href=\\\"https://lmspeed.net/zh-CN\\\"><font style=\\\"color:#06c\\\">LM Speed</font></a> 测试一下速度吧~~~</font></p> <p><font style=\\\"color:#020817\\\">如果 LM Speed 无法访问，多刷新几次就可以了😊</font></p> <p><strong>基础 URL 后面记得加 /v1</strong></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_7___ + \"\\\" alt=\\\"\\\"><img src=\\\"\" + ___HTML_LOADER_IMPORT_8___ + \"\\\" alt=\\\"\\\"></p> <p><br><br> <font style=\\\"color:#b2b2b2\\\">最后更新于: 2025/7/3 15:25</font></p> \";\n// Exports\nexport default code;"], "names": ["___HTML_LOADER_IMPORT_0___", "URL", "___HTML_LOADER_IMPORT_1___", "___HTML_LOADER_IMPORT_2___", "___HTML_LOADER_IMPORT_3___", "___HTML_LOADER_IMPORT_4___", "___HTML_LOADER_IMPORT_5___", "___HTML_LOADER_IMPORT_6___", "___HTML_LOADER_IMPORT_7___", "___HTML_LOADER_IMPORT_8___", "code"], "sourceRoot": ""}