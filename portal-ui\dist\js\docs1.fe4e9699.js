"use strict";(self["webpackChunkportal_ui"]=self["webpackChunkportal_ui"]||[]).push([[3168],{4670:function(t,e,o){o.r(e);var n=new URL(o(1404),o.b),r=new URL(o(4117),o.b),l=new URL(o(5766),o.b),i=new URL(o(5746),o.b),p=new URL(o(7225),o.b),s=new URL(o(5066),o.b),a=new URL(o(4475),o.b),c=new URL(o(231),o.b),u=new URL(o(798),o.b),d=new URL(o(1089),o.b),g='<h1 id="容器化部署-ace-step">容器化部署 ACE-Step</h1> <p><font style="color:#020817">本指南详细阐述了在天工开物平台上，高效部署与使用 ACE-Step 项目的技术方案。ACE-Step 是由人工智能公司阶跃星辰（StepFun）与数字音乐平台 ACE Studio 联合研发并于 2025 年 5 月 7 日开源。模型在 A100 GPU 上只需 20 秒即可合成长达 4 分钟的音乐，比基于 LLM 的基线快 15 倍，同时在旋律、和声和节奏指标方面实现了卓越的音乐连贯性和歌词对齐。此外，该模型保留了精细的声学细节，支持高级控制机制，例如语音克隆、歌词编辑、混音和音轨生成。</font></p> <h2 id="1在天工开物上运行-ace-step"><strong>1.在天工开物上运行 ACE-Step</strong></h2> <p><font style="color:#020817">天工开物平台提供预构建的 ACE-Step 容器镜像，用户无需本地复杂环境配置，可快速完成部署并启用服务。以下是详细部署步骤：</font></p> <h3 id="11-创建部署服务"><strong>1.1 创建部署服务</strong></h3> <p><font style="color:#020817">登录<a href="https://tiangongkaiwu.top/portal/#/console"><font style="color:#06c">天工开物控制台</font></a>，在控制台首页点击“弹性部署服务”进入管理页面。</font></p> <p><img src="'+n+'" alt=""></p> <h3 id="12-选择-gpu-型号"><strong>1.2 选择 GPU 型号</strong></h3> <p><font style="color:#020817">根据实际需求选择 GPU 型号：</font></p> <p><font style="color:#020817">初次使用或调试阶段，推荐配置单张 NVIDIA RTX 4090 GPU</font></p> <p><img src="'+r+'" alt=""></p> <h3 id="13-选择预制镜像"><strong>1.3 选择预制镜像</strong></h3> <p><font style="color:#020817">在“服务配置”模块切换至“预制服务”选项卡，选择 ACE-Step 官方镜像。</font></p> <h3 id="14-部署并访问服务"><strong>1.4 部署并访问服务</strong></h3> <p><font style="color:#020817">点击“部署服务”，平台将自动拉取镜像并启动容器。</font></p> <p><img src="'+l+'" alt=""></p> <p><font style="color:#020817">部署完成后，在“快捷访问”中找到端口为 7865 的公网访问链接，点击即可在浏览器中使用 ACE-Step 的 Web 界面，或通过该地址调用 API 服务。</font></p> <h2 id="2快速上手"><strong>2.快速上手</strong></h2> <p><font style="color:#020817">使用 Safari 浏览器时，音频可能无法直接播放，需要下载后进行播放。</font></p> <p><font style="color:#020817">该项目提供多任务创作面板：Text2Music Tab、Retake Tab、Repainting Tab、Edit Tab 和 Extend Tab。</font></p> <p><font style="color:#020817">各模块功能如下：</font></p> <h3 id="21-text2music-tab">2.1 Text2Music Tab</h3> <ul> <li>Input Fields<ul> <li>Tags：输入描述性标签、音乐流派或场景描述，用逗号分隔</li> <li>Lyrics：输入带有结构标签的歌词，如 [verse] 、 [chorus] 、 [bridge]</li> <li>Audio Duration：设置生成音频的时长（-1 表示随机生成）</li> </ul> </li> <li>Settings<ul> <li>Basic Settings：调整推理步数、指导比例和种子值</li> <li>Advanced Settings：微调调度器类型、CFG 类型、ERG 设置等参数</li> </ul> </li> <li>Generation<ul> <li>点击「Generate」按钮，根据输入内容创作音乐</li> </ul> </li> </ul> <p><img src="'+i+'" alt=""><img src="'+p+'" alt=""></p> <p><font style="color:#020817">生成结果：</font></p> <p><img src="'+s+'" alt=""></p> <h3 id="22-retake-tab">2.2 Retake Tab</h3> <ul> <li>通过不同种子值重新生成音乐并产生细微变化</li> <li>调整变化参数以控制新版本与原版的差异程度</li> </ul> <p><img src="'+a+'" alt=""></p> <h3 id="23-edit-tab">2.3 Edit Tab</h3> <ul> <li>通过修改标签或歌词来改编现有音乐</li> <li>可选择「only_lyrics」模式（保留原旋律）或「remix」模式（改变旋律）</li> <li>通过调整编辑参数控制对原曲的保留程度</li> </ul> <p><img src="'+c+'" alt=""></p> <h3 id="24-extend-tab">2.4 Extend Tab</h3> <ul> <li>在现有音乐的开头或结尾添加音乐片段</li> <li>指定左右两侧的扩展时长</li> <li>选择需要扩展的源音频</li> </ul> <p><img src="'+u+'" alt=""></p> <h2 id="3api-调用指南">3.API 调用指南</h2> <p><font style="color:#020817">ACE-Step 提供完整的 API 接口体系，支持通过编程方式实现音乐创作全流程自动化。以下为核心接口详解与调用示范：</font></p> <p><img src="'+d+'" alt=""></p> <h3 id="31-环境准备"><strong>3.1 环境准备</strong></h3> <pre><code class="language-powershell">pip install gradio_client\n\nfrom gradio_client import Client, handle_file\nclient = Client(&quot;https://&lt;您的部署 ID&gt;.550c.cloud/&quot;)\n</code></pre> <h3 id="32-核心功能接口"><strong>3.2 核心功能接口</strong></h3> <p><strong>1. 文本生成音乐（Text2Music）</strong></p> <pre><code class="language-python">result = client.predict(\n    format=&quot;wav&quot;,                         # 输出格式 [mp3/ogg/flac/wav]\n    audio_duration=-1,                    # 时长 (秒)，-1=随机生成\n    prompt=&quot;pop, upbeat, guitar, 120 BPM&quot;, # 音乐描述标签\n    lyrics=&quot;[verse] 清晨的阳光...[chorus] 自由飞翔...&quot;,  # 带结构标签的歌词\n    infer_step=60,                        # 推理步数（建议 50-80）\n    guidance_scale=15,                    # 控制生成自由度\n    cfg_type=&quot;apg&quot;,                       # 配置类型 [cfg/apg/cfg_star]\n    manual_seeds=&quot;12345&quot;,                 # 固定种子值保证可复现\n    api_name=&quot;/__call__&quot;                  # 固定端点名称\n)\naudio_path = result[0]  # 生成的音频路径\nparams_json = result[1] # 参数 JSON（用于后续操作）\n</code></pre> <p><strong>2. 音乐编辑（Edit）</strong></p> <pre><code class="language-python">result = client.predict(\n    edit_type=&quot;remix&quot;,                    # 编辑模式 [only_lyrics/remix]\n    edit_prompt=&quot;rock, electric guitar&quot;,  # 新音乐标签\n    edit_lyrics=&quot;[chorus] 新的副歌歌词...&quot;,  # 新歌词\n    edit_n_min=0.7,                       # 最小保留比例（0-1）\n    source_audio=handle_file(&quot;原曲.wav&quot;), # 上传待编辑音频\n    api_name=&quot;/edit_process_func&quot;\n)\n</code></pre> <p><strong>3. 音乐扩展（Extend）</strong></p> <pre><code class="language-python">result = client.predict(\n    left_extend_length=10,     # 开头延长秒数\n    right_extend_length=15,    # 结尾延长秒数\n    extend_source=&quot;text2music&quot;,# 源类型 [text2music/upload]\n    source_audio=handle_file(&quot;原曲.wav&quot;),\n    api_name=&quot;/extend_process_func&quot;\n)\n</code></pre> <p><strong>4. 局部重生成（Retake）</strong></p> <pre><code class="language-python">result = client.predict(\n    json_data=params_json,     # 原始生成参数\n    retake_variance=0.3,       # 变化强度（0.1 微调，&gt;0.5 巨变）\n    retake_seeds=&quot;67890&quot;,      # 新种子值\n    api_name=&quot;/retake_process_func&quot;\n)\n</code></pre> <h3 id="33-高级控制参数"><strong>3.3 高级控制参数</strong></h3> <table> <thead> <tr> <th><strong>参数</strong></th> <th><strong>作用</strong></th> <th><strong>推荐值</strong></th> </tr> </thead> <tbody><tr> <td><font style="color:#2f8ef4;background-color:#eff0f0">guidance_interval</font></td> <td>控制节奏变化密度</td> <td>0.3-0.7</td> </tr> <tr> <td><font style="color:#2f8ef4;background-color:#eff0f0">omega_scale</font></td> <td>音符粒度精细度</td> <td>8-12</td> </tr> <tr> <td><font style="color:#2f8ef4;background-color:#eff0f0">use_erg_diffusion</font></td> <td>启用声学细节增强</td> <td>True</td> </tr> <tr> <td><font style="color:#2f8ef4;background-color:#eff0f0">ref_audio_strength</font></td> <td>语音克隆强度（需上传参考音频）</td> <td>0.5-0.8</td> </tr> <tr> <td><font style="color:#2f8ef4;background-color:#eff0f0">lora_weight</font></td> <td>风格 LoRA 权重（如中文说唱）</td> <td>0.7-1.0</td> </tr> </tbody></table> <p><font style="color:#020817">通过 API 集成，开发者可构建自动化音乐生产线，结合 Retake 的种子控制、Edit 的歌词替换、Extend 的时长扩展等功能，实现全链路音乐创作智能化。</font></p> <p><br><br> <font style="color:#b2b2b2">最后更新于: 2025/6/30 10:51</font></p> ';e["default"]=g},1089:function(t,e,o){t.exports=o.p+"img/acestep10.4ff2a6d2.png"},5766:function(t,e,o){t.exports=o.p+"img/acestep3.c43403ae.png"},5746:function(t,e,o){t.exports=o.p+"img/acestep4.33b76526.png"},7225:function(t,e,o){t.exports=o.p+"img/acestep5.6c011420.png"},5066:function(t,e,o){t.exports=o.p+"img/acestep6.3b0cb334.png"},4475:function(t,e,o){t.exports=o.p+"img/acestep7.461f2903.png"},231:function(t,e,o){t.exports=o.p+"img/acestep8.cf5ed5e0.png"},798:function(t,e,o){t.exports=o.p+"img/acestep9.6ce300fd.png"},1404:function(t,e,o){t.exports=o.p+"img/universal1.9a1b3f4b.png"},4117:function(t,e,o){t.exports=o.p+"img/universal2.4306636e.png"}}]);
//# sourceMappingURL=docs1.fe4e9699.js.map