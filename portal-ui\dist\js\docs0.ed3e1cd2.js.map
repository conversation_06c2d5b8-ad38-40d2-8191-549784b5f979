{"version": 3, "file": "js/docs0.ed3e1cd2.js", "mappings": "yHACA,IAAIA,EAA6B,IAAIC,IAAI,aACrCC,EAA6B,IAAID,IAAI,aACrCE,EAA6B,IAAIF,IAAI,aACrCG,EAA6B,IAAIH,IAAI,aACrCI,EAA6B,IAAIJ,IAAI,WACrCK,EAA6B,IAAIL,IAAI,aACrCM,EAA6B,IAAIN,IAAI,aACrCO,EAA6B,IAAIP,IAAI,aACrCQ,EAA6B,IAAIR,IAAI,WACrCS,EAA6B,IAAIT,IAAI,aACrCU,EAA8B,IAAIV,IAAI,aACtCW,EAA8B,IAAIX,IAAI,aACtCY,EAA8B,IAAIZ,IAAI,aAEtCa,EAAO,6QAAwRd,EAA6B,yIAAiJE,EAA6B,qEAA6EC,EAA6B,+FAAuGC,EAA6B,yGAAiHC,EAA6B,4XAAwYC,EAA6B,sJAA8JC,EAA6B,sHAA8HC,EAA6B,sXAAoYC,EAA6B,slBAA0mBC,EAA6B,iLAA2LC,EAA8B,+iBAAmkBC,EAA8B,yQAAqRC,EAA8B,mZAE1vH,c", "sources": ["webpack://portal-ui/./src/docs/Flux.1-kontext-dev.md"], "sourcesContent": ["// Imports\nvar ___HTML_LOADER_IMPORT_0___ = new URL(\"./imgs/universal1.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_1___ = new URL(\"./imgs/universal2.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_2___ = new URL(\"./imgs/kontextdev3.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_3___ = new URL(\"./imgs/kontextdev4.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_4___ = new URL(\"./imgs/kontextdev5.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_5___ = new URL(\"./imgs/kontextdev6.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_6___ = new URL(\"./imgs/kontextdev7.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_7___ = new URL(\"./imgs/kontextdev8.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_8___ = new URL(\"./imgs/kontextdev9.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_9___ = new URL(\"./imgs/kontextdev10.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_10___ = new URL(\"./imgs/kontextdev11.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_11___ = new URL(\"./imgs/kontextdev12.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_12___ = new URL(\"./imgs/kontextdev13.png\", import.meta.url);\n// Module\nvar code = \"<h1 id=\\\"容器化部署-flux1-kontext-dev-图片编辑模型应用\\\">容器化部署 Flux.1 Kontext Dev 图片编辑模型应用</h1> <h2 id=\\\"1-部署步骤\\\">1 部署步骤</h2> <h3 id=\\\"11-登录天工开物控制台，在控制台首页点击弹性部署服务进入管理页面。\\\">1.1 登录<a href=\\\"https://tiangongkaiwu.top/portal/#/console\\\">天工开物控制台</a>，在控制台首页点击“弹性部署服务”进入管理页面。</h3> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_0___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"12-基于自身需要进行配置，参考配置为单卡-4090-和-1-个节点（初次使用进行调试）。\\\">1.2 基于自身需要进行配置，参考配置为单卡 4090 和 1 个节点（初次使用进行调试）。</h3> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_1___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"13-选择相应预制镜像\\\">1.3 选择相应预制镜像</h3> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_2___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"14-点击部署服务，耐心等待节点拉取镜像并启动。\\\">1.4 点击部署服务，耐心等待节点拉取镜像并启动。</h3> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_3___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"15-节点启动后，你所在任务详情页中看到的内容可能如下：\\\">1.5 节点启动后，你所在“任务详情页”中看到的内容可能如下：</h3> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_4___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"16-我们可以点击快速访问下方8188端口的链接，测试-comfyui-部署情况\\\">1.6 我们可以点击快速访问下方“8188”端口的链接，测试 comfyui 部署情况</h3> <p><font style=\\\"color:#020817\\\">系统会自动分配一个可公网访问的域名，点击 8188 端口的链接。接下来我们即可自由地通过使用工作流在 comfyui 中使用 Flux 模型进行图像生成。</font></p> <p><font style=\\\"color:#020817\\\">我们进入 8188 端口的 web ui 界面，选择左侧的“工作流“菜单，找到名为”flux_1_kontext_dev_basic.json“的工作流文件，鼠标双击进行插入。</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_5___ + \"\\\" alt=\\\"\\\"></p> <p><font style=\\\"color:#020817\\\">随后，我们可以看到工作流已经被正常载入 ComfyUI 了。接下来，我们找到图片上传节点，上传我们想要编辑的图片。我这里选取了一张动漫风格的图片作为示例。</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_6___ + \"\\\" alt=\\\"\\\"></p> <p><font style=\\\"color:#020817\\\">接下来，我们找到 prompt（提示词）的填写节点，输入“我们希望如何转变图像”的提示词。</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_7___ + \"\\\" alt=\\\"\\\"></p> <p><font style=\\\"color:#020817\\\">我希望将女孩的头发转变成蓝色，参考的 prompt 如下（仅支持英文）：</font></p> <p>convert the girl’s hair to blue</p> <p><font style=\\\"color:#020817\\\">稍等片刻，即可在后方的“保存图像”节点看到基于我们提示词生成的图片。</font></p> <p><font style=\\\"color:#020817\\\">可以看到，图片的一致性保存得很好，原有图片的纹理和细节大都能复刻下来。</font></p> <p><font style=\\\"color:#020817\\\">右键点击图片，选择“Save Image”即可保存图片。</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_8___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"17-通过-api-的形式来调用-comfyui-进行图像生成\\\">1.7 通过 API 的形式来调用 comfyui 进行图像生成</h3> <p><font style=\\\"color:#020817\\\">我们不推荐直接使用 comfyui 的默认 API 接口，因为多节点时需自行解决无状态问题。更推荐的做法是通过我们暴露的 3000 端口进行请求，其通过 <a href=\\\"https://github.com/SaladTechnologies/comfyui-api?tab=readme-ov-file\\\"><font style=\\\"color:#06c\\\">comfyui-api</font></a> 进行包装，支持默认的同步生图请求和 webhook 实现。</font></p> <p><font style=\\\"color:#020817\\\">以下以 POSTMAN 为例，简要描述如何向 3000 端口发送图片生成的 API 请求：</font></p> <h4 id=\\\"171-保存页面工作流\\\">1.7.1 保存页面工作流</h4> <p><font style=\\\"color:#020817\\\">点击”导航栏——工作流——导出（API）“菜单，浏览器会自动下载一个 json 文件。</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_9___ + \"\\\" alt=\\\"\\\"></p> <h4 id=\\\"172-打开-postman，新建一个-post-请求\\\">1.7.2 打开 POSTMAN，新建一个 POST 请求</h4> <p><font style=\\\"color:#020817\\\">新建一个 POST 请求，并命名为”prompt“，如下图所示：</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_10___ + \"\\\" alt=\\\"\\\"></p> <h4 id=\\\"173-完善请求信息\\\">1.7.3 完善请求信息</h4> <p><font style=\\\"color:#020817\\\">需要完善的信息如下：</font></p> <ul> <li><strong>请求的 URL</strong></li> </ul> <p><font style=\\\"color:#020817\\\">在 3000 端口的回传链接后加上“/prompt”的路径，保证其格式类似于<font style=\\\"color:#2f8ef4;background-color:#eff0f0\\\"><a href=\\\"https://xxx/prompt\\\">https://xxx/prompt</a></font>。</font></p> <ul> <li><strong>将请求体参数格式设置为 raw 和 json</strong></li> </ul> <p><font style=\\\"color:#020817\\\">如图。</font></p> <ul> <li><strong>设置参数内容基本格式</strong></li> </ul> <p><font style=\\\"color:#020817\\\">如图。</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_11___ + \"\\\" alt=\\\"\\\"></p> <h4 id=\\\"174-将我们下载好的工作流-json-文件粘贴为参数中prompt字段的值\\\">1.7.4 将我们下载好的工作流 json 文件粘贴为参数中<font style=\\\"color:#2f8ef4;background-color:#eff0f0\\\">prompt</font>字段的值</h4> <p><font style=\\\"color:#020817\\\">如下图所示，我们将鼠标移动至 prompt 字段的冒号后，粘贴工作流的内容。</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_12___ + \"\\\" alt=\\\"\\\"></p> <h4 id=\\\"175-上传图片\\\">1.7.5 上传图片</h4> <p><font style=\\\"color:#020817\\\">Flux.1 Kontext Dev 的工作流中，有个节点类型为“LoadImageOutput”</font></p> <h4 id=\\\"176-发送请求\\\">1.7.6 发送请求</h4> <p><font style=\\\"color:#020817\\\">返回结果如下所示，<font style=\\\"color:#2f8ef4;background-color:#eff0f0\\\">images</font>字段包含一个字符数组，其中的元素即为生成图片的 base64 编码。</font></p> <p><br><br> <font style=\\\"color:#b2b2b2\\\">最后更新于: 2025/7/1 15:35</font> </p> \";\n// Exports\nexport default code;"], "names": ["___HTML_LOADER_IMPORT_0___", "URL", "___HTML_LOADER_IMPORT_1___", "___HTML_LOADER_IMPORT_2___", "___HTML_LOADER_IMPORT_3___", "___HTML_LOADER_IMPORT_4___", "___HTML_LOADER_IMPORT_5___", "___HTML_LOADER_IMPORT_6___", "___HTML_LOADER_IMPORT_7___", "___HTML_LOADER_IMPORT_8___", "___HTML_LOADER_IMPORT_9___", "___HTML_LOADER_IMPORT_10___", "___HTML_LOADER_IMPORT_11___", "___HTML_LOADER_IMPORT_12___", "code"], "sourceRoot": ""}