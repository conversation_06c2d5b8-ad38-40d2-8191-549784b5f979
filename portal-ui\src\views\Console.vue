<template>
  <div class="iframe-page">
    <div class="iframe-container">
      <iframe
          :key="iframeKey"
          ref="iframe"
          :src="iframeSrc"
      ></iframe>
    </div>
  </div>
</template>

<script>
import Cookies from 'js-cookie'
export default {
  name: "Console",

  mounted() {
    const token = this.getCookie('Admin-Token');
    const isReal = this.getCookie('isReal');

    if (!token || isReal !== '1' ) {
      console.warn('[ConsolePage] 鉴权失败：未登录 / 未实名 跳转首页');
      window.location.hash = '#/index';
    }

    // 监听iframe回传的消息
    // window.addEventListener('message', (event) => {
    //     console.log('=== 收到iframe回传消息 ===');
    //     console.log('消息来源:', event.origin);
    //     console.log('消息数据:', event.data);
    //     console.log('消息类型:', typeof event.data);
    //     console.log('时间戳:', new Date().toLocaleTimeString());
    //     console.log('========================');
    // }
    // );

    this.$nextTick(() => {
      const iframe = this.$refs.iframe;
      if (iframe) {
        iframe.addEventListener('load', () => {
          setTimeout(() => {
            this.handleIframeLoad();
          }, 500);
        }, { once: true });
      }
    });
  },
  data(){
    return{
      error:null,
      keyPair:null,
      // 生产环境替换为 https://suanli.cn?type=other
      iframeSrc: null,
      config: {
      },
      iframeKey: 0,
      hasPostedMessage: false,
    }
  },
  created() {
    this.refreshIframe()
  },
  methods:{
    // 强制禁用浏览器缓存
    refreshIframe() {
      Object.keys(localStorage)
          .filter(key => key.startsWith('Hm_'))
          .forEach(key => localStorage.removeItem(key));
      // 同时更新 key 和 src
      this.iframeKey += 1;
      this.hasPostedMessage = false; // 重置发送标志
      this.iframeSrc = `https://console.suanli.cn/serverless/idc?type=other&isHiddenPrice=true`;
      if (this.config.token){
        this.config={
          "token":Cookies.get('suanlemeToken'),
          "rsa_pubk":Cookies.get('publicKey-C'),
          "rsa_prik":Cookies.get('privateKey-B'),
        }

      }
      // 确保 config 同步更新
      this.config = {
        token: Cookies.get('suanlemeToken'),
        rsa_pubk: Cookies.get('publicKey-C'),
        rsa_prik: Cookies.get('privateKey-B'),
      };

    },
    handleIframeLoad() {
      const iframe = this.$refs.iframe
      if (iframe && iframe.contentWindow) {
        // 防止重复发送
        if (this.hasPostedMessage) {
          // console.log('已发送过认证数据，跳过重复发送');
          return;
        }

        // 校验三个关键参数
        const token = this.config.token;
        const publicKey = this.config.rsa_pubk;
        const privateKey = this.config.rsa_prik;

        // console.log('=== iframe数据校验 ===');
        // console.log('Token:', token ? '存在' : '缺失',token);
        // console.log('公钥C:', publicKey ? '存在' : '缺失',publicKey);
        // console.log('私钥B:', privateKey ? '存在' : '缺失',privateKey);

        // 三个参数都存在才发送
        if (token && publicKey && privateKey) {
          // console.log('数据完整，发送给iframe');
          // console.log('=== 发送数据详情 ===');
          // console.log('发送时间:', new Date().toLocaleTimeString());
          // console.log('iframe URL:', iframe.src);

          // 转换为纯JavaScript对象，避免Vue响应式包装
          const pureData = {
            token: token,
            rsa_pubk: publicKey,
            rsa_prik: privateKey
          };

          // console.log('发送的完整数据:', pureData);
          // console.log('JSON序列化测试:', JSON.stringify(pureData));
          // console.log('数据类型检查:', typeof pureData);
          // console.log('是否为纯对象:', pureData.constructor === Object);
          // console.log('==================');

          iframe.contentWindow.postMessage(pureData, '*');
          this.hasPostedMessage = true; // 标记已发送

          console.log(' postMessage已发送');

          // 手动触发按钮状态更新
          if (this.$parent && this.$parent.$refs && this.$parent.$refs.header) {
            this.$parent.$refs.header.isConsoleReady = true;
            this.$parent.$refs.header.isConsoleLoading = false;
          }
        } else {
          // console.log('认证数据不完整，延迟重试...');
          // 数据不完整时，延迟重试
          setTimeout(() => {
            // console.log('重试获取认证数据...');
            // 重新获取Cookie数据
            this.config = {
              token: Cookies.get('suanlemeToken'),
              rsa_pubk: Cookies.get('publicKey-C'),
              rsa_prik: Cookies.get('privateKey-B'),
            };
            // 递归重试
            this.handleIframeLoad();
          }, 1000);
        }

      }
    },
    getCookie(name) {
      const match = document.cookie.match(new RegExp('(^| )' + name + '=([^;]+)'));
      return match ? match[2] : null;
    },
  },
  beforeDestroy() {

  },
}
</script>

<style scoped>
.iframe-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.header {
  width: 100%;
  height: 60px;
  background-color: #1a73e8;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.content {
  flex: 1;
  display: flex;
}

.sidebar {
  width: 200px;
  background-color: #000000;
  color: #ffffff;
  padding: 20px;
}

.iframe-container {
  flex: 1;
}

iframe {
  width: 100%;
  height: 100%;
  border: none;
}
</style>
