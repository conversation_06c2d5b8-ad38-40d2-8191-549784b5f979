"use strict";(self["webpackChunkportal_ui"]=self["webpackChunkportal_ui"]||[]).push([[6433],{6433:function(s,e,t){t.r(e),t.d(e,{default:function(){return v}});var a=function(){var s=this,e=s._self._c;return e("div",{staticClass:"personal-center"},[s.showNotification?e("SlideNotification",{attrs:{message:s.notificationMessage,type:s.notificationType},on:{close:function(e){s.showNotification=!1}}}):s._e(),e("div",{staticClass:"content-wrapper"},[e("div",{staticClass:"left-navigation"},[e("div",{staticClass:"center-title"},[s._v("个人中心")]),e("div",{staticClass:"nav-menu"},[e("div",{staticClass:"nav-item1",class:{active:"basic"===s.activeTab},on:{click:function(e){return s.switchTab("basic")}}},[s._v(" 基本信息 ")]),e("div",{staticClass:"nav-item1",class:{active:"verification"===s.activeTab},on:{click:function(e){return s.switchTab("verification")}}},[s._v(" 实名认证 ")])])]),e("div",{staticClass:"main-container"},["basic"===s.activeTab?e("div",{staticClass:"tab-content"},[s._m(0),e("div",{staticClass:"user-info-container"},[e("div",{staticClass:"profile-card"},[e("h3",{staticClass:"card-title"},[s._v("用户信息")]),e("div",{staticClass:"user-avatar-section"},[e("div",{staticClass:"avatar"},[s.user.avatarUrl?e("img",{staticClass:"avatar-img",attrs:{src:s.user.avatarUrl}}):e("span",{staticClass:"avatar-text"},[s._v(s._s(s.userInitial()))])]),e("div",{staticClass:"username-section"},[e("div",{staticClass:"username"},[s._v(" "+s._s(s.user.nickName||"未设置")+" "),e("span",{staticClass:"edit-icon",on:{click:function(e){s.showUsernameModal=!0}}},[s._v("🖊")])]),e("div",{staticClass:"user-info-item"},[e("span",{staticClass:"info-label"},[s._v("手机号")]),e("span",[s._v(s._s(s.user.phone||"未绑定"))])]),e("div",{staticClass:"user-info-item"},[e("span",{staticClass:"info-label"},[s._v("性别")]),e("span",[s._v(s._s(s.user.sex||"未设置"))]),e("span",{staticClass:"edit-icon",on:{click:function(e){s.showGenderModal=!0}}},[s._v("🖊")])]),e("div",{staticClass:"user-info-item"},[e("span",{staticClass:"info-label"},[s._v("余额")]),e("span",[s._v("¥"+s._s(s.user.balance?.toFixed(2)||"0.00"))])]),e("div",{staticClass:"verification-badge",on:{click:s.openIdVerification}},[e("span",{staticClass:"badge"},[e("span",{staticClass:"check-icon"}),s._v(" 个人认证 "),s.verificationStatus?e("span",{staticClass:"status-text"},[s._v("("+s._s(s.verificationStatus)+")")]):s._e()])])])])]),e("div",{staticClass:"login-card"},[e("h3",{staticClass:"card-title"},[s._v("登录信息")]),e("div",{staticClass:"login-section"},[e("h4",{staticClass:"section-subtitle"},[s._v("账号密码")]),e("div",{staticClass:"login-item"},[e("div",{staticClass:"login-label"},[s._v("账号")]),e("div",{staticClass:"login-value"},[s._v(" "+s._s(s.user.username)+" ")])]),e("div",{staticClass:"login-item"},[e("div",{staticClass:"login-label"},[s._v("密码")]),e("div",{staticClass:"login-content"},[e("div",{staticClass:"login-description"},[s._v("设置密码后可通过账号登录")]),e("div",{staticClass:"login-value"},[s._v(" •••••••• "),e("button",{staticClass:"edit-btn",on:{click:function(e){s.showPasswordModal=!0}}},[s._v("修改")])])])])]),e("div",{staticClass:"login-section"},[e("h4",{staticClass:"section-subtitle"},[s._v("安全手机")]),e("div",{staticClass:"login-item"},[e("div",{staticClass:"login-label"},[s._v("手机号")]),e("div",{staticClass:"login-value"},[s._v(" "+s._s(s.user.phone)+" ")])])]),s.user.email?e("div",{staticClass:"login-section"},[e("h4",{staticClass:"section-subtitle"},[s._v("电子邮箱")]),e("div",{staticClass:"login-item"},[e("div",{staticClass:"login-label"},[s._v("邮箱")]),e("div",{staticClass:"login-value"},[s._v(" "+s._s(s.user.email||"未绑定")+" "),e("button",{staticClass:"edit-btn",on:{click:function(e){s.showEmailModal=!0}}},[s._v("修改")])])])]):s._e()])])]):s._e(),"verification"===s.activeTab?e("div",{staticClass:"tab-content"},[e("div",{staticClass:"section-header"},[e("h2",[s._v("个人认证")]),s.verificationError?e("div",{staticClass:"verification-error"},[s._v(" "+s._s(s.verificationError)+" ")]):s._e()]),e("div",{staticClass:"verification-container"},[1===s.user.isReal?e("div",{staticClass:"verified-info"},[s._m(1),e("div",{staticClass:"verified-item"},[e("span",{staticClass:"verified-label"},[s._v("真实姓名：")]),e("span",[s._v(s._s(s.desensitizeName(s.user.realName)))])]),e("div",{staticClass:"verified-item"},[e("span",{staticClass:"verified-label"},[s._v("身份证号：")]),e("span",[s._v(s._s(s.desensitizeIdCard(s.user.realId)))])])]):e("div",{staticClass:"verification-form"},[e("div",{staticClass:"form-group"},[e("label",[s._v("真实姓名")]),e("input",{directives:[{name:"model",rawName:"v-model",value:s.realName,expression:"realName"}],class:{"error-input":s.realNameError},attrs:{type:"text",placeholder:"请输入真实姓名"},domProps:{value:s.realName},on:{input:function(e){e.target.composing||(s.realName=e.target.value)}}}),s.realNameError?e("div",{staticClass:"error-text"},[e("i",{staticClass:"error-icon"}),s._v(" "+s._s(s.realNameError)+" ")]):s._e()]),e("div",{staticClass:"form-group"},[e("label",[s._v("身份证号码")]),e("input",{directives:[{name:"model",rawName:"v-model",value:s.idCardNumber,expression:"idCardNumber"}],class:{"error-input":s.idCardError},attrs:{type:"text",placeholder:"请输入身份证号码"},domProps:{value:s.idCardNumber},on:{input:function(e){e.target.composing||(s.idCardNumber=e.target.value)}}}),s.idCardError?e("div",{staticClass:"error-text"},[e("i",{staticClass:"error-icon"}),s._v(" "+s._s(s.idCardError)+" ")]):s._e()]),e("div",{staticClass:"agreement-checkbox"},[e("input",{directives:[{name:"model",rawName:"v-model",value:s.agreementChecked,expression:"agreementChecked"}],attrs:{type:"checkbox",id:"agreement"},domProps:{checked:Array.isArray(s.agreementChecked)?s._i(s.agreementChecked,null)>-1:s.agreementChecked},on:{change:function(e){var t=s.agreementChecked,a=e.target,i=!!a.checked;if(Array.isArray(t)){var r=null,o=s._i(t,r);a.checked?o<0&&(s.agreementChecked=t.concat([r])):o>-1&&(s.agreementChecked=t.slice(0,o).concat(t.slice(o+1)))}else s.agreementChecked=i}}}),e("label",{attrs:{for:"agreement"}},[s._v(" 我已阅读并同意 天工开物的 "),e("router-link",{staticClass:"link",attrs:{to:"/help/user-agreement"}},[s._v("服务条款")]),s._v(" 和 "),e("router-link",{staticClass:"link",attrs:{to:"/help/privacy-policy"}},[s._v("隐私政策")])],1)]),e("button",{staticClass:"submit-btn",attrs:{disabled:!s.canSubmitVerification},on:{click:s.submitIdVerification}},[s._v("提交")])])])]):s._e()])]),s.showPasswordModal?e("div",{staticClass:"modal"},[e("div",{staticClass:"modal-content"},[e("div",{staticClass:"modal-header"},[e("h3",[s._v("修改密码")]),e("span",{staticClass:"close-btn",on:{click:function(e){s.showPasswordModal=!1}}},[s._v("×")])]),e("div",{staticClass:"modal-body"},[e("div",{staticClass:"form-group"},[e("label",[s._v("当前密码")]),e("input",{directives:[{name:"model",rawName:"v-model",value:s.currentPassword,expression:"currentPassword"}],attrs:{type:"password",placeholder:"请输入当前密码"},domProps:{value:s.currentPassword},on:{input:function(e){e.target.composing||(s.currentPassword=e.target.value)}}})]),e("div",{staticClass:"form-group"},[e("label",[s._v("新密码")]),e("input",{directives:[{name:"model",rawName:"v-model",value:s.newPassword,expression:"newPassword"}],attrs:{type:"password",placeholder:"请输入新密码"},domProps:{value:s.newPassword},on:{blur:s.validateNewPassword,input:function(e){e.target.composing||(s.newPassword=e.target.value)}}}),s.passwordError?e("div",{staticClass:"error-text"},[e("i",{staticClass:"error-icon"}),s._v(" "+s._s(s.passwordError)+" ")]):s._e()]),e("div",{staticClass:"form-group"},[e("label",[s._v("确认新密码")]),e("input",{directives:[{name:"model",rawName:"v-model",value:s.confirmPassword,expression:"confirmPassword"}],attrs:{type:"password",placeholder:"请再次输入新密码"},domProps:{value:s.confirmPassword},on:{blur:s.validateConfirmPassword,input:function(e){e.target.composing||(s.confirmPassword=e.target.value)}}}),s.confirmPasswordError?e("div",{staticClass:"error-text"},[e("i",{staticClass:"error-icon"}),s._v(" "+s._s(s.confirmPasswordError)+" ")]):s._e()])]),e("div",{staticClass:"modal-footer"},[e("button",{staticClass:"cancel-btn",on:{click:function(e){s.showPasswordModal=!1}}},[s._v("取消")]),e("button",{staticClass:"confirm-btn",on:{click:s.changePassword}},[s._v(" 确认 ")])])])]):s._e(),s.showUsernameModal?e("div",{staticClass:"modal"},[e("div",{staticClass:"modal-content"},[e("div",{staticClass:"modal-header"},[e("h3",[s._v("修改昵称")]),e("span",{staticClass:"close-btn",on:{click:function(e){s.showUsernameModal=!1}}},[s._v("×")])]),e("div",{staticClass:"modal-body"},[e("div",{staticClass:"form-group"},[e("label",[s._v("新昵称")]),e("input",{directives:[{name:"model",rawName:"v-model",value:s.newUsername,expression:"newUsername"}],attrs:{type:"text",placeholder:"请输入新昵称"},domProps:{value:s.newUsername},on:{input:function(e){e.target.composing||(s.newUsername=e.target.value)}}})])]),e("div",{staticClass:"modal-footer"},[e("button",{staticClass:"cancel-btn",on:{click:function(e){s.showUsernameModal=!1}}},[s._v("取消")]),e("button",{staticClass:"confirm-btn",on:{click:s.changeUsername}},[s._v("确认")])])])]):s._e(),s.showGenderModal?e("div",{staticClass:"modal"},[e("div",{staticClass:"modal-content"},[e("div",{staticClass:"modal-header"},[e("h3",[s._v("设置性别")]),e("span",{staticClass:"close-btn",on:{click:function(e){s.showGenderModal=!1}}},[s._v("×")])]),e("div",{staticClass:"modal-body"},[e("div",{staticClass:"gender-options"},[e("div",{staticClass:"gender-option"},[e("input",{directives:[{name:"model",rawName:"v-model",value:s.selectedGender,expression:"selectedGender"}],attrs:{type:"radio",id:"male",name:"gender",value:"男"},domProps:{checked:s._q(s.selectedGender,"男")},on:{change:function(e){s.selectedGender="男"}}}),e("label",{attrs:{for:"male"}},[s._v("男")])]),e("div",{staticClass:"gender-option"},[e("input",{directives:[{name:"model",rawName:"v-model",value:s.selectedGender,expression:"selectedGender"}],attrs:{type:"radio",id:"female",name:"gender",value:"女"},domProps:{checked:s._q(s.selectedGender,"女")},on:{change:function(e){s.selectedGender="女"}}}),e("label",{attrs:{for:"female"}},[s._v("女")])])])]),e("div",{staticClass:"modal-footer"},[e("button",{staticClass:"cancel-btn",on:{click:function(e){s.showGenderModal=!1}}},[s._v("取消")]),e("button",{staticClass:"confirm-btn",on:{click:s.changeGender}},[s._v("确认")])])])]):s._e(),s.showPhoneModal?e("div",{staticClass:"modal"},[e("div",{staticClass:"modal-content"},[e("div",{staticClass:"modal-header"},[e("h3",[s._v("修改手机号")]),e("span",{staticClass:"close-btn",on:{click:function(e){s.showPhoneModal=!1}}},[s._v("×")])]),e("div",{staticClass:"modal-body"},[e("div",{staticClass:"form-group"},[e("label",[s._v("新手机号")]),e("input",{directives:[{name:"model",rawName:"v-model",value:s.newPhone,expression:"newPhone"}],attrs:{type:"text",placeholder:"请输入新手机号"},domProps:{value:s.newPhone},on:{input:function(e){e.target.composing||(s.newPhone=e.target.value)}}})]),e("div",{staticClass:"form-group"},[e("label",[s._v("验证码")]),e("div",{staticClass:"verify-code-input"},[e("input",{directives:[{name:"model",rawName:"v-model",value:s.verifyCode,expression:"verifyCode"}],attrs:{type:"text",placeholder:"请输入验证码"},domProps:{value:s.verifyCode},on:{input:function(e){e.target.composing||(s.verifyCode=e.target.value)}}}),e("button",{staticClass:"get-code-btn",attrs:{disabled:s.isCountingDown},on:{click:s.getVerifyCode}},[s._v(" "+s._s(s.countdown>0?`${s.countdown}秒后重试`:"获取验证码")+" ")])])])]),e("div",{staticClass:"modal-footer"},[e("button",{staticClass:"cancel-btn",on:{click:function(e){s.showPhoneModal=!1}}},[s._v("取消")]),e("button",{staticClass:"confirm-btn",on:{click:s.changePhone}},[s._v("确认")])])])]):s._e()],1)},i=[function(){var s=this,e=s._self._c;return e("div",{staticClass:"section-header"},[e("h2",[s._v("基本信息")])])},function(){var s=this,e=s._self._c;return e("div",{staticClass:"verified-status"},[e("i",{staticClass:"el-icon-success"}),s._v(" 已认证 ")])}],r=(t(7658),t(4863)),o=t(2223),n=t(1955),c=t(7234),l={name:"PersonalCenter",components:{Layout:r.Z,SlideNotification:c.Z},data(){return{activeTab:"basic",user:{id:null,username:"",nickName:"",password:"",avatarUrl:null,isChangingPassword:!1,sex:"",phone:"",email:null,balance:0,isReal:0,realName:"",realId:"",realNumber:0,tenantId:"",isLogin:0,notificationMessage:"",notificationType:"info"},showNotification:!1,showPasswordModal:!1,currentPassword:"",newPassword:"",confirmPassword:"",passwordError:"",confirmPasswordError:"",showUsernameModal:!1,newUsername:"",showGenderModal:!1,selectedGender:"",showPhoneModal:!1,newPhone:"",verifyCode:"",countdown:0,isCountingDown:!1,realName:"",idCardNumber:"",realNameError:"",idCardError:"",verificationError:"",agreementChecked:!1,verificationStatus:"",isVerified:!1,userInfoCookieName:"user_info",userInfoExpiryDays:7}},computed:{canSubmitVerification(){return this.realName&&this.idCardNumber&&!this.realNameError&&!this.idCardError&&this.agreementChecked}},methods:{switchTab(s){this.activeTab=s,this.$router.push({query:{...this.$route.query,activeTab:s}})},userInitial(){return this.user.nickName&&this.user.nickName.length>0?this.user.nickName.charAt(0).toUpperCase():"N"},openIdVerification(){1!==this.user.isReal?this.activeTab="verification":this.$message.info("您已通过实名认证")},async getUserInfo(){try{const s=await(0,o.fB)("/logout/cilent/getInfo");if(200===s.data.code){const e=s.data.data;return this.user={...this.user,...e},this.verificationStatus=1===e.isReal?"已认证":"未认证",this.saveUserInfoToCookie(e),e}throw new Error(s.data.msg||"获取用户信息失败")}catch(s){throw s}},validateNewPassword(){const s=this.newPassword?.trim();if(this.passwordError="",!s)return void(this.passwordError="请输入新密码");const e=s.length>=8;if(!e)return void(this.passwordError="密码长度至少为8位");const t=/[a-zA-Z]/.test(s),a=/[0-9]/.test(s),i=/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(s),r=/[A-Z]/.test(s)&&/[a-z]/.test(s);let o=0;if(t&&a&&o++,i&&o++,r&&o++,o<2){const s=[];return t&&a||s.push("包含数字和字母"),i||s.push("包含特殊符号"),r||s.push("包含大小写字母"),void(this.passwordError=`密码需至少满足以下两项要求：${s.join("、")}`)}this.confirmPassword&&this.validateConfirmPassword()},validateConfirmPassword(){this.confirmPassword?this.confirmPassword!==this.newPassword?this.confirmPasswordError="两次输入的密码不一致":this.confirmPasswordError="":this.confirmPasswordError="请确认新密码"},async changePassword(){if(this.currentPassword){if(this.validateNewPassword(),this.validateConfirmPassword(),!this.passwordError&&!this.confirmPasswordError){this.isChangingPassword=!0;try{const s=await this.getUserInfo(),e={...s,password:this.currentPassword,newPassword:this.newPassword},t=await(0,o.fB)("/logout/cilent/changePwd",e);200===t.data.code?(this.showNotificationMessage("密码修改成功","success"),this.showPasswordModal=!1,this.currentPassword="",this.newPassword="",this.confirmPassword=""):this.$nextTick((()=>{this.showNotificationMessage("原密码输入错误，请检查重试","error")}))}catch(s){this.showNotificationMessage("密码修改过程中出错，请稍后重试","error")}finally{this.isChangingPassword=!1}}}else this.showNotificationMessage("请输入当前密码","warning")},showNotificationMessage(s,e="info"){this.notificationMessage=s,this.notificationType=e,this.showNotification=!0,setTimeout((()=>{this.showNotification=!1}),3e3)},async changeUsername(){if(this.newUsername.trim())try{const s=await this.getUserInfo(),e={...s,nickName:this.newUsername},t=await(0,o.fB)("/logout/cilent/updateInfo",e);200===t.data.code?(this.user.nickName=this.newUsername,this.saveUserInfoToCookie({...s,nickName:this.newUsername}),this.showUsernameModal=!1,this.newUsername="",this.$message.success("昵称修改成功")):this.$message.error(t.data.message||"修改失败")}catch(s){this.$message.error("网络错误")}else this.$message.warning("昵称不能为空")},async changeGender(){if(this.selectedGender)try{const s=await this.getUserInfo(),e={...s,sex:this.selectedGender},t=await(0,o.fB)("/logout/cilent/updateInfo",e);200===t.data.code?(this.user.sex=this.selectedGender,this.saveUserInfoToCookie({...s,sex:this.selectedGender}),this.showGenderModal=!1,this.$message.success("性别设置成功")):this.$message.error(t.data.message||"修改失败")}catch(s){this.$message.error("网络错误")}else this.$message.warning("请选择性别")},desensitizeName(s){return s?s.length<=1?s:s.charAt(0)+"*".repeat(s.length-1):""},desensitizeIdCard(s){return s?s.length<=8?s:s.substring(0,4)+"********"+s.substring(s.length-4):""},getVerifyCode(){if(!this.newPhone)return void this.$message.warning("请输入手机号");this.isCountingDown=!0,this.countdown=60;const s=setInterval((()=>{this.countdown--,this.countdown<=0&&(clearInterval(s),this.isCountingDown=!1)}),1e3);this.$message.success("验证码已发送")},async changePhone(){if(this.newPhone)if(this.verifyCode)try{const s=await this.getUserInfo(),e={...s,phone:this.newPhone},t=await(0,o.fB)("/logout/cilent/updateInfo",e);200===t.data.code?(this.user.phone=this.newPhone,this.saveUserInfoToCookie({...s,phone:this.newPhone}),this.showPhoneModal=!1,this.newPhone="",this.verifyCode="",this.$message.success("手机号修改成功")):this.$message.error(t.data.message||"修改失败")}catch(s){this.$message.error("网络错误")}else this.$message.warning("请输入验证码");else this.$message.warning("请输入新手机号")},validateRealName(){if(!this.realName)return this.realNameError="请输入正确的姓名",!1;const s=/^[\u4e00-\u9fa5]{2,10}$/;return s.test(this.realName)?(this.realNameError="",!0):(this.realNameError="请输入正确的姓名",!1)},validateIdCard(){if(!this.idCardNumber)return this.idCardError="请输入正确的身份证号码",!1;const s=/(^\d{15$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;return s.test(this.idCardNumber)?(this.idCardError="",!0):(this.idCardError="请输入正确的身份证号码",!1)},async submitIdVerification(){const s=this.validateRealName(),e=this.validateIdCard();if(!s||!e||!this.agreementChecked)return;const t=await this.getUserInfo(),a={name:this.realName,id:this.idCardNumber,username:t.username,userId:t.id},i=await(0,o.fB)("/idVerification/verify",a);200===i.data.code?(this.user.realName=this.realName,this.user.realId=this.idCardNumber,this.user.isReal=1,this.saveUserInfoToCookie({...t,realName:this.realName,realId:this.idCardNumber,isReal:1}),this.showNotificationMessage(i.data.msg,"success")):this.showNotificationMessage(i.data.msg,"error")},getUserInfoFromCookie(){const s=n.Z.get(this.userInfoCookieName);if(s)try{return JSON.parse(s)}catch(e){return null}return null},saveUserInfoToCookie(s){const e={...s,timestamp:(new Date).getTime()};n.Z.set(this.userInfoCookieName,JSON.stringify(e),{expires:this.userInfoExpiryDays,secure:!0,sameSite:"strict"})},isCacheValid(s){if(!s||!s.timestamp)return!1;const e=864e5;return(new Date).getTime()-s.timestamp<e},async fetchUserInfo(){const s=this.getUserInfoFromCookie();s&&this.isCacheValid(s)?this.updateLocalUserData(s):await this.getUserInfo()},updateLocalUserData(s){this.user={id:s.id||this.user.id,username:s.username||this.user.username,nickName:s.nickName||this.user.nickName,password:this.currentPassword||this.user.password,avatarUrl:s.avatarUrl||this.user.avatarUrl,sex:s.sex||this.user.sex,phone:s.phone||this.user.phone,email:s.email||this.user.email,balance:s.balance||this.user.balance,isReal:void 0!==s.isReal?s.isReal:this.user.isReal,realName:s.realName||this.user.realName,realId:s.realId||this.user.realId,realNumber:s.realNumber||this.user.realNumber,tenantId:s.tenantId||this.user.tenantId,isLogin:s.isLogin||this.user.isLogin},this.verificationStatus=1===this.user.isReal?"已认证":"未认证"}},watch:{realName(){this.validateRealName()},idCardNumber(){this.validateIdCard()},newPassword(){this.validateNewPassword()},confirmPassword(){this.validateConfirmPassword()}},async created(){this.user={username:"",phone:""};try{await this.fetchUserInfo(),this.selectedGender=this.user.sex,"verification"===this.$route.query.activeTab&&(this.activeTab="verification")}catch(s){}}},d=l,h=t(1001),u=(0,h.Z)(d,a,i,!1,null,"76db2dc1",null),v=u.exports}}]);
//# sourceMappingURL=6433.faf3b498.js.map