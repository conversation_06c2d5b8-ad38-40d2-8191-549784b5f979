"use strict";(self["webpackChunkportal_ui"]=self["webpackChunkportal_ui"]||[]).push([[7231],{5305:function(o,e,n){n.r(e);var t='<h1 id="k8s-yaml-导入"><font style="color:#020817">K8S YAML 导入</font></h1> <h2 id="概述"><font style="color:#020817">概述</font></h2> <p><font style="color:#020817">K8S YAML 导入功能允许熟悉 K8S 的用户以自定义 Deployment 资源的 YAML 配置文件的方式，实现更高自由度的自定义配置。</font></p> <p><font style="color:#020817">例如：</font></p> <ul> <li><a href="https://kubernetes.io/zh-cn/docs/reference/kubernetes-api/workload-resources/deployment-v1/"><font style="color:#2f8ef4">调整扩缩容和滚动更新策略</font></a></li> <li><a href="https://kubernetes.io/zh-cn/docs/reference/kubernetes-api/workload-resources/pod-v1/#PodSpec"><font style="color:#2f8ef4">调整容器从启动到销毁整个生命周期的行为</font></a></li> <li><a href="https://kubernetes.io/zh-cn/docs/reference/kubernetes-api/workload-resources/pod-v1/#PodSpec"><font style="color:#2f8ef4">调整容器启动时采用的用户</font></a></li> <li><a href="https://kubernetes.io/zh-cn/docs/reference/kubernetes-api/workload-resources/pod-v1/#PodSpec"><font style="color:#2f8ef4">为容器配置启动命令和环境变量</font></a></li> <li><a href="https://kubernetes.io/zh-cn/docs/reference/kubernetes-api/workload-resources/pod-v1/#PodSpec"><font style="color:#2f8ef4">为容器挂载内存盘</font></a></li> </ul> <p><font style="color:#020817">关于 K8S Deployment 的简要介绍可见</font><a href="https://kubernetes.io/zh-cn/docs/concepts/workloads/controllers/deployment/"><font style="color:#2f8ef4">Deployments</font></a><font style="color:#020817">，详细功能介绍可见</font><a href="https://kubernetes.io/zh-cn/docs/reference/kubernetes-api/workload-resources/deployment-v1/"><font style="color:#2f8ef4">Deployment</font></a><font style="color:#020817">。</font></p> <h2 id="限制"><font style="color:#020817">限制</font></h2> <p><font style="color:#020817">出于对集群安全性的考虑和业务逻辑的要求，目前不支持设置关于卡类型、数目、资源配额、容器镜像等信息（请通过 UI 界面或 API 接口进行设置），也不支持任何可能使得容器获取宿主机权限的配置，例如特权容器，访问主机网络，挂载 hostpath 等。如有相关需要，请联系我们。</font></p> <h2 id="样例"><font style="color:#020817">样例</font></h2> <p><font style="color:#020817">这里给出几个典型应用场景下的 K8S Yaml 配置文件样例</font></p> <h3 id="挂载-shm-内存盘"><font style="color:#020817">挂载 shm 内存盘</font></h3> <p><font style="color:#020817">部分业务程序需要挂载内存盘作为进程间通信的方式，这里给出挂载内存盘的 yaml 文件样例</font></p> <pre><code class="language-yaml">...... \n      containers:\n        - image: harbor.suanleme.cn/xiditgkw/mem-allocator:v1    #选卡后默认配置内容\n          name: d1749797718484-39886-container\n          resources:\n            limits:\n              cpu: &#39;14&#39;\n              memory: 63Gi\n              nvidia.com/gpu: &#39;1&#39;\n            requests:\n              cpu: &#39;7&#39;\n              memory: 32256Mi\n          volumeMounts:        #共享内存关键配置\n            - mountPath: /dev/shm\n              name: shm-volume\n      volumes:\n        - emptyDir:\n            medium: Memory\n          name: shm-volume\n</code></pre> <h3 id="实现容器内业务的优雅退出"><font style="color:#020817">实现容器内业务的优雅退出</font></h3> <p><font style="color:#020817">部分业务程序处理单次请求可能需要数十分钟级别的较长时间，在服务缩容等场景中，如果不等待请求完成即退出程序，可能会对用户体验造成较大不良影响。</font></p> <p><font style="color:#020817">对于此类程序，务必在业务代码里面处理 SIGTERM 信号：</font></p> <ul> <li><font style="color:#020817">捕获 SIGTREM 信号</font></li> <li><font style="color:#020817">执行对应的清理逻辑</font></li> <li><font style="color:#020817">主动退出程序</font></li> </ul> <p><font style="color:#020817">并在 yaml 中配置适当的优雅退出宽限时间来实现。这里给出 yaml 文件样例。</font></p> <pre><code class="language-yaml">......\n      containers:\n        - image: harbor.suanleme.cn/repository/imagename:v1    #选用的镜像\n          lifecycle:            #（可选）回调函数退出时执行的逻辑\n            preStop:\n              exec:\n                command:\n                  - /bin/sh\n                  - &#39;-c&#39;\n                  - sleep 30\n          name: d1749797718484-39886-container    #默认生成\n          resources:        #选卡时确定此配置，不可修改\n            limits:\n              cpu: &#39;14&#39;\n              memory: 63Gi\n              nvidia.com/gpu: &#39;1&#39;\n            requests:\n              cpu: &#39;7&#39;\n              memory: 32256Mi\n      terminationGracePeriodSeconds: 200        #（关键）定义优雅退出最大时间\n</code></pre> <h3 id="服务零中断滚动更新（待测试，需结合-s3）（待完善，暂时不公开）"><font style="color:#020817">服务零中断滚动更新（待测试，需结合 S3）（待完善，暂时不公开）</font></h3> <h3 id="以-root-用户启动容器（待完善，暂时不公开）"><font style="color:#020817">以 root 用户启动容器（待完善，暂时不公开）</font></h3> <p><font style="color:#020817">部分业务容器可能希望以 root 身份启动，这与 K8S 的默认行为不同，可以通过自定义配置来进行设置。</font></p> <pre><code class="language-plain">......\n      containers:\n        - image: harbor.suanleme.cn/repository/imagename:v1    #选用的镜像\n          name: d1749797718484-39886-container    #默认生成\n        securityContext:\n          runAsUser: 0  #此处填写用户 UID，0 标识 root，也可定义其他可用 UID\n          runAsGroup: 0 #（可选）：此处表示 root 组的 GID\n</code></pre> <h3 id="关闭-istio-放开四层流量限制"><font style="color:#020817">关闭 istio 放开四层流量限制</font></h3> <p><font style="color:#020817">这里是要解决什么场景下的什么问题？</font></p> <pre><code class="language-plain">apiVersion: apps/v1\nkind: Deployment\nmetadata:\n  creationTimestamp: null\n  labels:\n    app: d06161816-mem-allocatorv1-186-11icjyhf\n  name: d06161816-mem-allocatorv1-186-11icjyhf\n  namespace: wp2upu39jptxiuhkfkm0nx2bdkp9elwo-186\nspec:\n  replicas: 1\n  selector:\n    matchLabels:\n      app: d06161816-mem-allocatorv1-186-11icjyhf\n  strategy: {}\n  template:\n    metadata:\n      annotations:        #此项为放开四层流量的限制\n        sidecar.istio.io/inject: &#39;false&#39;\n      creationTimestamp: null\n      labels:\n        app: d06161816-mem-allocatorv1-186-11icjyhf\n    spec:\n......\n</code></pre> <p><br><br> <font style="color:#b2b2b2">最后更新于: 2025/6/19 17:06</font></p> ';e["default"]=t}}]);
//# sourceMappingURL=docs16.6a997ea0.js.map