<template>
  <div class="login-page">
    <div class="left-side">
      <backgroundlogin />
    </div>

    <div class="right-side">
      <div class="login-form-container">
        <h3>注册 天工开物</h3>

        <div class="form-container">
          <div class="login-form">
            <p class="form-note">只需一个 天工开物 账号，即可访问 天工开物 的所有服务。</p>

            <div class="input-group" :class="{ 'error': errors.phone }">
              <div class="phone-input-container">
                <input
                    type="text"
                    v-model="registrationForm.phone"
                    placeholder="请输入手机号"
                    @blur="validatePhone"
                />
              </div>
              <div class="error-container">
                <div v-if="errors.phone" class="error-message">{{ errors.phone }}</div>
              </div>
            </div>

            <div class="input-group" :class="{ 'error': errors.password }">
              <div class="password-input-container">
                <input
                    :type="passwordVisible ? 'text' : 'password'"
                    v-model="registrationForm.password"
                    placeholder="请输入密码"
                    @blur="validatePassword"
                />
                <span class="password-toggle" @click="togglePasswordVisibility">
                  <i :class="['eye-icon', passwordVisible ? 'visible' : '']"></i>
                </span>
              </div>
              <div class="error-container">
                <div v-if="errors.password" class="error-message">{{ errors.password }}</div>
              </div>
            </div>

            <div class="input-group" :class="{ 'error': errors.confirmPassword }">
              <div class="password-input-container">
                <input
                    :type="confirmPasswordVisible ? 'text' : 'password'"
                    v-model="registrationForm.confirmPassword"
                    placeholder="请再次输入密码"
                    @blur="validateConfirmPassword"
                />
                <span class="password-toggle" @click="toggleConfirmPasswordVisibility">
                  <i :class="['eye-icon', confirmPasswordVisible ? 'visible' : '']"></i>
                </span>
              </div>
              <div class="error-container">
                <div v-if="errors.confirmPassword" class="error-message">{{ errors.confirmPassword }}</div>
              </div>
            </div>

            <div class="input-group verification-code" :class="{ 'error': errors.code }">
              <div class="code-input-container">
                <input
                    type="text"
                    v-model="registrationForm.code"
                    placeholder="请输入验证码"
                    @blur="validateCodegeshi"
                />
                <button
                    class="get-code-btn-inline"
                    @click="getVerificationCode"
                    :disabled="!registrationForm.phone || errors.phone || codeSent"
                >
                  {{ codeSent ? `${countdown}秒后重试` : '获取验证码' }}
                </button>
              </div>
              <div class="error-container">
                <div v-if="errors.code" class="error-message">{{ errors.code }}</div>
              </div>
            </div>

            <div class="agreement-text">
              注册视为您已阅读并同意天工开物 
              <router-link to="/help/user-agreement" >服务条款</router-link> 和<router-link to="/help/privacy-policy" >隐私政策</router-link>
            </div>

            <button
                class="register-btn"
                @click="register"
            >
              注册
            </button>

            <div class="login-link">
              <a href="#" @click="navigateTo('/forgetpass')">忘记密码</a>
              <span class="divider">|</span>
              <a href="#" @click="navigateTo('/login')">返回登录</a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 使用 SlideNotification 组件替换原来的提示 -->
    <SlideNotification
        v-if="showCodeSent"
        message="验证码已发送，可能会有延迟，请耐心等待！"
        type="success"
        :duration="3000"
        @close="showCodeSent = false"
        :minHeight="minHeight"
    />
  </div>
</template>

<script>
import { postAnyData, getAnyData, postLogin, postNotAuth } from "@/api/login";
import { getToken, setToken, removeToken } from '@/utils/auth'
import SlideNotification from '@/components/common/header/SlideNotification.vue';
import backgroundlogin from '@/views/Login/backgroundlogin.vue';


export default {
  name: "register",
  components: {
    SlideNotification,
    backgroundlogin
  },
  data() {
    return {
      registrationForm: {
        phone: '',
        password: '',
        confirmPassword: '',
        code: '',
        username: 'user1',
        usage: '商业办公',
        otherUsage: '',
        agreement: false
      },
      isSendingCode: false,
      passwordVisible: false,
      confirmPasswordVisible: false,
      errors: {
        phone: '',
        password: '',
        confirmPassword: '',
        code: ''
      },
      codeSent: false,
      countdown: 60,
      timer: null,
      showCodeSent: false,
      minHeight: '50px'
    }
  },
  watch: {
    'registrationForm.phone'(newVal) {
      this.registrationForm.username = newVal;
    }
  },
  created() {
    // Check for existing timer on page load
    this.checkExistingTimer();
    this.$emit('hiden-layout')
  },
  computed: {
    isFormValid() {
      return (
          this.registrationForm.phone &&
          this.registrationForm.password &&
          this.registrationForm.confirmPassword &&
          this.registrationForm.code &&
          this.registrationForm.agreement &&
          !this.errors.phone &&
          !this.errors.password &&
          !this.errors.confirmPassword &&
          !this.errors.code
      );
    }
  },
  methods: {
    checkExistingTimer() {
      // Get stored verification code data
      const storedCodeData = localStorage.getItem('verificationCodeData');
      if (storedCodeData) {
        const codeData = JSON.parse(storedCodeData);
        // Check if phone number matches
        if (codeData.phone && this.registrationForm.phone && codeData.phone === this.registrationForm.phone) {
          // Check if timer is still valid
          const currentTime = Date.now();
          const expiryTime = codeData.timestamp + (60 * 1000); // 60 seconds from sent time

          if (currentTime < expiryTime) {
            // Calculate remaining time
            const remainingTime = Math.ceil((expiryTime - currentTime) / 1000);
            this.countdown = remainingTime;
            this.codeSent = true;
            this.startCountdown();
          } else {
            // Timer expired, clear storage
            localStorage.removeItem('verificationCodeData');
          }
        }
      }
    },
    validatePhone() {
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!this.registrationForm.phone) {
        this.errors.phone = '请输入手机号';
      } else if (!phoneRegex.test(this.registrationForm.phone)) {
        this.errors.phone = '请输入有效的手机号';
      } else {
        this.errors.phone = '';
      }
    },

    validatePassword() {
      const password = this.registrationForm.password;
      this.errors.password = ''; // 重置错误信息

      if (!password) {
        this.errors.password = '请输入密码';
        return;
      }

      // 强制最小长度要求
      if (password.length < 8) {
        this.errors.password = '密码长度至少为8位';
        return;
      }

      // 三选二的强度要求
      const hasLetter = /[a-zA-Z]/.test(password);
      const hasNumber = /[0-9]/.test(password);
      const hasSymbol = /[!@#$%^&*()_+\-=$${};':"\\|,.<>\/?]/.test(password);
      const hasUpperLower = /[A-Z]/.test(password) && /[a-z]/.test(password);

      let strengthCount = 0;
      if (hasLetter && hasNumber) strengthCount++;
      if (hasSymbol) strengthCount++;
      if (hasUpperLower) strengthCount++;

      if (strengthCount >= 2) {
        this.errors.password = ''; // 密码符合要求
      } else {
        const missingRequirements = [];
        if (!(hasLetter && hasNumber)) missingRequirements.push('包含数字和字母');
        if (!hasSymbol) missingRequirements.push('包含特殊符号');
        if (!hasUpperLower) missingRequirements.push('包含大小写字母');

        this.errors.password = `密码强度不足，请满足以下至少两项要求：${missingRequirements.join('、')}`;
      }

      // 如果确认密码已填写，联动验证
      if (this.registrationForm.confirmPassword) {
        this.validateConfirmPassword();
      }
    },

    navigateTo(path) {
      // 如果当前路径与目标路径相同，则重新加载页面
      if (this.$route.path === path) {
        // 先跳转到一个临时路由（如果有的话）或者重新加载页面
        this.$nextTick(() => {
          window.scrollTo({
            top: 0,
            behavior: 'instant' // 使用即时滚动而不是平滑滚动
          });
          this.$router.go(0); // 刷新当前页面
        });
      } else {
        // 不同路径，正常导航并滚动到顶部
        this.$router.push(path);
        window.scrollTo({
          top: 0,
          behavior: 'instant'
        });
      }
      this.currentPath = path;
    },
    validateConfirmPassword() {
      if (!this.registrationForm.confirmPassword) {
        this.errors.confirmPassword = '请再次输入密码';
      } else if (this.registrationForm.confirmPassword !== this.registrationForm.password) {
        this.errors.confirmPassword = '两次输入的密码不一致';
      } else {
        this.errors.confirmPassword = '';
      }
    },
    async validateCodegeshi() {
      // 清空错误
      this.errors.code = '';

      // 前端基础验证（保持原有长度判断）
      if (!this.registrationForm.code) {
        this.errors.code = '请输入验证码';
        return false;
      }
      if (this.registrationForm.code.length !== 4 || !/^\d+$/.test(this.registrationForm.code)) {
        this.errors.code = '验证码必须为4位数字';
        return false;
      }
    },
    async getVerificationCode() {
      // First check if there's an existing timer for this phone number
      const storedCodeData = localStorage.getItem('verificationCodeData');
      if (storedCodeData) {
        const codeData = JSON.parse(storedCodeData);
        if (codeData.phone === this.registrationForm.phone) {
          const currentTime = Date.now();
          const expiryTime = codeData.timestamp + (60 * 1000);

          if (currentTime < expiryTime) {
            // Timer still active, update countdown and prevent new request
            const remainingTime = Math.ceil((expiryTime - currentTime) / 1000);
            this.countdown = remainingTime;
            this.codeSent = true;
            this.startCountdown();
            return;
          }
        }
      }

      // 先验证手机号格式
      this.validatePhone();
      if (this.errors.phone) return;

      try {
        // 显示发送中状态
        this.isSendingCode = true;

        // 调用发送验证码接口
        const response = await postLogin("/auth/sendCode", {
          phone: this.registrationForm.phone
        });

        // 处理响应 - 只有在成功时才显示通知和启动倒计时
        if (response.data.code === 200) {
          // Store sent time and phone in localStorage
          const verificationData = {
            phone: this.registrationForm.phone,
            timestamp: Date.now()
          };
          localStorage.setItem('verificationCodeData', JSON.stringify(verificationData));

          this.codeSent = true;
          this.showCodeSent = true;
          this.startCountdown();
        } else {
          // 处理失败响应
          this.errors.code = response.data.msg || '验证码发送失败';
          // 不启动倒计时，允许用户重试
        }
      } catch (error) {
        this.errors.code = '网络异常，请稍后重试';
      } finally {
        this.isSendingCode = false;
      }
    },
    startCountdown() {
      // 清除可能存在的旧定时器
      if (this.timer) {
        clearInterval(this.timer);
      }

      // 使用固定的时间间隔
      this.timer = setInterval(() => {
        if (this.countdown <= 1) {
          clearInterval(this.timer);
          this.codeSent = false;
          this.countdown = 60;
          // Clear localStorage when timer expires
          localStorage.removeItem('verificationCodeData');
        } else {
          this.countdown--;
          // Update remaining time in localStorage
          const storedCodeData = localStorage.getItem('verificationCodeData');
          if (storedCodeData) {
            const codeData = JSON.parse(storedCodeData);
            // Only update if it's for the current phone
            if (codeData.phone === this.registrationForm.phone) {
              // Just keep the original timestamp, no need to update it
            }
          }
        }
      }, 1000);
    },
    register() {
      this.validatePhone();
      this.validatePassword();
      this.validateConfirmPassword();
      // 清空错误
      this.errors.code = '';
      // 新增接口验证（参数与发送接口一致）
      postLogin("/auth/verifyCode", {
        phone: this.registrationForm.phone, // 必须携带手机号
        code: this.registrationForm.code
      }).then(response => {
        // 统一响应码判断标准
        if (response.data.code !== 200) {
          this.errors.code = response.data.msg || '验证码错误';
          return;
        }
        // 注册API调用
        postNotAuth("/auth/register",this.registrationForm).then(res =>{
          if (res.data.code === 200) {
            // Clear verification code data after successful registration
            localStorage.removeItem('verificationCodeData');
            // 跳转到登录页面
            this.$router.push('/login')
          }
          else {
            this.errors.code = res.data.msg;
          }
        })
      })
    },
    togglePasswordVisibility(){
      this.passwordVisible = !this.passwordVisible;
    },
    toggleConfirmPasswordVisibility() {
      this.confirmPasswordVisible = !this.confirmPasswordVisible;
    },
    goToLogin() {
      // 在实际应用中重定向到登录页面
      // this.$router.push('/login');
    }
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer);
    }
    this.$emit('hiden-layout')
  }
}
</script>

<style scoped>
/* Full page layout */
.login-page {
  display: flex;
  min-height: 100vh;
  overflow: hidden;
}

/* Left side styling - keeping original styles */
.left-side {
  flex: 1;
  position: relative;
  background: linear-gradient(135deg, #cdb3e5, #5127d5);
  display: flex;
  flex-direction: column;
  padding: 0rem;
  color: #030303;
}

/* Animated background - keeping original styles */
.animated-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  opacity: 0.4;
}

.hex-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.hex {
  position: absolute;
  width: 100px;
  height: 110px;
  background: rgba(255, 255, 255, 0.15);
  clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
}

.hex1 {
  top: 20%;
  left: 10%;
  transform: scale(1.5);
  animation: float 8s infinite ease-in-out;
}

.hex2 {
  top: 60%;
  left: 20%;
  transform: scale(1.2);
  animation: float 7s infinite ease-in-out reverse;
}

.hex3 {
  top: 30%;
  left: 50%;
  transform: scale(1.3);
  animation: float 10s infinite ease-in-out 1s;
}

.hex4 {
  top: 70%;
  left: 70%;
  transform: scale(1.1);
  animation: float 6s infinite ease-in-out 2s;
}

.hex5 {
  top: 40%;
  left: 80%;
  transform: scale(1.4);
  animation: float 9s infinite ease-in-out 3s;
}

.floating-cube {
  position: absolute;
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.2);
  top: 25%;
  left: 30%;
  animation: float 8s infinite ease-in-out, rotate 15s infinite linear;
}

.floating-sphere {
  position: absolute;
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.15);
  top: 50%;
  left: 40%;
  animation: float 10s infinite ease-in-out reverse;
}

.floating-diamond {
  position: absolute;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  transform: rotate(45deg);
  top: 65%;
  left: 60%;
  animation: float 7s infinite ease-in-out 2s;
}

.ripple-effect {
  position: absolute;
  width: 200px;
  height: 200px;
  border-radius: 50%;
  border: 3px solid rgba(255, 255, 255, 0.1);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation: ripple 6s infinite linear;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(20px);
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes ripple {
  0% {
    width: 0;
    height: 0;
    opacity: 0.8;
  }
  100% {
    width: 300px;
    height: 300px;
    opacity: 0;
  }
}

/* Right side styling */
.right-side {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f8f9fa;
  padding: 2rem;
}

.login-form-container {
  background-color: white;
  border-radius: 8px;
  padding: 2rem;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.login-form-container h3 {
  font-size: 24px;
  font-weight: 500;
  margin-bottom: 20px;
  text-align: center;
  color: #333;
}

/* Form container */
.form-container {
  min-height: 300px;
  position: relative;
}

.login-form {
  margin-top: 20px;
  width: 100%;
}

.form-note {
  color: #999;
  font-size: 12px;
  margin-bottom: 15px;
}

.input-group {
  margin-bottom: 15px;
  position: relative;
}

.input-group input {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s;
}

.input-group input:focus {
  outline: none;
  border-color: #6a26cd;
}

/* Phone input with prefix */
.phone-input-container {
  display: flex;
  align-items: center;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.phone-prefix {
  padding: 0 10px;
  color: #333;
  border-right: 1px solid #ddd;
  background-color: #f5f5f5;
  font-size: 14px;
  display: flex;
  align-items: center;
  height: 42px;
}

.phone-input-container input {
  border: none;
  flex: 1;
}

.error-container {
  min-height: 19px;
  display: block;
}

.error-message {
  color: #f44336;
  font-size: 12px;
}

/* Verification code input and button */
.verification-code .code-input-container {
  display: flex;
  gap: 10px;
}

.verification-code input {
  flex: 1;
}

.get-code-btn-inline {
  flex-shrink: 0;
  width: 110px;
  background-color: #2196f3;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.get-code-btn-inline:hover:not(:disabled) {
  background-color: #4169E1;
}

.get-code-btn-inline:disabled {
  border-color: #2196f3;
  cursor: not-allowed;
}

/* Password input with toggle */
.password-input-container {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}
input[type="password"]::-ms-reveal,
input[type="password"]::-webkit-credentials-auto-fill-button,
input[type="password"]::-webkit-clear-button {
  display: none !important;
  pointer-events: none;
}

.eye-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path><circle cx="12" cy="12" r="3"></circle></svg>');
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0.5;
}

.eye-icon.visible {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path><circle cx="12" cy="12" r="3"></circle><line x1="1" y1="1" x2="23" y2="23"></line></svg>');
}

/* Usage section */
.usage-section {
  margin-bottom: 20px;
}

.usage-title {
  font-size: 14px;
  color: #333;
  margin-bottom: 10px;
}

.usage-options {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 10px;
}

.usage-option {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.usage-option input {
  margin-right: 5px;
  width: auto;
}

.other-usage {
  margin-top: 10px;
}

.other-usage input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

/* Agreement checkbox */
.agreement-section {
  margin-bottom: 20px;
}

.agreement-checkbox {
  display: flex;
  align-items: flex-start;
  cursor: pointer;
}

.agreement-checkbox input {
  margin-right: 8px;
  margin-top: 2px;
  width: auto;
}

.agreement-checkbox span {
  font-size: 13px;
  color: #666;
}

.link {
  color: #4169E1;
  text-decoration: none;
}

/* Register button */
.register-btn {
  width: 100%;
  padding: 12px 0;
  background-color: #2196f3;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
  margin-bottom: 15px;
}

.register-btn:hover:not(:disabled) {
  background-color: #4169E1;
}

.register-btn:disabled {
  background-color: #4169E1;
  cursor: not-allowed;
}

/* Login link */
.login-link {
  text-align: center;
  font-size: 14px;
}

.login-link a {
  color: #4169E1;
  text-decoration: none;
}

.divider {
  margin: 0 10px;
  color: #ddd;
}

.logo-area {
  flex: 0 0 auto;
  margin-right: 10px;
}

.logo-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  cursor: pointer;
}

.logo-area img {
  height: 30px;
  max-width: 100%;
}

.logo-text {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-left: 10px;
}
.agreement-text {
  font-size: 12px;
  color: #999;
  margin-bottom: 20px;
}

.agreement-text a {
  color: #4169E1;
  text-decoration: none;
}
@media screen and (max-width: 768px) {
  .left-side {
    display: none; /* 在手机端隐藏左侧背景 */
  }

  .right-side {
    flex: 1 0 100%; /* 让右侧占据全部宽度 */
    padding: 1rem; /* 减少内边距以适应小屏幕 */
  }

  .login-form-container {
    max-width: 100%; /* 让登录表单占据全部可用宽度 */
    box-shadow: none; /* 移除阴影以节省空间 */
    padding: 1.5rem; /* 调整内边距 */
  }

}
</style>
