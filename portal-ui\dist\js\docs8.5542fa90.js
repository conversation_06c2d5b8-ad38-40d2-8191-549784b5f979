"use strict";(self["webpackChunkportal_ui"]=self["webpackChunkportal_ui"]||[]).push([[4956],{328:function(o,t,n){n.r(t);var e=new URL(n(7906),n.b),u=new URL(n(1404),n.b),l=new URL(n(4117),n.b),f=new URL(n(5321),n.b),s=new URL(n(3505),n.b),r=new URL(n(4974),n.b),q=new URL(n(3299),n.b),a=new URL(n(2907),n.b),c=new URL(n(9392),n.b),p=new URL(n(9751),n.b),i=new URL(n(5225),n.b),d=new URL(n(1832),n.b),y=new URL(n(4444),n.b),h=new URL(n(4689),n.b),m=new URL(n(1915),n.b),g=new URL(n(2947),n.b),x='<h1 id="容器化部署-flux1-dev-文生图模型应用"><font style="color:#020817">容器化部署 Flux.1-dev 文生图模型应用</font></h1> <p><font style="color:#020817">本指南详细阐述了在天工开物上部署 Flux.1-dev 模型应用的解决方案。</font></p> <p><font style="color:#1f2937">鉴于 Serverless 类型的平台具备多节点特性，从专业角度出发，强烈不建议将 Web UI 用于生产环境。在生产场景中，使用 API 是更为适宜的选择。此镜像已集成封装好的 API，以便于您便捷使用。</font></p> <h2 id="1-开源案例"><font style="color:#020817">1 开源案例</font></h2> <p><font style="color:#020817">我们基于本教程开源了一套前端 Flux.1-dev 文生图服务网站解决方案。</font></p> <p><font style="color:#020817">具有完整的 Docker&amp;Serverless 化部署方案，您可以参考使用。</font></p> <p><font style="color:#020817">项目地址：</font><a href="https://github.com/slmnb-lab/FluxEz"><font style="color:#2f8ef4">https://github.com/slmnb-lab/FluxEz</font></a></p> <p><img src="'+e+'" alt=""></p> <h2 id="2-部署步骤"><font style="color:#020817">2 部署步骤</font></h2> <p><font style="color:#020817">我们提供了构建完毕的 Flux 镜像可以直接部署使用。</font></p> <p><font style="color:#020817">下面是部署步骤：</font></p> <h3 id="21-访问天工开物控制台，点击新增部署。"><font style="color:#020817">2.1 访问</font><a href="https://tiangongkaiwu.top/#/console">天工开物控制台</a><font style="color:#020817">，点击新增部署。</font></h3> <p><img src="'+u+'" alt=""></p> <h3 id="22-基于自身需要进行配置，参考配置为单卡-4090-和-1-个节点（初次使用进行调试）。"><font style="color:#020817">2.2 基于自身需要进行配置，参考配置为单卡 4090 和 1 个节点（初次使用进行调试）。</font></h3> <p><img src="'+l+'" alt=""></p> <h3 id="23-选择相应预制镜像"><font style="color:#020817">2.3 选择相应预制镜像</font></h3> <p><img src="'+f+'" alt=""></p> <h3 id="24-点击部署服务，耐心等待节点拉取镜像并启动。"><font style="color:#020817">2.4 点击部署服务，耐心等待节点拉取镜像并启动。</font></h3> <p><img src="'+s+'" alt=""></p> <h3 id="25-节点启动后，你所在任务详情页中看到的内容可能如下："><font style="color:#020817">2.5 节点启动后，你所在“任务详情页”中看到的内容可能如下：</font></h3> <p><img src="'+r+'" alt=""></p> <h3 id="26-我们可以点击快速访问下方8188端口的链接，测试-comfyui-部署情况"><font style="color:#020817">2.6 我们可以点击快速访问下方“8188”端口的链接，测试 comfyui 部署情况</font></h3> <p><font style="color:#020817">系统会自动分配一个可公网访问的域名，点击 8188 端口的链接。接下来我们即可自由地通过使用工作流在 comfyui 中使用 Flux 模型进行图像生成。</font></p> <p>我们进入 8188 端口的 web ui 界面，选择左侧的“工作流“菜单，找到名为”flux_dev_t5fp8.json“的工作流文件，鼠标点击。</p> <p><img src="'+q+'" alt=""></p> <p><font style="color:#020817">随后，我们可以看到工作流已经被正常载入 ComfyUI 了。接下来，我们找到 prompt（提示词）的填写节点，输入我们想要生成的图像描述文本。</font></p> <p><img src="'+a+'" alt=""></p> <p><font style="color:#020817">参考的 prompt 如下（FLUX 模型对英文支持较好）：</font></p> <p><font style="color:#67676c"> best quality, a cute anime boy and girl, sunshine, soft features, sitting together on a swing, blue and white outfits, flowers in their hair, blue eyes, blush, gentle smile, long hair (girl), short tousled hair (boy), barefoot, looking at viewer, full body, light particles, pale skin, floral background, off shoulder dress (girl), open shirt with vest (boy), collarbones, blue roses, vines and plants, romantic atmosphere, blue and white theme, ethereal lighting, soft breeze, whimsical mood </font></p> <p><font style="color:#020817">稍等片刻，即可在后方的“保存图像”节点看到基于我们提示词生成的图片。右键点击图片，选择“Save Image”即可保存图片。</font></p> <p><img src="'+c+'" alt=""></p> <h3 id="27-通过-api-的形式来调用-comfyui-进行图像生成"><font style="color:#020817">2.7 通过 API 的形式来调用 comfyui 进行图像生成</font></h3> <p><font style="color:#020817">我们不推荐直接使用 comfyui 的默认 API 接口，因为多节点时需自行解决无状态问题。更推荐的做法是通过我们暴露的 3000 端口进行请求，其通过 </font><a href="https://github.com/SaladTechnologies/comfyui-api?tab=readme-ov-file"><font style="color:#2f8ef4">comfyui-api</font></a><font style="color:#020817"> 进行包装，支持默认的同步生图请求和 webhook 实现。</font></p> <p><font style="color:#020817">以下以 POSTMAN 为例，简要描述如何向 3000 端口发送图片生成的 API 请求：</font></p> <h4 id="271-保存页面工作流"><font style="color:#020817">2.7.1 保存页面工作流</font></h4> <p><font style="color:#020817">点击”导航栏——工作流——导出（API）“菜单，浏览器会自动下载一个 json 文件。</font></p> <p><img src="'+p+'" alt=""></p> <h4 id="272-打开-postman，新建一个-post-请求"><font style="color:#020817">2.7.2 打开 POSTMAN，新建一个 POST 请求</font></h4> <p><font style="color:#020817">新建一个 POST 请求，并命名为”prompt“，如下图所示：</font></p> <p><img src="'+i+'" alt=""></p> <h4 id="273-完善请求信息"><font style="color:#020817">2.7.3 完善请求信息</font></h4> <p><font style="color:#020817">需要完善的信息如下：</font></p> <ul> <li><strong><font style="color:#020817">请求的 URL</font></strong></li> </ul> <p><font style="color:#020817">在 3000 端口的回传链接后加上“/prompt”的路径，保证其格式类似于</font><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)"><a href="https://xxx/prompt">https://xxx/prompt</a></font><font style="color:#020817">。</font></p> <ul> <li><strong><font style="color:#020817">将请求体参数格式设置为 raw 和 json</font></strong></li> </ul> <p><font style="color:#020817">如图。</font></p> <ul> <li><strong><font style="color:#020817">设置参数内容基本格式</font></strong></li> </ul> <p><font style="color:#020817">如图。</font></p> <p><img src="'+d+'" alt=""></p> <h4 id="274-将我们下载好的工作流-json-文件粘贴为参数中prompt字段的值"><font style="color:#020817">2.7.4 将我们下载好的工作流 json 文件粘贴为参数中prompt字段的值</font></h4> <p><font style="color:#020817">如下图所示，我们将鼠标移动至 prompt 字段的冒号后，粘贴工作流的内容。</font></p> <p><img src="'+y+'" alt=""></p> <h4 id="275-发送请求"><font style="color:#020817">2.7.5 发送请求</font></h4> <p><font style="color:#020817">返回结果如下所示，</font><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)">images</font><font style="color:#020817">字段包含一个字符数组，其中的元素即为生成图片的 base64 编码。</font></p> <p><img src="'+h+'" alt=""></p> <h2 id="3-构建-comfyui-镜像"><font style="color:#020817">3 构建 comfyui 镜像</font></h2> <blockquote> <p><font style="color:#67676c">温馨提示，如果你只希望使用我们默认的镜像，那么下面的内容您无需关注。</font></p> </blockquote> <p><font style="color:#020817">如果您希望构建自定义的镜像，而非仅仅使用我们提供的预设 Flux 镜像，以下是一个很好的参考。您可根据自身需要，添加需要的模型和插件。</font></p> <h3 id="31-下载模型文件"><font style="color:#020817">3.1 下载模型文件</font></h3> <p><font style="color:#020817">我们所需的模型文件共有四个，下载链接分别如下，分别将其下载至本地：</font></p> <ul> <li><a href="https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/clip_l.safetensors?download=true"><font style="color:#2f8ef4">https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/clip_l.safetensors?download=true</font></a></li> <li><a href="https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/t5xxl_fp16.safetensors?download=true"><font style="color:#2f8ef4">https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/t5xxl_fp16.safetensors?download=true</font></a></li> <li><a href="https://huggingface.co/black-forest-labs/FLUX.1-schnell/resolve/main/ae.safetensors?download=true"><font style="color:#2f8ef4">https://huggingface.co/black-forest-labs/FLUX.1-schnell/resolve/main/ae.safetensors?download=true</font></a></li> <li><a href="https://huggingface.co/black-forest-labs/FLUX.1-dev/resolve/main/flux1-dev.safetensors"><font style="color:#2f8ef4">https://huggingface.co/black-forest-labs/FLUX.1-dev/resolve/main/flux1-dev.safetensors</font></a></li> </ul> <p><font style="color:#020817">注意，如果你的显存低于 32GB，建议将上述文件中的 t5xxl_fp16.safetensors 替换为下面的低配版模型 t5xxl_fp8_e4m3n.safetensors（后续工作流中对应修改一下模型名即可），下面的案例中我们将使用低配版模型作为示例：</font></p> <p><a href="https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/t5xxl_fp8_e4m3fn.safetensors?download=true"><font style="color:#2f8ef4">https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/t5xxl_fp8_e4m3fn.safetensors?download=true</font></a></p> <h3 id="32-创建-dockerfile-文件"><font style="color:#020817">3.2 创建 Dockerfile 文件</font></h3> <p><font style="color:#020817">一个合适的 docker 基础镜像能帮助我们节省大量的时间，同时还能大大减少我们构建出错的概率。这里我们选择</font><a href="https://github.com/SaladTechnologies/comfyui-api"><font style="color:#2f8ef4">comfyui-api</font></a><font style="color:#020817">开源库的官方镜像作为我们的基础镜像，其预装了 comfyui 及 comfyui-api 以及基础的运行环境依赖。</font></p> <p><font style="color:#020817">我们最终的需要 Dockerfile 文件内容如下，请新建一个名为 Dockerfile 的文件（注意无后缀），通过任意编辑器打开，将下面的内容复制进去。</font></p> <pre><code class="language-dockerfile"># 使用 comfyui-api 基础镜像\nFROM ghcr.io/saladtechnologies/comfyui-api:comfy0.3.29-api1.8.3-torch2.6.0-cuda12.4-runtime\n\n# 设置环境变量\nENV COMFYUI_PORT=8188 \\\n    MODEL_DIR=/opt/ComfyUI/models \\\n    BASE=&quot;&quot;\n\n# 4. 预创建模型目录结构\nRUN mkdir -p ${MODEL_DIR}/{loras,vaes,text_encoders,diffusion_models}\n\n# 5. 复制模型文件\nCOPY diffusion_models/*.safetensors ${MODEL_DIR}/diffusion_models/\nCOPY vae/*.safetensors ${MODEL_DIR}/vae/\nCOPY text_encoders/*.safetensors ${MODEL_DIR}/text_encoders/\n\n# 6. 暴露端口\nEXPOSE ${COMFYUI_PORT}\n</code></pre> <h3 id="33-创建目录"><font style="color:#020817">3.3 创建目录</font></h3> <p><font style="color:#020817">创建目录是为了便于我们指定路径，请按照下面的路径放置上述的文件。</font></p> <p><font style="color:#020817">comfyUI/</font></p> <p><font style="color:#020817">└── Dockerfile</font></p> <p><font style="color:#020817">├── diffusion_models/</font></p> <p><font style="color:#020817">│ └── flux1-dev.safetensors</font></p> <p><font style="color:#020817">├── text_encoders/</font></p> <p><font style="color:#020817">│ ├── clip_l.safetensors</font></p> <p><font style="color:#020817">│ └── t5xxl_fp8_e4m3fn.safetensors</font></p> <p><font style="color:#020817">├── vae/</font></p> <p><font style="color:#020817">│ └── ae.safetensors</font></p> <h3 id="34-执行构建"><font style="color:#020817">3.4 执行构建</font></h3> <p><font style="color:#020817">进入 comfyuiUI 目录，打开控制台，执行如下命令（可根据需要自行修改标签和镜像名）：</font></p> <pre><code class="language-dockerfile">docker build -t comfyui-flux:0.1 .\n</code></pre> <p><font style="color:#020817">耐心等待构建完毕，最终生成的镜像体积约 42GB。</font></p> <h3 id="35-本地测试（推荐）"><font style="color:#020817">3.5 本地测试（推荐）</font></h3> <p><font style="color:#020817">虽然即使不经过本地测试也可以直接上传使用，但本地测试可以更快的预览镜像构建效果，还能提前排除可能的一些问题，避免构建过程中的错误在上传远程仓库后才发现。</font></p> <p><font style="color:#020817">我们可以在任意位置打开控制台，键入以下指令：</font></p> <pre><code class="language-dockerfile">docker run --rm --gpus all -p 3000:3000 comfyui-flux:0.1\n</code></pre> <p><font style="color:#020817">当容器运行完毕后，，我们可以通过 API 来判断其是否正常工作。</font></p> <h4 id="351-获取-api-文档"><font style="color:#020817">3.5.1 获取 API 文档</font></h4> <p><font style="color:#020817">我们可以打开浏览器，网址栏输入</font><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)"><a href="http://localhost:3000/docs">http://localhost:3000/docs</a></font><font style="color:#020817">，即可打开默认的 comfyui-api 基于 swagger 的文档页面，如下图所示：</font></p> <p><img src="'+m+'" alt=""></p> <p><font style="color:#020817">其中介绍了我们可以使用的 4 个常用方法，可自行了解详情。</font></p> <h4 id="352-测试生图接口"><font style="color:#020817">3.5.2 测试生图接口</font></h4> <p><font style="color:#020817">接下来我们需要测试镜像容器的生图功能——当然也是最重要的功能。这一步推荐使用 PostMan 这类 API 测试工具，以下将以 PostMan 作为示例：</font></p> <h5 id="3521-创建请求"><font style="color:#020817">******* 创建请求</font></h5> <p><font style="color:#020817">新建一个请求，将其请求方法设置为 POST，并键入</font><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)"><a href="http://localhost:3000/prompt">http://localhost:3000/prompt</a></font><font style="color:#020817">作为请求的 URL。</font></p> <h5 id="3522-选择参数"><font style="color:#020817">******* 选择参数</font></h5> <p><font style="color:#020817">找到下方的</font><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)">body</font><font style="color:#020817">栏，点击</font><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)">raw</font><font style="color:#020817">一项，并选择右侧的类型为</font><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)">JSON</font><font style="color:#020817">。在下方键入我们的 JSON 参数。</font></p> <p><font style="color:#020817">我们所需的参数如下，prompt 对应的是 comfyui 的工作流内容（API 形式）如果有自定义需要，我们也可以修改其中的参数值。</font></p> <p><img src="'+g+'" alt=""></p> <pre><code class="language-json">{\n  &quot;prompt&quot;:{\n    &quot;8&quot;: {\n      &quot;inputs&quot;: {\n        &quot;samples&quot;: [\n          &quot;40&quot;,\n          0\n        ],\n        &quot;vae&quot;: [\n          &quot;10&quot;,\n          0\n        ]\n      },\n      &quot;class_type&quot;: &quot;VAEDecode&quot;,\n      &quot;_meta&quot;: {\n        &quot;title&quot;: &quot;VAE解码&quot;\n      }\n    },\n    &quot;10&quot;: {\n      &quot;inputs&quot;: {\n        &quot;vae_name&quot;: &quot;ae.safetensors&quot;\n      },\n      &quot;class_type&quot;: &quot;VAELoader&quot;,\n      &quot;_meta&quot;: {\n        &quot;title&quot;: &quot;加载VAE&quot;\n      }\n    },\n    &quot;11&quot;: {\n      &quot;inputs&quot;: {\n        &quot;clip_name1&quot;: &quot;t5xxl_fp8_e4m3fn.safetensors&quot;,\n        &quot;clip_name2&quot;: &quot;clip_l.safetensors&quot;,\n        &quot;type&quot;: &quot;flux&quot;,\n        &quot;device&quot;: &quot;default&quot;\n      },\n      &quot;class_type&quot;: &quot;DualCLIPLoader&quot;,\n      &quot;_meta&quot;: {\n        &quot;title&quot;: &quot;双CLIP加载器&quot;\n      }\n    },\n    &quot;17&quot;: {\n      &quot;inputs&quot;: {\n        &quot;scheduler&quot;: &quot;normal&quot;,\n        &quot;steps&quot;: 25,\n        &quot;denoise&quot;: 1,\n        &quot;model&quot;: [\n          &quot;46&quot;,\n          0\n        ]\n      },\n      &quot;class_type&quot;: &quot;BasicScheduler&quot;,\n      &quot;_meta&quot;: {\n        &quot;title&quot;: &quot;基本调度器&quot;\n      }\n    },\n    &quot;38&quot;: {\n      &quot;inputs&quot;: {\n        &quot;model&quot;: [\n          &quot;46&quot;,\n          0\n        ],\n        &quot;conditioning&quot;: [\n          &quot;42&quot;,\n          0\n        ]\n      },\n      &quot;class_type&quot;: &quot;BasicGuider&quot;,\n      &quot;_meta&quot;: {\n        &quot;title&quot;: &quot;基本引导器&quot;\n      }\n    },\n    &quot;39&quot;: {\n      &quot;inputs&quot;: {\n        &quot;filename_prefix&quot;: &quot;FluxEz&quot;,\n        &quot;images&quot;: [\n          &quot;8&quot;,\n          0\n        ]\n      },\n      &quot;class_type&quot;: &quot;SaveImage&quot;,\n      &quot;_meta&quot;: {\n        &quot;title&quot;: &quot;保存图像&quot;\n      }\n    },\n    &quot;40&quot;: {\n      &quot;inputs&quot;: {\n        &quot;noise&quot;: [\n          &quot;45&quot;,\n          0\n        ],\n        &quot;guider&quot;: [\n          &quot;38&quot;,\n          0\n        ],\n        &quot;sampler&quot;: [\n          &quot;47&quot;,\n          0\n        ],\n        &quot;sigmas&quot;: [\n          &quot;17&quot;,\n          0\n        ],\n        &quot;latent_image&quot;: [\n          &quot;44&quot;,\n          0\n        ]\n      },\n      &quot;class_type&quot;: &quot;SamplerCustomAdvanced&quot;,\n      &quot;_meta&quot;: {\n        &quot;title&quot;: &quot;自定义采样器（高级）&quot;\n      }\n    },\n    &quot;42&quot;: {\n      &quot;inputs&quot;: {\n        &quot;guidance&quot;: 3.5,\n        &quot;conditioning&quot;: [\n          &quot;43&quot;,\n          0\n        ]\n      },\n      &quot;class_type&quot;: &quot;FluxGuidance&quot;,\n      &quot;_meta&quot;: {\n        &quot;title&quot;: &quot;Flux引导&quot;\n      }\n    },\n    &quot;43&quot;: {\n      &quot;inputs&quot;: {\n        &quot;text&quot;: &quot;beautiful photography of a gonger haired artist with Lots of Colorful coloursplashes in face and pn her hands, she is natural, having her hair in a casual bun, looking happily into camera, cinematic,&quot;,\n        &quot;clip&quot;: [\n          &quot;11&quot;,\n          0\n        ]\n      },\n      &quot;class_type&quot;: &quot;CLIPTextEncode&quot;,\n      &quot;_meta&quot;: {\n        &quot;title&quot;: &quot;CLIP文本编码&quot;\n      }\n    },\n    &quot;44&quot;: {\n      &quot;inputs&quot;: {\n        &quot;width&quot;: 1024,\n        &quot;height&quot;: 1024,\n        &quot;batch_size&quot;: 1\n      },\n      &quot;class_type&quot;: &quot;EmptySD3LatentImage&quot;,\n      &quot;_meta&quot;: {\n        &quot;title&quot;: &quot;空Latent图像（SD3）&quot;\n      }\n    },\n    &quot;45&quot;: {\n      &quot;inputs&quot;: {\n        &quot;noise_seed&quot;: 454905699352480\n      },\n      &quot;class_type&quot;: &quot;RandomNoise&quot;,\n      &quot;_meta&quot;: {\n        &quot;title&quot;: &quot;随机噪波&quot;\n      }\n    },\n    &quot;46&quot;: {\n      &quot;inputs&quot;: {\n        &quot;max_shift&quot;: 1.15,\n        &quot;base_shift&quot;: 0.5,\n        &quot;width&quot;: 1024,\n        &quot;height&quot;: 1024,\n        &quot;model&quot;: [\n          &quot;48&quot;,\n          0\n        ]\n      },\n      &quot;class_type&quot;: &quot;ModelSamplingFlux&quot;,\n      &quot;_meta&quot;: {\n        &quot;title&quot;: &quot;采样算法（Flux）&quot;\n      }\n    },\n    &quot;47&quot;: {\n      &quot;inputs&quot;: {\n        &quot;sampler_name&quot;: &quot;euler&quot;\n      },\n      &quot;class_type&quot;: &quot;KSamplerSelect&quot;,\n      &quot;_meta&quot;: {\n        &quot;title&quot;: &quot;K采样器选择&quot;\n      }\n    },\n    &quot;48&quot;: {\n      &quot;inputs&quot;: {\n        &quot;unet_name&quot;: &quot;flux1-dev.safetensors&quot;,\n        &quot;weight_dtype&quot;: &quot;default&quot;\n      },\n      &quot;class_type&quot;: &quot;UNETLoader&quot;,\n      &quot;_meta&quot;: {\n        &quot;title&quot;: &quot;UNet加载器&quot;\n      }\n    }\n  }\n}\n</code></pre> <h5 id="3523-发送请求"><font style="color:#020817">3.5.2.3 发送请求</font></h5> <p><font style="color:#020817">请求返回的结果应该如下：</font></p> <pre><code class="language-dockerfile">{\n    &quot;id&quot;: &quot;************************************&quot;,\n    &quot;prompt&quot;://你的工作流参数,\n    &quot;images&quot;: []//返回的图片数组,\n}\n</code></pre> <h3 id="36-推送镜像到平台"><font style="color:#020817">3.6 推送镜像到平台</font></h3> <p><font style="color:#020817">将上一步中自定义的镜像上传到我们的镜像仓库服务中。请参考文档镜像仓库。</font></p> <p><strong><font style="color:#67676c"></font></strong></p> <p><br><br> <font style="color:#b2b2b2">最后更新于: 2025/6/16 14:36</font></p> ';t["default"]=x},7906:function(o,t,n){o.exports=n.p+"img/flux_dev1.21182f8b.png"},9751:function(o,t,n){o.exports=n.p+"img/flux_dev10.dd2126b4.png"},5225:function(o,t,n){o.exports=n.p+"img/flux_dev11.c821ef82.png"},1832:function(o,t,n){o.exports=n.p+"img/flux_dev12.d0c427b4.png"},4444:function(o){o.exports="data:image/png;base64,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"},4689:function(o,t,n){o.exports=n.p+"img/flux_dev14.8ff174fa.png"},1915:function(o,t,n){o.exports=n.p+"img/flux_dev15.6a9908a1.png"},2947:function(o,t,n){o.exports=n.p+"img/flux_dev16.c58c3d2e.png"},5321:function(o,t,n){o.exports=n.p+"img/flux_dev4.f1711c01.png"},3505:function(o,t,n){o.exports=n.p+"img/flux_dev5.cf77aadf.png"},4974:function(o,t,n){o.exports=n.p+"img/flux_dev6.bd8faa6e.png"},3299:function(o,t,n){o.exports=n.p+"img/flux_dev7.50379362.png"},2907:function(o,t,n){o.exports=n.p+"img/flux_dev8.6f830af0.png"},9392:function(o,t,n){o.exports=n.p+"img/flux_dev9.cdb7e5a8.png"},1404:function(o,t,n){o.exports=n.p+"img/universal1.9a1b3f4b.png"},4117:function(o,t,n){o.exports=n.p+"img/universal2.4306636e.png"}}]);
//# sourceMappingURL=docs8.5542fa90.js.map