# <font style="color:rgb(2, 8, 23);">容器化部署 StableDiffusion-3.5-large 文生图模型应用</font>
## <font style="color:rgb(2, 8, 23);">1 部署步骤</font>
### <font style="color:rgb(2, 8, 23);">1.1 访问</font>[天工开物控制台](https://tiangongkaiwu.top/#/console)<font style="color:rgb(2, 8, 23);">，点击新增部署。</font>
![](./imgs/universal1.png)

### <font style="color:rgb(2, 8, 23);">1.2 基于自身需要进行配置，参考配置为单卡 4090 和 1 个节点（初次使用进行调试）。</font>
![](./imgs/universal2.png)

### <font style="color:rgb(2, 8, 23);">1.3 选择相应预制镜像</font>
![](./imgs/comfyui3.png)

### <font style="color:rgb(2, 8, 23);">1.4 点击部署服务，耐心等待节点拉取镜像并启动。</font>
![](./imgs/comfyui4.png)

### <font style="color:rgb(2, 8, 23);">1.5 节点启动后，你所在“任务详情页”中看到的内容可能如下：</font>
![](./imgs/comfyui5.png)

### <font style="color:rgb(2, 8, 23);">1.6 我们可以点击快速访问下方“8188”端口的链接，测试 comfyui 部署情况</font>
<font style="color:rgb(2, 8, 23);">系统会自动分配一个可公网访问的域名，点击 8188 端口的链接。接下来我们即可自由地通过使用工作流在 comfyui 中使用 Flux 模型进行图像生成。</font>

<font style="color:rgb(2, 8, 23);">我们进入 8188 端口的 web ui 界面，选择左侧的“工作流“菜单，找到名为”sd_text_encoder_exampl.json“的工作流文件，鼠标点击。</font>

![](./imgs/comfyui6.png)

<font style="color:rgb(2, 8, 23);">随后，我们可以看到工作流已经被正常载入 ComfyUI 了。接下来，我们找到 prompt（提示词）的填写节点，输入我们想要生成的图像描述文本。</font>

![](./imgs/comfyui7.png)

<font style="color:rgb(2, 8, 23);">参考的 prompt 如下（SD 模型对英文支持较好）：</font>

<font style="color:rgb(103, 103, 108);">best quality, a cute anime girl, sunset light, warm atmosphere, soft features, dreamy mood, sitting on a swing, solo, looking at viewer, gentle smile, blush, pale skin, full body, long flowing hair, off-shoulder dress with blue and white edges, visible collarbone, barefoot, golden hour lighting, light particles, flower petals floating, orange sky, backlight glow, surrounded by vines and plants, roses and blue flowers around, soft shadows, natural environment, cinematic framing</font>

<font style="color:rgb(2, 8, 23);">稍等片刻，即可在后方的“保存图像”节点看到基于我们提示词生成的图片。右键点击图片，选择“Save Image”即可保存图片。</font>

![](./imgs/comfyui8.png)

### <font style="color:rgb(2, 8, 23);">1.7 通过 API 的形式来调用 comfyui 进行图像生成</font>
<font style="color:rgb(2, 8, 23);">我们不推荐直接使用 comfyui 的默认 API 接口，因为多节点时需自行解决无状态问题。更推荐的做法是通过我们暴露的 3000 端口进行请求，其通过 </font>[<font style="color:#2F8EF4;">comfyui-api</font>](https://github.com/SaladTechnologies/comfyui-api?tab=readme-ov-file)<font style="color:rgb(2, 8, 23);"> 进行包装，支持默认的同步生图请求和 webhook 实现。</font>

<font style="color:rgb(2, 8, 23);">以下以 POSTMAN 为例，简要描述如何向 3000 端口发送图片生成的 API 请求：</font>

#### <font style="color:rgb(2, 8, 23);">1.7.1 保存页面工作流</font>
<font style="color:rgb(2, 8, 23);">点击”导航栏——工作流——导出（API）“菜单，浏览器会自动下载一个 json 文件。</font>

![](./imgs/comfyui9.png)

#### <font style="color:rgb(2, 8, 23);">1.7.2 打开 POSTMAN，新建一个 POST 请求</font>
<font style="color:rgb(2, 8, 23);">新建一个 POST 请求，并命名为”prompt“，如下图所示：</font>

![](./imgs/comfyui10.png)

#### <font style="color:rgb(2, 8, 23);">1.7.3 完善请求信息</font>
<font style="color:rgb(2, 8, 23);">需要完善的信息如下：</font>

+ **<font style="color:rgb(2, 8, 23);">请求的 URL</font>**

<font style="color:rgb(2, 8, 23);">在 3000 端口的回传链接后加上“/prompt”的路径，保证其格式类似于</font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">https://xxx/prompt</font><font style="color:rgb(2, 8, 23);">。</font>

+ **<font style="color:rgb(2, 8, 23);">将请求体参数格式设置为 raw 和 json</font>**

<font style="color:rgb(2, 8, 23);">如图。</font>

+ **<font style="color:rgb(2, 8, 23);">设置参数内容基本格式</font>**

<font style="color:rgb(2, 8, 23);">如图。</font>

![](./imgs/comfyui11.png)

#### <font style="color:rgb(2, 8, 23);">1.7.4 将我们下载好的工作流 json 文件粘贴为参数中</font><font style="color:rgb(2, 8, 23);background-color:rgba(142, 150, 170, 0.14);">prompt</font><font style="color:rgb(2, 8, 23);">字段的值</font>
<font style="color:rgb(2, 8, 23);">如下图所示，我们将鼠标移动至 prompt 字段的冒号后，粘贴工作流的内容。</font>

![](./imgs/comfyui12.png)

#### <font style="color:rgb(2, 8, 23);">1.7.5 发送请求</font>
<font style="color:rgb(2, 8, 23);">返回结果如下所示，</font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">images</font><font style="color:rgb(2, 8, 23);">字段包含一个字符数组，其中的元素即为生成图片的 base64 编码。</font>

**<font style="color:rgb(103, 103, 108);"></font>**

<br><br>
<font style="color:rgba(178,178,178,1);">最后更新于: 2025/6/18 16:03</font>
