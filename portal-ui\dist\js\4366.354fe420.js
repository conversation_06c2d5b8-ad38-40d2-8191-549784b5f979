"use strict";(self["webpackChunkportal_ui"]=self["webpackChunkportal_ui"]||[]).push([[4366],{4366:function(s,e,r){r.r(e),r.d(e,{default:function(){return m}});var t=function(){var s=this,e=s._self._c;return e("div",{staticClass:"login-page"},[s.showCodeSent?e("SlideNotification",{attrs:{message:"验证码已发送，可能会有延迟，请耐心等待！",type:"success","min-height":s.notificationMinHeight},on:{close:function(e){s.showCodeSent=!1}}}):s._e(),e("div",{staticClass:"left-side"},[e("backgroundlogin")],1),e("div",{staticClass:"right-side"},[e("div",{staticClass:"login-form-container"},[e("h3",[s._v("重置统一登录密码")]),e("div",{staticClass:"form-container"},[e("div",{staticClass:"login-form"},[e("p",{staticClass:"form-note"},[s._v("请输入手机号接收验证码")]),e("div",{staticClass:"input-group",class:{error:s.errors.phone}},[e("input",{directives:[{name:"model",rawName:"v-model",value:s.resetForm.phone,expression:"resetForm.phone"}],attrs:{type:"text",placeholder:"请输入手机号"},domProps:{value:s.resetForm.phone},on:{blur:s.validatePhone,input:function(e){e.target.composing||s.$set(s.resetForm,"phone",e.target.value)}}}),e("div",{staticClass:"error-container"},[s.errors.phone?e("div",{staticClass:"error-message"},[s._v(s._s(s.errors.phone))]):s._e()])]),e("div",{staticClass:"input-group verification-code",class:{error:s.errors.code}},[e("div",{staticClass:"code-input-container"},[e("input",{directives:[{name:"model",rawName:"v-model",value:s.resetForm.code,expression:"resetForm.code"}],attrs:{type:"text",placeholder:"请输入验证码"},domProps:{value:s.resetForm.code},on:{blur:s.validateCode,input:function(e){e.target.composing||s.$set(s.resetForm,"code",e.target.value)}}}),e("button",{staticClass:"get-code-btn-inline",attrs:{disabled:!s.resetForm.phone||s.errors.phone||s.codeSent},on:{click:s.getVerificationCode}},[s._v(" "+s._s(s.codeSent?`${s.countdown}秒后重试`:"获取验证码")+" ")])]),e("div",{staticClass:"error-container"},[s.errors.code?e("div",{staticClass:"error-message"},[s._v(s._s(s.errors.code))]):s._e()])]),e("div",{staticClass:"input-group",class:{error:s.errors.newPassword}},[e("div",{staticClass:"password-input-container"},["checkbox"===(s.passwordVisible?"text":"password")?e("input",{directives:[{name:"model",rawName:"v-model",value:s.resetForm.newPassword,expression:"resetForm.newPassword"}],attrs:{placeholder:"请输入新密码",type:"checkbox"},domProps:{checked:Array.isArray(s.resetForm.newPassword)?s._i(s.resetForm.newPassword,null)>-1:s.resetForm.newPassword},on:{blur:s.validateNewPassword,change:function(e){var r=s.resetForm.newPassword,t=e.target,o=!!t.checked;if(Array.isArray(r)){var i=null,a=s._i(r,i);t.checked?a<0&&s.$set(s.resetForm,"newPassword",r.concat([i])):a>-1&&s.$set(s.resetForm,"newPassword",r.slice(0,a).concat(r.slice(a+1)))}else s.$set(s.resetForm,"newPassword",o)}}}):"radio"===(s.passwordVisible?"text":"password")?e("input",{directives:[{name:"model",rawName:"v-model",value:s.resetForm.newPassword,expression:"resetForm.newPassword"}],attrs:{placeholder:"请输入新密码",type:"radio"},domProps:{checked:s._q(s.resetForm.newPassword,null)},on:{blur:s.validateNewPassword,change:function(e){return s.$set(s.resetForm,"newPassword",null)}}}):e("input",{directives:[{name:"model",rawName:"v-model",value:s.resetForm.newPassword,expression:"resetForm.newPassword"}],attrs:{placeholder:"请输入新密码",type:s.passwordVisible?"text":"password"},domProps:{value:s.resetForm.newPassword},on:{blur:s.validateNewPassword,input:function(e){e.target.composing||s.$set(s.resetForm,"newPassword",e.target.value)}}}),e("span",{staticClass:"password-toggle",on:{click:s.togglePasswordVisibility}},[e("i",{class:["eye-icon",s.passwordVisible?"visible":""]})])]),e("div",{staticClass:"error-container"},[s.errors.newPassword?e("div",{staticClass:"error-message"},[s._v(s._s(s.errors.newPassword))]):s._e()])]),e("div",{staticClass:"input-group",class:{error:s.errors.confirmPassword}},[e("div",{staticClass:"password-input-container"},["checkbox"===(s.confirmPasswordVisible?"text":"password")?e("input",{directives:[{name:"model",rawName:"v-model",value:s.resetForm.confirmPassword,expression:"resetForm.confirmPassword"}],attrs:{placeholder:"请再次输入新密码",type:"checkbox"},domProps:{checked:Array.isArray(s.resetForm.confirmPassword)?s._i(s.resetForm.confirmPassword,null)>-1:s.resetForm.confirmPassword},on:{blur:s.validateConfirmPassword,change:function(e){var r=s.resetForm.confirmPassword,t=e.target,o=!!t.checked;if(Array.isArray(r)){var i=null,a=s._i(r,i);t.checked?a<0&&s.$set(s.resetForm,"confirmPassword",r.concat([i])):a>-1&&s.$set(s.resetForm,"confirmPassword",r.slice(0,a).concat(r.slice(a+1)))}else s.$set(s.resetForm,"confirmPassword",o)}}}):"radio"===(s.confirmPasswordVisible?"text":"password")?e("input",{directives:[{name:"model",rawName:"v-model",value:s.resetForm.confirmPassword,expression:"resetForm.confirmPassword"}],attrs:{placeholder:"请再次输入新密码",type:"radio"},domProps:{checked:s._q(s.resetForm.confirmPassword,null)},on:{blur:s.validateConfirmPassword,change:function(e){return s.$set(s.resetForm,"confirmPassword",null)}}}):e("input",{directives:[{name:"model",rawName:"v-model",value:s.resetForm.confirmPassword,expression:"resetForm.confirmPassword"}],attrs:{placeholder:"请再次输入新密码",type:s.confirmPasswordVisible?"text":"password"},domProps:{value:s.resetForm.confirmPassword},on:{blur:s.validateConfirmPassword,input:function(e){e.target.composing||s.$set(s.resetForm,"confirmPassword",e.target.value)}}}),e("span",{staticClass:"password-toggle",on:{click:s.toggleConfirmPasswordVisibility}},[e("i",{class:["eye-icon",s.confirmPasswordVisible?"visible":""]})])]),e("div",{staticClass:"error-container"},[s.errors.confirmPassword?e("div",{staticClass:"error-message"},[s._v(s._s(s.errors.confirmPassword))]):s._e()])]),e("button",{staticClass:"login-btn",attrs:{disabled:!s.isFormValid||s.isVerifying},on:{click:s.resetPassword}},[s._v(" "+s._s(s.isVerifying?"验证中...":"重置密码")+" ")]),e("div",{staticClass:"login-link"},[e("a",{attrs:{href:"#"},on:{click:s.goToLogin}},[s._v("返回登录")])])])])])])],1)},o=[],i=(r(7658),r(2223)),a=r(7234),n=r(6963),d={name:"forgetpass",components:{SlideNotification:a.Z,backgroundlogin:n.Z},data(){return{resetForm:{phone:"",code:"",newPassword:"",confirmPassword:""},passwordVisible:!1,confirmPasswordVisible:!1,errors:{phone:"",code:"",newPassword:"",confirmPassword:""},codeSent:!1,countdown:60,timer:null,showCodeSent:!1,isVerifying:!1,showVerifying:!1,notificationMinHeight:"50px"}},computed:{isFormValid(){return this.resetForm.phone&&this.resetForm.code&&this.resetForm.newPassword&&this.resetForm.confirmPassword&&!this.errors.phone&&!this.errors.code&&!this.errors.newPassword&&!this.errors.confirmPassword}},created(){this.$emit("hiden-layout")},methods:{validatePhone(){const s=/^1[3-9]\d{9}$/;this.resetForm.phone?s.test(this.resetForm.phone)?this.errors.phone="":this.errors.phone="请输入有效的手机号":this.errors.phone="请输入手机号"},validateCode(){this.resetForm.code?4===this.resetForm.code.length&&/^\d+$/.test(this.resetForm.code)?this.errors.code="":this.errors.code="验证码格式不正确":this.errors.code="请输入验证码"},validateNewPassword(){const s=this.resetForm.newPassword;if(this.errors.newPassword="",!s)return void(this.errors.newPassword="请输入新密码");const e=s.length>=8;if(!e)return void(this.errors.newPassword="密码长度至少为8位");const r=/[a-zA-Z]/.test(s),t=/[0-9]/.test(s),o=/[!@#$%^&*()_+\-=$${};':"\\|,.<>\/?]/.test(s),i=/[A-Z]/.test(s)&&/[a-z]/.test(s);let a=0;if(r&&t&&a++,o&&a++,i&&a++,a>=2)this.errors.newPassword="";else{const s=[];r&&t||s.push("包含数字和字母"),o||s.push("包含特殊符号"),i||s.push("包含大小写字母"),this.errors.newPassword=`密码强度不足，请满足以下至少两项要求：${s.join("、")}`}this.resetForm.confirmPassword&&this.validateConfirmPassword()},validateConfirmPassword(){this.resetForm.confirmPassword?this.resetForm.confirmPassword!==this.resetForm.newPassword?this.errors.confirmPassword="两次输入的密码不一致":this.errors.confirmPassword="":this.errors.confirmPassword="请确认新密码"},async getVerificationCode(){if(this.validatePhone(),!this.errors.phone)try{this.codeSent=!0,this.isSendingCode=!0;const s=await(0,i.SV)("/auth/sendCode",{phone:this.resetForm.phone});200===s.data.code?(this.showCodeSent=!0,setTimeout((()=>{this.showCodeSent=!1}),3e3),this.startCountdown()):(this.errors.code=s.data.msg||"验证码发送失败",this.codeSent=!1)}catch(s){this.errors.code="验证码发送失败，请稍后重试",this.codeSent=!1}finally{this.isSendingCode=!1}},startCountdown(){this.timer&&clearInterval(this.timer),this.countdown=60,this.timer=setInterval((()=>{this.countdown<=1?(clearInterval(this.timer),this.codeSent=!1,this.countdown=60):this.countdown--}),1e3)},verifyCode(){return new Promise(((s,e)=>{this.isVerifying=!0,this.showVerifying=!0,(0,i.fB)("/auth/verifyCode",{phone:this.resetForm.phone,code:this.resetForm.code}).then((r=>{this.showVerifying=!1,r.data&&200===r.data.code?s(!0):(this.errors.code=r.data.msg||"验证码验证失败",e(new Error(r.data.msg||"验证码验证失败")))})).catch((s=>{this.showVerifying=!1,this.errors.code="验证码验证失败",e(s)})).finally((()=>{this.isVerifying=!1}))}))},resetPassword(){this.validatePhone(),this.validateCode(),this.validateNewPassword(),this.validateConfirmPassword(),this.isFormValid&&this.verifyCode().then((s=>{s&&(0,i.SV)("/auth/resetPassword",{phone:this.resetForm.phone,code:this.resetForm.code,password:this.resetForm.newPassword}).then((s=>{s.data&&200===s.data.code?this.goToLogin():this.errors.code=s.data.message||"密码重置失败"})).catch((s=>{this.errors.code=res.data.message||"网络异常，密码重置失败"}))})).catch((s=>{}))},goToLogin(){this.$router.push("/login")},togglePasswordVisibility(){this.passwordVisible=!this.passwordVisible},toggleConfirmPasswordVisibility(){this.confirmPasswordVisible=!this.confirmPasswordVisible}},beforeDestroy(){this.$emit("hiden-layout")}},c=d,l=r(1001),h=(0,l.Z)(c,t,o,!1,null,"174895c0",null),m=h.exports},6963:function(s,e,r){r.d(e,{Z:function(){return w}});var t=function(){var s=this,e=s._self._c;s._self._setupProxy;return e("div",{staticClass:"login-left-side"},[e("div",{staticClass:"logo-container"},[e("a",{staticClass:"logo-link",on:{click:function(e){return s.navigateTo("/index")}}},[e("img",{staticClass:"logo",attrs:{src:r(2504),alt:"算力租赁"}})])]),s._m(0),e("div",{staticClass:"visual-element"},[e("div",{staticClass:"server-illustration"},s._l(s.servers,(function(s,r){return e("div",{key:r,staticClass:"server-unit",style:{animationDelay:.2*r+"s",transform:`translateY(${4*r}px)`}},[e("div",{staticClass:"server-light"})])})),0),e("div",{staticClass:"connections"})]),e("div",{staticClass:"features"},s._l(s.features,(function(r,t){return e("div",{key:t,staticClass:"feature-item"},[e("div",{staticClass:"feature-text"},[e("h3",[s._v(s._s(r.title))]),e("p",[s._v(s._s(r.description))])])])})),0),e("div",{staticClass:"background-elements"},s._l(20,(function(s){return e("div",{key:s,staticClass:"floating-particle",style:{left:100*Math.random()+"%",top:100*Math.random()+"%",animationDuration:3+10*Math.random()+"s",animationDelay:5*Math.random()+"s"}})})),0)])},o=[function(){var s=this,e=s._self._c;s._self._setupProxy;return e("div",{staticClass:"bottom-text"},[e("h2",{staticClass:"slogan"},[s._v("高效算力 · 智慧未来")]),e("p",{staticClass:"sub-slogan"},[s._v("专业算力租赁服务，为您的业务提供强大支持")])])}],i=(r(7658),r(144));const a={template:'\n    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">\n      <path d="M12 2v4"></path>\n      <path d="m16.24 7.76 2.83-2.83"></path>\n      <path d="M18 12h4"></path>\n      <path d="m16.24 16.24 2.83 2.83"></path>\n      <path d="M12 18v4"></path>\n      <path d="m7.76 16.24-2.83 2.83"></path>\n      <path d="M6 12H2"></path>\n      <path d="m7.76 7.76-2.83-2.83"></path>\n      <circle cx="12" cy="12" r="4"></circle>\n    </svg>\n  '},n={template:'\n    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">\n      <rect width="20" height="8" x="2" y="2" rx="2" ry="2"></rect>\n      <rect width="20" height="8" x="2" y="14" rx="2" ry="2"></rect>\n      <line x1="6" x2="6" y1="6" y2="6"></line>\n      <line x1="6" x2="6" y1="18" y2="18"></line>\n    </svg>\n  '},d={template:'\n    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">\n      <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>\n    </svg>\n  '};var c=(0,i.aZ)({name:"backgroundlogin",components:{PerformanceIcon:a,ServerIcon:n,ShieldIcon:d},methods:{navigateTo(s){this.currentPath&&this.currentPath!==s?(this.previousActivePath=this.currentPath,this.$nextTick((()=>{const e=document.querySelectorAll(".nav-link, .btn-login");e.forEach((e=>{(e.classList.contains("active")||"/login"===s&&e.classList.contains("btn-login"))&&!e.classList.contains("active-exit")&&(e.classList.add("active-exit"),setTimeout((()=>{e.classList.remove("active-exit")}),300))})),this.currentPath=s}))):this.currentPath=s,this.$route.path===s?this.$nextTick((()=>{window.scrollTo({top:0,behavior:"instant"}),this.$router.go(0)})):(this.$router.push(s),window.scrollTo({top:0,behavior:"instant"}))}},setup(){const s=(0,i.iH)("/api/placeholder/100/100"),e=(0,i.iH)(Array(5).fill(null)),r=(0,i.iH)([{icon:"PerformanceIcon",title:"高性能算力",description:"提供GPU/CPU灵活配置，满足AI训练、渲染等高算力需求"},{icon:"am-icon-shield",title:"安全可靠",description:"数据加密传输，多重备份，确保您的业务安全稳定运行"}]);return{logoSrc:s,servers:e,features:r}}}),l=c,h=r(1001),m=(0,h.Z)(l,t,o,!1,null,"771899f4",null),w=m.exports},2504:function(s,e,r){s.exports=r.p+"img/logo_tiangong.48cfbe63.png"}}]);
//# sourceMappingURL=4366.354fe420.js.map