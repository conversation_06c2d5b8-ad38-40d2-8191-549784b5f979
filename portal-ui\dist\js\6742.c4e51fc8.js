(self["webpackChunkportal_ui"]=self["webpackChunkportal_ui"]||[]).push([[6742],{7742:function(e,t,i){"use strict";i.r(t),i.d(t,{default:function(){return v}});var s=function(){var e=this,t=e._self._c;return t("div",{staticClass:"help-layout"},[e.isMobile?t("div",{staticClass:"mobile-controls"},[t("button",{staticClass:"sidebar-toggle",class:{active:e.sidebarVisible},on:{click:e.toggleSidebar}},[t("i",{staticClass:"icon-menu"}),t("span",[e._v("菜单")])]),t("button",{staticClass:"toc-toggle",class:{active:e.tocVisible},on:{click:e.toggleToc}},[t("i",{staticClass:"icon-list"}),t("span",[e._v("目录")])])]):e._e(),e.isMobile&&(e.sidebarVisible||e.tocVisible)?t("div",{staticClass:"overlay",on:{click:e.closeAllPanels}}):e._e(),t("aside",{ref:"sidebar",staticClass:"sidebar",class:{"sidebar-hidden":!e.sidebarVisible&&e.isMobile,"sidebar-visible":e.sidebarVisible||!e.isMobile}},[e.isMobile?t("div",{staticClass:"sidebar-header"},[t("span",{staticClass:"sidebar-title"},[e._v("帮助文档")]),t("button",{staticClass:"close-btn",on:{click:e.closeSidebar}},[t("i",{staticClass:"icon-close"},[e._v("×")])])]):e._e(),t("div",{staticClass:"sidebar-menu"},e._l(e.menu,(function(i){return t("div",{key:i.title,staticClass:"menu-category"},[t("div",{staticClass:"category-title"},[e._v(e._s(i.title))]),t("ul",{staticClass:"menu-list"},e._l(i.items,(function(i){return t("li",{key:i.path,staticClass:"menu-item"},[t("router-link",{staticClass:"menu-link",class:{"menu-link-active":e.isMenuActive(i.path)},attrs:{to:i.path},on:{click:e.onMenuItemClick}},[e._v(" "+e._s(i.name)+" ")])],1)})),0)])})),0)]),t("main",{ref:"mainContent",staticClass:"main-content",class:{"content-expanded":(!e.sidebarVisible||!e.tocVisible)&&e.isMobile,"content-full":!e.sidebarVisible&&!e.tocVisible&&e.isMobile}},[t("HelpContent",{attrs:{doc:e.currentDoc,"prev-page":e.getPrevPage(),"next-page":e.getNextPage()},on:{"content-loaded":e.buildToc}})],1),t("aside",{staticClass:"toc",class:{"toc-hidden":!e.tocVisible&&e.isMobile,"toc-visible":e.tocVisible||!e.isMobile}},[e.isMobile?t("div",{staticClass:"toc-header"},[t("span",{staticClass:"toc-title"},[e._v("文章导航")]),t("button",{staticClass:"close-btn",on:{click:e.closeToc}},[t("i",{staticClass:"icon-close"},[e._v("×")])])]):e._e(),e.isMobile?e._e():t("div",{staticClass:"toc-title"},[e._v("文章导航")]),t("ul",{staticClass:"toc-list"},e._l(e.toc,(function(i){return t("li",{key:i.id,staticClass:"toc-item",class:{"toc-item-h3":3===i.level,active:i.id===e.activeTocId}},[t("a",{staticClass:"toc-link",attrs:{href:"#"+i.id},on:{click:function(t){return t.preventDefault(),e.scrollToAnchor(i.id)}}},[e._v(" "+e._s(i.text)+" ")])])})),0)]),t("Mider"),t("chatAi")],1)},a=[],l=(i(7658),function(){var e=this,t=e._self._c;return t("div",{staticClass:"doc-content"},[e.loading?t("div",{staticClass:"loading"},[e._v("文档加载中...")]):e.error?t("div",{staticClass:"error"},[e._v("文档加载失败: "+e._s(e.error))]):t("div",[t("div",{ref:"contentRef",domProps:{innerHTML:e._s(e.markdownContent)}}),t("div",{staticClass:"page-navigation"},[t("div",{staticClass:"prev-next-container"},[e.prevPage?t("router-link",{staticClass:"prev-page",attrs:{to:e.prevPage.path}},[t("div",{staticClass:"nav-label"},[e._v("上一篇")]),t("div",{staticClass:"nav-title"},[e._v(e._s(e.prevPage.name))])]):t("div",{staticClass:"prev-page empty"}),e.nextPage?t("router-link",{staticClass:"next-page",attrs:{to:e.nextPage.path}},[t("div",{staticClass:"nav-label"},[e._v("下一篇")]),t("div",{staticClass:"nav-title"},[e._v(e._s(e.nextPage.name))])]):t("div",{staticClass:"next-page empty"})],1)])])])}),n=[],o={props:{doc:String,prevPage:Object,nextPage:Object},data(){return{markdownContent:"",loading:!1,error:null}},watch:{doc:{immediate:!0,async handler(e){this.loading=!0,this.error=null;try{const t=await i(817)(`./${e}.md`);this.markdownContent=t.default,this.$nextTick((()=>{this.processContent(),this.$emit("content-loaded")}))}catch(t){this.error=t.message,this.markdownContent="<h1>文档加载失败</h1>"}finally{this.loading=!1}}}},methods:{slugify(e){let t=e.toLowerCase().replace(/\s+/g,"-").replace(/[^a-z0-9\-]+/g,"").replace(/\-\-+/g,"-").replace(/^-+/,"").replace(/-+$/,"");return t&&!/^[0-9]+$/.test(t)||(t="content-section-"+t+"-"+Math.random().toString(36).substring(2,11)),t},processContent(){if(this.$refs.contentRef){const e=this.$refs.contentRef.querySelectorAll("h2, h3");e.forEach((e=>{e.id=this.slugify(e.textContent)}));const t=this.$refs.contentRef.querySelectorAll("pre code");t.forEach((e=>{e.classList.add("hljs")}));const i=this.$refs.contentRef.querySelectorAll('a[href^="#"]');i.forEach((e=>{e.addEventListener("click",(t=>{t.preventDefault();const i=e.getAttribute("href").substring(1),s=document.getElementById(i);s&&s.scrollIntoView({behavior:"smooth"})}))}))}}}},r=o,c=i(1001),h=(0,c.Z)(r,l,n,!1,null,null,null),d=h.exports,p=i(9484),m=i(3644),u={components:{HelpContent:d,chatAi:p.Z,Mider:m.Z},data(){return{menu:[{title:"弹性部署服务",items:[{name:"平台概要",path:"/help/summary"},{name:"快速开始",path:"/help/quick-start"},{name:"常见问题",path:"/help/qustion"}]},{title:"功能介绍",items:[{name:"镜像仓库",path:"/help/mirror"},{name:"GPU选型指南",path:"/help/gpu-selection"},{name:"健康检查",path:"/help/health-check"},{name:"K8S YAML 导入",path:"/help/k8s-yaml-import"},{name:"云存储加速",path:"/help/cloud-storage"}]},{title:"最佳实践",items:[{name:"弹性部署服务-Serverless 基础认识",path:"/help/deploy-serverless"},{name:"容器化部署 Ollama+Qwen3",path:"/help/ollama-qwen"},{name:"容器化部署 Ollama+Qwen3+Open WebUI",path:"/help/ollama-qwen-webui"},{name:"容器化部署 JupyterLab",path:"/help/jupyter-lab"},{name:"容器化部署 Flux.1-dev 文生图模型应用",path:"/help/flux-dev"},{name:"容器化部署 FramePack 图生视频框架",path:"/help/frame-pack"},{name:"容器化部署 Whisper",path:"/help/whisper"},{name:"容器化部署 StableDiffusion1.5-WebUI 应用",path:"/help/stable-diffusion1.5"},{name:"容器化部署 StableDiffusion2.1-WebUI 应用",path:"/help/stable-diffusion2.1"},{name:"容器化部署 StableDiffusion3.5-large-文生图模型应用",path:"/help/stable-diffusion3.5-large"},{name:"容器化部署 DailyHot",path:"/help/daily-hot"},{name:"容器化部署 ACE-Step",path:"/help/ace-step"},{name:"容器化部署 CosyVoice",path:"/help/cosy-voice"},{name:"容器化部署 Flux.1 Kontext Dev 图片编辑模型应用",path:"/help/Flux.1-kontext-dev"},{name:"容器化部署 HivisionIDPhotos",path:"/help/hivision-photos"},{name:"容器化部署 minicpm4",path:"/help/minicpm4"},{name:"容器化部署 minerU",path:"/help/mineru"},{name:"容器化部署 FunASR",path:"/help/funasr"},{name:"容器化部署 HunyuanPortrait",path:"/help/hunyuan-portrait"},{name:"容器化部署 CodeFormer",path:"/help/code-former"}]},{title:"账户与实名",items:[{name:"手机号注册与登录",path:"/help/register-login"},{name:"个人用户实名",path:"/help/personal-certification"}]},{title:"服务协议",items:[{name:"服务协议",path:"/help/user-agreement"},{name:"隐私政策",path:"/help/privacy-policy"}]},{title:"其他",items:[{name:"Docker 教程",path:"/help/docker-tutorial"}]}],toc:[],allPages:[],activeTocId:null,isAnchorClicking:!1,isMobile:!1,windowWidth:0,sidebarVisible:!0,tocVisible:!0,sidebarScrollPosition:0}},computed:{currentDoc(){return this.$route.params.doc||"summary"},currentPath(){return this.$route.path}},created(){this.flattenPages(),this.checkScreenSize()},mounted(){this.updatePageTitle();const e=localStorage.getItem("helpSidebarScrollPosition");e&&(this.sidebarScrollPosition=parseInt(e,10)),this.$nextTick((()=>{const e=this.$refs.mainContent;e&&e.addEventListener("scroll",this.handleContentScroll);const t=this.$refs.sidebar;t&&t.addEventListener("scroll",this.handleSidebarScroll),this.restoreSidebarScrollPosition()})),window.addEventListener("resize",this.handleResize),this.checkScreenSize()},beforeDestroy(){const e=this.$refs.mainContent;e&&e.removeEventListener("scroll",this.handleContentScroll);const t=this.$refs.sidebar;t&&t.removeEventListener("scroll",this.handleSidebarScroll),window.removeEventListener("resize",this.handleResize)},watch:{"$route.path"(){this.updatePageTitle(),this.$nextTick((()=>{setTimeout((()=>{this.restoreSidebarScrollPosition()}),50)}))}},methods:{slugify(e){let t=e.toLowerCase().replace(/\s+/g,"-").replace(/[^a-z0-9\-]+/g,"").replace(/\-\-+/g,"-").replace(/^-+/,"").replace(/-+$/,"");return t&&!/^[0-9]+$/.test(t)||(t="toc-section-"+t+"-"+Date.now()),t},flattenPages(){this.allPages=[],this.menu.forEach((e=>{e.items.forEach((t=>{this.allPages.push({name:t.name,path:t.path,category:e.title})}))}))},getPrevPage(){const e=this.allPages.findIndex((e=>e.path===this.currentPath));return e>0?this.allPages[e-1]:null},getNextPage(){const e=this.allPages.findIndex((e=>e.path===this.currentPath));return-1!==e&&e<this.allPages.length-1?this.allPages[e+1]:null},buildToc(){this.$nextTick((()=>{const e=this.$refs.mainContent,t=e.querySelector(".doc-content");if(!t)return;const i=t.querySelectorAll("h2, h3");this.toc=Array.from(i).map((e=>{const t=e.id,i=t||this.slugify(e.textContent);return t||(e.id=i),{id:i,text:e.textContent,level:parseInt(e.tagName.substring(1))}})),this.$nextTick(this.handleContentScroll)}))},updatePageTitle(){const e=this.$route.path;for(const t of this.menu)for(const i of t.items)if(i.path===e)return void(this.currentPageTitle=i.name)},scrollToAnchor(e){const t=30;this.isAnchorClicking=!0,this.activeTocId=e;const i=this.$refs.mainContent,s=i.querySelector(".doc-content"),a=document.getElementById(e);if(a){const e=a.offsetTop-s.offsetTop;i.scrollTop=e-t}setTimeout((()=>{this.isAnchorClicking=!1}),100)},handleContentScroll(){if(this.isAnchorClicking)return;const e=30,t=this.$refs.mainContent,i=t.querySelector(".doc-content");if(!i)return;const s=i.querySelectorAll("h2, h3");let a=null;const l=t.scrollTop;for(let n=s.length-1;n>=0;n--){const t=s[n];if(t.offsetTop-e<=l){a=t.id;break}}this.activeTocId=a},isMenuActive(e){return this.$route.path===e||("/help"===this.$route.path||"/help/"===this.$route.path)&&this.menu[0].items[0].path===e},checkScreenSize(){this.windowWidth=window.innerWidth;const e=this.isMobile;this.isMobile=this.windowWidth<=992,e&&!this.isMobile?(this.sidebarVisible=!0,this.tocVisible=!0):!e&&this.isMobile&&(this.sidebarVisible=!1,this.tocVisible=!1)},handleResize(){clearTimeout(this.resizeTimer),this.resizeTimer=setTimeout((()=>{this.checkScreenSize()}),250)},toggleSidebar(){this.sidebarVisible=!this.sidebarVisible,this.sidebarVisible&&this.tocVisible&&(this.tocVisible=!1)},toggleToc(){this.tocVisible=!this.tocVisible,this.tocVisible&&this.sidebarVisible&&(this.sidebarVisible=!1)},closeSidebar(){this.sidebarVisible=!1},closeToc(){this.tocVisible=!1},closeAllPanels(){this.sidebarVisible=!1,this.tocVisible=!1},onMenuItemClick(){this.isMobile&&(this.sidebarVisible=!1)},handleSidebarScroll(){const e=this.$refs.sidebar;e&&(this.sidebarScrollPosition=e.scrollTop,localStorage.setItem("helpSidebarScrollPosition",e.scrollTop.toString()))},restoreSidebarScrollPosition(){const e=this.$refs.sidebar;e&&this.sidebarScrollPosition>=0&&requestAnimationFrame((()=>{e.scrollTop=this.sidebarScrollPosition}))}}},b=u,f=(0,c.Z)(b,s,a,!1,null,"618f8321",null),v=f.exports},817:function(e,t,i){var s={"./Flux.1-kontext-dev.md":[6141,4594],"./ace-step.md":[4670,3168],"./cloud-storage.md":[412,1912],"./code-former.md":[895,5061],"./cosy-voice.md":[7788,6202],"./daily-hot.md":[8723,9198],"./deploy-serverless.md":[7838,2719],"./docker-tutorial.md":[354,3469],"./flux-dev.md":[328,4956],"./frame-pack.md":[1493,4675],"./funasr.md":[7631,1447],"./gpu-selection.md":[2696,5838],"./health-check.md":[1063,2261],"./hivision-photos.md":[2478,9802],"./hunyuan-portrait.md":[3062,370],"./jupyter-lab.md":[2395,166],"./k8s-yaml-import.md":[5305,7231],"./mineru.md":[4633,2576],"./minicpm4.md":[5936,2091],"./mirror.md":[1628,371],"./ollama-qwen-webui.md":[9961,2543],"./ollama-qwen.md":[6585,8991],"./personal-certification.md":[2733,3959],"./privacy-policy.md":[3751,8295],"./quick-start.md":[1273,8265],"./qustion.md":[4226,7083],"./register-login.md":[4493,32],"./stable-diffusion1.5.md":[7507,6193],"./stable-diffusion2.1.md":[1362,8449],"./stable-diffusion3.5-large.md":[9045,9240],"./summary.md":[1782,762],"./user-agreement.md":[7553,8303],"./whisper.md":[3797,5042]};function a(e){if(!i.o(s,e))return Promise.resolve().then((function(){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}));var t=s[e],a=t[0];return i.e(t[1]).then((function(){return i(a)}))}a.keys=function(){return Object.keys(s)},a.id=817,e.exports=a}}]);
//# sourceMappingURL=6742.c4e51fc8.js.map