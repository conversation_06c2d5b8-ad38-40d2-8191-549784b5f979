{"version": 3, "file": "js/1910.de1689ab.js", "mappings": "kJAAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,YAAYC,GAAG,CAAC,WAAa,SAASC,GAAQ,OAAON,EAAIO,UAAU,SAAS,EAAE,WAAa,SAASD,GAAQ,OAAON,EAAIQ,UAAU,SAAS,IAAI,CAACN,EAAG,IAAI,CAACE,YAAY,wBAAwB,CAACF,EAAG,MAAM,CAACO,MAAM,CAAC,QAAU,gBAAgB,MAAQ,KAAK,OAAS,OAAO,CAACP,EAAG,OAAO,CAACO,MAAM,CAAC,EAAI,sfAAsf,KAAO,aAAaP,EAAG,OAAO,CAACO,MAAM,CAAC,EAAI,6MAA6M,KAAO,aAAaP,EAAG,OAAO,CAACO,MAAM,CAAC,EAAI,w3BAAw3B,KAAO,aAAaP,EAAG,OAAO,CAACO,MAAM,CAAC,EAAI,wEAAwE,KAAO,aAAaP,EAAG,OAAO,CAACO,MAAM,CAAC,EAAI,wEAAwE,KAAO,iBAAiBP,EAAG,MAAM,CAACQ,WAAW,CAAC,CAACC,KAAK,OAAOC,QAAQ,SAASC,MAA2B,WAApBb,EAAIc,YAA0BC,WAAW,6BAA6BX,YAAY,gCAAgC,CAACF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,KAAK,CAACF,EAAIgB,GAAG,cAAcd,EAAG,MAAM,CAACE,YAAY,WAAW,CAACF,EAAG,MAAM,CAACO,MAAM,CAAC,IAAMT,EAAIiB,aAAa,IAAM,qBAAqBf,EAAG,MAAM,CAACE,YAAY,YAAYC,GAAG,CAAC,WAAa,SAASC,GAAQ,OAAON,EAAIO,UAAU,UAAU,EAAE,WAAa,SAASD,GAAQ,OAAON,EAAIQ,UAAU,UAAU,IAAI,CAACN,EAAG,IAAI,CAACE,YAAY,uBAAuB,CAACF,EAAG,MAAM,CAACO,MAAM,CAAC,QAAU,gBAAgB,MAAQ,KAAK,OAAS,OAAO,CAACP,EAAG,OAAO,CAACO,MAAM,CAAC,EAAI,mfAAmf,KAAO,iBAAiBP,EAAG,MAAM,CAACQ,WAAW,CAAC,CAACC,KAAK,OAAOC,QAAQ,SAASC,MAA2B,YAApBb,EAAIc,YAA2BC,WAAW,8BAA8BX,YAAY,iCAAiC,CAACF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,KAAK,CAACF,EAAIgB,GAAG,eAAed,EAAG,IAAI,CAACE,YAAY,gBAAgB,CAACJ,EAAIgB,GAAG,iBAAiBd,EAAG,IAAI,CAACF,EAAIgB,GAAG,iBAAiBd,EAAG,MAAM,CAACE,YAAY,WAAW,CAACF,EAAG,MAAM,CAACO,MAAM,CAAC,IAAMT,EAAIkB,cAAc,IAAM,qBAAqBhB,EAAG,MAAM,CAACE,YAAY,YAAYC,GAAG,CAAC,MAAQL,EAAImB,kBAAkB,WAAanB,EAAIoB,YAAY,WAAapB,EAAIqB,cAAc,CAACnB,EAAG,IAAI,CAACE,YAAY,0BAA0B,CAACF,EAAG,MAAM,CAACO,MAAM,CAAC,QAAU,gBAAgB,MAAQ,KAAK,OAAS,OAAO,CAACP,EAAG,OAAO,CAACO,MAAM,CAAC,EAAI,qKAAqK,KAAO,aAAaP,EAAG,OAAO,CAACO,MAAM,CAAC,EAAI,kxBAAkxB,KAAO,iBAAiBP,EAAG,MAAM,CAACQ,WAAW,CAAC,CAACC,KAAK,OAAOC,QAAQ,SAASC,MAAOb,EAAIsB,oBAAqBP,WAAW,wBAAwBX,YAAY,WAAW,CAACJ,EAAIgB,GAAG,mBAAoBhB,EAAIuB,UAAWrB,EAAG,MAAM,CAACE,YAAY,gBAAgBC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAGA,EAAOkB,SAAWlB,EAAOmB,cAAqB,KAAYzB,EAAI0B,mBAAmBC,MAAM,KAAMC,UAAU,IAAI,CAAC1B,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,KAAK,CAACF,EAAIgB,GAAG,WAAWd,EAAG,OAAO,CAACE,YAAY,YAAYC,GAAG,CAAC,MAAQL,EAAI0B,qBAAqB,CAAC1B,EAAIgB,GAAG,SAASd,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAACF,EAAG,IAAI,CAACE,YAAY,yBAAyB,CAACF,EAAG,MAAM,CAACO,MAAM,CAAC,QAAU,gBAAgB,MAAQ,KAAK,OAAS,OAAO,CAACP,EAAG,OAAO,CAACO,MAAM,CAAC,EAAI,2PAA2P,KAAO,iBAAiBT,EAAIgB,GAAG,+BAA+Bd,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,QAAQ,CAACE,YAAY,YAAY,CAACJ,EAAIgB,GAAG,WAAWd,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,SAAS,CAACQ,WAAW,CAAC,CAACC,KAAK,QAAQC,QAAQ,UAAUC,MAAOb,EAAI6B,SAASC,KAAMf,WAAW,kBAAkBN,MAAM,CAAC,SAAW,IAAIJ,GAAG,CAAC,OAAS,SAASC,GAAQ,IAAIyB,EAAgBC,MAAMC,UAAUC,OAAOC,KAAK7B,EAAOkB,OAAOY,SAAQ,SAASC,GAAG,OAAOA,EAAEC,QAAQ,IAAGC,KAAI,SAASF,GAAG,IAAIG,EAAM,WAAYH,EAAIA,EAAEI,OAASJ,EAAExB,MAAM,OAAO2B,CAAG,IAAIxC,EAAI0C,KAAK1C,EAAI6B,SAAU,OAAQvB,EAAOkB,OAAOmB,SAAWZ,EAAgBA,EAAc,GAAG,IAAI,CAAC7B,EAAG,SAAS,CAACO,MAAM,CAAC,MAAQ,KAAK,CAACT,EAAIgB,GAAG,SAASd,EAAG,SAAS,CAACO,MAAM,CAAC,MAAQ,SAAS,CAACT,EAAIgB,GAAG,UAAUd,EAAG,SAAS,CAACO,MAAM,CAAC,MAAQ,SAAS,CAACT,EAAIgB,GAAG,UAAUd,EAAG,SAAS,CAACO,MAAM,CAAC,MAAQ,SAAS,CAACT,EAAIgB,GAAG,UAAUd,EAAG,SAAS,CAACO,MAAM,CAAC,MAAQ,SAAS,CAACT,EAAIgB,GAAG,UAAUd,EAAG,SAAS,CAACO,MAAM,CAAC,MAAQ,OAAO,CAACT,EAAIgB,GAAG,aAAchB,EAAI6B,SAASC,MAAQ9B,EAAI4C,WAAY1C,EAAG,IAAI,CAACE,YAAY,cAAc,CAACJ,EAAIgB,GAAG,aAAahB,EAAI6C,OAAO3C,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,QAAQ,CAACE,YAAY,YAAY,CAACJ,EAAIgB,GAAG,WAAWd,EAAG,WAAW,CAACQ,WAAW,CAAC,CAACC,KAAK,QAAQC,QAAQ,UAAUC,MAAOb,EAAI6B,SAASiB,YAAa/B,WAAW,yBAAyBN,MAAM,CAAC,YAAc,MAAM,SAAW,IAAIsC,SAAS,CAAC,MAAS/C,EAAI6B,SAASiB,aAAczC,GAAG,CAAC,MAAQ,SAASC,GAAWA,EAAOkB,OAAOwB,WAAiBhD,EAAI0C,KAAK1C,EAAI6B,SAAU,cAAevB,EAAOkB,OAAOX,MAAM,MAAOb,EAAI6B,SAASiB,aAAe9C,EAAI4C,WAAY1C,EAAG,IAAI,CAACE,YAAY,cAAc,CAACJ,EAAIgB,GAAG,aAAahB,EAAI6C,OAAO3C,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,QAAQ,CAACE,YAAY,aAAa,CAACJ,EAAIgB,GAAG,WAAWd,EAAG,MAAM,CAACE,YAAY,iBAAiBC,GAAG,CAAC,MAAQL,EAAIiD,kBAAkB,SAAW,SAAS3C,GAAQA,EAAO4C,gBAAiB,EAAE,KAAO,SAAS5C,GAAgC,OAAxBA,EAAO4C,iBAAwBlD,EAAImD,WAAWxB,MAAM,KAAMC,UAAU,IAAI,CAAC1B,EAAG,QAAQ,CAACkD,IAAI,YAAYC,YAAY,CAAC,QAAU,QAAQ5C,MAAM,CAAC,KAAO,OAAO,OAAS,WAAWJ,GAAG,CAAC,OAASL,EAAIsD,gBAAkBtD,EAAI6B,SAAS0B,MAAuoCrD,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgBK,MAAM,CAAC,IAAMT,EAAI6B,SAAS2B,aAAa,IAAM,YAAYtD,EAAG,MAAM,CAACE,YAAY,eAAeC,GAAG,CAAC,MAAQ,SAASC,GAAiC,OAAzBA,EAAOmD,kBAAyBzD,EAAI0D,YAAY/B,MAAM,KAAMC,UAAU,IAAI,CAAC5B,EAAIgB,GAAG,SAA/5Cd,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACF,EAAG,IAAI,CAACE,YAAY,wBAAwB,CAACF,EAAG,MAAM,CAACO,MAAM,CAAC,QAAU,gBAAgB,MAAQ,KAAK,OAAS,OAAO,CAACP,EAAG,OAAO,CAACO,MAAM,CAAC,EAAI,oKAAoK,KAAO,aAAaP,EAAG,OAAO,CAACO,MAAM,CAAC,EAAI,8rBAA8rB,KAAO,iBAAiBP,EAAG,IAAI,CAACF,EAAIgB,GAAG,0BAAkUd,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,SAAS,CAACE,YAAY,iBAAiBC,GAAG,CAAC,MAAQL,EAAI0B,qBAAqB,CAAC1B,EAAIgB,GAAG,QAAQd,EAAG,SAAS,CAACE,YAAY,iBAAiBC,GAAG,CAAC,MAAQL,EAAI2D,gBAAgB,CAAC3D,EAAIgB,GAAG,cAAchB,EAAI6C,KAAM7C,EAAI4D,iBAAkB1D,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAACF,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACF,EAAG,MAAM,CAACO,MAAM,CAAC,QAAU,gBAAgB,MAAQ,KAAK,OAAS,OAAO,CAACP,EAAG,OAAO,CAACO,MAAM,CAAC,EAAI,sRAAsR,KAAO,iBAAiBP,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACJ,EAAIgB,GAAG,UAAUd,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACJ,EAAIgB,GAAG,oBAAoBd,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,SAAS,CAACE,YAAY,kBAAkBC,GAAG,CAAC,MAAQL,EAAI6D,oBAAoB,CAAC7D,EAAIgB,GAAG,cAAchB,EAAI6C,MAC5tV,EACIiB,EAAkB,GCuKtB,GACAnD,KAAA,QACAoD,OACA,OACAjD,YAAA,KACAS,WAAA,EACAqB,YAAA,EACAtB,qBAAA,EACAsC,kBAAA,EACA3C,aAAA+C,EAAA,IACA9C,cAAA8C,EAAA,IACAnC,SAAA,CACAC,KAAA,GACAgB,YAAA,GACAmB,WAAA,GACAV,MAAA,KACAC,aAAA,MAGA,EACAU,QAAA,CACA3D,UAAAuB,GACA,KAAAhB,YAAAgB,CACA,EACAtB,UAAAsB,GACA,KAAAhB,cAAAgB,IACA,KAAAhB,YAAA,KAEA,EACAM,cACA,KAAAE,qBAAA,CACA,EACAD,cACA,KAAAC,qBAAA,CACA,EACAH,oBACA,KAAAI,WAAA,EACA,KAAAqB,YAAA,CACA,EACAlB,qBACA,KAAAH,WAAA,EAEA,KAAAM,SAAA,CACAC,KAAA,GACAgB,YAAA,GACAmB,WAAA,GACAV,MAAA,KACAC,aAAA,MAEA,KAAAZ,YAAA,CACA,EACAK,oBACA,KAAAkB,MAAAC,UAAAC,OACA,EACAf,aAAAgB,GACA,MAAAC,EAAAD,EAAA9C,OAAAgD,MAAA,GACAD,GACA,KAAAE,aAAAF,EAEA,EACApB,WAAAmB,GACA,MAAAC,EAAAD,EAAAI,aAAAF,MAAA,GACAD,GAAAA,EAAAzC,KAAA6C,MAAA,YACA,KAAAF,aAAAF,EAEA,EACAE,aAAAF,GACA,GAAAA,GAAAA,EAAAzC,KAAA6C,MAAA,YACA,KAAA9C,SAAA0B,MAAAgB,EAGA,MAAAK,EAAA,IAAAC,WACAD,EAAAE,OAAAC,IACA,KAAAlD,SAAA2B,aAAAuB,EAAAvD,OAAAwD,MAAA,EAEAJ,EAAAK,cAAAV,EACA,CACA,EACAb,cACA,KAAA7B,SAAA0B,MAAA,KACA,KAAA1B,SAAA2B,aAAA,IACA,EACAG,gBAEA,KAAAf,YAAA,EACA,KAAAf,SAAAC,MAAA,KAAAD,SAAAiB,aAKA,KAAAoC,gBACA,EACAA,iBAIA,QAAArD,SAAA0B,MAAA,CACA,MAAA4B,EAAA,IAAAC,SACAD,EAAAE,OAAA,YAAAxD,SAAAC,MACAqD,EAAAE,OAAA,mBAAAxD,SAAAiB,aACAqC,EAAAE,OAAA,kBAAAxD,SAAAoC,YACAkB,EAAAE,OAAA,aAAAxD,SAAA0B,MAIA,CAGA,KAAAhC,WAAA,EACA,KAAAqC,kBAAA,EAGA,KAAA/B,SAAA,CACAC,KAAA,GACAgB,YAAA,GACAmB,WAAA,GACAV,MAAA,KACAC,aAAA,KAEA,EACAK,oBACA,KAAAD,kBAAA,CACA,ICnSuQ,I,UCQnQ0B,GAAY,OACd,EACAvF,EACA+D,GACA,EACA,KACA,WACA,MAIF,EAAewB,EAAiB,O,uDCnBhC,IAAIvF,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACA,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,oBAAoBC,GAAG,CAAC,WAAaL,EAAIuF,cAAc,WAAavF,EAAIwF,iBAAiB,CAACtF,EAAG,mBAAmB,CAACE,YAAY,mBAAmBK,MAAM,CAAC,KAAO,QAAQ,IAAM,QAAQT,EAAIyF,GAAIzF,EAAI0F,WAAW,SAASC,EAASC,GAAO,OAAO1F,EAAG,MAAM,CAACQ,WAAW,CAAC,CAACC,KAAK,OAAOC,QAAQ,SAASC,MAAOb,EAAI6F,uBAAyBD,EAAO7E,WAAW,mCAAmC+E,IAAIH,EAASvF,YAAY,gBAAgBC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAON,EAAI+F,qBAAqBJ,EAAS,EAAE,WAAa,SAASrF,GAAQ,OAAON,EAAIgG,MAAMJ,EAAM,IAAI,CAAC5F,EAAIgB,GAAG,IAAIhB,EAAIiG,GAAGN,GAAU,MAAM,IAAG,IAAI,GAAGzF,EAAG,MAAM,CAACE,YAAY,YAAY8F,MAAM,CAAE,mBAAoBlG,EAAImG,UAAW9F,GAAG,CAAC,MAAQL,EAAIoG,aAAa,CAAClG,EAAG,IAAI,CAACE,YAAY,uBAAuBF,EAAG,MAAM,CAACQ,WAAW,CAAC,CAACC,KAAK,OAAOC,QAAQ,SAASC,MAAOb,EAAImG,SAAUpF,WAAW,aAAaX,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIqG,GAAG,GAAGnG,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,IAAI,CAACE,YAAY,eAAeC,GAAG,CAAC,MAAQL,EAAIoG,kBAAkBlG,EAAG,MAAM,CAACkD,IAAI,oBAAoBhD,YAAY,iBAAiB,CAACJ,EAAIyF,GAAIzF,EAAIsG,UAAU,SAASC,EAAQX,GAAO,OAAO1F,EAAG,MAAM,CAAC4F,IAAIF,EAAMM,MAAM,CAAC,UAAWK,EAAQzE,OAAO,CAAmB,QAAjByE,EAAQzE,KAAgB5B,EAAG,MAAM,CAACE,YAAY,UAAU,CAACF,EAAG,IAAI,CAACE,YAAY,mBAAmBJ,EAAI6C,KAAK3C,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe2C,SAAS,CAAC,UAAY/C,EAAIiG,GAAGjG,EAAIwG,cAAcD,EAAQE,UAAUvG,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIgB,GAAGhB,EAAIiG,GAAGjG,EAAI0G,WAAWH,EAAQI,aAAa,IAAI3G,EAAI4G,QAAS1G,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,MAAM,CAACE,YAAY,QAAQF,EAAG,MAAM,CAACE,YAAY,QAAQF,EAAG,MAAM,CAACE,YAAY,UAAUJ,EAAI6C,MAAM,GAAG3C,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,QAAQ,CAACQ,WAAW,CAAC,CAACC,KAAK,QAAQC,QAAQ,UAAUC,MAAOb,EAAI6G,UAAW9F,WAAW,cAAcN,MAAM,CAAC,KAAO,OAAO,YAAc,aAAa,SAAWT,EAAI4G,SAAS7D,SAAS,CAAC,MAAS/C,EAAI6G,WAAYxG,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAIA,EAAOwB,KAAKgF,QAAQ,QAAQ9G,EAAI+G,GAAGzG,EAAO0G,QAAQ,QAAQ,GAAG1G,EAAOwF,IAAI,SAAgB,KAAY9F,EAAIiH,YAAYtF,MAAM,KAAMC,UAAU,EAAE,MAAQ,SAAStB,GAAWA,EAAOkB,OAAOwB,YAAiBhD,EAAI6G,UAAUvG,EAAOkB,OAAOX,MAAK,KAAKX,EAAG,SAAS,CAACO,MAAM,CAAC,SAAWT,EAAI4G,UAAY5G,EAAI6G,UAAUK,QAAQ7G,GAAG,CAAC,MAAQL,EAAIiH,cAAc,CAAC/G,EAAG,IAAI,CAACE,YAAY,8BAC36E,EACI0D,EAAkB,CAAC,WAAY,IAAI9D,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBF,EAAG,OAAO,CAACF,EAAIgB,GAAG,WACnK,GC6EA,G,QAAA,CACAL,KAAA,SAEAoD,OACA,OACAoC,UAAA,EACAU,UAAA,GACAP,SAAA,CACA,CACAxE,KAAA,MACA2E,KAAA,uBACAE,KAAA,IAAAQ,OAGAP,SAAA,EACAQ,gBAAA,GACA1B,UAAA,CACA,aACA,YACA,aAEAG,qBAAA,EACAwB,cAAA,KACAC,iBAAA,IACAC,UAAA,EAEA,EACAC,gBACA,KAAAC,eACA,EACAC,UAGA,GAFA,KAAAC,iBAEAC,SAAAC,eAAA,iBACA,MAAAC,EAAAF,SAAAG,cAAA,QACAD,EAAAE,GAAA,eACAF,EAAAG,IAAA,aACAH,EAAAI,KAAA,6EACAN,SAAAO,KAAAC,YAAAN,EACA,CACA,EAEA5D,QAAA,CACA8B,MAAAJ,GACA,KAAAC,qBAAAD,EACA,KAAAL,eACA,EACAoC,gBACA,IAAAU,EAAA,KACA,KAAAZ,gBACA,KAAAJ,cAAAiB,aAAA,KACAD,EAAAd,WACA,KAAA1B,sBACA,KAAAA,qBAAA,QAAAH,UAAA6C,OACAC,QAAAC,IAAA,UAAA5C,sBACA2C,QAAAC,IAAA,WAAAJ,EAAAd,UACA,GACA,KAAAD,iBACA,EACA/B,gBACA,KAAAgC,UAAA,CACA,EACA/B,iBACA,KAAA+B,UAAA,CACA,EACAE,gBACA,KAAAJ,gBACAqB,cAAA,KAAArB,eACA,KAAAA,cAAA,KAEA,EAEAtB,qBAAAJ,GACA,KAAAkB,UAAAlB,EACA,KAAAsB,aACA,EACAb,aACA,KAAAD,UAAA,KAAAA,SAEA,KAAAA,UACA,KAAAwC,WAAA,KACA,KAAAC,gBAAA,GAGA,EAEA,oBACA,SAAA/B,UAAAK,QAAA,KAAAN,QAAA,OAGA,KAAAN,SAAAuC,KAAA,CACA/G,KAAA,OACA2E,KAAA,KAAAI,UACAF,KAAA,IAAAQ,OAGA,MAAA2B,EAAA,KAAAjC,UACA,KAAAA,UAAA,GACA,KAAAD,SAAA,EAEA,KAAAQ,gBAAAyB,KAAA,CACAE,KAAA,OACAC,QAAAF,IAIA,MAAAG,EAAA,CACAC,MAAA,eACA5C,SAAA,EAAAyC,KAAA,SAAAC,QAAA,+HAAA5B,iBACA+B,QAAA,EACA/G,QAAA,CACAgH,iBAAA,IACAC,kBAAA,IAEAC,KAAA,QAIA,KAAAX,WAAA,KACA,KAAAC,gBAAA,IAGA,IAGA,MAAAW,QAAAC,MAAA,kDACAC,OAAA,OACAC,QAAA,CACAC,cAAA,6DACA,mCAEAC,KAAAC,KAAAC,UAAAb,KAIArE,EAAA2E,EAAAK,KAAAG,YACAC,EAAA,IAAAC,YACAC,EAAA,KAAA5D,SAAAuC,KAAA,CACA/G,KAAA,MACA2E,KAAA,GACAE,KAAA,IAAAQ,OACA,EACA,SACA,WAAAgD,EAAA,MAAAtJ,SAAA+D,EAAAwF,OACA,GAAAD,EAAA,MAGA,MAAAE,EAAAL,EAAAM,OAAAzJ,GACA0J,EAAAF,EAAAG,MAAA,MAAAtI,QAAAuI,GAAAA,EAAAvD,SAEA,UAAAuD,KAAAF,EACA,IACA,MAAAG,EAAAD,EAAAE,MAAA,GAAAzD,OACA,QAAAwD,GAAA,WAAAA,EAAA,SAEA,IAAA3G,EAAA8F,KAAAe,MAAAF,GACA,GAAA3G,EAAA8G,QAAA,CACA,SAAA9G,EAAA8G,QAAA,GAAAC,MAAAC,kBACA,SAEA,WAAAhH,EAAA8G,QAAA,GAAAC,MAAA9B,QACA,SAEA,KAAA1C,SAAA4D,GAAAzD,MAAA1C,EAAA8G,QAAA,GAAAC,MAAA9B,OACA,CACA,OAAAjE,GACA,CAEA,CACA,KAAAqC,gBAAAyB,KAAA,CACAE,KAAA,YACAC,QAAA,KAAA1C,SAAA4D,GAAAzD,MASA,OAAAuE,GAGA,KAAA1E,SAAAuC,KAAA,CACA/G,KAAA,MACA2E,KAAA,qBACAE,KAAA,IAAAQ,MAEA,SACA,KAAAP,SAAA,EAGA,KAAA+B,WAAA,KACA,KAAAC,gBAAA,GAEA,CACA,EAGA,kBAAArC,GAeA,aAbA,IAAA0E,SAAAC,GAAAC,WAAAD,EAAA,OAaA,YAAA3E,0BACA,EAEAqC,iBACA,MAAAwC,EAAA,KAAAjH,MAAAkH,kBACAD,EAAAE,UAAAF,EAAAG,YACA,EAEA7E,WAAA8E,GACA,WAAArE,KAAAqE,GAAAC,mBAAA,IAAAC,KAAA,UAAAC,OAAA,WACA,EAEAnF,cAAAC,GAEA,OAAAA,EACAmF,QAAA,cACAA,QAAA,6DACA,KCvTwQ,I,UCQpQtG,GAAY,OACd,EACAvF,EACA+D,GACA,EACA,KACA,WACA,MAIF,EAAewB,EAAiB,O", "sources": ["webpack://portal-ui/./src/components/common/mider/Mider.vue", "webpack://portal-ui/src/components/common/mider/Mider.vue", "webpack://portal-ui/./src/components/common/mider/Mider.vue?6cd8", "webpack://portal-ui/./src/components/common/mider/Mider.vue?9943", "webpack://portal-ui/./src/components/common/mider/chatAi.vue", "webpack://portal-ui/src/components/common/mider/chatAi.vue", "webpack://portal-ui/./src/components/common/mider/chatAi.vue?09db", "webpack://portal-ui/./src/components/common/mider/chatAi.vue?3a6e"], "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"mider-container\"},[_c('div',{staticClass:\"mider-sidebar\"},[_c('div',{staticClass:\"icon-wrapper\"},[_c('div',{staticClass:\"icon-item\",on:{\"mouseenter\":function($event){return _vm.showPopup('wechat')},\"mouseleave\":function($event){return _vm.hidePopup('wechat')}}},[_c('i',{staticClass:\"iconfont icon-wechat\"},[_c('svg',{attrs:{\"viewBox\":\"0 0 1024 1024\",\"width\":\"24\",\"height\":\"24\"}},[_c('path',{attrs:{\"d\":\"M690.1 377.4c5.9 0 11.8 0.2 17.6 0.5-24.4-128.7-158.3-227.1-319.9-227.1C209 150.8 64 271.4 64 420.2c0 81.1 43.6 154.2 111.9 203.6 5.5 3.9 9.1 10.3 9.1 17.6 0 2.4-0.5 4.6-1.1 6.9-5.5 20.3-14.2 52.8-14.6 54.3-0.7 2.6-1.7 5.2-1.7 7.9 0 5.9 4.8 10.8 10.8 10.8 2.3 0 4.2-0.9 6.2-2l70.9-40.9c5-2.9 10.5-4.6 16.3-4.6 3 0 5.9 0.5 8.7 1.4 35.9 10.5 74.6 16.2 114.2 16.2 9.8 0 19.5-0.3 29-1C624.9 597 702.1 497.6 702.1 378.1c0-0.2-0.1-0.4-0.1-0.7 0 0-0.1-0.3-0.1-0.5-0.8 0-1.8-0.1-2.8-0.1-3.1 0-6.2 0.2-9 0.6z\",\"fill\":\"#82c91e\"}}),_c('path',{attrs:{\"d\":\"M380.5 503.3c-27.9 0-50.7-22.8-50.7-50.7s22.8-50.7 50.7-50.7 50.7 22.8 50.7 50.7-22.8 50.7-50.7 50.7zM534.3 503.3c-27.9 0-50.7-22.8-50.7-50.7s22.8-50.7 50.7-50.7 50.7 22.8 50.7 50.7-22.8 50.7-50.7 50.7z\",\"fill\":\"#82c91e\"}}),_c('path',{attrs:{\"d\":\"M683.5 673.6c82.1 0 151.2-56 171.5-131.8 4.1-15.2-9.3-29.5-24.9-26.6-3.5 0.6-7.1 1-10.7 1-34.4 0-62.4-28-62.4-62.4 0-1.6 0.1-3.3 0.2-4.9 0.8-19.3-24.5-28.7-35.7-13.5-39.1 53-102.7 87.5-174.5 87.5-12.3 0-24.4-1-36.1-3-5.2-0.9-10.5 0.8-14.1 4.5-4.6 4.6-16.7 17.2-16.7 17.2l-0.6 0.6c-5.4 5.4-8.7 12.3-9.5 19.8-0.9 8.5 1.9 17.1 7.7 23.6l1.3 1.3c6.3 7.5 15.6 11.7 25.6 11.7 7.1 0 14.1-2.3 19.7-6.5l10.6-7.8c6.2-4.5 14.8-3.3 19.3 2.8 4.3 5.8 3.4 14-2.1 18.8-14.4 12.6-32.9 19.7-51.9 19.7-4.3 0-8.7-0.3-13-1-21.3-3.5-40.2-14.4-54.3-31.3l-0.3-0.3c-12.9-15.7-19.4-34.9-18.3-54.5 0.8-14.5 6.4-28.5 16.1-39.7 1.2-1.4 2.5-2.6 3.8-3.9l31.5-31.5c1.9-1.9 3.8-3.9 5.4-6.1 5.8-7.7 8.9-17 8.9-26.8 0-11.3-4.2-22.1-11.8-30.4-12.4-13.5-31.5-15.2-48.5-11.7-91.3 18.9-160.7 100.1-160.8 196.9 0 109.5 89.1 198.6 198.6 198.6 36.7 0 71.9-9.9 102.8-28.5 5.7-3.4 12.8-3.3 18.5 0.2 36.7 22.7 79.1 35.3 123.2 35.3z\",\"fill\":\"#82c91e\"}}),_c('path',{attrs:{\"d\":\"M770.9 576.9m-50.7 0a50.7 50.7 0 1 0 101.4 0 50.7 50.7 0 1 0-101.4 0Z\",\"fill\":\"#82c91e\"}}),_c('path',{attrs:{\"d\":\"M602.4 576.9m-50.7 0a50.7 50.7 0 1 0 101.4 0 50.7 50.7 0 1 0-101.4 0Z\",\"fill\":\"#82c91e\"}})])]),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.activePopup === 'wechat'),expression:\"activePopup === 'wechat'\"}],staticClass:\"popup-container wechat-popup\"},[_c('div',{staticClass:\"popup-content\"},[_c('h3',[_vm._v(\"微信扫码咨询客服\")]),_c('div',{staticClass:\"qr-code\"},[_c('img',{attrs:{\"src\":_vm.wechatQRCode,\"alt\":\"微信客服二维码\"}})])])])]),_c('div',{staticClass:\"icon-item\",on:{\"mouseenter\":function($event){return _vm.showPopup('contact')},\"mouseleave\":function($event){return _vm.hidePopup('contact')}}},[_c('i',{staticClass:\"iconfont icon-phone\"},[_c('svg',{attrs:{\"viewBox\":\"0 0 1024 1024\",\"width\":\"24\",\"height\":\"24\"}},[_c('path',{attrs:{\"d\":\"M877.1 238.7L770.6 132.3c-13-13-30.4-20.3-48.8-20.3s-35.8 7.2-48.8 20.3L558.3 246.8c-13 13-20.3 30.5-20.3 48.9 0 18.5 7.2 35.8 20.3 48.9l89.6 89.7c-20.6 47.8-49.6 90.6-86.4 127.3-36.7 36.9-79.6 66-127.2 86.6l-89.6-89.7c-13-13-30.4-20.3-48.8-20.3-18.5 0-35.8 7.2-48.8 20.3L132.3 673c-13 13-20.3 30.5-20.3 48.9 0 18.5 7.2 35.8 20.3 48.9l106.4 106.4c22.2 22.2 52.8 34.9 84.2 34.9 6.5 0 12.8-0.5 19.2-1.6 132.4-21.8 263.8-92.3 369.9-198.3C818 606 888.4 474.6 910.4 342.1c6.3-37.6-6.3-76.3-33.3-103.4z\",\"fill\":\"#1677ff\"}})])]),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.activePopup === 'contact'),expression:\"activePopup === 'contact'\"}],staticClass:\"popup-container contact-popup\"},[_c('div',{staticClass:\"popup-content\"},[_c('h3',[_vm._v(\"商务合作请联系电话\")]),_c('p',{staticClass:\"phone-number\"},[_vm._v(\"13913283376\")]),_c('p',[_vm._v(\"使用问题请咨询微信客服\")]),_c('div',{staticClass:\"qr-code\"},[_c('img',{attrs:{\"src\":_vm.contactQRCode,\"alt\":\"联系电话二维码\"}})])])])]),_c('div',{staticClass:\"icon-item\",on:{\"click\":_vm.showFeedbackModal,\"mouseenter\":_vm.showTooltip,\"mouseleave\":_vm.hideTooltip}},[_c('i',{staticClass:\"iconfont icon-feedback\"},[_c('svg',{attrs:{\"viewBox\":\"0 0 1024 1024\",\"width\":\"24\",\"height\":\"24\"}},[_c('path',{attrs:{\"d\":\"M573 421c-23.1 0-41 17.9-41 40s17.9 40 41 40c21.1 0 39-17.9 39-40s-17.9-40-39-40zM293 421c-23.1 0-41 17.9-41 40s17.9 40 41 40c21.1 0 39-17.9 39-40s-17.9-40-39-40z\",\"fill\":\"#fa8c16\"}}),_c('path',{attrs:{\"d\":\"M894 345c-48.1-66-115.3-110.1-189-130v0.1c-17.1-19-36.4-36.5-58-52.1-163.7-119-393.5-82.7-513 81-96.3 133-92.2 311.9 6 439l0.8 132.6c0 3.2 0.5 6.4 1.5 9.4 5.3 16.9 23.3 26.2 40.1 20.9L309 806c33.4 11.9 68.4 18 104.7 18 72.2 0 143.1-21.4 205.3-61.8 57.4-37.3 104-89.9 131.8-150.1 23.9-52 35.2-106.8 33.6-160.9 28.8-29.9 50.4-64.7 63.6-103 17.8-52.7 13.1-108.7-14-155.1z m-68 106.7c-25.8 53.9-71.3 89.7-126.2 99.8-5.7 1-9.7 3.9-13 10.3-8.1 16.1-19.1 30.6-32.7 43.1-11.8 10.9-24.9 20.5-42.9 14.6-9.7-3.2-15.2-9.4-19.1-20.8-6.6-19.2-12.9-18.2-27.2-9.9-81.5 47.5-165.2 37.3-232.5-25.3-30.5-28.6-54.2-65.1-62.7-109-8.4-44 0.4-89.6 26.7-130.5 22.9-35.6 52.4-62 89.9-79.3 24.8-11.5 50.9-18.7 78.5-20.5 101.5-6.2 197.2 41.2 244.1 132.2 12.8 24.8 25.6 60.7 30.4 87.3 5.5 30.3 2.9 53.5-13.3 87z\",\"fill\":\"#fa8c16\"}})])]),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.showFeedbackTooltip),expression:\"showFeedbackTooltip\"}],staticClass:\"tooltip\"},[_vm._v(\" 反馈与建议 \")])])])]),(_vm.showModal)?_c('div',{staticClass:\"modal-overlay\",on:{\"click\":function($event){if($event.target !== $event.currentTarget)return null;return _vm.closeFeedbackModal.apply(null, arguments)}}},[_c('div',{staticClass:\"feedback-modal\"},[_c('div',{staticClass:\"modal-header\"},[_c('h3',[_vm._v(\"反馈与建议\")]),_c('span',{staticClass:\"close-btn\",on:{\"click\":_vm.closeFeedbackModal}},[_vm._v(\"×\")])]),_c('div',{staticClass:\"modal-body\"},[_c('div',{staticClass:\"alert alert-warning\"},[_c('i',{staticClass:\"iconfont icon-warning\"},[_c('svg',{attrs:{\"viewBox\":\"0 0 1024 1024\",\"width\":\"16\",\"height\":\"16\"}},[_c('path',{attrs:{\"d\":\"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296z m32 440c-26.5 0-48-21.5-48-48s21.5-48 48-48 48 21.5 48 48-21.5 48-48 48z\",\"fill\":\"#faad14\"}})])]),_vm._v(\" 您的反馈我们将认真对待，不断优化产品功能和体验 \")]),_c('div',{staticClass:\"form-group\"},[_c('label',{staticClass:\"required\"},[_vm._v(\"问题类型：\")]),_c('div',{staticClass:\"select-wrapper\"},[_c('select',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.feedback.type),expression:\"feedback.type\"}],attrs:{\"required\":\"\"},on:{\"change\":function($event){var $$selectedVal = Array.prototype.filter.call($event.target.options,function(o){return o.selected}).map(function(o){var val = \"_value\" in o ? o._value : o.value;return val}); _vm.$set(_vm.feedback, \"type\", $event.target.multiple ? $$selectedVal : $$selectedVal[0])}}},[_c('option',{attrs:{\"value\":\"\"}},[_vm._v(\"请选择\")]),_c('option',{attrs:{\"value\":\"功能建议\"}},[_vm._v(\"功能建议\")]),_c('option',{attrs:{\"value\":\"产品故障\"}},[_vm._v(\"产品故障\")]),_c('option',{attrs:{\"value\":\"体验不佳\"}},[_vm._v(\"体验不佳\")]),_c('option',{attrs:{\"value\":\"账户相关\"}},[_vm._v(\"账户相关\")]),_c('option',{attrs:{\"value\":\"其他\"}},[_vm._v(\"其他\")])])]),(!_vm.feedback.type && _vm.showErrors)?_c('p',{staticClass:\"error-text\"},[_vm._v(\"请选择问题类型\")]):_vm._e()]),_c('div',{staticClass:\"form-group\"},[_c('label',{staticClass:\"required\"},[_vm._v(\"问题描述：\")]),_c('textarea',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.feedback.description),expression:\"feedback.description\"}],attrs:{\"placeholder\":\"请输入\",\"required\":\"\"},domProps:{\"value\":(_vm.feedback.description)},on:{\"input\":function($event){if($event.target.composing)return;_vm.$set(_vm.feedback, \"description\", $event.target.value)}}}),(!_vm.feedback.description && _vm.showErrors)?_c('p',{staticClass:\"error-text\"},[_vm._v(\"请输入问题描述\")]):_vm._e()]),_c('div',{staticClass:\"form-group\"},[_c('label',{staticClass:\"required1\"},[_vm._v(\"问题截图：\")]),_c('div',{staticClass:\"image-uploader\",on:{\"click\":_vm.triggerFileUpload,\"dragover\":function($event){$event.preventDefault();},\"drop\":function($event){$event.preventDefault();return _vm.onFileDrop.apply(null, arguments)}}},[_c('input',{ref:\"fileInput\",staticStyle:{\"display\":\"none\"},attrs:{\"type\":\"file\",\"accept\":\"image/*\"},on:{\"change\":_vm.onFileChange}}),(!_vm.feedback.image)?_c('div',{staticClass:\"upload-placeholder\"},[_c('i',{staticClass:\"iconfont icon-upload\"},[_c('svg',{attrs:{\"viewBox\":\"0 0 1024 1024\",\"width\":\"28\",\"height\":\"28\"}},[_c('path',{attrs:{\"d\":\"M518.3 459c-3.2-4.1-9.4-4.1-12.6 0l-112 141.7c-4.1 5.2-0.4 12.9 6.3 12.9h73.9V856c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V613.7H624c6.7 0 10.4-7.7 6.3-12.9L518.3 459z\",\"fill\":\"#bfbfbf\"}}),_c('path',{attrs:{\"d\":\"M811.4 366.7C765.6 245.9 648.9 160 512.2 160S258.8 245.8 213 366.6C127.3 389.1 64 467.2 64 560c0 110.5 89.5 200 199.9 200H304c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8h-40.1c-33.7 0-65.4-13.4-89-37.7-23.5-24.2-36-56.8-34.9-90.6 0.9-26.4 9.9-51.2 26.2-72.1 16.7-21.3 40.1-36.8 66.1-43.7l37.9-9.9 13.9-36.6c8.6-22.8 20.6-44.1 35.7-63.4 14.9-19.2 32.6-35.9 52.4-49.9 41.1-28.9 89.5-44.2 140-44.2s98.9 15.3 140 44.2c19.9 14 37.5 30.8 52.4 49.9 15.1 19.3 27.1 40.7 35.7 63.4l13.8 36.5 37.8 10c26.1 6.9 49.6 22.5 66.3 43.8 16.4 21 25.4 45.9 26.3 72.3 1.1 33.9-11.4 66.5-34.9 90.7-23.6 24.4-55.3 37.7-89 37.7h-40c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h40.1C758.5 760 848 670.5 848 560c0-92.7-63.1-170.7-148.6-193.3z\",\"fill\":\"#bfbfbf\"}})])]),_c('p',[_vm._v(\"点击/拖拽至此处添加图片\")])]):_c('div',{staticClass:\"preview-container\"},[_c('img',{staticClass:\"image-preview\",attrs:{\"src\":_vm.feedback.imagePreview,\"alt\":\"问题截图预览\"}}),_c('div',{staticClass:\"remove-image\",on:{\"click\":function($event){$event.stopPropagation();return _vm.removeImage.apply(null, arguments)}}},[_vm._v(\"×\")])])])])]),_c('div',{staticClass:\"modal-footer\"},[_c('button',{staticClass:\"btn btn-cancel\",on:{\"click\":_vm.closeFeedbackModal}},[_vm._v(\"取消\")]),_c('button',{staticClass:\"btn btn-submit\",on:{\"click\":_vm.confirmSubmit}},[_vm._v(\"提交\")])])])]):_vm._e(),(_vm.showConfirmation)?_c('div',{staticClass:\"modal-overlay\"},[_c('div',{staticClass:\"confirmation-dialog\"},[_c('div',{staticClass:\"confirmation-icon\"},[_c('svg',{attrs:{\"viewBox\":\"0 0 1024 1024\",\"width\":\"32\",\"height\":\"32\"}},[_c('path',{attrs:{\"d\":\"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m193.5 301.7l-210.6 292a31.8 31.8 0 0 1-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z\",\"fill\":\"#52c41a\"}})])]),_c('div',{staticClass:\"confirmation-title\"},[_vm._v(\"提交成功\")]),_c('div',{staticClass:\"confirmation-message\"},[_vm._v(\"感谢您的反馈，我们会尽快处理\")]),_c('div',{staticClass:\"confirmation-actions\"},[_c('button',{staticClass:\"btn btn-primary\",on:{\"click\":_vm.closeConfirmation}},[_vm._v(\"确定\")])])])]):_vm._e()])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"mider-container\">\r\n    <!-- Floating sidebar with icons -->\r\n    <div class=\"mider-sidebar\">\r\n<!--      <div class=\"coupon-tag\">-->\r\n<!--        <span>领</span>-->\r\n<!--        <span>优</span>-->\r\n<!--        <span>惠</span>-->\r\n<!--        <span>券</span>-->\r\n<!--      </div>-->\r\n\r\n      <div class=\"icon-wrapper\">\r\n        <div class=\"icon-item\" @mouseenter=\"showPopup('wechat')\" @mouseleave=\"hidePopup('wechat')\">\r\n          <i class=\"iconfont icon-wechat\">\r\n            <svg viewBox=\"0 0 1024 1024\" width=\"24\" height=\"24\">\r\n              <path d=\"M690.1 377.4c5.9 0 11.8 0.2 17.6 0.5-24.4-128.7-158.3-227.1-319.9-227.1C209 150.8 64 271.4 64 420.2c0 81.1 43.6 154.2 111.9 203.6 5.5 3.9 9.1 10.3 9.1 17.6 0 2.4-0.5 4.6-1.1 6.9-5.5 20.3-14.2 52.8-14.6 54.3-0.7 2.6-1.7 5.2-1.7 7.9 0 5.9 4.8 10.8 10.8 10.8 2.3 0 4.2-0.9 6.2-2l70.9-40.9c5-2.9 10.5-4.6 16.3-4.6 3 0 5.9 0.5 8.7 1.4 35.9 10.5 74.6 16.2 114.2 16.2 9.8 0 19.5-0.3 29-1C624.9 597 702.1 497.6 702.1 378.1c0-0.2-0.1-0.4-0.1-0.7 0 0-0.1-0.3-0.1-0.5-0.8 0-1.8-0.1-2.8-0.1-3.1 0-6.2 0.2-9 0.6z\" fill=\"#82c91e\"></path>\r\n              <path d=\"M380.5 503.3c-27.9 0-50.7-22.8-50.7-50.7s22.8-50.7 50.7-50.7 50.7 22.8 50.7 50.7-22.8 50.7-50.7 50.7zM534.3 503.3c-27.9 0-50.7-22.8-50.7-50.7s22.8-50.7 50.7-50.7 50.7 22.8 50.7 50.7-22.8 50.7-50.7 50.7z\" fill=\"#82c91e\"></path>\r\n              <path d=\"M683.5 673.6c82.1 0 151.2-56 171.5-131.8 4.1-15.2-9.3-29.5-24.9-26.6-3.5 0.6-7.1 1-10.7 1-34.4 0-62.4-28-62.4-62.4 0-1.6 0.1-3.3 0.2-4.9 0.8-19.3-24.5-28.7-35.7-13.5-39.1 53-102.7 87.5-174.5 87.5-12.3 0-24.4-1-36.1-3-5.2-0.9-10.5 0.8-14.1 4.5-4.6 4.6-16.7 17.2-16.7 17.2l-0.6 0.6c-5.4 5.4-8.7 12.3-9.5 19.8-0.9 8.5 1.9 17.1 7.7 23.6l1.3 1.3c6.3 7.5 15.6 11.7 25.6 11.7 7.1 0 14.1-2.3 19.7-6.5l10.6-7.8c6.2-4.5 14.8-3.3 19.3 2.8 4.3 5.8 3.4 14-2.1 18.8-14.4 12.6-32.9 19.7-51.9 19.7-4.3 0-8.7-0.3-13-1-21.3-3.5-40.2-14.4-54.3-31.3l-0.3-0.3c-12.9-15.7-19.4-34.9-18.3-54.5 0.8-14.5 6.4-28.5 16.1-39.7 1.2-1.4 2.5-2.6 3.8-3.9l31.5-31.5c1.9-1.9 3.8-3.9 5.4-6.1 5.8-7.7 8.9-17 8.9-26.8 0-11.3-4.2-22.1-11.8-30.4-12.4-13.5-31.5-15.2-48.5-11.7-91.3 18.9-160.7 100.1-160.8 196.9 0 109.5 89.1 198.6 198.6 198.6 36.7 0 71.9-9.9 102.8-28.5 5.7-3.4 12.8-3.3 18.5 0.2 36.7 22.7 79.1 35.3 123.2 35.3z\" fill=\"#82c91e\"></path>\r\n              <path d=\"M770.9 576.9m-50.7 0a50.7 50.7 0 1 0 101.4 0 50.7 50.7 0 1 0-101.4 0Z\" fill=\"#82c91e\"></path>\r\n              <path d=\"M602.4 576.9m-50.7 0a50.7 50.7 0 1 0 101.4 0 50.7 50.7 0 1 0-101.4 0Z\" fill=\"#82c91e\"></path>\r\n            </svg>\r\n          </i>\r\n          <!-- WeChat QR code popup -->\r\n          <div class=\"popup-container wechat-popup\" v-show=\"activePopup === 'wechat'\">\r\n            <div class=\"popup-content\">\r\n              <h3>微信扫码咨询客服</h3>\r\n              <div class=\"qr-code\">\r\n                <img :src=\"wechatQRCode\" alt=\"微信客服二维码\">\r\n              </div>\r\n<!--              <div class=\"popup-footer\">-->\r\n<!--                <button class=\"btn-consult\">桌面版微信点击咨询客服</button>-->\r\n<!--              </div>-->\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"icon-item\" @mouseenter=\"showPopup('contact')\" @mouseleave=\"hidePopup('contact')\">\r\n          <i class=\"iconfont icon-phone\">\r\n            <svg viewBox=\"0 0 1024 1024\" width=\"24\" height=\"24\">\r\n              <path d=\"M877.1 238.7L770.6 132.3c-13-13-30.4-20.3-48.8-20.3s-35.8 7.2-48.8 20.3L558.3 246.8c-13 13-20.3 30.5-20.3 48.9 0 18.5 7.2 35.8 20.3 48.9l89.6 89.7c-20.6 47.8-49.6 90.6-86.4 127.3-36.7 36.9-79.6 66-127.2 86.6l-89.6-89.7c-13-13-30.4-20.3-48.8-20.3-18.5 0-35.8 7.2-48.8 20.3L132.3 673c-13 13-20.3 30.5-20.3 48.9 0 18.5 7.2 35.8 20.3 48.9l106.4 106.4c22.2 22.2 52.8 34.9 84.2 34.9 6.5 0 12.8-0.5 19.2-1.6 132.4-21.8 263.8-92.3 369.9-198.3C818 606 888.4 474.6 910.4 342.1c6.3-37.6-6.3-76.3-33.3-103.4z\" fill=\"#1677ff\"></path>\r\n            </svg>\r\n          </i>\r\n          <!-- Contact information popup -->\r\n          <div class=\"popup-container contact-popup\" v-show=\"activePopup === 'contact'\">\r\n            <div class=\"popup-content\">\r\n              <h3>商务合作请联系电话</h3>\r\n              <p class=\"phone-number\">13913283376</p>\r\n              <p>使用问题请咨询微信客服</p>\r\n              <div class=\"qr-code\">\r\n                <img :src=\"contactQRCode\" alt=\"联系电话二维码\">\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"icon-item\" @click=\"showFeedbackModal\" @mouseenter=\"showTooltip\" @mouseleave=\"hideTooltip\">\r\n          <i class=\"iconfont icon-feedback\">\r\n            <svg viewBox=\"0 0 1024 1024\" width=\"24\" height=\"24\">\r\n              <path d=\"M573 421c-23.1 0-41 17.9-41 40s17.9 40 41 40c21.1 0 39-17.9 39-40s-17.9-40-39-40zM293 421c-23.1 0-41 17.9-41 40s17.9 40 41 40c21.1 0 39-17.9 39-40s-17.9-40-39-40z\" fill=\"#fa8c16\"></path>\r\n              <path d=\"M894 345c-48.1-66-115.3-110.1-189-130v0.1c-17.1-19-36.4-36.5-58-52.1-163.7-119-393.5-82.7-513 81-96.3 133-92.2 311.9 6 439l0.8 132.6c0 3.2 0.5 6.4 1.5 9.4 5.3 16.9 23.3 26.2 40.1 20.9L309 806c33.4 11.9 68.4 18 104.7 18 72.2 0 143.1-21.4 205.3-61.8 57.4-37.3 104-89.9 131.8-150.1 23.9-52 35.2-106.8 33.6-160.9 28.8-29.9 50.4-64.7 63.6-103 17.8-52.7 13.1-108.7-14-155.1z m-68 106.7c-25.8 53.9-71.3 89.7-126.2 99.8-5.7 1-9.7 3.9-13 10.3-8.1 16.1-19.1 30.6-32.7 43.1-11.8 10.9-24.9 20.5-42.9 14.6-9.7-3.2-15.2-9.4-19.1-20.8-6.6-19.2-12.9-18.2-27.2-9.9-81.5 47.5-165.2 37.3-232.5-25.3-30.5-28.6-54.2-65.1-62.7-109-8.4-44 0.4-89.6 26.7-130.5 22.9-35.6 52.4-62 89.9-79.3 24.8-11.5 50.9-18.7 78.5-20.5 101.5-6.2 197.2 41.2 244.1 132.2 12.8 24.8 25.6 60.7 30.4 87.3 5.5 30.3 2.9 53.5-13.3 87z\" fill=\"#fa8c16\"></path>\r\n            </svg>\r\n          </i>\r\n          <!-- Tooltip for feedback icon -->\r\n          <div class=\"tooltip\" v-show=\"showFeedbackTooltip\">\r\n            反馈与建议\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n    </div>\r\n\r\n    <!-- Feedback modal - Reduced size -->\r\n    <div class=\"modal-overlay\" v-if=\"showModal\" @click.self=\"closeFeedbackModal\">\r\n      <div class=\"feedback-modal\">\r\n        <div class=\"modal-header\">\r\n          <h3>反馈与建议</h3>\r\n          <span class=\"close-btn\" @click=\"closeFeedbackModal\">×</span>\r\n        </div>\r\n\r\n        <div class=\"modal-body\">\r\n          <div class=\"alert alert-warning\">\r\n            <i class=\"iconfont icon-warning\">\r\n              <svg viewBox=\"0 0 1024 1024\" width=\"16\" height=\"16\">\r\n                <path d=\"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296z m32 440c-26.5 0-48-21.5-48-48s21.5-48 48-48 48 21.5 48 48-21.5 48-48 48z\" fill=\"#faad14\"></path>\r\n              </svg>\r\n            </i>\r\n            您的反馈我们将认真对待，不断优化产品功能和体验\r\n          </div>\r\n\r\n          <div class=\"form-group\">\r\n            <label class=\"required\">问题类型：</label>\r\n            <div class=\"select-wrapper\">\r\n              <select v-model=\"feedback.type\" required>\r\n                <option value=\"\">请选择</option>\r\n                <option value=\"功能建议\">功能建议</option>\r\n                <option value=\"产品故障\">产品故障</option>\r\n                <option value=\"体验不佳\">体验不佳</option>\r\n                <option value=\"账户相关\">账户相关</option>\r\n                <option value=\"其他\">其他</option>\r\n              </select>\r\n            </div>\r\n            <p class=\"error-text\" v-if=\"!feedback.type && showErrors\">请选择问题类型</p>\r\n          </div>\r\n\r\n          <div class=\"form-group\">\r\n            <label class=\"required\">问题描述：</label>\r\n            <textarea v-model=\"feedback.description\" placeholder=\"请输入\" required></textarea>\r\n            <p class=\"error-text\" v-if=\"!feedback.description && showErrors\">请输入问题描述</p>\r\n          </div>\r\n\r\n\r\n          <div class=\"form-group\">\r\n            <label class=\"required1\">问题截图：</label>\r\n            <div\r\n                class=\"image-uploader\"\r\n                @click=\"triggerFileUpload\"\r\n                @dragover.prevent\r\n                @drop.prevent=\"onFileDrop\"\r\n            >\r\n              <input\r\n                  type=\"file\"\r\n                  ref=\"fileInput\"\r\n                  accept=\"image/*\"\r\n                  @change=\"onFileChange\"\r\n                  style=\"display: none\"\r\n              >\r\n              <div v-if=\"!feedback.image\" class=\"upload-placeholder\">\r\n                <i class=\"iconfont icon-upload\">\r\n                  <svg viewBox=\"0 0 1024 1024\" width=\"28\" height=\"28\">\r\n                    <path d=\"M518.3 459c-3.2-4.1-9.4-4.1-12.6 0l-112 141.7c-4.1 5.2-0.4 12.9 6.3 12.9h73.9V856c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V613.7H624c6.7 0 10.4-7.7 6.3-12.9L518.3 459z\" fill=\"#bfbfbf\"></path>\r\n                    <path d=\"M811.4 366.7C765.6 245.9 648.9 160 512.2 160S258.8 245.8 213 366.6C127.3 389.1 64 467.2 64 560c0 110.5 89.5 200 199.9 200H304c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8h-40.1c-33.7 0-65.4-13.4-89-37.7-23.5-24.2-36-56.8-34.9-90.6 0.9-26.4 9.9-51.2 26.2-72.1 16.7-21.3 40.1-36.8 66.1-43.7l37.9-9.9 13.9-36.6c8.6-22.8 20.6-44.1 35.7-63.4 14.9-19.2 32.6-35.9 52.4-49.9 41.1-28.9 89.5-44.2 140-44.2s98.9 15.3 140 44.2c19.9 14 37.5 30.8 52.4 49.9 15.1 19.3 27.1 40.7 35.7 63.4l13.8 36.5 37.8 10c26.1 6.9 49.6 22.5 66.3 43.8 16.4 21 25.4 45.9 26.3 72.3 1.1 33.9-11.4 66.5-34.9 90.7-23.6 24.4-55.3 37.7-89 37.7h-40c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h40.1C758.5 760 848 670.5 848 560c0-92.7-63.1-170.7-148.6-193.3z\" fill=\"#bfbfbf\"></path>\r\n                  </svg>\r\n                </i>\r\n                <p>点击/拖拽至此处添加图片</p>\r\n              </div>\r\n              <div v-else class=\"preview-container\">\r\n                <img :src=\"feedback.imagePreview\" alt=\"问题截图预览\" class=\"image-preview\">\r\n                <div class=\"remove-image\" @click.stop=\"removeImage\">×</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"modal-footer\">\r\n          <button class=\"btn btn-cancel\" @click=\"closeFeedbackModal\">取消</button>\r\n          <button class=\"btn btn-submit\" @click=\"confirmSubmit\">提交</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Confirmation Dialog -->\r\n    <div class=\"modal-overlay\" v-if=\"showConfirmation\">\r\n      <div class=\"confirmation-dialog\">\r\n        <div class=\"confirmation-icon\">\r\n          <svg viewBox=\"0 0 1024 1024\" width=\"32\" height=\"32\">\r\n            <path d=\"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m193.5 301.7l-210.6 292a31.8 31.8 0 0 1-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z\" fill=\"#52c41a\"></path>\r\n          </svg>\r\n        </div>\r\n        <div class=\"confirmation-title\">提交成功</div>\r\n        <div class=\"confirmation-message\">感谢您的反馈，我们会尽快处理</div>\r\n        <div class=\"confirmation-actions\">\r\n          <button class=\"btn btn-primary\" @click=\"closeConfirmation\">确定</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'Mider',\r\n  data() {\r\n    return {\r\n      activePopup: null,\r\n      showModal: false,\r\n      showErrors: false,\r\n      showFeedbackTooltip: false,\r\n      showConfirmation: false,\r\n      wechatQRCode: require('@/assets/images/footer/wechat.jpg'), // 替换为实际路径\r\n      contactQRCode: require('@/assets/images/footer/wechat.jpg'), // 替换为实际路径\r\n      feedback: {\r\n        type: '',\r\n        description: '',\r\n        instanceId: '',\r\n        image: null,\r\n        imagePreview: null\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    showPopup(type) {\r\n      this.activePopup = type;\r\n    },\r\n    hidePopup(type) {\r\n      if (this.activePopup === type) {\r\n        this.activePopup = null;\r\n      }\r\n    },\r\n    showTooltip() {\r\n      this.showFeedbackTooltip = true;\r\n    },\r\n    hideTooltip() {\r\n      this.showFeedbackTooltip = false;\r\n    },\r\n    showFeedbackModal() {\r\n      this.showModal = true;\r\n      this.showErrors = false;\r\n    },\r\n    closeFeedbackModal() {\r\n      this.showModal = false;\r\n      // Reset form\r\n      this.feedback = {\r\n        type: '',\r\n        description: '',\r\n        instanceId: '',\r\n        image: null,\r\n        imagePreview: null\r\n      };\r\n      this.showErrors = false;\r\n    },\r\n    triggerFileUpload() {\r\n      this.$refs.fileInput.click();\r\n    },\r\n    onFileChange(event) {\r\n      const file = event.target.files[0];\r\n      if (file) {\r\n        this.processImage(file);\r\n      }\r\n    },\r\n    onFileDrop(event) {\r\n      const file = event.dataTransfer.files[0];\r\n      if (file && file.type.match('image.*')) {\r\n        this.processImage(file);\r\n      }\r\n    },\r\n    processImage(file) {\r\n      if (file && file.type.match('image.*')) {\r\n        this.feedback.image = file;\r\n\r\n        // Create preview\r\n        const reader = new FileReader();\r\n        reader.onload = (e) => {\r\n          this.feedback.imagePreview = e.target.result;\r\n        };\r\n        reader.readAsDataURL(file);\r\n      }\r\n    },\r\n    removeImage() {\r\n      this.feedback.image = null;\r\n      this.feedback.imagePreview = null;\r\n    },\r\n    confirmSubmit() {\r\n      // Validate form\r\n      this.showErrors = true;\r\n      if (!this.feedback.type || !this.feedback.description) {\r\n        return;\r\n      }\r\n\r\n      // Submit feedback\r\n      this.submitFeedback();\r\n    },\r\n    submitFeedback() {\r\n      // Here you would typically send the data to your backend\r\n\r\n      // Create FormData object if there's an image\r\n      if (this.feedback.image) {\r\n        const formData = new FormData();\r\n        formData.append('type', this.feedback.type);\r\n        formData.append('description', this.feedback.description);\r\n        formData.append('instanceId', this.feedback.instanceId);\r\n        formData.append('image', this.feedback.image);\r\n\r\n        // Send formData to your API\r\n        // this.$axios.post('/api/feedback', formData)\r\n      }\r\n\r\n      // Close feedback modal and show confirmation dialog\r\n      this.showModal = false;\r\n      this.showConfirmation = true;\r\n\r\n      // Reset form data\r\n      this.feedback = {\r\n        type: '',\r\n        description: '',\r\n        instanceId: '',\r\n        image: null,\r\n        imagePreview: null\r\n      };\r\n    },\r\n    closeConfirmation() {\r\n      this.showConfirmation = false;\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.mider-container {\r\n  position: relative;\r\n\r\n}\r\n\r\n/* Sidebar styles */\r\n.mider-sidebar {\r\n  /* 原有定位属性 */\r\n  position: fixed;\r\n  right: 0;\r\n  top: 85%;             /* 当前纵向定位 */\r\n  transform: translateY(-50%);\r\n  z-index: 1000;\r\n\r\n  /* 新增尺寸控制 */\r\n  width: 42px;         /* 固定宽度 */\r\n  height: 50vh;         /* 视口高度的50% */\r\n  max-width: 90%;       /* 防溢出保护 */\r\n  max-height: 80vh;     /* 高度上限 */\r\n  min-height: 200px;    /* 高度下限 */\r\n  /*box-sizing: 0; !* 包含内边距 *!*/\r\n\r\n  /* 布局优化 */\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n\r\n.coupon-tag {\r\n  background-color: #ff6b6b;\r\n  color: white;\r\n  padding: 8px 12px;\r\n  border-radius: 8px 0 0 8px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  font-size: 14px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.icon-wrapper {\r\n  display: flex;\r\n  flex-direction: column;\r\n  background-color: white;\r\n  border-radius: 8px 0 0 8px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.icon-item {\r\n  position: relative;\r\n  padding: 15px;\r\n  cursor: pointer;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.icon-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.icon-item i {\r\n  font-size: 24px;\r\n  color: #666;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.icon-item:hover i {\r\n  color: #1890ff;\r\n}\r\n\r\n.icon-item:hover i svg path {\r\n  fill: #1890ff;\r\n}\r\n\r\n/* Tooltip styles */\r\n.tooltip {\r\n  position: absolute;\r\n  left: -90px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  background-color: #fff;\r\n  color: #333;\r\n  padding: 6px 10px;\r\n  border-radius: 4px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\r\n  font-size: 12px;\r\n  white-space: nowrap;\r\n  z-index: 1000;\r\n}\r\n\r\n.tooltip:after {\r\n  content: '';\r\n  position: absolute;\r\n  right: -6px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  border-width: 6px 0 6px 6px;\r\n  border-style: solid;\r\n  border-color: transparent transparent transparent white;\r\n}\r\n\r\n/* Popup styles */\r\n.popup-container {\r\n  position: absolute;\r\n  right: 60px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  background-color: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n  width: 240px;\r\n  z-index: 1000;\r\n}\r\n\r\n.popup-container:after {\r\n  content: '';\r\n  position: absolute;\r\n  right: -10px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  border-width: 10px 0 10px 10px;\r\n  border-style: solid;\r\n  border-color: transparent transparent transparent white;\r\n}\r\n\r\n.popup-content {\r\n  padding: 20px;\r\n  text-align: center;\r\n}\r\n\r\n.popup-content h3 {\r\n  margin-top: 0;\r\n  margin-bottom: 16px;\r\n  font-size: 16px;\r\n  color: #333;\r\n  font-weight: 500;\r\n}\r\n\r\n.qr-code {\r\n  margin: 10px 0;\r\n}\r\n\r\n.qr-code img {\r\n  width: 150px;\r\n  height: 150px;\r\n}\r\n\r\n.phone-number {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  margin: 5px 0;\r\n}\r\n\r\n.popup-footer {\r\n  margin-top: 10px;\r\n}\r\n\r\n.btn-consult {\r\n  background-color: #f5f5f5;\r\n  border: none;\r\n  border-radius: 4px;\r\n  padding: 8px 16px;\r\n  font-size: 14px;\r\n  cursor: pointer;\r\n  color: #333;\r\n  width: 100%;\r\n}\r\n\r\n/* Modal styles - Reduced size */\r\n.modal-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 1001;\r\n}\r\n\r\n.feedback-modal {\r\n  background-color: white;\r\n  border-radius: 8px;\r\n  width: 500px;\r\n  max-width: 100vw;\r\n  max-height: 100vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.modal-header {\r\n  padding: 14px 20px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.modal-header h3 {\r\n  margin: 0;\r\n  font-size: 16px;\r\n  color: #333;\r\n}\r\n\r\n.close-btn {\r\n  cursor: pointer;\r\n  font-size: 20px;\r\n  color: #999;\r\n}\r\n\r\n.modal-body {\r\n  padding: 20px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.alert {\r\n  padding: 8px 12px;\r\n  border-radius: 4px;\r\n  margin-bottom: 16px;\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 13px;\r\n}\r\n\r\n.alert-warning {\r\n  background-color: #fffbe6;\r\n  border: 1px solid #ffe58f;\r\n  font-size: 10px;\r\n  color: #d48806;\r\n}\r\n\r\n.alert i {\r\n  margin-right: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.form-group {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.form-group label {\r\n  display: block;\r\n  margin-bottom: 6px;\r\n  font-size: 13px;\r\n  color: #333;\r\n}\r\n\r\n.required:before {\r\n  content: '*';\r\n  color: #ff4d4f;\r\n  margin-left: 4px;\r\n}\r\n.required1:before {\r\n  content: '';\r\n  color: #ff4d4f;\r\n  margin-left: 9px;\r\n}\r\n\r\ninput, select, textarea {\r\n  width: 100%;\r\n  padding: 8px 12px;\r\n  border: 1px solid #d9d9d9;\r\n  border-radius: 4px;\r\n  font-size: 13px;\r\n  transition: all 0.3s;\r\n}\r\n\r\ninput:focus, select:focus, textarea:focus {\r\n  outline: none;\r\n  border-color: #40a9ff;\r\n  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\r\n}\r\n\r\ntextarea {\r\n  min-height: 70px;\r\n  resize: vertical;\r\n}\r\n\r\n.select-wrapper {\r\n  position: relative;\r\n}\r\n\r\n.select-wrapper:after {\r\n  content: '';\r\n  position: absolute;\r\n  right: 12px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  font-size: 12px;\r\n  color: #999;\r\n  pointer-events: none;\r\n}\r\n\r\n.error-text {\r\n  color: #ff4d4f;\r\n  font-size: 12px;\r\n  margin-top: 4px;\r\n}\r\n\r\n.image-uploader {\r\n  border: 1px dashed #d9d9d9;\r\n  border-radius: 4px;\r\n  padding: 20px;\r\n  text-align: center;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n  background-color: #fafafa;\r\n  min-height: 120px;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.image-uploader:hover {\r\n  border-color: #40a9ff;\r\n}\r\n\r\n.upload-placeholder {\r\n  color: #999;\r\n}\r\n\r\n.upload-placeholder i {\r\n  font-size: 32px;\r\n  display: block;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.preview-container {\r\n  position: relative;\r\n  width: 100%;\r\n}\r\n\r\n.image-preview {\r\n  max-width: 100%;\r\n  max-height: 200px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.remove-image {\r\n  position: absolute;\r\n  top: -10px;\r\n  right: -10px;\r\n  width: 24px;\r\n  height: 24px;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  color: white;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  cursor: pointer;\r\n}\r\n\r\n.modal-footer {\r\n  padding: 10px 24px 24px;\r\n  text-align: right;\r\n}\r\n\r\n.btn {\r\n  padding: 8px 16px;\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n  border: none;\r\n}\r\n\r\n.btn-cancel {\r\n  background-color: white;\r\n  border: 1px solid #d9d9d9;\r\n  color: #333;\r\n  margin-right: 12px;\r\n}\r\n\r\n.btn-cancel:hover {\r\n  color: #40a9ff;\r\n  border-color: #40a9ff;\r\n}\r\n\r\n.btn-submit {\r\n  background-color: #1890ff;\r\n  color: white;\r\n}\r\n\r\n.btn-submit:hover {\r\n  background-color: #40a9ff;\r\n}\r\n\r\n/* For responsiveness */\r\n@media (max-width: 768px) {\r\n  .popup-container {\r\n    width: 200px;\r\n  }\r\n\r\n  .qr-code img {\r\n    width: 120px;\r\n    height: 120px;\r\n  }\r\n}\r\n\r\n.confirmation-dialog {\r\n  background-color: white;\r\n  border-radius: 8px;\r\n  padding: 24px;\r\n  /*margin-left: 50%;*/\r\n  /*text-align: left;*/\r\n  width: 500px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.confirmation-icon {\r\n  margin-left: 1%;\r\n  margin-bottom: -37px;\r\n}\r\n\r\n.confirmation-title {\r\n  font-size: 18px;\r\n  font-weight: 500;\r\n  margin-bottom: 8px;\r\n  margin-left: 10%;\r\n  color: #333;\r\n}\r\n\r\n.confirmation-message {\r\n  color: #666;\r\n  font-size: 14px;\r\n  margin-left: 10%;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.confirmation-actions {\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.btn-primary {\r\n  background-color: #1677ff;\r\n  /*margin-right: 2px;*/\r\n  color: white;\r\n  cursor: pointer;\r\n  border: none;\r\n  border-radius: 4px;\r\n  padding: 8px 32px;\r\n  font-size: 14px;\r\n  margin-left: 80%;\r\n}\r\n\r\n.btn-primary:hover {\r\n  background-color: #4096ff;\r\n}\r\n\r\n/* Form styles */\r\n.form-group {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.form-group label {\r\n  display: block;\r\n  margin-bottom: 8px;\r\n  font-size: 14px;\r\n  color: #333;\r\n}\r\n\r\n.required:after {\r\n  content: '*';\r\n  color: #f5222d;\r\n  margin-left: 4px;\r\n}\r\n\r\n.select-wrapper {\r\n  position: relative;\r\n}\r\n\r\n.select-wrapper:after {\r\n  content: '▼';\r\n  font-size: 10px;\r\n  color: #999;\r\n  position: absolute;\r\n  right: 10px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  pointer-events: none;\r\n}\r\n\r\nselect {\r\n  width: 100%;\r\n  padding: 8px 10px;\r\n  border: 1px solid #d9d9d9;\r\n  border-radius: 4px;\r\n  background-color: white;\r\n  font-size: 14px;\r\n  appearance: none;\r\n}\r\n\r\ntextarea {\r\n  width: 100%;\r\n  padding: 8px 10px;\r\n  border: 1px solid #d9d9d9;\r\n  border-radius: 4px;\r\n  min-height: 100px;\r\n  resize: vertical;\r\n  font-size: 14px;\r\n}\r\n\r\n.error-text {\r\n  color: #f5222d;\r\n  font-size: 12px;\r\n  margin-top: 4px;\r\n}\r\n\r\n.image-uploader {\r\n  border: 1px dashed #d9d9d9;\r\n  border-radius: 4px;\r\n  padding: 16px;\r\n  text-align: center;\r\n  cursor: pointer;\r\n  background-color: #fafafa;\r\n  transition: border-color 0.3s;\r\n}\r\n\r\n.image-uploader:hover {\r\n  border-color: #1890ff;\r\n}\r\n\r\n.upload-placeholder {\r\n  color: #bfbfbf;\r\n}\r\n\r\n.upload-placeholder p {\r\n  margin-top: 8px;\r\n  font-size: 13px;\r\n}\r\n\r\n.preview-container {\r\n  position: relative;\r\n}\r\n\r\n.image-preview {\r\n  max-width: 100%;\r\n  max-height: 200px;\r\n}\r\n\r\n.remove-image {\r\n  position: absolute;\r\n  top: -10px;\r\n  right: -10px;\r\n  width: 20px;\r\n  height: 20px;\r\n  background-color: rgba(0, 0, 0, 0.6);\r\n  color: white;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  cursor: pointer;\r\n}\r\n\r\n.modal-footer {\r\n  padding: 16px 20px;\r\n  border-top: 1px solid #f0f0f0;\r\n  text-align: right;\r\n}\r\n\r\n.btn {\r\n  padding: 8px 16px;\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.btn-cancel {\r\n  background-color: white;\r\n  border: 1px solid #d9d9d9;\r\n  color: #666;\r\n  margin-right: 8px;\r\n}\r\n\r\n.btn-cancel:hover {\r\n  color: #1890ff;\r\n  border-color: #1890ff;\r\n}\r\n\r\n.btn-submit {\r\n  background-color: #1677ff;\r\n  border: none;\r\n  color: white;\r\n}\r\n\r\n.btn-submit:hover {\r\n  background-color: #4096ff;\r\n}\r\n</style>", "import mod from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Mider.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Mider.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Mider.vue?vue&type=template&id=37397f14&scoped=true&\"\nimport script from \"./Mider.vue?vue&type=script&lang=js&\"\nexport * from \"./Mider.vue?vue&type=script&lang=js&\"\nimport style0 from \"./Mider.vue?vue&type=style&index=0&id=37397f14&prod&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"37397f14\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[_c('div',{staticClass:\"chat-container\"},[_c('div',{staticClass:\"question-carousel\",on:{\"mouseenter\":_vm.pauseCarousel,\"mouseleave\":_vm.resumeCarousel}},[_c('transition-group',{staticClass:\"carousel-wrapper\",attrs:{\"name\":\"slide\",\"tag\":\"div\"}},_vm._l((_vm.questions),function(question,index){return _c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.currentQuestionIndex === index),expression:\"currentQuestionIndex === index\"}],key:question,staticClass:\"question-item\",on:{\"click\":function($event){return _vm.sendCarouselQuestion(question)},\"mouseenter\":function($event){return _vm.witde(index)}}},[_vm._v(\" \"+_vm._s(question)+\" \")])}),0)],1),_c('div',{staticClass:\"chat-icon\",class:{ 'chat-icon-active': _vm.showChat },on:{\"click\":_vm.toggleChat}},[_c('i',{staticClass:\"fas fa-comment\"})])]),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.showChat),expression:\"showChat\"}],staticClass:\"chat-window\"},[_c('div',{staticClass:\"chat-header\"},[_vm._m(0),_c('div',{staticClass:\"chat-controls\"},[_c('i',{staticClass:\"fas fa-times\",on:{\"click\":_vm.toggleChat}})])]),_c('div',{ref:\"messagesContainer\",staticClass:\"chat-messages\"},[_vm._l((_vm.messages),function(message,index){return _c('div',{key:index,class:['message', message.type]},[(message.type === 'bot')?_c('div',{staticClass:\"avatar\"},[_c('i',{staticClass:\"fas fa-robot\"})]):_vm._e(),_c('div',{staticClass:\"message-content\"},[_c('div',{staticClass:\"message-text\",domProps:{\"innerHTML\":_vm._s(_vm.formatMessage(message.text))}}),_c('div',{staticClass:\"message-time\"},[_vm._v(_vm._s(_vm.formatTime(message.time)))])])])}),(_vm.loading)?_c('div',{staticClass:\"typing-indicator\"},[_c('div',{staticClass:\"dot\"}),_c('div',{staticClass:\"dot\"}),_c('div',{staticClass:\"dot\"})]):_vm._e()],2),_c('div',{staticClass:\"chat-input\"},[_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.userInput),expression:\"userInput\"}],attrs:{\"type\":\"text\",\"placeholder\":\"请输入您的问题...\",\"disabled\":_vm.loading},domProps:{\"value\":(_vm.userInput)},on:{\"keyup\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"enter\",13,$event.key,\"Enter\"))return null;return _vm.sendMessage.apply(null, arguments)},\"input\":function($event){if($event.target.composing)return;_vm.userInput=$event.target.value}}}),_c('button',{attrs:{\"disabled\":_vm.loading || !_vm.userInput.trim()},on:{\"click\":_vm.sendMessage}},[_c('i',{staticClass:\"fas fa-paper-plane\"})])])])])\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"chat-title\"},[_c('i',{staticClass:\"fas fa-robot\"}),_c('span',[_vm._v(\"智能客服\")])])\n}]\n\nexport { render, staticRenderFns }", "<template>\r\n    <div >\r\n<!--        问题轮播-->\r\n        <!-- 悬浮客服容器 -->\r\n        <div class=\"chat-container\">\r\n            <!-- 问题轮播区 -->\r\n            <div class=\"question-carousel\"\r\n                 @mouseenter=\"pauseCarousel\"\r\n                 @mouseleave=\"resumeCarousel\">\r\n                <transition-group name=\"slide\" tag=\"div\" class=\"carousel-wrapper\">\r\n                    <div v-for=\"(question, index) in questions\"\r\n                         :key=\"question\"\r\n                         class=\"question-item\"\r\n                         v-show=\"currentQuestionIndex === index\"\r\n                         @click=\"sendCarouselQuestion(question)\"\r\n                         @mouseenter=\"witde(index)\"\r\n                    >\r\n                        {{ question }}\r\n                    </div>\r\n                </transition-group>\r\n            </div>\r\n\r\n            <!-- 原有悬浮按钮 -->\r\n            <div class=\"chat-icon\"\r\n                 :class=\"{ 'chat-icon-active': showChat }\"\r\n                 @click=\"toggleChat\">\r\n                <i class=\"fas fa-comment\"></i>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- 聊天窗口 -->\r\n        <div class=\"chat-window\" v-show=\"showChat\">\r\n            <div class=\"chat-header\">\r\n                <div class=\"chat-title\">\r\n                    <i class=\"fas fa-robot\"></i>\r\n                    <span>智能客服</span>\r\n                </div>\r\n                <div class=\"chat-controls\">\r\n                    <i class=\"fas fa-times\" @click=\"toggleChat\"></i>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"chat-messages\" ref=\"messagesContainer\">\r\n                <div\r\n                        v-for=\"(message, index) in messages\"\r\n                        :key=\"index\"\r\n                        :class=\"['message', message.type]\"\r\n                >\r\n                    <div class=\"avatar\" v-if=\"message.type === 'bot'\">\r\n                        <i class=\"fas fa-robot\"></i>\r\n                    </div>\r\n                    <div class=\"message-content\">\r\n                        <div class=\"message-text\" v-html=\"formatMessage(message.text)\"></div>\r\n                        <div class=\"message-time\">{{ formatTime(message.time) }}</div>\r\n                    </div>\r\n                </div>\r\n                <div class=\"typing-indicator\" v-if=\"loading\">\r\n                    <div class=\"dot\"></div>\r\n                    <div class=\"dot\"></div>\r\n                    <div class=\"dot\"></div>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"chat-input\">\r\n                <input\r\n                        type=\"text\"\r\n                        v-model=\"userInput\"\r\n                        placeholder=\"请输入您的问题...\"\r\n                        @keyup.enter=\"sendMessage\"\r\n                        :disabled=\"loading\"\r\n                />\r\n                <button @click=\"sendMessage\" :disabled=\"loading || !userInput.trim()\">\r\n                    <i class=\"fas fa-paper-plane\"></i>\r\n                </button>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    export default {\r\n        name: 'chatAi',\r\n\r\n        data() {\r\n            return {\r\n                showChat: false,\r\n                userInput: '',\r\n                messages: [\r\n                    {\r\n                        type: 'bot',\r\n                        text: '您好！我是智能客服助手，有什么可以帮您？',\r\n                        time: new Date()\r\n                    }\r\n                ],\r\n                loading: false,\r\n                historyMessages:[],\r\n                questions: [\r\n                    \"如何租赁GPU算力？\",\r\n                    \"支持哪些支付方式？\",\r\n                    \"如何查看订单状态？\"\r\n                ],\r\n                currentQuestionIndex: 0,\r\n                carouselTimer: null,\r\n                carouselInterval: 3000,\r\n                isPaused: false\r\n            }\r\n        },\r\n        beforeDestroy() {\r\n            this.clearCarousel()\r\n        },\r\n        mounted() {\r\n            this.startCarousel();\r\n            // 导入 Font Awesome 图标库\r\n            if (!document.getElementById('font-awesome')) {\r\n                const link = document.createElement('link');\r\n                link.id = 'font-awesome';\r\n                link.rel = 'stylesheet';\r\n                link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css';\r\n                document.head.appendChild(link);\r\n            }\r\n        },\r\n\r\n        methods: {\r\n            witde(index){\r\n                this.currentQuestionIndex = index;\r\n                this.pauseCarousel();\r\n            },\r\n            startCarousel() {\r\n                let that = this\r\n                this.clearCarousel();\r\n                this.carouselTimer = setInterval(() => {\r\n                    if (!that.isPaused) {\r\n                        this.currentQuestionIndex =\r\n                            (this.currentQuestionIndex + 1) % this.questions.length\r\n                        console.log(\"数据\", this.currentQuestionIndex)\r\n                        console.log(\"ispasued\",that.isPaused)\r\n                    }\r\n                }, this.carouselInterval)\r\n            },\r\n            pauseCarousel() {\r\n                this.isPaused = true;\r\n            },\r\n            resumeCarousel() {\r\n                this.isPaused = false;\r\n            },\r\n            clearCarousel() {\r\n                if (this.carouselTimer) {\r\n                    clearInterval(this.carouselTimer);\r\n                    this.carouselTimer = null;\r\n                }\r\n            },\r\n            // 点击轮播问题自动提问\r\n            sendCarouselQuestion(question) {\r\n                this.userInput = question;\r\n                this.sendMessage();\r\n            },\r\n            toggleChat() {\r\n                this.showChat = !this.showChat;\r\n\r\n                if (this.showChat) {\r\n                    this.$nextTick(() => {\r\n                        this.scrollToBottom();\r\n                    });\r\n                }\r\n            },\r\n\r\n            async sendMessage() {\r\n                if (!this.userInput.trim() || this.loading) return;\r\n\r\n                // 添加用户消息\r\n                this.messages.push({\r\n                    type: 'user',\r\n                    text: this.userInput,\r\n                    time: new Date()\r\n                });\r\n\r\n                const userQuestion = this.userInput;\r\n                this.userInput = '';\r\n                this.loading = true;\r\n                //添加历史记录\r\n                this.historyMessages.push({\r\n                    role:'user',\r\n                    content:userQuestion,\r\n                })\r\n\r\n                // 构造请求体\r\n                const requestBody = {\r\n                    model: 'Qwen/QwQ-32B',\r\n                    messages: [{role:\"system\",content: \"你是天工开物算力租赁网站的客服，当我问到算力租赁相关问题的时候再进行回答，如果我的问题与算力无关则回答“小天也不知道怎么办了呢，不如我们来聊聊算力相关的话题吧！”,如果问题涉及到你是谁或者你好的这种礼貌性踊跃，可以进行礼貌性的回复\"},...this.historyMessages], // 携带上下文历史\r\n                    stream: true,\r\n                    options: {\r\n                        presence_penalty: 1.2,  // 重复内容惩罚（0-2）\r\n                        frequency_penalty: 1.5, // 高频词惩罚（0-2）\r\n                        // repeat_last_n: 64,      // 检查重复的上下文长度\r\n                        seed: 12345             // 固定随机种子\r\n                    }\r\n                };\r\n                // 滚动到底部\r\n                this.$nextTick(() => {\r\n                    this.scrollToBottom();\r\n                });\r\n\r\n                try {\r\n                    // 调用后端API获取回复\r\n                    // 替换为你的实际API\r\n                    const response = await fetch('https://api.siliconflow.cn/v1/chat/completions', {\r\n                        method: 'POST',\r\n                        headers: {\r\n                            Authorization: 'Bearer sk-yxjtnfarjndtcwbjwmscdejrgtlpaaebnfdxcfnhhuanbqty',\r\n                            'Content-Type': 'application/json',\r\n                        },\r\n                        body: JSON.stringify(requestBody)\r\n                    });\r\n\r\n                    // 处理流式数据\r\n                    const reader = response.body.getReader();\r\n                    const decoder = new TextDecoder();\r\n                    const aiResponseIndex = this.messages.push({\r\n                        type: 'bot',\r\n                        text: '',\r\n                        time:new Date()\r\n                    }) - 1;\r\n                    while (true) {\r\n                        const { done, value } = await reader.read();\r\n                        if (done) break;\r\n\r\n                        // 解析流式数据块（可能包含多个JSON对象）\r\n                        const chunk = decoder.decode(value);\r\n                        const lines = chunk.split('\\n').filter(line => line.trim());\r\n\r\n                        for (const line of lines) {\r\n                            try {\r\n                                const jsonString = line.slice(6).trim();\r\n                                if (jsonString === \"\" || jsonString === \"[DONE]\") continue;\r\n\r\n                                let data = JSON.parse(jsonString)\r\n                                if (data.choices) {\r\n                                    if (data.choices[0].delta.reasoning_content!=null){\r\n                                        continue\r\n                                    }\r\n                                    if (data.choices[0].delta.content == '\\n\\n'){\r\n                                        continue\r\n                                    }\r\n                                    this.messages[aiResponseIndex].text += data.choices[0].delta.content;\r\n                                }\r\n                            } catch (e) {\r\n                            }\r\n                        }\r\n                    }\r\n                    this.historyMessages.push({\r\n                        role:\"assistant\",\r\n                        content:this.messages[aiResponseIndex].text\r\n                    })\r\n\r\n                    // 添加机器人回复\r\n                    // this.messages.push({\r\n                    //     type: 'bot',\r\n                    //     text: response,\r\n                    //     time: new Date()\r\n                    // });\r\n                } catch (error) {\r\n\r\n                    // 添加错误消息\r\n                    this.messages.push({\r\n                        type: 'bot',\r\n                        text: '抱歉，系统暂时无法响应，请稍后再试。',\r\n                        time: new Date()\r\n                    });\r\n                } finally {\r\n                    this.loading = false;\r\n\r\n                    // 滚动到底部\r\n                    this.$nextTick(() => {\r\n                        this.scrollToBottom();\r\n                    });\r\n                }\r\n            },\r\n\r\n            // 模拟API调用，实际使用时替换为真实API\r\n            async callChatAPI(message) {\r\n                // 模拟网络延迟\r\n                await new Promise(resolve => setTimeout(resolve, 1000));\r\n\r\n                // TODO: 替换为实际的API调用\r\n                // const response = await fetch('YOUR_API_ENDPOINT', {\r\n                //   method: 'POST',\r\n                //   headers: {\r\n                //     'Content-Type': 'application/json',\r\n                //   },\r\n                //   body: JSON.stringify({ message }),\r\n                // });\r\n                // return await response.json();\r\n\r\n                // 模拟返回数据\r\n                return `感谢您的提问: \"${message}\"。这是一个模拟回复，请替换为真实API调用。`;\r\n            },\r\n\r\n            scrollToBottom() {\r\n                const container = this.$refs.messagesContainer;\r\n                container.scrollTop = container.scrollHeight;\r\n            },\r\n\r\n            formatTime(date) {\r\n                return new Date(date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\r\n            },\r\n\r\n            formatMessage(text) {\r\n                // 处理文本中的链接、表情等\r\n                return text\r\n                    .replace(/\\n/g, '<br>')\r\n                    .replace(/(https?:\\/\\/[^\\s]+)/g, '<a href=\"$1\" target=\"_blank\">$1</a>');\r\n            }\r\n        }\r\n    }\r\n</script>\r\n\r\n<style scoped>\r\n    /* 修正后的轮播样式 */\r\n    .chat-container {\r\n        position: fixed;\r\n        bottom: 20px;\r\n        right: 20px;\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: flex-end;\r\n        gap: 10px;\r\n        z-index: 1001;\r\n    }\r\n\r\n    .question-carousel {\r\n        left: 20px;\r\n        padding-bottom: 85px;\r\n        padding-top: 20px;\r\n        color: white;\r\n        min-width: 150px;\r\n        text-align: left;\r\n        cursor: pointer;\r\n        overflow: hidden;\r\n        position: relative;\r\n        height: 60px; /* 固定高度避免跳动 */\r\n    }\r\n\r\n    .carousel-wrapper {\r\n        position: relative;\r\n        height: 100%;\r\n    }\r\n\r\n    .question-item {\r\n        position: absolute;\r\n        border-radius: 20px 20px 20px 20px;\r\n        background-color: black;\r\n        width: 100%;\r\n        left: 0;\r\n        top: 50%;\r\n        transform: translateY(-50%);\r\n        padding: 10px 10px;\r\n        font-size: 14px;\r\n        opacity: 1;\r\n        transition: all 0.5s ease;\r\n    }\r\n\r\n    .question-item:hover {\r\n        color: #4286f4;\r\n    }\r\n\r\n    /* 过渡动画修正 */\r\n    .slide-enter-active,\r\n    .slide-leave-active {\r\n        transition: all 0.5s ease;\r\n    }\r\n    .slide-enter-from {\r\n        opacity: 0;\r\n        transform: translateY(20px) translateY(-50%);\r\n    }\r\n    .slide-leave-to {\r\n        opacity: 0;\r\n        transform: translateY(-20px) translateY(-50%);\r\n    }\r\n    .slide-enter-to,\r\n    .slide-leave-from {\r\n        opacity: 1;\r\n        transform: translateY(0) translateY(-50%);\r\n    }\r\n    .chat-icon {\r\n        position: fixed;\r\n        bottom: 20px;\r\n        right: 20px;\r\n        width: 60px;\r\n        height: 60px;\r\n        background-color: blue;\r\n        border-radius: 50%;\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        cursor: pointer;\r\n        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);\r\n        transition: all 0.3s ease;\r\n        z-index: 1001;\r\n    }\r\n\r\n    .chat-icon i {\r\n        color: white;\r\n        font-size: 24px;\r\n    }\r\n\r\n    .chat-icon:hover, .chat-icon-active {\r\n        background-color: #3367d6;\r\n        transform: scale(1.05);\r\n    }\r\n\r\n    .chat-window {\r\n        position: fixed;\r\n        bottom: 90px;\r\n        right: 20px;\r\n        width: 350px;\r\n        height: 500px;\r\n        background-color: #fff;\r\n        border-radius: 10px;\r\n        box-shadow: 0 5px 40px rgba(0, 0, 0, 0.16);\r\n        display: flex;\r\n        flex-direction: column;\r\n        overflow: hidden;\r\n        z-index: 1002;\r\n    }\r\n\r\n    .chat-header {\r\n        padding: 15px;\r\n        background-color: #4286f4;\r\n        color: white;\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n    }\r\n\r\n    .chat-title {\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 10px;\r\n        font-weight: bold;\r\n    }\r\n\r\n    .chat-controls i {\r\n        cursor: pointer;\r\n        font-size: 18px;\r\n    }\r\n\r\n    .chat-messages {\r\n        flex: 1;\r\n        padding: 15px;\r\n        overflow-y: auto;\r\n        background-color: #f5f5f5;\r\n    }\r\n\r\n    .message {\r\n        display: flex;\r\n        margin-bottom: 15px;\r\n        align-items: flex-start;\r\n    }\r\n\r\n    .message.user {\r\n        flex-direction: row-reverse;\r\n    }\r\n\r\n    .avatar {\r\n        width: 36px;\r\n        height: 36px;\r\n        border-radius: 50%;\r\n        background-color: #4286f4;\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        color: white;\r\n        margin-right: 10px;\r\n    }\r\n\r\n    .message-content {\r\n        max-width: 70%;\r\n    }\r\n\r\n    .message-text {\r\n        padding: 10px 15px;\r\n        border-radius: 18px;\r\n        margin-bottom: 5px;\r\n        word-break: break-word;\r\n    }\r\n\r\n    .message.bot .message-text {\r\n        background-color: white;\r\n        border: 1px solid #e0e0e0;\r\n    }\r\n\r\n    .message.user .message-text {\r\n        background-color: #4286f4;\r\n        color: white;\r\n        text-align: right;\r\n    }\r\n\r\n    .message-time {\r\n        font-size: 12px;\r\n        color: #999;\r\n        margin-left: 10px;\r\n    }\r\n\r\n    .message.user .message-time {\r\n        text-align: right;\r\n    }\r\n\r\n    .typing-indicator {\r\n        display: flex;\r\n        padding: 10px 15px;\r\n        background-color: white;\r\n        border-radius: 18px;\r\n        border: 1px solid #e0e0e0;\r\n        width: fit-content;\r\n        margin-bottom: 15px;\r\n    }\r\n\r\n    .dot {\r\n        width: 8px;\r\n        height: 8px;\r\n        background-color: #999;\r\n        border-radius: 50%;\r\n        margin: 0 2px;\r\n        animation: bounce 1.5s infinite;\r\n    }\r\n\r\n    .dot:nth-child(2) {\r\n        animation-delay: 0.2s;\r\n    }\r\n\r\n    .dot:nth-child(3) {\r\n        animation-delay: 0.4s;\r\n    }\r\n\r\n    @keyframes bounce {\r\n        0%, 60%, 100% {\r\n            transform: translateY(0);\r\n        }\r\n        30% {\r\n            transform: translateY(-4px);\r\n        }\r\n    }\r\n\r\n    .chat-input {\r\n        padding: 15px;\r\n        display: flex;\r\n        border-top: 1px solid #e0e0e0;\r\n        background-color: white;\r\n    }\r\n\r\n    .chat-input input {\r\n        flex: 1;\r\n        padding: 10px 15px;\r\n        border: 1px solid #e0e0e0;\r\n        border-radius: 20px;\r\n        font-size: 14px;\r\n        outline: none;\r\n    }\r\n\r\n    .chat-input button {\r\n        margin-left: 10px;\r\n        width: 40px;\r\n        height: 40px;\r\n        border-radius: 50%;\r\n        background-color: #4286f4;\r\n        color: white;\r\n        border: none;\r\n        cursor: pointer;\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n    }\r\n\r\n    .chat-input button:disabled {\r\n        background-color: #b3c9f4;\r\n        cursor: not-allowed;\r\n    }\r\n\r\n    .chat-input button i {\r\n        font-size: 16px;\r\n    }\r\n\r\n    /* 移动端适配 */\r\n    @media (max-width: 480px) {\r\n        .chat-window {\r\n            width: 100%;\r\n            height: 100%;\r\n            bottom: 0;\r\n            right: 0;\r\n            border-radius: 0;\r\n        }\r\n\r\n        .chat-icon {\r\n            bottom: 15px;\r\n            right: 15px;\r\n        }\r\n    }\r\n</style>", "import mod from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./chatAi.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./chatAi.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./chatAi.vue?vue&type=template&id=46c63c47&scoped=true&\"\nimport script from \"./chatAi.vue?vue&type=script&lang=js&\"\nexport * from \"./chatAi.vue?vue&type=script&lang=js&\"\nimport style0 from \"./chatAi.vue?vue&type=style&index=0&id=46c63c47&prod&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"46c63c47\",\n  null\n  \n)\n\nexport default component.exports"], "names": ["render", "_vm", "this", "_c", "_self", "staticClass", "on", "$event", "showPopup", "hidePopup", "attrs", "directives", "name", "rawName", "value", "activePopup", "expression", "_v", "wechatQRCode", "contactQRCode", "showFeedbackModal", "showTooltip", "hideTooltip", "showFeedbackTooltip", "showModal", "target", "currentTarget", "closeFeedbackModal", "apply", "arguments", "feedback", "type", "$$selectedVal", "Array", "prototype", "filter", "call", "options", "o", "selected", "map", "val", "_value", "$set", "multiple", "showErrors", "_e", "description", "domProps", "composing", "triggerFileUpload", "preventDefault", "onFileDrop", "ref", "staticStyle", "onFileChange", "image", "imagePreview", "stopPropagation", "removeImage", "confirmSubmit", "showConfirmation", "closeConfirmation", "staticRenderFns", "data", "require", "instanceId", "methods", "$refs", "fileInput", "click", "event", "file", "files", "processImage", "dataTransfer", "match", "reader", "FileReader", "onload", "e", "result", "readAsDataURL", "submitFeedback", "formData", "FormData", "append", "component", "pauseCarousel", "resumeCarousel", "_l", "questions", "question", "index", "currentQuestionIndex", "key", "sendCarouselQuestion", "witde", "_s", "class", "showChat", "toggleChat", "_m", "messages", "message", "formatMessage", "text", "formatTime", "time", "loading", "userInput", "indexOf", "_k", "keyCode", "sendMessage", "trim", "Date", "historyMessages", "carouselTimer", "carouselI<PERSON>val", "isPaused", "<PERSON><PERSON><PERSON><PERSON>", "clearCarousel", "mounted", "startCarousel", "document", "getElementById", "link", "createElement", "id", "rel", "href", "head", "append<PERSON><PERSON><PERSON>", "that", "setInterval", "length", "console", "log", "clearInterval", "$nextTick", "scrollToBottom", "push", "userQuestion", "role", "content", "requestBody", "model", "stream", "presence_penalty", "frequency_penalty", "seed", "response", "fetch", "method", "headers", "Authorization", "body", "JSON", "stringify", "<PERSON><PERSON><PERSON><PERSON>", "decoder", "TextDecoder", "aiResponseIndex", "done", "read", "chunk", "decode", "lines", "split", "line", "jsonString", "slice", "parse", "choices", "delta", "reasoning_content", "error", "Promise", "resolve", "setTimeout", "container", "messagesContainer", "scrollTop", "scrollHeight", "date", "toLocaleTimeString", "hour", "minute", "replace"], "sourceRoot": ""}