{"version": 3, "file": "js/index.07f66490.js", "mappings": "qFAAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,OAAO,CAACE,YAAY,gBAAgB,CAACF,EAAG,UAAUA,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIK,GAAG,YAAY,GAAGH,EAAG,WAAW,EACnM,EACII,EAAkB,G,UCctB,GACAC,KAAA,SACAC,WAAA,CAAAC,OAAAA,EAAAA,IClBsQ,I,UCQlQC,GAAY,OACd,EACAX,EACAO,GACA,EACA,KACA,WACA,MAIF,EAAeI,EAAiB,O,uDCnBhC,IAAIX,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAAEJ,EAAIW,eAAgBT,EAAG,oBAAoB,CAACU,MAAM,CAAC,QAAUZ,EAAIa,oBAAoB,KAAO,UAAU,SAAW,KAAMC,GAAG,CAAC,MAAQ,SAASC,GAAQf,EAAIW,gBAAiB,CAAK,KAAKX,EAAIgB,KAAKd,EAAG,MAAM,CAACE,YAAY,kBAAkBa,MAAO,CAAEC,OAAQlB,EAAImB,UAAY,QAAUjB,EAAG,MAAM,CAACkB,IAAI,UAAUhB,YAAY,YAAY,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAAGJ,EAAIqB,SAAg2HnB,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,SAAS,CAACE,YAAY,gBAAgBU,GAAG,CAAC,MAAQd,EAAIsB,mBAAmB,CAACpB,EAAG,OAAO,CAACE,YAAY,iBAAiBmB,MAAM,CAAC,SAAUvB,EAAIwB,kBAAkBtB,EAAG,OAAO,CAACE,YAAY,iBAAiBmB,MAAM,CAAC,SAAUvB,EAAIwB,kBAAkBtB,EAAG,OAAO,CAACE,YAAY,iBAAiBmB,MAAM,CAAC,SAAUvB,EAAIwB,oBAAoBtB,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,IAAI,CAACE,YAAY,YAAYU,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOf,EAAIyB,WAAW,IAAI,IAAI,CAACvB,EAAG,MAAM,CAACU,MAAM,CAAC,IAAM,2BAA2B,IAAM,cAAcV,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAAGJ,EAAI0B,WAAsQ,CAACxB,EAAG,IAAI,CAACE,YAAY,qBAAqBU,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOf,EAAIyB,WAAW,QAAQ,IAAI,CAACzB,EAAI2B,GAAG,YAAYzB,EAAG,IAAI,CAACE,YAAY,qBAAqBU,GAAG,CAAC,MAAQd,EAAI4B,0BAA0B,CAAC5B,EAAI2B,GAAG,WAAWzB,EAAG,MAAM,CAACE,YAAY,sBAAsBU,GAAG,CAAC,MAAQd,EAAI6B,iBAAiB,CAAC3B,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACJ,EAAI2B,GAAG3B,EAAI8B,GAAG9B,EAAI+B,qBAAlpB,CAAC7B,EAAG,IAAI,CAACE,YAAY,mBAAmBU,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOf,EAAIyB,WAAW,SAAS,IAAI,CAACzB,EAAI2B,GAAG,UAAUzB,EAAG,IAAI,CAACE,YAAY,sBAAsBU,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOf,EAAIyB,WAAW,YAAY,IAAI,CAACzB,EAAI2B,GAAG,YAAyb,KAAKzB,EAAG,MAAM,CAACE,YAAY,cAAcmB,MAAM,CAAC,KAAQvB,EAAIwB,iBAAiB,CAACtB,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAACF,EAAG,KAAK,CAACE,YAAY,mBAAmB,CAACF,EAAG,KAAK,CAACE,YAAY,mBAAmB,CAACF,EAAG,IAAI,CAACE,YAAY,kBAAkBmB,MAAM,CAAC,OAAUvB,EAAIgC,SAAS,MAAMlB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOf,EAAIyB,WAAW,IAAI,IAAI,CAACvB,EAAG,IAAI,CAACE,YAAY,uBAAuBJ,EAAI2B,GAAG,WAAWzB,EAAG,KAAK,CAACE,YAAY,mBAAmB,CAACF,EAAG,IAAI,CAACE,YAAY,kBAAkBmB,MAAM,CAAC,OAAUvB,EAAIgC,SAAS,aAAalB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOf,EAAIyB,WAAW,WAAW,IAAI,CAACvB,EAAG,IAAI,CAACE,YAAY,yBAAyBJ,EAAI2B,GAAG,aAAazB,EAAG,KAAK,CAACE,YAAY,mBAAmB,CAACF,EAAG,IAAI,CAACE,YAAY,kBAAkBU,GAAG,CAAC,MAAQd,EAAIiC,oBAAoB,CAAC/B,EAAG,IAAI,CAACE,YAAY,4BAA4BJ,EAAI2B,GAAG,aAAazB,EAAG,KAAK,CAACE,YAAY,mBAAmB,CAACF,EAAG,IAAI,CAACE,YAAY,kBAAkBmB,MAAM,CAAC,OAAUvB,EAAIgC,SAAS,WAAWlB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOf,EAAIyB,WAAW,SAAS,IAAI,CAACvB,EAAG,IAAI,CAACE,YAAY,uBAAuBJ,EAAI2B,GAAG,aAAe3B,EAAI0B,WAAk/B,CAACxB,EAAG,KAAK,CAACE,YAAY,mBAAmB,CAACF,EAAG,IAAI,CAACE,YAAY,kBAAkBmB,MAAM,CAAC,OAAUvB,EAAIgC,SAAS,UAAUlB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOf,EAAIyB,WAAW,QAAQ,IAAI,CAACvB,EAAG,IAAI,CAACE,YAAY,uBAAuBJ,EAAI2B,GAAG,aAAazB,EAAG,KAAK,CAACE,YAAY,mBAAmB,CAACF,EAAG,IAAI,CAACE,YAAY,kBAAkBmB,MAAM,CAAC,OAAUvB,EAAIgC,SAAS,aAAalB,GAAG,CAAC,MAAQd,EAAI4B,0BAA0B,CAAC1B,EAAG,IAAI,CAACE,YAAY,0BAA0BJ,EAAI2B,GAAG,YAAYzB,EAAG,KAAK,CAACE,YAAY,mBAAmB,CAACF,EAAG,IAAI,CAACE,YAAY,kBAAkBmB,MAAM,CAAC,OAAUvB,EAAIgC,SAAS,cAAclB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOf,EAAIyB,WAAW,YAAY,IAAI,CAACvB,EAAG,IAAI,CAACE,YAAY,0BAA0BJ,EAAI2B,GAAG,aAAazB,EAAG,KAAK,CAACE,YAAY,mBAAmB,CAACF,EAAG,IAAI,CAACE,YAAY,kBAAkBU,GAAG,CAAC,MAAQd,EAAIkC,SAAS,CAAChC,EAAG,IAAI,CAACE,YAAY,yBAAyBJ,EAAI2B,GAAG,cAAj2D,CAACzB,EAAG,KAAK,CAACE,YAAY,mBAAmB,CAACF,EAAG,IAAI,CAACE,YAAY,kBAAkBmB,MAAM,CAAC,OAAUvB,EAAIgC,SAAS,UAAUlB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOf,EAAIyB,WAAW,QAAQ,IAAI,CAACvB,EAAG,IAAI,CAACE,YAAY,uBAAuBJ,EAAI2B,GAAG,aAAazB,EAAG,KAAK,CAACE,YAAY,mBAAmB,CAACF,EAAG,IAAI,CAACE,YAAY,kBAAkBmB,MAAM,CAAC,OAAUvB,EAAIgC,SAAS,WAAWlB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOf,EAAIyB,WAAW,SAAS,IAAI,CAACvB,EAAG,IAAI,CAACE,YAAY,uBAAuBJ,EAAI2B,GAAG,WAAWzB,EAAG,KAAK,CAACE,YAAY,mBAAmB,CAACF,EAAG,IAAI,CAACE,YAAY,kBAAkBmB,MAAM,CAAC,OAAUvB,EAAIgC,SAAS,cAAclB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOf,EAAIyB,WAAW,YAAY,IAAI,CAACvB,EAAG,IAAI,CAACE,YAAY,uBAAuBJ,EAAI2B,GAAG,WAAWzB,EAAG,KAAK,CAACE,YAAY,mBAAmB,CAACF,EAAG,IAAI,CAACE,YAAY,kBAAkBmB,MAAM,CAAC,OAAUvB,EAAIgC,SAAS,aAAalB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOf,EAAIyB,WAAW,SAAS,IAAI,CAACvB,EAAG,IAAI,CAACE,YAAY,0BAA0BJ,EAAI2B,GAAG,cAAu5B,OAAQ3B,EAAImC,aAAcjC,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACJ,EAAI2B,GAAG3B,EAAI8B,GAAG9B,EAAIoC,aAAalC,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACJ,EAAI2B,GAAG3B,EAAI8B,GAAG9B,EAAIqC,gBAAgBnC,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAACF,EAAG,IAAI,CAACE,YAAY,mBAAmBmB,MAAM,CAAC,OAAUvB,EAAIgC,SAAS,cAAclB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOf,EAAIyB,WAAW,YAAY,IAAI,CAACvB,EAAG,IAAI,CAACE,YAAY,0BAA0BJ,EAAI2B,GAAG,WAAWzB,EAAG,IAAI,CAACE,YAAY,mBAAmBmB,MAAM,CAAC,OAAUvB,EAAIgC,SAAS,eAAelB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOf,EAAIyB,WAAW,aAAa,IAAI,CAACvB,EAAG,IAAI,CAACE,YAAY,wBAAwBJ,EAAI2B,GAAG,WAAWzB,EAAG,IAAI,CAACE,YAAY,mBAAmBmB,MAAM,CAAC,QAAU,GAAOT,GAAG,CAAC,MAAQd,EAAIsC,qBAAqB,CAACpC,EAAG,IAAI,CAACE,YAAY,2BAA2BJ,EAAI2B,GAAG,SAASzB,EAAG,IAAI,CAACE,YAAY,0BAA0BU,GAAG,CAAC,MAAQd,EAAIkC,SAAS,CAAChC,EAAG,IAAI,CAACE,YAAY,yBAAyBJ,EAAI2B,GAAG,eAAe3B,EAAIgB,OAAlnSd,EAAG,MAAM,CAACE,YAAY,6BAA6B,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,IAAI,CAACE,YAAY,YAAYU,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOf,EAAIyB,WAAW,IAAI,IAAI,CAACvB,EAAG,MAAM,CAACU,MAAM,CAAC,IAAM,2BAA2B,IAAM,OAAO,QAAU,eAAeV,EAAG,MAAM,CAACE,YAAY,YAAY,CAACF,EAAG,KAAK,CAACE,YAAY,YAAY,CAACF,EAAG,KAAK,CAACE,YAAY,YAAY,CAACF,EAAG,IAAI,CAACE,YAAY,WAAWmB,MAAM,CAAC,OAAUvB,EAAIgC,SAAS,MAAMlB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOf,EAAIyB,WAAW,IAAI,IAAI,CAACzB,EAAI2B,GAAG,UAAUzB,EAAG,KAAK,CAACE,YAAY,qBAAqB,CAACF,EAAG,IAAI,CAACE,YAAY,WAAWmB,MAAM,CAAC,OAAUvB,EAAIgC,SAAS,aAAalB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOf,EAAIyB,WAAW,WAAW,IAAI,CAACzB,EAAI2B,GAAG,SAASzB,EAAG,IAAI,CAACE,YAAY,iCAAiCF,EAAG,KAAK,CAACE,YAAY,qBAAqB,CAACF,EAAG,IAAI,CAACE,YAAY,WAAWmB,MAAM,CAAC,QAAU,GAAOT,GAAG,CAAC,MAAQd,EAAIiC,oBAAoB,CAACjC,EAAI2B,GAAG,QAAQzB,EAAG,IAAI,CAACE,YAAY,iCAAiCF,EAAG,KAAK,CAACE,YAAY,YAAY,CAACF,EAAG,IAAI,CAACE,YAAY,WAAWmB,MAAM,CAAC,OAAUvB,EAAIgC,SAAS,WAAWlB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOf,EAAIyB,WAAW,SAAS,IAAI,CAACzB,EAAI2B,GAAG,gBAAgBzB,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAAGJ,EAAI0B,WAAqrB1B,EAAIgB,KAA7qBd,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,IAAI,CAACE,YAAY,gBAAgBmB,MAAM,CAAC,OAAUvB,EAAIgC,SAAS,UAAUlB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOf,EAAIyB,WAAW,QAAQ,IAAI,CAACzB,EAAI2B,GAAG,UAAUzB,EAAG,IAAI,CAACE,YAAY,gBAAgBmB,MAAM,CAAC,OAAUvB,EAAIgC,SAAS,WAAWlB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOf,EAAIyB,WAAW,SAAS,IAAI,CAACzB,EAAI2B,GAAG,SAASzB,EAAG,IAAI,CAACE,YAAY,gBAAgBmB,MAAM,CAAC,OAAUvB,EAAIgC,SAAS,WAAWlB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOf,EAAIyB,WAAW,SAAS,IAAI,CAACzB,EAAI2B,GAAG,QAAQzB,EAAG,IAAI,CAACE,YAAY,mBAAmBmB,MAAM,CAAC,OAAUvB,EAAIgC,SAAS,cAAclB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOf,EAAIyB,WAAW,YAAY,IAAI,CAACzB,EAAI2B,GAAG,YAAsB3B,EAAI0B,WAAYxB,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,IAAI,CAACE,YAAY,gBAAgBmB,MAAM,CAAC,OAAUvB,EAAIgC,SAAS,UAAUlB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOf,EAAIyB,WAAW,QAAQ,IAAI,CAACzB,EAAI2B,GAAG,UAAUzB,EAAG,IAAI,CAACE,YAAY,gBAAgBmB,MAAM,CAAC,OAAUvB,EAAIgC,SAAS,YAAY,SAAYhC,EAAIuC,kBAAmB3B,MAAM,CAAC,MAAQZ,EAAIuC,iBAAmB,gBAAkB,IAAIzB,GAAG,CAAC,MAAQd,EAAI4B,0BAA0B,CAAC5B,EAAI2B,GAAG,SAASzB,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACJ,EAAI2B,GAAG3B,EAAI8B,GAAG9B,EAAI+B,kBAAkB7B,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,OAAO,CAACE,YAAY,SAAS,CAACJ,EAAI2B,GAAG,UAAUzB,EAAG,OAAO,CAACE,YAAY,SAAS,CAACJ,EAAI2B,GAAG3B,EAAI8B,GAAG9B,EAAIoC,eAAelC,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,OAAO,CAACE,YAAY,SAAS,CAACJ,EAAI2B,GAAG,UAAUzB,EAAG,OAAO,CAACE,YAAY,SAAS,CAACF,EAAG,IAAI,CAACE,YAAY,cAAcJ,EAAI2B,GAAG,IAAI3B,EAAI8B,GAAG9B,EAAIqC,gBAAgBnC,EAAG,MAAM,CAACE,YAAY,mBAAmBU,GAAG,CAAC,MAAQd,EAAIwC,eAAe,CAACtC,EAAG,OAAO,CAACE,YAAY,eAAeJ,EAAI2B,GAAG,UAAUzB,EAAG,OAAO,CAACE,YAAY,eAAe,CAACJ,EAAI2B,GAAG,IAAI3B,EAAI8B,GAAkB,IAAf9B,EAAIyC,OAAe,MAAQ,OAAO,SAASvC,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,OAAO,CAACE,YAAY,SAAS,CAACJ,EAAI2B,GAAG,WAAWzB,EAAG,OAAO,CAACE,YAAY,SAAS,CAACJ,EAAI2B,GAAG,IAAI3B,EAAI8B,GAAG9B,EAAI0C,YAAYC,QAAQ,OAAOzC,EAAG,MAAM,CAACE,YAAY,gCAAgCU,GAAG,CAAC,MAAQd,EAAIsC,qBAAqB,CAACtC,EAAI2B,GAAG,cAAczB,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,IAAI,CAACqB,MAAM,CAAC,OAAUvB,EAAIgC,SAAS,cAAclB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOf,EAAIyB,WAAW,YAAY,IAAI,CAACzB,EAAI2B,GAAG,UAAUzB,EAAG,IAAI,CAACqB,MAAM,CAAC,OAAUvB,EAAIgC,SAAS,eAAelB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOf,EAAIyB,WAAW,aAAa,IAAI,CAACzB,EAAI2B,GAAG,YAAYzB,EAAG,MAAM,CAACE,YAAY,2BAA2B,CAACF,EAAG,SAAS,CAACE,YAAY,gBAAgBU,GAAG,CAAC,MAAQd,EAAIkC,SAAS,CAAClC,EAAI2B,GAAG,kBAAkB3B,EAAIgB,cAAizK,EAC/lT,EACIV,EAAkB,G,kDC8QtB,GACAC,KAAA,SACAC,WAAA,CACAoC,kBAAAA,EAAAA,GAEAC,OACA,OACAH,YAAA,KACA/B,gBAAA,EACAE,oBAAA,GACAa,YAAA,EACAU,SAAA,OACAC,UAAA,GACAS,kBAAA,EACAC,YAAA,GACA5B,UAAA,GACA6B,oBAAA,KACAC,mBAAA,KACA5B,UAAA,EACAG,gBAAA,EACAW,cAAA,EACAe,YAAA,EACAT,OAAA,EACAU,cAAA,KACAZ,kBAAA,EACAa,gBAAA,EACAC,qBAAA,EAEA,EACAC,SAAA,CACAvB,cACA,YAAAK,UAAA,KAAAA,SAAAmB,OAAA,EACA,KAAAnB,SAAAoB,OAAA,GAAAC,cACA,GACA,GAEAC,MAAA,CACA,OAAAC,GACA,KAAAZ,YAAAY,EAAAC,KACA,aAAAD,EAAAC,KACA,KAAAb,YAAA,WACA,WAAAY,EAAAC,OACA,KAAAb,YAAA,SAEA,GAEAc,QAAA,CACA7B,SAAA8B,GACA,YAAAf,cAAAe,GACA,MAAAA,GAAA,gBAAAf,aACA,aAAAe,GAAA,KAAAf,YAAAgB,WAAA,aACA,WAAAD,GAAA,gBAAAf,aACA,UAAAe,GAAA,KAAAf,YAAAgB,WAAA,QACA,EAGAC,kBACA,KAAAd,YAAAe,OAAAC,WACA,KAAA7C,SAAA,KAAA6B,aAAA,IACA,KAAA7B,WACA,KAAAG,gBAAA,EACA,KAAAW,cAAA,EAEA,EAEAK,eACA,KAAA2B,gBACA,KAAAC,QAAAC,KAAA,CACAT,KAAA,YACAU,MAAA,CAAAC,UAAA,iBAEA,EAEAjC,qBACA,KAAA6B,gBACA,KAAAC,QAAAC,KAAA,CACAT,KAAA,aACAU,MAAA,CAAAC,UAAA,aAEA,EAEA3C,0BAGA,GAFA,KAAAuC,iBAEA,KAAA5B,iBAEA,QAAAb,WAAA,CAKA,YAAAe,OASA,OAPA,KAAA5B,oBAAA,uBACA,KAAAF,gBAAA,OAGA6D,YAAA,KACA,KAAA/C,WAAA,aAAA8C,UAAA,mBACA,KAIA,SAAAnB,eAaA,OAZA,KAAAvC,oBAAA,iBACA,KAAAF,gBAAA,EACA,KAAA4B,kBAAA,OAGA,KAAAc,sBACA,KAAAA,qBAAA,EACAmB,YAAA,KACA,KAAAC,cACA,KAAAC,oBAAA,GACA,OAKA,KAAAnC,kBAAA,EACA,KAAAd,WAAA,YAEA,KAAA2B,gBAAA,EACA,KAAAb,kBAAA,CAlCA,MAFA,KAAAd,WAAA,SAqCA,EAEAkD,eACA,KAAA3B,sBAEA,KAAAA,oBAAAwB,YAAA,KACA,KAAAxB,oBAAA,OACA,IACA,EAEAd,SACA,KAAAiC,iBACAS,EAAAA,EAAAA,IAAA,yBAAAC,MAAAC,IACA,MAAAA,EAAAjC,KAAAkC,OACAC,OAAAC,KAAAC,EAAAA,EAAAA,OAAAC,SAAAC,IACAF,EAAAA,EAAAA,OAAAE,EAAA,KAEAC,EAAAA,EAAAA,MACA,KAAA3D,YAAA,EACA,KAAA0C,QAAAC,KAAA,UACA,IACAiB,OAAAC,OAEA,EAEAtD,oBACA,KAAAtB,iBACA,KAAAE,oBAAA,wBACA,KAAAF,gBAAA,EACA,KAAAwD,gBACA,EAEA1C,WAAAmC,EAAAU,EAAA,IACA,KAAAvB,aAAA,KAAAA,cAAAa,GACA,KAAAX,mBAAA,KAAAF,YAEA,KAAAyC,WAAA,KACA,MAAAC,EAAAC,SAAAC,iBAAA,2CACAF,EAAAN,SAAAS,KACAA,EAAAC,UAAAC,SAAA,WACA,WAAAlC,GAAAgC,EAAAC,UAAAC,SAAA,gBACAF,EAAAC,UAAAC,SAAA,iBACAF,EAAAC,UAAAE,IAAA,eAEAvB,YAAA,KACAoB,EAAAC,UAAAG,OAAA,iBACA,KACA,IAGA,KAAAjD,YAAAa,CAAA,KAGA,KAAAb,YAAAa,EAGA,KAAAO,gBAEA,KAAA8B,OAAArC,OAAAA,GAAAsC,KAAAC,UAAA,KAAAF,OAAA3B,SAAA4B,KAAAC,UAAA7B,KAIA,KAAAF,QAAAC,KAAA,CACAT,KAAAA,EACAU,MAAAA,IAGAL,OAAAmC,SAAA,CACAC,IAAA,EACAC,SAAA,YAEA,EAEAC,mBACA,QAAAC,MAAAC,QAAA,CACA,MAAAC,EAAA,KAAAF,MAAAC,QAAAE,wBACAD,EAAAxF,OAAA,IACA,KAAAC,UAAAuF,EAAAxF,OAEA,CACA,EAEA0F,qBAEA,KAAAlF,YAAA,GACA2D,EAAAA,EAAAA,MACAL,OAAAC,KAAAC,EAAAA,EAAAA,OAAAC,SAAAC,IACAF,EAAAA,EAAAA,OAAAE,EAAA,IAIA,KAAAvE,oBAAA,yBACA,KAAAF,gBAAA,EAIA6D,YAAA,KACA,KAAAJ,QAAAC,KAAA,YACA,IACA,EAEAwC,yBACA,YAAAnB,SAAAoB,iBAAA,KAAApF,YAEA,KAAA+C,aAEA,EACAA,eAEAG,EAAAA,EAAAA,IAAA,0BAAAC,MAAAC,IACA,SAAAA,EAAAjC,KAAAkC,KAAA,CAkBA,GAjBA,KAAA3C,SAAA0C,EAAAjC,KAAAA,KAAAkE,UAAA,cACA7B,EAAAA,EAAAA,IAAA,WAAAJ,EAAAjC,KAAAA,KAAAkE,UACA,KAAA1E,UAAAyC,EAAAjC,KAAAA,KAAAmE,SACA9B,EAAAA,EAAAA,IAAA,YAAAJ,EAAAjC,KAAAA,KAAAmE,UACA,KAAAC,UAAAnC,EAAAjC,KAAAA,KAAAqE,OAAA,mBACAhC,EAAAA,EAAAA,IAAA,YAAAJ,EAAAjC,KAAAA,KAAAqE,OACA,KAAAC,SAAArC,EAAAjC,KAAAA,KAAAsE,UAAA,aACAjC,EAAAA,EAAAA,IAAA,WAAAJ,EAAAjC,KAAAA,KAAAsE,UACA,KAAAC,OAAAtC,EAAAjC,KAAAA,KAAAwE,IAAA,aACAnC,EAAAA,EAAAA,IAAA,SAAAJ,EAAAjC,KAAAA,KAAAwE,IACA,KAAA3E,YAAAoC,EAAAjC,KAAAA,KAAAyE,QACApC,EAAAA,EAAAA,IAAA,cAAAJ,EAAAjC,KAAAA,KAAAyE,SACA,KAAA7E,OAAAqC,EAAAjC,KAAAA,KAAAJ,OACAyC,EAAAA,EAAAA,IAAA,cAAAzC,QACA,KAAAf,YAAA,EAGAwD,EAAAA,EAAAA,IAAA,eAEA,QAEAqC,EAAAA,EAAAA,IAAA,mBACAC,cAAAtC,EAAAA,EAAAA,IAAA,UACAuC,QAAAvC,EAAAA,EAAAA,IAAA,iBACAL,MAAAC,IAEAI,EAAAA,EAAAA,IAAA,cAAAJ,EAAAjC,KAAAA,KAAA6E,UACAxC,EAAAA,EAAAA,IAAA,gBAAAJ,EAAAjC,KAAAA,KAAA8E,MAAA,GAGA,MACA,KAAAf,oBACA,GAEA,EAEAtF,mBACA,KAAAE,gBAAA,KAAAA,eACA,KAAAA,iBACA,KAAAW,cAAA,EAEA,EAEAN,iBACA,KAAAM,cAAA,KAAAA,aACA,KAAAA,eACA,KAAAX,gBAAA,EAEA,EAEA2C,gBACA,KAAA3C,gBAAA,EACA,KAAAW,cAAA,CACA,EAEAuC,qBACA,KAAAvB,cAAAyE,aAAA,KAEA,MAAAD,EAAA,KAAAE,UAAA,iBACAC,EAAA,KAAAD,UAAA,eACAF,GAAAG,IACAC,cAAA,KAAA5E,eACA,KAAAA,cAAA,KACA,KAAAC,gBAAA,EACA,KAAAb,kBAAA,EACA,GACA,IACA,EAEAsF,UAAAtH,GACA,MAAAyH,EAAAtC,SAAAuC,OAAAD,MAAA,IAAAE,OAAA,QAAA3H,EAAA,aACA,OAAAyH,EAAAA,EAAA,OACA,GAGAG,UACAlE,OAAAmE,iBAAA,cAAAzD,aAAA,CAAA0D,SAAA,IACApE,OAAAmE,iBAAA,cAAApE,iBAGA,MAAA2D,GAAAW,EAAAA,EAAAA,MACA,KAAA5G,aAAAiG,EAGAA,IACA,KAAAvF,SAAA8C,EAAAA,EAAAA,IAAA,oBACA,KAAA7C,UAAA6C,EAAAA,EAAAA,IAAA,iBACA,KAAAxC,YAAA6F,WAAArD,EAAAA,EAAAA,IAAA,mBACA,KAAAzC,OAAA+F,SAAAtD,EAAAA,EAAAA,IAAA,cAGA,KAAAT,eAIAiB,SAAA0C,iBAAA,wBAAAvB,wBAEA,KAAA9D,YAAA,KAAAkD,OAAArC,KACA,KAAAI,iBACA,EAEAyE,UACA,KAAAjD,WAAA,KAGA,IAAAkD,EAFA,KAAAnC,mBAGAtC,OAAAmE,iBAAA,eACAO,aAAAD,GACAA,EAAAlE,YAAA,KACA,KAAA+B,kBAAA,GACA,OACA,CAAA8B,SAAA,IAEA,KAAA3D,oBAAA,GAEA,EAEAkE,gBACA3E,OAAA4E,oBAAA,cAAAlE,cACAV,OAAA4E,oBAAA,cAAAtC,kBACAtC,OAAA4E,oBAAA,cAAA7E,iBACA0B,SAAAmD,oBAAA,wBAAAhC,wBACA8B,aAAA,KAAA3F,oBACA,GCtnBwQ,I,UCQpQtC,GAAY,OACd,EACAX,EACAO,GACA,EACA,KACA,WACA,MAIF,EAAeI,EAAiB,O,uDCnBhC,IAAIX,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,aAAa,CAACU,MAAM,CAAC,KAAO,UAAU,CAAEZ,EAAI8I,QAAS5I,EAAG,MAAM,CAACqB,MAAM,CAAC,eAAiB,gBAAevB,EAAI+I,QAAQ9H,MAAO,CAAE+H,UAAWhJ,EAAIgJ,WAAapI,MAAM,CAAC,KAAO,UAAU,CAACV,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAAe,UAAbJ,EAAI+I,KAAkB7I,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,OAAO,CAACE,YAAY,cAAc,CAACJ,EAAI2B,GAAG,SAAS3B,EAAIgB,KAAKd,EAAG,OAAO,CAACE,YAAY,WAAW,CAACJ,EAAI2B,GAAG3B,EAAI8B,GAAG9B,EAAIiJ,YAAajJ,EAAIkJ,SAAUhJ,EAAG,SAAS,CAACE,YAAY,YAAYU,GAAG,CAAC,MAAQd,EAAImJ,QAAQ,CAACnJ,EAAI2B,GAAG,OAAO3B,EAAIgB,SAAShB,EAAIgB,MAC/kB,EACIV,EAAkB,GCgBtB,GACAC,KAAA,oBACA6I,MAAA,CACAH,QAAA,CACAF,KAAAM,OACAC,UAAA,GAEAP,KAAA,CACAA,KAAAM,OACAE,QAAA,OACAC,UAAAC,GAAA,qCAAAC,SAAAD,IAEAE,SAAA,CACAZ,KAAAa,OACAL,QAAA,KAEAL,SAAA,CACAH,KAAAc,QACAN,SAAA,GAEAP,UAAA,CACAD,KAAAM,OACAE,QAAA,SAGA1G,OACA,OACAiG,SAAA,EACAgB,MAAA,KAEA,EACAjG,QAAA,CACAkG,OACA,KAAAvE,WAAA,KACA,KAAAsD,SAAA,EACA,KAAAkB,YAAA,GAEA,EACAb,QACA,KAAAL,SAAA,EACA,KAAAmB,MAAA,QACA,EACAD,aACA,KAAAL,SAAA,IACAhB,aAAA,KAAAmB,OACA,KAAAA,MAAAtF,YAAA,KACA,KAAA2E,OAAA,GACA,KAAAQ,UAEA,GAEAlB,UACAjE,YAAA,KACA,KAAAuF,MAAA,GACA,IACA,EACAG,gBACAvB,aAAA,KAAAmB,MACA,GC5EmR,I,UCQ/QpJ,GAAY,OACd,EACAX,EACAO,GACA,EACA,KACA,WACA,MAIF,EAAeI,EAAiB,O,wMCdhC,IAAIyJ,EAAO,OAOJ,MAAMvF,EAAYA,CAACwF,EAAIC,KACnBC,EAAAA,EAAAA,GAAM,CACTC,QAAS,CACL,cAAiBrF,EAAAA,EAAAA,IAAY,eAC7B,eAAgB,qCAEpBsF,OAAO,OACPJ,IAAK,GAAED,IAAOC,IACdvH,KAAMwH,IAGD9C,EAAcA,CAAC6C,EAAIK,KACrBH,EAAAA,EAAAA,GAAM,CACTC,QAAQ,CACJ,cAAiBrF,EAAAA,EAAAA,IAAY,eAC7B,eAAgB,kCAEpBsF,OAAO,OACPJ,IAAK,GAAED,IAAOC,IACdvH,KAAK4H,IAIAC,EAAUA,CAACN,EAAIC,KACjBC,EAAAA,EAAAA,GAAM,CACTC,QAAS,CACL,eAAgB,qCAEpBC,OAAQ,OACRJ,IAAM,GAAED,IAAOC,IACfvH,KAAMwH,IAGDM,EAAYA,CAACP,EAAIC,KACnBC,EAAAA,EAAAA,GAAM,CACTC,QAAS,CACL,eAAgB,kCAEpBC,OAAQ,OACRJ,IAAM,GAAED,IAAOC,IACfvH,KAAMwH,IAGDO,EAAWA,CAACR,EAAIC,KAClBC,EAAAA,EAAAA,GAAM,CACTC,QAAS,CACL,eAAgB,kCAEpBC,OAAQ,MACRJ,IAAM,GAAED,IAAOC,IACfC,OAAQA,IAIHQ,EAAWA,CAACT,EAAIC,KAClBC,EAAAA,EAAAA,GAAM,CACTC,QAAS,CACL,cAAiBrF,EAAAA,EAAAA,IAAY,eAC7B,eAAgB,qCAEpBsF,OAAO,MACPJ,IAAK,GAAED,IAAOC,IACdC,OAAQA,G,oCC1EZtK,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACU,MAAM,CAAC,GAAK,QAAQ,CAAEZ,EAAI8K,WAAY5K,EAAG,SAAS,CAAC6K,IAAI/K,EAAIgL,YAAYhL,EAAIgB,KAAKd,EAAG,cAAc,CAAC6K,IAAI/K,EAAIiG,OAAOgF,SAASnK,GAAG,CAAC,iBAAiBd,EAAIkL,cAAc,eAAelL,EAAImL,gBAAgB,EACjR,EACI7K,EAAkB,G,UCStB,GACAE,WAAA,CAAA4K,OAAAA,EAAAA,GACAvI,OACA,OACAmI,UAAA,EACAF,YAAA,EAEA,EACAjH,QAAA,CACAsH,cACA,KAAAL,YAAA,KAAAA,UACA,EAEAI,gBACAG,QAAAC,IAAA,OACA,KAAAN,WAAA,CACA,IC3B0O,I,UCQtOtK,GAAY,OACd,EACAX,EACAO,GACA,EACA,KACA,KACA,MAIF,EAAeI,EAAiB,Q,UChBhC6K,EAAAA,GAAAA,IAAQC,EAAAA,IAER,MAAMC,EAAS,CACX,CACI7H,KAAK,IACL8H,SAAS,UAEb,CACI9H,KAAM,SACNrD,KAAM,QACNG,UAAWA,IAAM,yDAGrB,CACIkD,KAAM,WACNrD,KAAM,UACNG,UAAWA,IAAM,gCAOrB,CACIkD,KAAM,WACNrD,KAAM,UACNG,UAAWA,IAAM,wDAErB,CACIkD,KAAM,sBACNrD,KAAM,qBACNG,UAAWA,IAAM,8BAErB,CACIkD,KAAM,QACNrD,KAAM,OACNG,UAAWA,IAAM,yDAErB,CACIkD,KAAM,uBACNrD,KAAM,cACNG,UAAWA,IAAM,yDAErB,CACIkD,KAAM,SACNrD,KAAM,QACNG,UAAWA,IAAM,8BAErB,CACIkD,KAAM,YACNrD,KAAM,WACNG,UAAWA,IAAM,gCAErB,CACIkD,KAAM,cACNrD,KAAM,aACNG,UAAWA,IAAM,gCAGrB,CACIkD,KAAM,SACNrD,KAAM,QACNG,UAAWA,IAAM,yDAErB,CACIkD,KAAM,QACN8H,SAAU,iBAEd,CACI9H,KAAM,cACNrD,KAAM,OACNG,UAAWA,IAAM,wDACjB0I,OAAO,GAEX,CACIxF,KAAM,SACNrD,KAAM,QACNG,UAAWA,IAAM,gCAErB,CACIkD,KAAM,YACNrD,KAAM,WACNG,UAAWA,IAAM,gCAErB,CACIkD,KAAM,aACNrD,KAAM,YACNG,UAAWA,IAAM,gCAErB,CACIkD,KAAM,WACNrD,KAAM,YACNG,UAAWA,IAAM,iCAUnBiL,EAAS,IAAIH,EAAAA,GAAU,CACzBC,WAGJ,Q,oBCxGA,IAAItB,EAAO,wBAGJ,MAAMyB,EAAWA,CAACxB,EAAIC,KAClBC,EAAAA,EAAAA,GAAM,CACTE,OAAO,MACPJ,IAAK,GAAED,IAAOC,IACdC,OAAQA,ICJhBkB,EAAAA,GAAAA,UAAAA,WAA2BK,EAE3BL,EAAAA,GAAAA,IAAQM,EAAAA,GAAO,CACbC,SAAU,aACVC,QAAS,IACTC,cAAc,IAEhBT,EAAAA,GAAAA,OAAAA,eAA2B,EAE3B,IAAIA,EAAAA,GAAI,CACNI,OAAM,EACN5L,OAAQkM,GAAKA,EAAEC,KACdC,OAAO,O,sHClBV,MAAMC,EAAW,cAEV,SAAS9D,IACZ,OAAOpD,EAAAA,EAAAA,IAAYkH,EACvB,CAEO,SAASC,EAAS1E,GACrB,OAAOzC,EAAAA,EAAAA,IAAYkH,EAAUzE,EACjC,CAEO,SAAStC,IACZ,OAAOH,EAAAA,EAAAA,OAAekH,EAC1B,C,GCbIE,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBE,IAAjBD,EACH,OAAOA,EAAaE,QAGrB,IAAIC,EAASN,EAAyBE,GAAY,CAGjDG,QAAS,CAAC,GAOX,OAHAE,EAAoBL,GAAUI,EAAQA,EAAOD,QAASJ,GAG/CK,EAAOD,OACf,CAGAJ,EAAoBO,EAAID,E,WCzBxB,IAAIE,EAAW,GACfR,EAAoBS,EAAI,SAASC,EAAQC,EAAUC,EAAIC,GACtD,IAAGF,EAAH,CAMA,IAAIG,EAAeC,IACnB,IAASC,EAAI,EAAGA,EAAIR,EAASxJ,OAAQgK,IAAK,CACrCL,EAAWH,EAASQ,GAAG,GACvBJ,EAAKJ,EAASQ,GAAG,GACjBH,EAAWL,EAASQ,GAAG,GAE3B,IAJA,IAGIC,GAAY,EACPC,EAAI,EAAGA,EAAIP,EAAS3J,OAAQkK,MACpB,EAAXL,GAAsBC,GAAgBD,IAAapI,OAAOC,KAAKsH,EAAoBS,GAAGU,OAAM,SAAS3C,GAAO,OAAOwB,EAAoBS,EAAEjC,GAAKmC,EAASO,GAAK,IAChKP,EAASS,OAAOF,IAAK,IAErBD,GAAY,EACTJ,EAAWC,IAAcA,EAAeD,IAG7C,GAAGI,EAAW,CACbT,EAASY,OAAOJ,IAAK,GACrB,IAAIK,EAAIT,SACET,IAANkB,IAAiBX,EAASW,EAC/B,CACD,CACA,OAAOX,CArBP,CAJCG,EAAWA,GAAY,EACvB,IAAI,IAAIG,EAAIR,EAASxJ,OAAQgK,EAAI,GAAKR,EAASQ,EAAI,GAAG,GAAKH,EAAUG,IAAKR,EAASQ,GAAKR,EAASQ,EAAI,GACrGR,EAASQ,GAAK,CAACL,EAAUC,EAAIC,EAwB/B,C,eC5BAb,EAAoBsB,EAAI,SAASlB,EAASmB,GACzC,IAAI,IAAI/C,KAAO+C,EACXvB,EAAoBwB,EAAED,EAAY/C,KAASwB,EAAoBwB,EAAEpB,EAAS5B,IAC5E/F,OAAOgJ,eAAerB,EAAS5B,EAAK,CAAEkD,YAAY,EAAMC,IAAKJ,EAAW/C,IAG3E,C,eCPAwB,EAAoB4B,EAAI,CAAC,EAGzB5B,EAAoB6B,EAAI,SAASC,GAChC,OAAOC,QAAQC,IAAIvJ,OAAOC,KAAKsH,EAAoB4B,GAAGK,QAAO,SAASC,EAAU1D,GAE/E,OADAwB,EAAoB4B,EAAEpD,GAAKsD,EAASI,GAC7BA,CACR,GAAG,IACJ,C,eCPAlC,EAAoBmC,EAAI,SAASL,GAEhC,MAAO,OAAS,CAAC,GAAK,SAAS,IAAM,SAAS,IAAM,SAAS,IAAM,SAAS,IAAM,SAAS,KAAO,SAAS,KAAO,QAAQ,KAAO,SAAS,KAAO,SAAS,KAAO,SAAS,KAAO,SAAS,KAAO,QAAQ,KAAO,QAAQ,KAAO,QAAQ,KAAO,SAAS,KAAO,QAAQ,KAAO,QAAQ,KAAO,QAAQ,KAAO,SAAS,KAAO,QAAQ,KAAO,SAAS,KAAO,SAAS,KAAO,QAAQ,KAAO,SAAS,KAAO,SAAS,KAAO,SAAS,KAAO,SAAS,KAAO,SAAS,KAAO,SAAS,KAAO,SAAS,KAAO,QAAQ,KAAO,SAAS,KAAO,UAAUA,IAAYA,GAAW,IAAM,CAAC,GAAK,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,YAAYA,GAAW,KACv6C,C,eCHA9B,EAAoBoC,SAAW,SAASN,GAEvC,MAAO,OAASA,EAAU,IAAM,CAAC,IAAM,WAAW,IAAM,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,WAAW,KAAO,YAAYA,GAAW,MAC1T,C,eCJA9B,EAAoBqC,EAAI,WACvB,GAA0B,kBAAfC,WAAyB,OAAOA,WAC3C,IACC,OAAO5O,MAAQ,IAAI6O,SAAS,cAAb,EAChB,CAAE,MAAOV,GACR,GAAsB,kBAAXnK,OAAqB,OAAOA,MACxC,CACA,CAPuB,E,eCAxBsI,EAAoBwB,EAAI,SAASgB,EAAKC,GAAQ,OAAOhK,OAAOiK,UAAUC,eAAeC,KAAKJ,EAAKC,EAAO,C,eCAtG,IAAII,EAAa,CAAC,EACdC,EAAoB,aAExB9C,EAAoB+C,EAAI,SAASlF,EAAKmF,EAAMxE,EAAKsD,GAChD,GAAGe,EAAWhF,GAAQgF,EAAWhF,GAAK/F,KAAKkL,OAA3C,CACA,IAAIC,EAAQC,EACZ,QAAW/C,IAAR3B,EAEF,IADA,IAAI2E,EAAUhK,SAASiK,qBAAqB,UACpCpC,EAAI,EAAGA,EAAImC,EAAQnM,OAAQgK,IAAK,CACvC,IAAIqC,EAAIF,EAAQnC,GAChB,GAAGqC,EAAEC,aAAa,QAAUzF,GAAOwF,EAAEC,aAAa,iBAAmBR,EAAoBtE,EAAK,CAAEyE,EAASI,EAAG,KAAO,CACpH,CAEGJ,IACHC,GAAa,EACbD,EAAS9J,SAASoK,cAAc,UAEhCN,EAAOO,QAAU,QACjBP,EAAOzD,QAAU,IACbQ,EAAoByD,IACvBR,EAAOS,aAAa,QAAS1D,EAAoByD,IAElDR,EAAOS,aAAa,eAAgBZ,EAAoBtE,GACxDyE,EAAOU,IAAM9F,GAEdgF,EAAWhF,GAAO,CAACmF,GACnB,IAAIY,EAAmB,SAASC,EAAMC,GAErCb,EAAOc,QAAUd,EAAOe,OAAS,KACjC5H,aAAaoD,GACb,IAAIyE,EAAUpB,EAAWhF,GAIzB,UAHOgF,EAAWhF,GAClBoF,EAAOiB,YAAcjB,EAAOiB,WAAWC,YAAYlB,GACnDgB,GAAWA,EAAQrL,SAAQ,SAASgI,GAAM,OAAOA,EAAGkD,EAAQ,IACzDD,EAAM,OAAOA,EAAKC,EACtB,EACItE,EAAUvH,WAAW2L,EAAiBQ,KAAK,UAAMjE,EAAW,CAAE3D,KAAM,UAAW6H,OAAQpB,IAAW,MACtGA,EAAOc,QAAUH,EAAiBQ,KAAK,KAAMnB,EAAOc,SACpDd,EAAOe,OAASJ,EAAiBQ,KAAK,KAAMnB,EAAOe,QACnDd,GAAc/J,SAASmL,KAAKC,YAAYtB,EAnCkB,CAoC3D,C,eCvCAjD,EAAoBqB,EAAI,SAASjB,GACX,qBAAXoE,QAA0BA,OAAOC,aAC1ChM,OAAOgJ,eAAerB,EAASoE,OAAOC,YAAa,CAAEvH,MAAO,WAE7DzE,OAAOgJ,eAAerB,EAAS,aAAc,CAAElD,OAAO,GACvD,C,eCNA8C,EAAoB0E,EAAI,U,eCAxB,GAAwB,qBAAbvL,SAAX,CACA,IAAIwL,EAAmB,SAAS7C,EAAS8C,EAAUC,EAAQC,EAASC,GACnE,IAAIC,EAAU7L,SAASoK,cAAc,QAErCyB,EAAQC,IAAM,aACdD,EAAQxI,KAAO,WACf,IAAI0I,EAAiB,SAASpB,GAG7B,GADAkB,EAAQjB,QAAUiB,EAAQhB,OAAS,KAChB,SAAfF,EAAMtH,KACTsI,QACM,CACN,IAAIK,EAAYrB,IAAyB,SAAfA,EAAMtH,KAAkB,UAAYsH,EAAMtH,MAChE4I,EAAWtB,GAASA,EAAMO,QAAUP,EAAMO,OAAOgB,MAAQT,EACzD5L,EAAM,IAAIsM,MAAM,qBAAuBxD,EAAU,cAAgBsD,EAAW,KAChFpM,EAAIR,KAAO,wBACXQ,EAAIwD,KAAO2I,EACXnM,EAAIuM,QAAUH,EACVJ,EAAQd,YAAYc,EAAQd,WAAWC,YAAYa,GACvDD,EAAO/L,EACR,CACD,EASA,OARAgM,EAAQjB,QAAUiB,EAAQhB,OAASkB,EACnCF,EAAQK,KAAOT,EAEXC,EACHA,EAAOX,WAAWsB,aAAaR,EAASH,EAAOY,aAE/CtM,SAASmL,KAAKC,YAAYS,GAEpBA,CACR,EACIU,EAAiB,SAASL,EAAMT,GAEnC,IADA,IAAIe,EAAmBxM,SAASiK,qBAAqB,QAC7CpC,EAAI,EAAGA,EAAI2E,EAAiB3O,OAAQgK,IAAK,CAChD,IAAI4E,EAAMD,EAAiB3E,GACvB6E,EAAWD,EAAItC,aAAa,cAAgBsC,EAAItC,aAAa,QACjE,GAAe,eAAZsC,EAAIX,MAAyBY,IAAaR,GAAQQ,IAAajB,GAAW,OAAOgB,CACrF,CACA,IAAIE,EAAoB3M,SAASiK,qBAAqB,SACtD,IAAQpC,EAAI,EAAGA,EAAI8E,EAAkB9O,OAAQgK,IAAK,CAC7C4E,EAAME,EAAkB9E,GACxB6E,EAAWD,EAAItC,aAAa,aAChC,GAAGuC,IAAaR,GAAQQ,IAAajB,EAAU,OAAOgB,CACvD,CACD,EACIG,EAAiB,SAASjE,GAC7B,OAAO,IAAIC,SAAQ,SAAS+C,EAASC,GACpC,IAAIM,EAAOrF,EAAoBoC,SAASN,GACpC8C,EAAW5E,EAAoB0E,EAAIW,EACvC,GAAGK,EAAeL,EAAMT,GAAW,OAAOE,IAC1CH,EAAiB7C,EAAS8C,EAAU,KAAME,EAASC,EACpD,GACD,EAEIiB,EAAqB,CACxB,KAAM,GAGPhG,EAAoB4B,EAAEqE,QAAU,SAASnE,EAASI,GACjD,IAAIgE,EAAY,CAAC,IAAM,EAAE,IAAM,EAAE,KAAO,EAAE,KAAO,EAAE,KAAO,EAAE,KAAO,EAAE,KAAO,EAAE,KAAO,EAAE,KAAO,EAAE,KAAO,EAAE,KAAO,EAAE,KAAO,EAAE,KAAO,EAAE,KAAO,EAAE,KAAO,GACjJF,EAAmBlE,GAAUI,EAASpK,KAAKkO,EAAmBlE,IACzB,IAAhCkE,EAAmBlE,IAAkBoE,EAAUpE,IACtDI,EAASpK,KAAKkO,EAAmBlE,GAAWiE,EAAejE,GAASxJ,MAAK,WACxE0N,EAAmBlE,GAAW,CAC/B,IAAG,SAASD,GAEX,aADOmE,EAAmBlE,GACpBD,CACP,IAEF,CAtE2C,C,eCA3C7B,EAAoBmG,EAAIhN,SAASiN,SAAWC,KAAKC,SAASjB,KAK1D,IAAIkB,EAAkB,CACrB,KAAM,GAGPvG,EAAoB4B,EAAEV,EAAI,SAASY,EAASI,GAE1C,IAAIsE,EAAqBxG,EAAoBwB,EAAE+E,EAAiBzE,GAAWyE,EAAgBzE,QAAW3B,EACtG,GAA0B,IAAvBqG,EAGF,GAAGA,EACFtE,EAASpK,KAAK0O,EAAmB,QAC3B,CAGL,IAAIC,EAAU,IAAI1E,SAAQ,SAAS+C,EAASC,GAAUyB,EAAqBD,EAAgBzE,GAAW,CAACgD,EAASC,EAAS,IACzH7C,EAASpK,KAAK0O,EAAmB,GAAKC,GAGtC,IAAI5I,EAAMmC,EAAoB0E,EAAI1E,EAAoBmC,EAAEL,GAEpD4E,EAAQ,IAAIpB,MACZqB,EAAe,SAAS7C,GAC3B,GAAG9D,EAAoBwB,EAAE+E,EAAiBzE,KACzC0E,EAAqBD,EAAgBzE,GACX,IAAvB0E,IAA0BD,EAAgBzE,QAAW3B,GACrDqG,GAAoB,CACtB,IAAIrB,EAAYrB,IAAyB,SAAfA,EAAMtH,KAAkB,UAAYsH,EAAMtH,MAChEoK,EAAU9C,GAASA,EAAMO,QAAUP,EAAMO,OAAOV,IACpD+C,EAAMhK,QAAU,iBAAmBoF,EAAU,cAAgBqD,EAAY,KAAOyB,EAAU,IAC1FF,EAAM1S,KAAO,iBACb0S,EAAMlK,KAAO2I,EACbuB,EAAMnB,QAAUqB,EAChBJ,EAAmB,GAAGE,EACvB,CAEF,EACA1G,EAAoB+C,EAAElF,EAAK8I,EAAc,SAAW7E,EAASA,EAE/D,CAEH,EAUA9B,EAAoBS,EAAES,EAAI,SAASY,GAAW,OAAoC,IAA7ByE,EAAgBzE,EAAgB,EAGrF,IAAI+E,EAAuB,SAASC,EAA4BxQ,GAC/D,IAKI2J,EAAU6B,EALVnB,EAAWrK,EAAK,GAChByQ,EAAczQ,EAAK,GACnB0Q,EAAU1Q,EAAK,GAGI0K,EAAI,EAC3B,GAAGL,EAASsG,MAAK,SAASnM,GAAM,OAA+B,IAAxByL,EAAgBzL,EAAW,IAAI,CACrE,IAAImF,KAAY8G,EACZ/G,EAAoBwB,EAAEuF,EAAa9G,KACrCD,EAAoBO,EAAEN,GAAY8G,EAAY9G,IAGhD,GAAG+G,EAAS,IAAItG,EAASsG,EAAQhH,EAClC,CAEA,IADG8G,GAA4BA,EAA2BxQ,GACrD0K,EAAIL,EAAS3J,OAAQgK,IACzBc,EAAUnB,EAASK,GAChBhB,EAAoBwB,EAAE+E,EAAiBzE,IAAYyE,EAAgBzE,IACrEyE,EAAgBzE,GAAS,KAE1ByE,EAAgBzE,GAAW,EAE5B,OAAO9B,EAAoBS,EAAEC,EAC9B,EAEIwG,EAAqBb,KAAK,yBAA2BA,KAAK,0BAA4B,GAC1Fa,EAAmBtO,QAAQiO,EAAqBzC,KAAK,KAAM,IAC3D8C,EAAmBpP,KAAO+O,EAAqBzC,KAAK,KAAM8C,EAAmBpP,KAAKsM,KAAK8C,G,ICpFvF,IAAIC,EAAsBnH,EAAoBS,OAAEN,EAAW,CAAC,OAAO,WAAa,OAAOH,EAAoB,KAAO,IAClHmH,EAAsBnH,EAAoBS,EAAE0G,E", "sources": ["webpack://portal-ui/./src/components/common/Layout-header.vue", "webpack://portal-ui/src/components/common/Layout-header.vue", "webpack://portal-ui/./src/components/common/Layout-header.vue?6a7e", "webpack://portal-ui/./src/components/common/Layout-header.vue?ad77", "webpack://portal-ui/./src/components/common/header/Header.vue", "webpack://portal-ui/src/components/common/header/Header.vue", "webpack://portal-ui/./src/components/common/header/Header.vue?38db", "webpack://portal-ui/./src/components/common/header/Header.vue?ef70", "webpack://portal-ui/./src/components/common/header/SlideNotification.vue", "webpack://portal-ui/src/components/common/header/SlideNotification.vue", "webpack://portal-ui/./src/components/common/header/SlideNotification.vue?ef1a", "webpack://portal-ui/./src/components/common/header/SlideNotification.vue?353c", "webpack://portal-ui/./src/api/login.js", "webpack://portal-ui/./src/App.vue", "webpack://portal-ui/src/App.vue", "webpack://portal-ui/./src/App.vue?51dd", "webpack://portal-ui/./src/App.vue?0e40", "webpack://portal-ui/./src/router/index.js", "webpack://portal-ui/./src/api/api.js", "webpack://portal-ui/./src/main.js", "webpack://portal-ui/./src/utils/auth.js", "webpack://portal-ui/webpack/bootstrap", "webpack://portal-ui/webpack/runtime/chunk loaded", "webpack://portal-ui/webpack/runtime/define property getters", "webpack://portal-ui/webpack/runtime/ensure chunk", "webpack://portal-ui/webpack/runtime/get javascript chunk filename", "webpack://portal-ui/webpack/runtime/get mini-css chunk filename", "webpack://portal-ui/webpack/runtime/global", "webpack://portal-ui/webpack/runtime/hasOwnProperty shorthand", "webpack://portal-ui/webpack/runtime/load script", "webpack://portal-ui/webpack/runtime/make namespace object", "webpack://portal-ui/webpack/runtime/publicPath", "webpack://portal-ui/webpack/runtime/css loading", "webpack://portal-ui/webpack/runtime/jsonp chunk loading", "webpack://portal-ui/webpack/startup"], "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('main',{staticClass:\"page-wrapper\"},[_c('Header'),_c('div',{staticClass:\"main-content\"},[_vm._t(\"default\")],2),_c('Footer')],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n\t<main class=\"page-wrapper\">\r\n\t\t<Header/>\r\n\r\n\t\t<div class=\"main-content\">\r\n\t\t\t<slot></slot>\r\n\t\t</div>\r\n\t\r\n\t\t<Footer/>\r\n\t</main>\r\n</template>\r\n\r\n<script>\r\nimport Header from \"@/components/common/header/Header\";\r\n// import Footer from \"@/components/common/footer/Footer\";\r\n\r\nexport default {\r\n\tname: \"Layout\",\r\n\tcomponents:{Header}\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.main-content{\r\n\twidth: 100%;\r\n  max-width: 2560px;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Layout-header.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Layout-header.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Layout-header.vue?vue&type=template&id=18a49a4c&scoped=true&\"\nimport script from \"./Layout-header.vue?vue&type=script&lang=js&\"\nexport * from \"./Layout-header.vue?vue&type=script&lang=js&\"\nimport style0 from \"./Layout-header.vue?vue&type=style&index=0&id=18a49a4c&prod&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"18a49a4c\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"header-wrapper\"},[(_vm.showComingSoon)?_c('SlideNotification',{attrs:{\"message\":_vm.notificationMessage,\"type\":\"warning\",\"duration\":2000},on:{\"close\":function($event){_vm.showComingSoon = false}}}):_vm._e(),_c('div',{staticClass:\"nav-placeholder\",style:({ height: _vm.navHeight + 'px' })}),_c('div',{ref:\"mainNav\",staticClass:\"main-nav\"},[_c('div',{staticClass:\"container\"},[(!_vm.isMobile)?_c('div',{staticClass:\"nav-container desktop-nav\"},[_c('div',{staticClass:\"logo-area\"},[_c('a',{staticClass:\"logo-link\",on:{\"click\":function($event){return _vm.navigateTo('/')}}},[_c('img',{attrs:{\"src\":\"images/logo-tiangong.png\",\"alt\":\"算力租赁\",\"loading\":\"eager\"}})])]),_c('div',{staticClass:\"nav-menu\"},[_c('ul',{staticClass:\"nav-list\"},[_c('li',{staticClass:\"nav-item\"},[_c('a',{staticClass:\"nav-link\",class:{'active': _vm.isActive('/')},on:{\"click\":function($event){return _vm.navigateTo('/')}}},[_vm._v(\"首页\")])]),_c('li',{staticClass:\"nav-item dropdown\"},[_c('a',{staticClass:\"nav-link\",class:{'active': _vm.isActive('/product')},on:{\"click\":function($event){return _vm.navigateTo('/product')}}},[_vm._v(\" 算力市场\"),_c('i',{staticClass:\"iconfont icon-arrow-down\"})])]),_c('li',{staticClass:\"nav-item dropdown\"},[_c('a',{staticClass:\"nav-link\",class:{'active': false},on:{\"click\":_vm.triggerComingSoon}},[_vm._v(\"算法社区\"),_c('i',{staticClass:\"iconfont icon-arrow-down\"})])]),_c('li',{staticClass:\"nav-item\"},[_c('a',{staticClass:\"nav-link\",class:{'active': _vm.isActive('/about')},on:{\"click\":function($event){return _vm.navigateTo('/about')}}},[_vm._v(\"关于我们\")])])])]),_c('div',{staticClass:\"user-actions\"},[(!_vm.isLoggedIn)?_c('div',{staticClass:\"auth-buttons\"},[_c('a',{staticClass:\"btn btn-login\",class:{'active': _vm.isActive('/help')},on:{\"click\":function($event){return _vm.navigateTo('/help')}}},[_vm._v(\"帮助文档\")]),_c('a',{staticClass:\"btn btn-login\",class:{'active': _vm.isActive('/login')},on:{\"click\":function($event){return _vm.navigateTo('/login')}}},[_vm._v(\"控制台\")]),_c('a',{staticClass:\"btn btn-login\",class:{'active': _vm.isActive('/login')},on:{\"click\":function($event){return _vm.navigateTo('/login')}}},[_vm._v(\"登录\")]),_c('a',{staticClass:\"btn btn-register\",class:{'active': _vm.isActive('/register')},on:{\"click\":function($event){return _vm.navigateTo('/register')}}},[_vm._v(\"立即注册\")])]):_vm._e(),(_vm.isLoggedIn)?_c('div',{staticClass:\"user-profile\"},[_c('a',{staticClass:\"btn btn-login\",class:{'active': _vm.isActive('/help')},on:{\"click\":function($event){return _vm.navigateTo('/help')}}},[_vm._v(\"帮助文档\")]),_c('a',{staticClass:\"btn btn-login\",class:{'active': _vm.isActive('/console'),'disabled': _vm.isConsoleLoading },attrs:{\"title\":_vm.isConsoleLoading ? '控制台加载中，请稍后...' : ''},on:{\"click\":_vm.handleConsoleNavigation}},[_vm._v(\"控制台\")]),_c('div',{staticClass:\"user-dropdown\"},[_c('div',{staticClass:\"user-avatar\"},[_c('div',{staticClass:\"avatar-letter\"},[_vm._v(_vm._s(_vm.userInitial))])]),_c('div',{staticClass:\"dropdown-menu\"},[_c('div',{staticClass:\"user-info-section\"},[_c('div',{staticClass:\"detail-item\"},[_c('span',{staticClass:\"label\"},[_vm._v(\"用户名:\")]),_c('span',{staticClass:\"value\"},[_vm._v(_vm._s(_vm.userName))])]),_c('div',{staticClass:\"detail-item\"},[_c('span',{staticClass:\"label\"},[_vm._v(\"手机号:\")]),_c('span',{staticClass:\"value\"},[_c('i',{staticClass:\"copy-icon\"}),_vm._v(\" \"+_vm._s(_vm.userPhone))])]),_c('div',{staticClass:\"verification-tag\",on:{\"click\":_vm.gotoPersonal}},[_c('span',{staticClass:\"check-icon\"}),_vm._v(\" 个人认证 \"),_c('span',{staticClass:\"status-text\"},[_vm._v(\"(\"+_vm._s(_vm.isReal === 1 ? '已认证' : '未认证')+\")\")])]),_c('div',{staticClass:\"detail-item\"},[_c('span',{staticClass:\"label\"},[_vm._v(\"可用余额:\")]),_c('span',{staticClass:\"value\"},[_vm._v(\"￥\"+_vm._s(_vm.userBalance.toFixed(2)))]),_c('div',{staticClass:\"verification-tag recharge-btn\",on:{\"click\":_vm.navigateToRecharge}},[_vm._v(\" 充值 \")])])]),_c('div',{staticClass:\"menu-options\"},[_c('a',{class:{'active': _vm.isActive('/personal')},on:{\"click\":function($event){return _vm.navigateTo('/personal')}}},[_vm._v(\"个人中心\")]),_c('a',{class:{'active': _vm.isActive('/userorder')},on:{\"click\":function($event){return _vm.navigateTo('/userorder')}}},[_vm._v(\"费用中心\")])]),_c('div',{staticClass:\"logout-button-container\"},[_c('button',{staticClass:\"logout-button\",on:{\"click\":_vm.logout}},[_vm._v(\"退出登录\")])])])])]):_vm._e()])]):_c('div',{staticClass:\"mobile-nav\"},[_c('div',{staticClass:\"mobile-nav-container\"},[_c('button',{staticClass:\"hamburger-btn\",on:{\"click\":_vm.toggleMobileMenu}},[_c('span',{staticClass:\"hamburger-line\",class:{'line-1': _vm.mobileMenuOpen}}),_c('span',{staticClass:\"hamburger-line\",class:{'line-2': _vm.mobileMenuOpen}}),_c('span',{staticClass:\"hamburger-line\",class:{'line-3': _vm.mobileMenuOpen}})]),_c('div',{staticClass:\"mobile-logo-area\"},[_c('a',{staticClass:\"logo-link\",on:{\"click\":function($event){return _vm.navigateTo('/')}}},[_c('img',{attrs:{\"src\":\"images/logo_tiangong.png\",\"alt\":\"算力租赁\"}})])]),_c('div',{staticClass:\"mobile-user-actions\"},[(!_vm.isLoggedIn)?[_c('a',{staticClass:\"mobile-login-btn\",on:{\"click\":function($event){return _vm.navigateTo('/login')}}},[_vm._v(\" 登录 \")]),_c('a',{staticClass:\"mobile-register-btn\",on:{\"click\":function($event){return _vm.navigateTo('/register')}}},[_vm._v(\" 注册 \")])]:[_c('a',{staticClass:\"mobile-console-btn\",on:{\"click\":function($event){return _vm.navigateTo('/help')}}},[_vm._v(\" 帮助文档 \")]),_c('a',{staticClass:\"mobile-console-btn\",on:{\"click\":_vm.handleConsoleNavigation}},[_vm._v(\" 控制台 \")]),_c('div',{staticClass:\"mobile-user-profile\",on:{\"click\":_vm.toggleUserMenu}},[_c('div',{staticClass:\"mobile-user-avatar\"},[_c('div',{staticClass:\"avatar-letter\"},[_vm._v(_vm._s(_vm.userInitial))])])])]],2)]),_c('div',{staticClass:\"mobile-menu\",class:{'open': _vm.mobileMenuOpen}},[_c('div',{staticClass:\"mobile-menu-content\"},[_c('ul',{staticClass:\"mobile-nav-list\"},[_c('li',{staticClass:\"mobile-nav-item\"},[_c('a',{staticClass:\"mobile-nav-link\",class:{'active': _vm.isActive('/')},on:{\"click\":function($event){return _vm.navigateTo('/')}}},[_c('i',{staticClass:\"iconfont icon-home\"}),_vm._v(\"首页 \")])]),_c('li',{staticClass:\"mobile-nav-item\"},[_c('a',{staticClass:\"mobile-nav-link\",class:{'active': _vm.isActive('/product')},on:{\"click\":function($event){return _vm.navigateTo('/product')}}},[_c('i',{staticClass:\"iconfont icon-server\"}),_vm._v(\"算力市场 \")])]),_c('li',{staticClass:\"mobile-nav-item\"},[_c('a',{staticClass:\"mobile-nav-link\",on:{\"click\":_vm.triggerComingSoon}},[_c('i',{staticClass:\"iconfont icon-community\"}),_vm._v(\"算法社区 \")])]),_c('li',{staticClass:\"mobile-nav-item\"},[_c('a',{staticClass:\"mobile-nav-link\",class:{'active': _vm.isActive('/about')},on:{\"click\":function($event){return _vm.navigateTo('/about')}}},[_c('i',{staticClass:\"iconfont icon-info\"}),_vm._v(\"关于我们 \")])]),(!_vm.isLoggedIn)?[_c('li',{staticClass:\"mobile-nav-item\"},[_c('a',{staticClass:\"mobile-nav-link\",class:{'active': _vm.isActive('/help')},on:{\"click\":function($event){return _vm.navigateTo('/help')}}},[_c('i',{staticClass:\"iconfont icon-docs\"}),_vm._v(\"帮助文档 \")])]),_c('li',{staticClass:\"mobile-nav-item\"},[_c('a',{staticClass:\"mobile-nav-link\",class:{'active': _vm.isActive('/login')},on:{\"click\":function($event){return _vm.navigateTo('/login')}}},[_c('i',{staticClass:\"iconfont icon-user\"}),_vm._v(\"登录 \")])]),_c('li',{staticClass:\"mobile-nav-item\"},[_c('a',{staticClass:\"mobile-nav-link\",class:{'active': _vm.isActive('/register')},on:{\"click\":function($event){return _vm.navigateTo('/register')}}},[_c('i',{staticClass:\"iconfont icon-edit\"}),_vm._v(\"注册 \")])]),_c('li',{staticClass:\"mobile-nav-item\"},[_c('a',{staticClass:\"mobile-nav-link\",class:{'active': _vm.isActive('/console')},on:{\"click\":function($event){return _vm.navigateTo('/login')}}},[_c('i',{staticClass:\"iconfont icon-console\"}),_vm._v(\"控制台 \")])])]:[_c('li',{staticClass:\"mobile-nav-item\"},[_c('a',{staticClass:\"mobile-nav-link\",class:{'active': _vm.isActive('/help')},on:{\"click\":function($event){return _vm.navigateTo('/help')}}},[_c('i',{staticClass:\"iconfont icon-docs\"}),_vm._v(\"帮助文档 \")])]),_c('li',{staticClass:\"mobile-nav-item\"},[_c('a',{staticClass:\"mobile-nav-link\",class:{'active': _vm.isActive('/console')},on:{\"click\":_vm.handleConsoleNavigation}},[_c('i',{staticClass:\"iconfont icon-console\"}),_vm._v(\"控制台 \")])]),_c('li',{staticClass:\"mobile-nav-item\"},[_c('a',{staticClass:\"mobile-nav-link\",class:{'active': _vm.isActive('/personal')},on:{\"click\":function($event){return _vm.navigateTo('/personal')}}},[_c('i',{staticClass:\"iconfont icon-profile\"}),_vm._v(\"个人中心 \")])]),_c('li',{staticClass:\"mobile-nav-item\"},[_c('a',{staticClass:\"mobile-nav-link\",on:{\"click\":_vm.logout}},[_c('i',{staticClass:\"iconfont icon-logout\"}),_vm._v(\"退出登录 \")])])]],2)])]),(_vm.showUserMenu)?_c('div',{staticClass:\"mobile-user-menu\"},[_c('div',{staticClass:\"mobile-user-info\"},[_c('div',{staticClass:\"mobile-username\"},[_vm._v(_vm._s(_vm.userName))]),_c('div',{staticClass:\"mobile-user-phone\"},[_vm._v(_vm._s(_vm.userPhone))])]),_c('div',{staticClass:\"mobile-menu-options\"},[_c('a',{staticClass:\"mobile-menu-item\",class:{'active': _vm.isActive('/personal')},on:{\"click\":function($event){return _vm.navigateTo('/personal')}}},[_c('i',{staticClass:\"iconfont icon-profile\"}),_vm._v(\"个人中心 \")]),_c('a',{staticClass:\"mobile-menu-item\",class:{'active': _vm.isActive('/userorder')},on:{\"click\":function($event){return _vm.navigateTo('/userorder')}}},[_c('i',{staticClass:\"iconfont icon-order\"}),_vm._v(\"费用中心 \")]),_c('a',{staticClass:\"mobile-menu-item\",class:{'active': false},on:{\"click\":_vm.navigateToRecharge}},[_c('i',{staticClass:\"iconfont icon-recharge\"}),_vm._v(\"充值 \")]),_c('a',{staticClass:\"mobile-menu-item logout\",on:{\"click\":_vm.logout}},[_c('i',{staticClass:\"iconfont icon-logout\"}),_vm._v(\"退出登录 \")])])]):_vm._e()])])])],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"header-wrapper\">\r\n    <SlideNotification v-if=\"showComingSoon\"\r\n                       :message=\"notificationMessage\"\r\n                       type=\"warning\"\r\n                       :duration=\"2000\"\r\n                       @close=\"showComingSoon = false\" />\r\n\r\n    <!-- 导航占位符 -->\r\n    <div class=\"nav-placeholder\" :style=\"{ height: navHeight + 'px' }\"></div>\r\n\r\n    <!-- 主导航栏 -->\r\n    <div class=\"main-nav\" ref=\"mainNav\">\r\n      <div class=\"container\">\r\n        <!-- 电脑端导航 -->\r\n        <div class=\"nav-container desktop-nav\" v-if=\"!isMobile\">\r\n          <!-- Logo区域 -->\r\n          <div class=\"logo-area\">\r\n            <a @click=\"navigateTo('/')\" class=\"logo-link\">\r\n              <img src=\"images/logo-tiangong.png\" alt=\"算力租赁\" loading=\"eager\">\r\n            </a>\r\n          </div>\r\n\r\n          <!-- 导航菜单 -->\r\n          <div class=\"nav-menu\">\r\n            <ul class=\"nav-list\">\r\n              <li class=\"nav-item\">\r\n                <a @click=\"navigateTo('/')\" class=\"nav-link\" :class=\"{'active': isActive('/')}\">首页</a>\r\n              </li>\r\n              <li class=\"nav-item dropdown\">\r\n                <a @click=\"navigateTo('/product')\" class=\"nav-link\" :class=\"{'active': isActive('/product')}\">\r\n                  算力市场<i class=\"iconfont icon-arrow-down\"></i>\r\n                </a>\r\n              </li>\r\n              <li class=\"nav-item dropdown\">\r\n                <a @click=\"triggerComingSoon\" class=\"nav-link\" :class=\"{'active': false}\">算法社区<i class=\"iconfont icon-arrow-down\"></i></a>\r\n              </li>\r\n\r\n              <!-- 私有云 -->\r\n              <!-- <li class=\"nav-item\">\r\n                <a @click=\"triggerComingSoon\" class=\"nav-link\" :class=\"{'active': false}\">私有云</a>\r\n              </li> -->\r\n\r\n              <!-- 关于我们 -->\r\n              <li class=\"nav-item\">\r\n                <a @click=\"navigateTo('/about')\" class=\"nav-link\" :class=\"{'active': isActive('/about')}\">关于我们</a>\r\n              </li>\r\n              <!-- 帮助文档 -->\r\n              <!-- <li class=\"nav-item\">\r\n                <a @click=\"navigateTo('/help')\" class=\"nav-link\" :class=\"{'active': isActive('/help')}\">帮助文档</a>\r\n              </li> -->\r\n            </ul>\r\n          </div>\r\n\r\n          <!-- 用户操作区 -->\r\n          <div class=\"user-actions\">\r\n            <div class=\"auth-buttons\" v-if=\"!isLoggedIn\">\r\n              <a @click=\"navigateTo('/help')\" class=\"btn btn-login\" :class=\"{'active': isActive('/help')}\">帮助文档</a>\r\n              <a @click=\"navigateTo('/login')\" class=\"btn btn-login\" :class=\"{'active': isActive('/login')}\">控制台</a>\r\n              <a @click=\"navigateTo('/login')\" class=\"btn btn-login\" :class=\"{'active': isActive('/login')}\">登录</a>\r\n              <a @click=\"navigateTo('/register')\" class=\"btn btn-register\" :class=\"{'active': isActive('/register')}\">立即注册</a>\r\n            </div>\r\n            <div class=\"user-profile\" v-if=\"isLoggedIn\">\r\n              <a @click=\"navigateTo('/help')\" class=\"btn btn-login\" :class=\"{'active': isActive('/help')}\">帮助文档</a>\r\n              <a @click=\"handleConsoleNavigation\" class=\"btn btn-login\" :class=\"{'active': isActive('/console'),'disabled': isConsoleLoading }\" :title=\"isConsoleLoading ? '控制台加载中，请稍后...' : ''\">控制台</a>\r\n              <div class=\"user-dropdown\">\r\n                <div class=\"user-avatar\">\r\n                  <div class=\"avatar-letter\">{{ userInitial }}</div>\r\n                </div>\r\n                <div class=\"dropdown-menu\">\r\n                  <!-- 用户信息区域 -->\r\n                  <div class=\"user-info-section\">\r\n                    <div class=\"detail-item\">\r\n                      <span class=\"label\">用户名:</span>\r\n                      <span class=\"value\">{{ userName }}</span>\r\n                    </div>\r\n                    <div class=\"detail-item\">\r\n                      <span class=\"label\">手机号:</span>\r\n                      <span class=\"value\"><i class=\"copy-icon\"></i> {{ userPhone }}</span>\r\n                    </div>\r\n                    <div class=\"verification-tag\" @click=\"gotoPersonal\">\r\n                      <span class=\"check-icon\"></span> 个人认证\r\n                      <span class=\"status-text\">({{isReal === 1 ? '已认证' : '未认证'}})</span>\r\n                    </div>\r\n                    <div class=\"detail-item\">\r\n                      <span class=\"label\">可用余额:</span>\r\n                      <span class=\"value\">￥{{ userBalance.toFixed(2) }}</span>\r\n                      <div class=\"verification-tag recharge-btn\" @click=\"navigateToRecharge\">\r\n                        充值\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"menu-options\">\r\n                    <a @click=\"navigateTo('/personal')\" :class=\"{'active': isActive('/personal')}\">个人中心</a>\r\n                    <a @click=\"navigateTo('/userorder')\" :class=\"{'active': isActive('/userorder')}\">费用中心</a>\r\n                  </div>\r\n\r\n                  <!-- 退出登录按钮 -->\r\n                  <div class=\"logout-button-container\">\r\n                    <button class=\"logout-button\" @click=\"logout\">退出登录</button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 移动端导航 -->\r\n        <div class=\"mobile-nav\" v-else>\r\n          <div class=\"mobile-nav-container\">\r\n            <!-- 左侧折叠栏按钮 -->\r\n            <button class=\"hamburger-btn\" @click=\"toggleMobileMenu\">\r\n              <span class=\"hamburger-line\" :class=\"{'line-1': mobileMenuOpen}\"></span>\r\n              <span class=\"hamburger-line\" :class=\"{'line-2': mobileMenuOpen}\"></span>\r\n              <span class=\"hamburger-line\" :class=\"{'line-3': mobileMenuOpen}\"></span>\r\n            </button>\r\n\r\n            <!-- 中间Logo -->\r\n            <div class=\"mobile-logo-area\">\r\n              <a @click=\"navigateTo('/')\" class=\"logo-link\">\r\n                <img src=\"images/logo_tiangong.png\" alt=\"算力租赁\">\r\n              </a>\r\n            </div>\r\n\r\n            <!-- 右侧用户操作区 -->\r\n            <div class=\"mobile-user-actions\">\r\n              <template v-if=\"!isLoggedIn\">\r\n                <!-- <a @click=\"navigateTo('/help')\" class=\"mobile-console-btn\">\r\n                  帮助文档\r\n                </a> -->\r\n                <a @click=\"navigateTo('/login')\" class=\"mobile-login-btn\">\r\n                  登录\r\n                </a>\r\n                <a @click=\"navigateTo('/register')\" class=\"mobile-register-btn\">\r\n                  注册\r\n                </a>\r\n              </template>\r\n              <template v-else>\r\n                <a @click=\"navigateTo('/help')\" class=\"mobile-console-btn\">\r\n                  帮助文档\r\n                </a>\r\n                <a @click=\"handleConsoleNavigation\" class=\"mobile-console-btn\">\r\n                  控制台\r\n                </a>\r\n                <div class=\"mobile-user-profile\" @click=\"toggleUserMenu\">\r\n                  <div class=\"mobile-user-avatar\">\r\n                    <div class=\"avatar-letter\">{{ userInitial }}</div>\r\n                  </div>\r\n                </div>\r\n              </template>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 移动端菜单 -->\r\n          <div class=\"mobile-menu\" :class=\"{'open': mobileMenuOpen}\">\r\n            <div class=\"mobile-menu-content\">\r\n              <!-- 导航链接 -->\r\n              <ul class=\"mobile-nav-list\">\r\n                <li class=\"mobile-nav-item\">\r\n                  <a @click=\"navigateTo('/')\" class=\"mobile-nav-link\" :class=\"{'active': isActive('/')}\">\r\n                    <i class=\"iconfont icon-home\"></i>首页\r\n                  </a>\r\n                </li>\r\n                <li class=\"mobile-nav-item\">\r\n                  <a @click=\"navigateTo('/product')\" class=\"mobile-nav-link\" :class=\"{'active': isActive('/product')}\">\r\n                    <i class=\"iconfont icon-server\"></i>算力市场\r\n                  </a>\r\n                </li>\r\n                <li class=\"mobile-nav-item\">\r\n                  <a @click=\"triggerComingSoon\" class=\"mobile-nav-link\">\r\n                    <i class=\"iconfont icon-community\"></i>算法社区\r\n                  </a>\r\n                </li>\r\n                <!-- <li class=\"mobile-nav-item\">\r\n                  <a @click=\"triggerComingSoon\" class=\"mobile-nav-link\">\r\n                    <i class=\"iconfont icon-cloud\"></i>私有云\r\n                  </a>\r\n                </li> -->\r\n                <li class=\"mobile-nav-item\">\r\n                  <a @click=\"navigateTo('/about')\" class=\"mobile-nav-link\" :class=\"{'active': isActive('/about')}\">\r\n                    <i class=\"iconfont icon-info\"></i>关于我们\r\n                  </a>\r\n                </li>\r\n                <!-- <li class=\"mobile-nav-item\">\r\n                  <a @click=\"navigateTo('/help')\" class=\"mobile-nav-link\" :class=\"{'active': isActive('/help')}\">\r\n                    <i class=\"iconfont icon-docs\"></i>帮助文档\r\n                  </a>\r\n                </li> -->\r\n\r\n                <!-- 登录/注册/控制台入口 -->\r\n                <template v-if=\"!isLoggedIn\">\r\n                  <li class=\"mobile-nav-item\">\r\n                  <a @click=\"navigateTo('/help')\" class=\"mobile-nav-link\" :class=\"{'active': isActive('/help')}\">\r\n                    <i class=\"iconfont icon-docs\"></i>帮助文档\r\n                  </a>\r\n                </li>\r\n                  <li class=\"mobile-nav-item\">\r\n                    <a @click=\"navigateTo('/login')\" class=\"mobile-nav-link\" :class=\"{'active': isActive('/login')}\">\r\n                      <i class=\"iconfont icon-user\"></i>登录\r\n                    </a>\r\n                  </li>\r\n                  <li class=\"mobile-nav-item\">\r\n                    <a @click=\"navigateTo('/register')\" class=\"mobile-nav-link\" :class=\"{'active': isActive('/register')}\">\r\n                      <i class=\"iconfont icon-edit\"></i>注册\r\n                    </a>\r\n                  </li>\r\n                  <li class=\"mobile-nav-item\">\r\n                    <a @click=\"navigateTo('/login')\" class=\"mobile-nav-link\" :class=\"{'active': isActive('/console')}\">\r\n                      <i class=\"iconfont icon-console\"></i>控制台\r\n                    </a>\r\n                  </li>\r\n\r\n                </template>\r\n                <template v-else>\r\n                <li class=\"mobile-nav-item\"> \r\n                  <a @click=\"navigateTo('/help')\" class=\"mobile-nav-link\" :class=\"{'active': isActive('/help')}\">\r\n                    <i class=\"iconfont icon-docs\"></i>帮助文档\r\n                  </a>\r\n                </li>\r\n                  <li class=\"mobile-nav-item\">\r\n                    <a @click=\"handleConsoleNavigation\" class=\"mobile-nav-link\" :class=\"{'active': isActive('/console')}\">\r\n                      <i class=\"iconfont icon-console\"></i>控制台\r\n                    </a>\r\n                  </li>\r\n                  <li class=\"mobile-nav-item\">\r\n                    <a @click=\"navigateTo('/personal')\" class=\"mobile-nav-link\" :class=\"{'active': isActive('/personal')}\">\r\n                      <i class=\"iconfont icon-profile\"></i>个人中心\r\n                    </a>\r\n                  </li>\r\n                  <li class=\"mobile-nav-item\">\r\n                    <a @click=\"logout\" class=\"mobile-nav-link\">\r\n                      <i class=\"iconfont icon-logout\"></i>退出登录\r\n                    </a>\r\n                  </li>\r\n                </template>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 用户菜单 -->\r\n          <div class=\"mobile-user-menu\" v-if=\"showUserMenu\">\r\n            <div class=\"mobile-user-info\">\r\n              <div class=\"mobile-username\">{{ userName }}</div>\r\n              <div class=\"mobile-user-phone\">{{ userPhone }}</div>\r\n            </div>\r\n            <div class=\"mobile-menu-options\">\r\n              <a @click=\"navigateTo('/personal')\" class=\"mobile-menu-item\" :class=\"{'active': isActive('/personal')}\">\r\n                <i class=\"iconfont icon-profile\"></i>个人中心\r\n              </a>\r\n              <a @click=\"navigateTo('/userorder')\" class=\"mobile-menu-item\" :class=\"{'active': isActive('/userorder')}\">\r\n                <i class=\"iconfont icon-order\"></i>费用中心\r\n              </a>\r\n              <a @click=\"navigateToRecharge\" class=\"mobile-menu-item\" :class=\"{'active': false}\">\r\n                <i class=\"iconfont icon-recharge\"></i>充值\r\n              </a>\r\n              <a @click=\"logout\" class=\"mobile-menu-item logout\">\r\n                <i class=\"iconfont icon-logout\"></i>退出登录\r\n              </a>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {postAnyData, getAnyData, postLogin, postJsonData} from \"@/api/login\";\r\nimport { removeToken, getToken } from \"@/utils/auth\"\r\nimport SlideNotification from './SlideNotification.vue';\r\nimport Cookies from 'js-cookie'\r\n\r\nexport default {\r\n  name: \"Header\",\r\n  components: {\r\n    SlideNotification\r\n  },\r\n  data() {\r\n    return {\r\n      userBalance: null,\r\n      showComingSoon: false,\r\n      notificationMessage: \"\",\r\n      isLoggedIn: false,\r\n      userName: \"null\",\r\n      userPhone: \"\",\r\n      notificationCount: 2,\r\n      currentPath: '',\r\n      navHeight: 60,\r\n      scrollThrottleTimer: null,\r\n      previousActivePath: null,\r\n      isMobile: false,\r\n      mobileMenuOpen: false,\r\n      showUserMenu: false,\r\n      windowWidth: 0,\r\n      isReal:0,\r\n      cookieWatcher: null,\r\n      isConsoleLoading: false,\r\n      isConsoleReady: false,\r\n      hasTriggeredRefresh: false,\r\n    };\r\n  },\r\n  computed: {\r\n    userInitial() {\r\n      return this.userName && this.userName.length > 0\r\n          ? this.userName.charAt(0).toUpperCase()\r\n          : 'N';\r\n    }\r\n  },\r\n  watch: {\r\n    '$route'(to) {\r\n      this.currentPath = to.path;\r\n      if (to.path === '/product') {\r\n        this.currentPath = '/product';\r\n      } else if (to.path === '/about') {\r\n        this.currentPath = '/about';\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    isActive(route) {\r\n      return this.currentPath === route ||\r\n          (route === '/' && this.currentPath === '/index') ||\r\n          (route === '/product' && this.currentPath.startsWith('/product')) ||\r\n          (route === '/about' && this.currentPath === '/about') ||\r\n          (route === '/help' && this.currentPath.startsWith('/help'));\r\n    },\r\n\r\n\r\n    checkScreenSize() {\r\n      this.windowWidth = window.innerWidth;\r\n      this.isMobile = this.windowWidth <= 992;\r\n      if (!this.isMobile) {\r\n        this.mobileMenuOpen = false;\r\n        this.showUserMenu = false;\r\n      }\r\n    },\r\n\r\n    gotoPersonal(){\r\n      this.closeAllMenus();\r\n      this.$router.push({\r\n        path: '/personal',\r\n        query: { activeTab: 'verification' }\r\n      });\r\n    },\r\n\r\n    navigateToRecharge() {\r\n      this.closeAllMenus();\r\n      this.$router.push({\r\n        path: '/userorder',\r\n        query: { activeTab: 'recharge' }\r\n      });\r\n    },\r\n\r\n    handleConsoleNavigation() {\r\n      this.closeAllMenus();\r\n\r\n      if (this.isConsoleLoading) return;\r\n\r\n      if (!this.isLoggedIn) {\r\n        this.navigateTo('/login');\r\n        return;\r\n      }\r\n\r\n      if (this.isReal !== 1) {\r\n        // 未实名认证，显示弹窗\r\n        this.notificationMessage = \"请先完成实名认证，正在为您跳转到对应页面\";\r\n        this.showComingSoon = true;\r\n\r\n        // 2秒后跳转到实名认证页面\r\n        setTimeout(() => {\r\n          this.navigateTo('/personal', { activeTab: 'verification' });\r\n        }, 2000);\r\n        return;\r\n      }\r\n\r\n      if (!this.isConsoleReady) {\r\n        this.notificationMessage = '控制台初始化中，请稍候...';\r\n        this.showComingSoon = true;\r\n        this.isConsoleLoading = true;\r\n\r\n\r\n        if (!this.hasTriggeredRefresh) {\r\n          this.hasTriggeredRefresh = true;\r\n          setTimeout(() => {\r\n            this.getUserInfo();\r\n            this.startCookieWatcher();\r\n          }, 2000); // 2 秒后自动刷新\r\n        }\r\n        return;\r\n      }\r\n\r\n      this.isConsoleLoading = true;\r\n      this.navigateTo('/console');\r\n\r\n      this.isConsoleReady = true;\r\n      this.isConsoleLoading = false;\r\n    },\r\n\r\n    handleScroll() {\r\n      if (this.scrollThrottleTimer) return;\r\n\r\n      this.scrollThrottleTimer = setTimeout(() => {\r\n        this.scrollThrottleTimer = null;\r\n      }, 50);\r\n    },\r\n\r\n    logout() {\r\n      this.closeAllMenus();\r\n      postAnyData(\"/logout/cilent/logout\").then(res => {\r\n        if (res.data.code === 200) {\r\n          Object.keys(Cookies.get()).forEach(cookieName => {\r\n            Cookies.remove(cookieName);\r\n          });\r\n          removeToken();\r\n          this.isLoggedIn = false;\r\n          this.$router.push('/login');\r\n        }\r\n      }).catch(err => {\r\n      });\r\n    },\r\n\r\n    triggerComingSoon() {\r\n      if (this.showComingSoon) return;\r\n      this.notificationMessage = \"我们正在努力建设中，敬请期待更多精彩内容！\";\r\n      this.showComingSoon = true;\r\n      this.closeAllMenus();\r\n    },\r\n\r\n    navigateTo(path, query = {}) {\r\n      if (this.currentPath && this.currentPath !== path) {\r\n        this.previousActivePath = this.currentPath;\r\n\r\n        this.$nextTick(() => {\r\n          const navLinks = document.querySelectorAll('.nav-link, .btn-login, .mobile-nav-link');\r\n          navLinks.forEach(link => {\r\n            if ((link.classList.contains('active') ||\r\n                    (path === '/login' && link.classList.contains('btn-login'))) &&\r\n                !link.classList.contains('active-exit')) {\r\n              link.classList.add('active-exit');\r\n\r\n              setTimeout(() => {\r\n                link.classList.remove('active-exit');\r\n              }, 300);\r\n            }\r\n          });\r\n\r\n          this.currentPath = path;\r\n        });\r\n      } else {\r\n        this.currentPath = path;\r\n      }\r\n\r\n      this.closeAllMenus();\r\n\r\n      if (this.$route.path === path && JSON.stringify(this.$route.query) === JSON.stringify(query)) {\r\n        return;\r\n      }\r\n\r\n      this.$router.push({\r\n        path: path,\r\n        query: query\r\n      });\r\n\r\n      window.scrollTo({\r\n        top: 0,\r\n        behavior: 'instant'\r\n      });\r\n    },\r\n\r\n    measureNavHeight() {\r\n      if (this.$refs.mainNav) {\r\n        const rect = this.$refs.mainNav.getBoundingClientRect();\r\n        if (rect.height > 0) {\r\n          this.navHeight = rect.height;\r\n        }\r\n      }\r\n    },\r\n    // 处理 token 过期\r\n    handleTokenExpired() {\r\n      // 清除登录状态\r\n      this.isLoggedIn = false;\r\n      removeToken();\r\n      Object.keys(Cookies.get()).forEach(cookieName => {\r\n        Cookies.remove(cookieName);\r\n      });\r\n\r\n      // 显示通知\r\n      this.notificationMessage = \"由于长时间未操作，登录状态已失效，请重新登录\";\r\n      this.showComingSoon = true;\r\n\r\n\r\n      // 2秒后自动跳转到登录页面\r\n      setTimeout(() => {\r\n        this.$router.push('/login');\r\n      }, 2000);\r\n    },\r\n    // 处理页面可见性变化\r\n    handleVisibilityChange() {\r\n      if (document.visibilityState === 'visible' && this.isLoggedIn) {\r\n        // 页面从隐藏变为可见时，检查 token 是否仍然有效\r\n        this.getUserInfo();\r\n      }\r\n    },\r\n    getUserInfo() {\r\n      // removeToken()\r\n      postAnyData(\"/logout/cilent/getInfo\").then(res => {\r\n        if (res.data.code === 200) {\r\n          this.userName = res.data.data.nickName || \"NCloud-user\";\r\n          Cookies.set('userName', res.data.data.nickName);\r\n          this.userPhone = res.data.data.username;\r\n          Cookies.set('userPhone', res.data.data.username);\r\n          this.userEmail = res.data.data.email || \"<EMAIL>\";\r\n          Cookies.set('userEmail', res.data.data.email);\r\n          this.tenantId = res.data.data.tenantId || \"te-default\";\r\n          Cookies.set('tenantId', res.data.data.tenantId);\r\n          this.userId = res.data.data.id || \"ac-default\";\r\n          Cookies.set('userId', res.data.data.id);\r\n          this.userBalance = res.data.data.balance;\r\n          Cookies.set('userBalance', res.data.data.balance);\r\n          this.isReal = res.data.data.isReal;\r\n          Cookies.set('isReal', this.isReal);\r\n          this.isLoggedIn = true;\r\n\r\n\r\n          if (Cookies.get('publicKey-C')) {\r\n            // console.log('rsa_pubk',publicKey-C);\r\n            return;\r\n          }\r\n          postJsonData(\"/suanleme/login\", {\r\n            correlationId: Cookies.get('userId'),\r\n            rsaPubk: Cookies.get('publicKey-B')\r\n          }).then(res => {\r\n            // console.log('rsa_pubk',res.data.data.rsa_pubk);\r\n            Cookies.set('publicKey-C', res.data.data.rsa_pubk);\r\n            Cookies.set('suanlemeToken', res.data.data.token);\r\n\r\n          });\r\n        } else {\r\n          this.handleTokenExpired();\r\n        }\r\n      })\r\n    },\r\n\r\n    toggleMobileMenu() {\r\n      this.mobileMenuOpen = !this.mobileMenuOpen;\r\n      if (this.mobileMenuOpen) {\r\n        this.showUserMenu = false;\r\n      }\r\n    },\r\n\r\n    toggleUserMenu() {\r\n      this.showUserMenu = !this.showUserMenu;\r\n      if (this.showUserMenu) {\r\n        this.mobileMenuOpen = false;\r\n      }\r\n    },\r\n\r\n    closeAllMenus() {\r\n      this.mobileMenuOpen = false;\r\n      this.showUserMenu = false;\r\n    },\r\n\r\n    startCookieWatcher() {\r\n      this.cookieWatcher = setInterval(() => {\r\n\r\n        const token = this.getCookie('suanlemeToken');\r\n        const pubKey = this.getCookie('publicKey-C');\r\n        if (token && pubKey) {\r\n          clearInterval(this.cookieWatcher);\r\n          this.cookieWatcher = null;\r\n          this.isConsoleReady = true;\r\n          this.isConsoleLoading = false;\r\n        }\r\n      }, 1000); // 每秒检查一次\r\n    },\r\n\r\n    getCookie(name) {\r\n      const match = document.cookie.match(new RegExp('(^| )' + name + '=([^;]+)'));\r\n      return match ? match[2] : null;\r\n    }\r\n  },\r\n\r\n  created() {\r\n    window.addEventListener('scroll', this.handleScroll, { passive: true });\r\n    window.addEventListener('resize', this.checkScreenSize);\r\n\r\n    // 优先从本地存储读取登录状态\r\n    const token = getToken();\r\n    this.isLoggedIn = !!token;\r\n\r\n    // 如果存在token，立即从cookie中读取用户信息\r\n    if (token) {\r\n      this.userName = Cookies.get('userName') || \"null\";\r\n      this.userPhone = Cookies.get('userPhone') || \"\";\r\n      this.userBalance = parseFloat(Cookies.get('userBalance')) || 0;\r\n      this.isReal = parseInt(Cookies.get('isReal')) || 0;\r\n\r\n      // 然后异步验证token有效性\r\n      this.getUserInfo();\r\n    }\r\n\r\n    // 添加页面可见性变化监听\r\n    document.addEventListener('visibilitychange', this.handleVisibilityChange);\r\n\r\n    this.currentPath = this.$route.path;\r\n    this.checkScreenSize();\r\n  },\r\n\r\n  mounted() {\r\n    this.$nextTick(() => {\r\n      this.measureNavHeight();\r\n\r\n      let resizeTimer;\r\n      window.addEventListener('resize', () => {\r\n        clearTimeout(resizeTimer);\r\n        resizeTimer = setTimeout(() => {\r\n          this.measureNavHeight();\r\n        }, 250);\r\n      }, { passive: true });\r\n\r\n      this.startCookieWatcher();\r\n    });\r\n  },\r\n\r\n  beforeDestroy() {\r\n    window.removeEventListener('scroll', this.handleScroll);\r\n    window.removeEventListener('resize', this.measureNavHeight);\r\n    window.removeEventListener('resize', this.checkScreenSize);\r\n    document.removeEventListener('visibilitychange', this.handleVisibilityChange);\r\n    clearTimeout(this.scrollThrottleTimer);\r\n  },\r\n\r\n\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 原有样式保持不变 */\r\n.header-wrapper {\r\n  position: relative;\r\n  width: 100%;\r\n  max-width: 100vw;\r\n  overflow: hidden;\r\n}\r\n\r\n.main-nav {\r\n  background-image: url(\"../../../assets/images/index/back3.png\");\r\n  background-size: cover;\r\n  background-position: top;\r\n  background-repeat: no-repeat;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n  width: 100%;\r\n  z-index: 1005;\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  transform: translate3d(0, 0, 0);\r\n  will-change: transform;\r\n  transition: background-color 0.3s ease;\r\n  width: 100vw;\r\n}\r\n\r\n.nav-placeholder {\r\n  width: 100%;\r\n  will-change: height;\r\n}\r\n\r\n.container {\r\n  width: 100%;\r\n  max-width: 100%;\r\n  height: 60px;\r\n  margin: 0 auto;\r\n  padding: 0 15px;\r\n  box-sizing: border-box;\r\n}\r\n\r\n/* 电脑端导航样式 */\r\n.nav-container {\r\n  display: flex;\r\n  align-items: center;\r\n  height: 60px;\r\n  position: relative;\r\n}\r\n\r\n.logo-area {\r\n  flex: 0 0 auto;\r\n  margin-right: -50px;\r\n}\r\n\r\n.logo-link {\r\n  height: 60px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  overflow: hidden;\r\n  cursor: pointer;\r\n}\r\n\r\n\r\n.logo-area img {\r\n  height: 120px;\r\n  max-width: 100%;\r\n  display: block;\r\n}\r\n\r\n.nav-menu {\r\n  flex: 1;\r\n  display: flex;\r\n  justify-content: flex-start;\r\n}\r\n\r\n.nav-list {\r\n  display: flex;\r\n  list-style: none;\r\n  margin: 0 70px;\r\n  padding: 0;\r\n}\r\n\r\n.nav-item {\r\n  position: relative;\r\n  margin: 0 10px;\r\n}\r\n\r\n.nav-link {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0 15px;\r\n  height: 70px;\r\n  color: #ffffff;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  text-decoration: none;\r\n  transition: all 0.3s ease-in-out;\r\n  white-space: nowrap;\r\n  cursor: pointer;\r\n  position: relative;\r\n}\r\n\r\n.nav-link.active {\r\n  color: #ffffff;\r\n  font-weight: 500;\r\n}\r\n\r\n.nav-link::after {\r\n  content: \"\";\r\n  position: absolute;\r\n  bottom: 2vh;\r\n  left: 50%;\r\n  transform: translateX(-50%) scaleX(0);\r\n  width: calc(100% - 4vh);\r\n  height: 2px;\r\n  background-color: #ffffff;\r\n  transition: transform 0.3s ease-in-out;\r\n  transform-origin: center;\r\n}\r\n\r\n.nav-link:hover::after,\r\n.nav-link.active::after {\r\n  transform: translateX(-50%) scaleX(1);\r\n}\r\n\r\n.nav-link.active-exit::after {\r\n  transform: translateX(-50%) scaleX(0);\r\n}\r\n\r\n.nav-link i {\r\n  font-size: 12px;\r\n  margin-left: 5px;\r\n}\r\n\r\n.user-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-left: auto;\r\n}\r\n\r\n.auth-buttons {\r\n  display: inline-flex !important;\r\n  align-items: center !important;\r\n  height: 100%;\r\n  white-space: nowrap;\r\n}\r\n\r\n.btn {\r\n  display: inline-block !important;\r\n  padding: 8px 16px !important;\r\n  font-size: 16px !important;\r\n  border-radius: 4px !important;\r\n  text-decoration: none !important;\r\n  transition: all 0.3s !important;\r\n  margin: 0 !important;\r\n  height: auto !important;\r\n  line-height: normal !important;\r\n  border: none !important;\r\n  cursor: pointer;\r\n}\r\n\r\n.btn-login {\r\n  color: #ffffff !important;\r\n  margin-right: 10px !important;\r\n  position: relative;\r\n}\r\n\r\n.btn-login::after {\r\n  content: \"\";\r\n  position: absolute;\r\n  bottom: 2px;\r\n  left: 50%;\r\n  transform: translateX(-50%) scaleX(0);\r\n  width: calc(100% - 20px);\r\n  height: 2px;\r\n  background-color: #ffffff;\r\n  transition: transform 0.3s ease-in-out;\r\n  transform-origin: center;\r\n}\r\n\r\n.btn-login:hover::after,\r\n.btn-login.active::after {\r\n  transform: translateX(-50%) scaleX(1);\r\n}\r\n\r\n.btn-login.active-exit::after {\r\n  transform: translateX(-50%) scaleX(0);\r\n}\r\n\r\n.btn-register {\r\n  color: #fff !important;\r\n  border: 1px solid #ffffff!important;\r\n  border-radius: 0px !important;\r\n  font-size: 15px !important;\r\n}\r\n\r\n.btn-register:hover {\r\n  background-color: rgba(194, 187, 187, 0.3) !important;\r\n}\r\n\r\n.user-profile {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.user-dropdown {\r\n  position: relative;\r\n  cursor: pointer;\r\n  margin-right: 2vh;\r\n}\r\n\r\n.user-avatar {\r\n  width: 36px;\r\n  height: 36px;\r\n  border-radius: 50%;\r\n  overflow: hidden;\r\n  background-color: #6366f1;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.avatar-letter {\r\n  color: white;\r\n  font-size: 18px;\r\n  font-weight: 500;\r\n  text-transform: uppercase;\r\n}\r\n\r\n.user-dropdown .dropdown-menu {\r\n  position: absolute;\r\n  top: 100%;\r\n  right: -10px;\r\n  background-color: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);\r\n  min-width: 240px;\r\n  opacity: 0;\r\n  visibility: hidden;\r\n  transform: translateY(10px);\r\n  transition: all 0.3s ease;\r\n  z-index: 1010;\r\n  padding: 0;\r\n  overflow: hidden;\r\n}\r\n\r\n.user-dropdown:hover .dropdown-menu {\r\n  opacity: 1;\r\n  visibility: visible;\r\n  transform: translateY(0);\r\n}\r\n\r\n.user-info-section {\r\n  padding: 16px;\r\n  position: relative;\r\n  background-color: #f9fafb;\r\n  border-bottom: 1px solid #e5e7eb;\r\n}\r\n\r\n.detail-item {\r\n  display: flex;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.detail-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.label {\r\n  color: #666;\r\n  font-size: 14px;\r\n  width: 60px;\r\n}\r\n\r\n.value {\r\n  color: #333;\r\n  font-size: 14px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.verification-tag {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  background-color: #e6f7ff;\r\n  color: #1890ff;\r\n  font-size: 12px;\r\n  padding: 2px 8px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.recharge-btn {\r\n  cursor: pointer;\r\n  margin-left: 10px;\r\n  transition: all 0.2s;\r\n}\r\n\r\n.recharge-btn:hover {\r\n  background-color: #bae7ff;\r\n}\r\n\r\n.check-icon {\r\n  display: inline-block;\r\n  width: 12px;\r\n  height: 12px;\r\n  background-color: #1890ff;\r\n  margin-right: 4px;\r\n  mask: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E\") no-repeat center / contain;\r\n}\r\n\r\n.menu-options {\r\n  padding: 8px 0;\r\n}\r\n\r\n.menu-options a {\r\n  display: block;\r\n  padding: 10px 16px;\r\n  color: #333;\r\n  text-decoration: none;\r\n  transition: background-color 0.2s;\r\n  font-size: 14px;\r\n}\r\n\r\n.menu-options a:hover {\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n.logout-button-container {\r\n  padding: 12px 16px;\r\n  background-color: #f9fafb;\r\n}\r\n\r\n.logout-button {\r\n  width: 100%;\r\n  padding: 10px 0;\r\n  background-color: #f0f0f0;\r\n  border: none;\r\n  border-radius: 4px;\r\n  color: #333;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s;\r\n}\r\n\r\n.logout-button:hover {\r\n  background-color: #e0e0e0;\r\n}\r\n\r\n/* 移动端导航样式 */\r\n.mobile-nav {\r\n  position: relative;\r\n  height: 60px;\r\n}\r\n\r\n.mobile-nav-container {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  height: 100%;\r\n  padding: 0 15px;\r\n  position: relative;\r\n}\r\n\r\n/* 汉堡菜单按钮 */\r\n.hamburger-btn {\r\n  background: none;\r\n  border: none;\r\n  width: 25px;\r\n  height: 20px;\r\n  position: relative;\r\n  cursor: pointer;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: space-between;\r\n  padding: 0;\r\n  z-index: 1001;\r\n  flex: 0 0 auto;\r\n}\r\n\r\n.hamburger-line {\r\n  display: block;\r\n  width: 100%;\r\n  height: 3px;\r\n  background-color: #fff;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.hamburger-btn .line-1 {\r\n  transform: translateY(10px) rotate(45deg);\r\n}\r\n\r\n.hamburger-btn .line-2 {\r\n  opacity: 0;\r\n}\r\n\r\n.hamburger-btn .line-3 {\r\n  transform: translateY(-10px) rotate(-45deg);\r\n}\r\n\r\n/* 中间Logo - 调大尺寸 */\r\n.mobile-logo-area {\r\n  flex: 1;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  padding: 0 10px;\r\n}\r\n\r\n.mobile-logo-area img {\r\n  height: 100px;\r\n  max-width: 100%;\r\n  object-fit: contain;\r\n}\r\n\r\n/* 右侧用户操作区 - 增加登录/注册按钮 */\r\n.mobile-user-actions {\r\n  flex: 0 0 auto;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.mobile-login-btn,\r\n.mobile-register-btn,\r\n.mobile-console-btn {\r\n  color: #fff;\r\n  font-size: 14px;\r\n  text-decoration: none;\r\n  padding: 6px 10px;\r\n  border-radius: 4px;\r\n  white-space: nowrap;\r\n}\r\n\r\n.mobile-login-btn {\r\n  background-color: transparent;\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n}\r\n\r\n.mobile-register-btn {\r\n  background-color: rgba(255, 255, 255, 0.2);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n}\r\n\r\n.mobile-console-btn {\r\n  background-color: transparent;\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  margin-right: 5px;\r\n}\r\n\r\n.mobile-user-profile {\r\n  position: relative;\r\n  cursor: pointer;\r\n}\r\n\r\n.mobile-user-avatar {\r\n  width: 36px;\r\n  height: 36px;\r\n  border-radius: 50%;\r\n  background-color: #6366f1;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  font-size: 18px;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 移动端菜单 */\r\n.mobile-menu {\r\n  position: fixed;\r\n  top: 0;\r\n  right: -100%;\r\n  width: 80%;\r\n  max-width: 320px;\r\n  height: 100vh;\r\n  background-color: #1a1a2e;\r\n  z-index: 1000;\r\n  transition: right 0.3s ease;\r\n  overflow-y: auto;\r\n}\r\n\r\n.mobile-menu.open {\r\n  right: 0;\r\n}\r\n\r\n.mobile-menu-content {\r\n  padding: 70px 20px 20px;\r\n}\r\n\r\n.mobile-nav-list {\r\n  list-style: none;\r\n  padding: 0;\r\n  margin: 0;\r\n}\r\n\r\n.mobile-nav-item {\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.mobile-nav-link {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 12px 15px;\r\n  color: #fff;\r\n  text-decoration: none;\r\n  font-size: 16px;\r\n  border-radius: 5px;\r\n  transition: background-color 0.2s;\r\n}\r\n\r\n.mobile-nav-link i {\r\n  margin-right: 15px;\r\n  font-size: 20px;\r\n  color: rgba(255, 255, 255, 0.7);\r\n}\r\n\r\n.mobile-nav-link.active {\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n  font-weight: 500;\r\n}\r\n\r\n.mobile-nav-link:hover {\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n/* 用户菜单 */\r\n.mobile-user-menu {\r\n  position: fixed;\r\n  top: 60px;\r\n  right: 15px;\r\n  background-color: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);\r\n  width: 220px;\r\n  z-index: 1011;\r\n  padding: 15px;\r\n  animation: fadeIn 0.2s ease;\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from { opacity: 0; transform: translateY(-10px); }\r\n  to { opacity: 1; transform: translateY(0); }\r\n}\r\n\r\n.mobile-user-info {\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #eee;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.mobile-username {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  color: #333;\r\n}\r\n\r\n.mobile-user-phone {\r\n  font-size: 14px;\r\n  color: #666;\r\n}\r\n\r\n.mobile-menu-options {\r\n  margin-top: 10px;\r\n}\r\n\r\n.mobile-menu-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 10px 0;\r\n  color: #333;\r\n  text-decoration: none;\r\n  font-size: 15px;\r\n  transition: color 0.2s;\r\n}\r\n\r\n.mobile-menu-item i {\r\n  margin-right: 10px;\r\n  font-size: 18px;\r\n  color: #666;\r\n}\r\n\r\n.mobile-menu-item:hover {\r\n  color: #1890ff;\r\n}\r\n\r\n.mobile-menu-item:hover i {\r\n  color: #1890ff;\r\n}\r\n\r\n.mobile-menu-item.logout {\r\n  color: #f5222d;\r\n}\r\n\r\n.mobile-menu-item.logout i {\r\n  color: #f5222d;\r\n}\r\n\r\n.disabled {\r\n  pointer-events: none;\r\n  opacity: 0.5;\r\n  cursor: not-allowed;\r\n}\r\n\r\n/* 响应式调整 */\r\n@media (max-width: 992px) {\r\n  .desktop-nav {\r\n    display: none !important;\r\n  }\r\n\r\n  .mobile-nav {\r\n    display: block !important;\r\n  }\r\n}\r\n\r\n@media (min-width: 993px) {\r\n  .desktop-nav {\r\n    display: flex !important;\r\n  }\r\n\r\n  .mobile-nav {\r\n    display: none !important;\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Header.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Header.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Header.vue?vue&type=template&id=48fc90c3&scoped=true&\"\nimport script from \"./Header.vue?vue&type=script&lang=js&\"\nexport * from \"./Header.vue?vue&type=script&lang=js&\"\nimport style0 from \"./Header.vue?vue&type=style&index=0&id=48fc90c3&prod&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"48fc90c3\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('transition',{attrs:{\"name\":\"slide\"}},[(_vm.visible)?_c('div',{class:['notification', `notification-${_vm.type}`],style:({ minHeight: _vm.minHeight }),attrs:{\"role\":\"alert\"}},[_c('div',{staticClass:\"notification-content\"},[(_vm.type === 'error')?_c('div',{staticClass:\"icon-wrapper\"},[_c('span',{staticClass:\"error-icon\"},[_vm._v(\"×\")])]):_vm._e(),_c('span',{staticClass:\"message\"},[_vm._v(_vm._s(_vm.message))]),(_vm.closable)?_c('button',{staticClass:\"close-btn\",on:{\"click\":_vm.close}},[_vm._v(\"×\")]):_vm._e()])]):_vm._e()])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <transition name=\"slide\">\r\n    <div v-if=\"visible\"\r\n         :class=\"['notification', `notification-${type}`]\"\r\n         role=\"alert\"\r\n         :style=\"{ minHeight: minHeight }\">\r\n      <div class=\"notification-content\">\r\n        <div class=\"icon-wrapper\" v-if=\"type === 'error'\">\r\n          <span class=\"error-icon\">×</span>\r\n        </div>\r\n        <span class=\"message\">{{ message }}</span>\r\n        <button v-if=\"closable\" class=\"close-btn\" @click=\"close\">×</button>\r\n      </div>\r\n    </div>\r\n  </transition>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"SlideNotification\",\r\n  props: {\r\n    message: {\r\n      type: String,\r\n      required: true\r\n    },\r\n    type: {\r\n      type: String,\r\n      default: 'info',\r\n      validator: value => ['success', 'warning', 'error', 'info'].includes(value)\r\n    },\r\n    duration: {\r\n      type: Number,\r\n      default: 4000\r\n    },\r\n    closable: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    minHeight: {\r\n      type: String,\r\n      default: 'auto'\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      visible: false,\r\n      timer: null\r\n    }\r\n  },\r\n  methods: {\r\n    show() {\r\n      this.$nextTick(() => {\r\n        this.visible = true;\r\n        this.startTimer();\r\n      });\r\n    },\r\n    close() {\r\n      this.visible = false;\r\n      this.$emit('close');\r\n    },\r\n    startTimer() {\r\n      if (this.duration > 0) {\r\n        clearTimeout(this.timer);\r\n        this.timer = setTimeout(() => {\r\n          this.close();\r\n        }, this.duration);\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    setTimeout(() => {\r\n      this.show();\r\n    }, 100);\r\n  },\r\n  beforeUnmount() {\r\n    clearTimeout(this.timer);\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.notification {\r\n  position: fixed;\r\n  top: 20px;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  width: 90%;\r\n  max-width: 450px;\r\n  border-radius: 6px;\r\n  z-index: 9999;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n  padding: 0;\r\n}\r\n\r\n.notification-content {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 12px 16px;\r\n}\r\n\r\n.notification-error {\r\n  background-color: #f3f5f6;\r\n  /*border: 1px solid #ffecb3;*/\r\n}\r\n\r\n.notification-success {\r\n  background-color: #f3f5f6;\r\n  /*border: 1px solid #ffecb3;*/\r\n}\r\n\r\n.notification-warning {\r\n  background-color: #f3f5f6;\r\n  /*border: 1px solid #ffecb3;*/\r\n}\r\n\r\n.notification-info {\r\n  background-color: #f3f5f6;\r\n  /*border: 1px solid #ffecb3;*/\r\n}\r\n\r\n.icon-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 24px;\r\n  height: 24px;\r\n  border-radius: 50%;\r\n  background-color: #f44336;\r\n  margin-right: 12px;\r\n}\r\n\r\n.error-icon {\r\n  color: white;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n}\r\n\r\n.message {\r\n  flex: 1;\r\n  color: #444444;\r\n  font-size: 14px;\r\n  padding: 2px 0;\r\n}\r\n\r\n.notification-error .message {\r\n  color: #d32f2f;\r\n}\r\n\r\n.notification-success .message {\r\n  color: #388e3c;\r\n}\r\n\r\n.notification-warning .message {\r\n  color: #f57c00;\r\n}\r\n\r\n.notification-info .message {\r\n  color: #1976d2;\r\n}\r\n\r\n.close-btn {\r\n  background: transparent;\r\n  border: none;\r\n  color: #9e9e9e;\r\n  cursor: pointer;\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  padding: 0;\r\n  margin-left: 10px;\r\n  opacity: 0.8;\r\n  width: 24px;\r\n  height: 24px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.close-btn:hover {\r\n  opacity: 1;\r\n  color: #616161;\r\n}\r\n\r\n/* 改进后的滑动动画 */\r\n.slide-enter-active {\r\n  animation: slideDown 0.5s ease-out forwards;\r\n}\r\n\r\n.slide-leave-active {\r\n  animation: slideUp 0.5s ease-in forwards;\r\n}\r\n\r\n@keyframes slideDown {\r\n  0% {\r\n    transform: translateX(-50%) translateY(-100%);\r\n    opacity: 0;\r\n  }\r\n  70% {\r\n    transform: translateX(-50%) translateY(10%);\r\n    opacity: 0.9;\r\n  }\r\n  100% {\r\n    transform: translateX(-50%) translateY(0);\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n@keyframes slideUp {\r\n  0% {\r\n    transform: translateX(-50%) translateY(0);\r\n    opacity: 1;\r\n  }\r\n  30% {\r\n    transform: translateX(-50%) translateY(10%);\r\n    opacity: 0.9;\r\n  }\r\n  100% {\r\n    transform: translateX(-50%) translateY(-100%);\r\n    opacity: 0;\r\n  }\r\n}\r\n</style>", "import mod from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./SlideNotification.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./SlideNotification.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./SlideNotification.vue?vue&type=template&id=80a289c0&scoped=true&\"\nimport script from \"./SlideNotification.vue?vue&type=script&lang=js&\"\nexport * from \"./SlideNotification.vue?vue&type=script&lang=js&\"\nimport style0 from \"./SlideNotification.vue?vue&type=style&index=0&id=80a289c0&prod&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"80a289c0\",\n  null\n  \n)\n\nexport default component.exports", "// 引入 axios\r\nimport axios from \"axios\";\r\n\r\nimport Cookies from 'js-cookie'\r\n\r\nlet base = '/api';\r\n// let base = ' http://**************/prod-api';\r\n// let base = ' https://test.tiangongkaiwu.top/prod-api';\r\n   // let base = ' http://***************:8080';\r\n//let base = ' https://tiangongkaiwu.top/prod-api';\r\n\r\n//传送json格式的Post请求55\r\nexport const postAnyData=(url,params)=>{\r\n    return axios({\r\n        headers: {\r\n            'authorization': Cookies.get(\"Admin-Token\"), // 必须设置\r\n            'Content-Type': 'application/x-www-form-urlencoded' // 必须设置\r\n        },\r\n        method:'post',\r\n        url:`${base}${url}`,\r\n        data: params,\r\n    })\r\n}\r\nexport const postJsonData =(url,parmas)=>{\r\n    return axios({\r\n        headers:{\r\n            'authorization': Cookies.get(\"Admin-Token\"), // 必须设置\r\n            'Content-Type': 'application/json;charset=UTF-8' // 必须设置\r\n        },\r\n        method:'post',\r\n        url:`${base}${url}`,\r\n        data:parmas\r\n    })\r\n}\r\n\r\nexport const postLogin=(url,params)=> {\r\n    return axios({\r\n        headers: {\r\n            'Content-Type': 'application/x-www-form-urlencoded' // 必须设置\r\n        },\r\n        method: 'post',\r\n        url: `${base}${url}`,\r\n        data: params\r\n    })\r\n}\r\nexport const postNotAuth=(url,params)=> {\r\n    return axios({\r\n        headers: {\r\n            'Content-Type': 'application/json;charset=UTF-8' // 必须设置\r\n        },\r\n        method: 'post',\r\n        url: `${base}${url}`,\r\n        data: params\r\n    })\r\n}\r\nexport const getNotAuth=(url,params)=> {\r\n    return axios({\r\n        headers: {\r\n            'Content-Type': 'application/json;charset=UTF-8' // 必须设置\r\n        },\r\n        method: 'get',\r\n        url: `${base}${url}`,\r\n        params: params\r\n    })\r\n}\r\n\r\nexport const getAnyData=(url,params)=>{\r\n    return axios({\r\n        headers: {\r\n            'authorization': Cookies.get(\"Admin-Token\"), // 必须设置\r\n            'Content-Type': 'application/x-www-form-urlencoded' // 必须设置\r\n        },\r\n        method:'get',\r\n        url:`${base}${url}`,\r\n        params: params\r\n    })\r\n}\r\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{attrs:{\"id\":\"app\"}},[(_vm.showLayout)?_c('Layout',{key:_vm.headerKey}):_vm._e(),_c('router-view',{key:_vm.$route.fullPath,on:{\"refresh-header\":_vm.refreshHeader,\"hiden-layout\":_vm.hidenLayout}})],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n    <div id=\"app\">\r\n\t<!-- :key=\"$route.fullPath\" 解决了路由前缀相同时跳转不刷新 -->\r\n        <Layout v-if=\"showLayout\"  :key=\"headerKey\">\r\n        </Layout>\r\n        <router-view @refresh-header=\"refreshHeader\" @hiden-layout=\"hidenLayout\" :key=\"$route.fullPath\"/>\r\n\r\n    </div>\r\n</template>\r\n<script>\r\n    import Layout from \"@/components/common/Layout-header\";\r\n    export default {\r\n        components: {Layout},\r\n        data() {\r\n            return {\r\n                headerKey: 0,\r\n                showLayout:true\r\n            }\r\n        },\r\n        methods: {\r\n            hidenLayout(){\r\n                this.showLayout = !this.showLayout\r\n            },\r\n            // 需要刷新 Header 时调用\r\n            refreshHeader() {\r\n                console.log(\"刷新了\")\r\n                this.headerKey += 1\r\n            }\r\n        }\r\n    }\r\n</script>\r\n<style>\r\n/* 隐藏垂直滚动条但保留滚动功能 */\r\nhtml {\r\n  overflow-y: scroll; /* 强制显示滚动条占位防止内容跳动 */\r\n  scrollbar-width: none; /* Firefox 隐藏滚动条 */\r\n  -ms-overflow-style: none; /* IE 10+ 隐藏滚动条 */\r\n}\r\n\r\n/* Chrome/Safari/Edge 隐藏滚动条 */\r\nhtml::-webkit-scrollbar,\r\nbody::-webkit-scrollbar {\r\n  width: 0 !important;\r\n  height: 0 !important;\r\n  display: none !important;\r\n}\r\n\r\nbody {\r\n  overflow: -moz-scrollbars-none; /* Firefox 旧版 */\r\n  -webkit-overflow-scrolling: touch; /* 启用惯性滚动 */\r\n  scrollbar-width: none; /* 新版 Firefox */\r\n}\r\n\r\n/* 所有可滚动容器统一处理 */\r\n* {\r\n  scrollbar-width: none !important; /* Firefox */\r\n  -ms-overflow-style: none !important; /* IE/Edge */\r\n}\r\n\r\n*::-webkit-scrollbar {\r\n  display: none !important; /* WebKit 内核 */\r\n}\r\n</style>\r\n\r\n", "import mod from \"-!../node_modules/thread-loader/dist/cjs.js!../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./App.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/thread-loader/dist/cjs.js!../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./App.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./App.vue?vue&type=template&id=d7cc1b48&\"\nimport script from \"./App.vue?vue&type=script&lang=js&\"\nexport * from \"./App.vue?vue&type=script&lang=js&\"\nimport style0 from \"./App.vue?vue&type=style&index=0&id=d7cc1b48&prod&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "import Vue from 'vue'\r\nimport VueRouter from 'vue-router'\r\n\r\nVue.use(VueRouter)\r\n\r\nconst routes = [\r\n    {\r\n        path:'/',\r\n        redirect:'/index'\r\n    },\r\n    {\r\n        path: '/index',\r\n        name: 'index',\r\n        component: () => import('../views/Index/IndexView.vue')\r\n    },\r\n\r\n    {\r\n        path: '/product',\r\n        name: 'product',\r\n        component: () => import('../views/Product/ProductView.vue'),\r\n    },\r\n    // {\r\n    //     path: '/product/productId/:productId',\r\n    //     name: 'productDetails',\r\n    //     component: () => import(\"../views/ProductDetailsView.vue\")\r\n    // },\r\n    {\r\n        path: '/example',\r\n        name: 'example',\r\n        component: () => import('../views/ExampleView.vue')\r\n    },\r\n    {\r\n        path: '/algorithmcommunity',\r\n        name: 'algorithmcommunity',\r\n        component: () => import('../views/AlgorithmCommunity.vue')\r\n    },\r\n    {\r\n        path: '/news',\r\n        name: 'news',\r\n        component: () => import('../views/NewsView.vue')\r\n    },\r\n    {\r\n        path: '/news/newsId/:newsId',\r\n        name: 'newsDetails',\r\n        component: () => import('../views/NewsDetailsView.vue')\r\n    },\r\n    {\r\n        path: '/login',\r\n        name: 'login',\r\n        component: () => import('../views/Login/login.vue')\r\n    },\r\n    {\r\n        path: '/register',\r\n        name: 'register',\r\n        component: () => import('../views/Login/register.vue')\r\n    },\r\n    {\r\n        path: '/forgetpass',\r\n        name: 'forgetpass',\r\n        component: () => import('../views/Login/ForgetPassView.vue')\r\n\r\n    },\r\n    {\r\n        path: '/about',\r\n        name: 'about',\r\n        component: () => import('../views/About/AboutView.vue')\r\n    },\r\n    {\r\n        path: '/help',\r\n        redirect: '/help/summary'\r\n    },\r\n    {\r\n        path: '/help/:doc?',\r\n        name: 'help',\r\n        component: () => import('../views/HelpView.vue'),\r\n        props: true\r\n    },\r\n    {\r\n        path: '/order',\r\n        name: 'order',\r\n        component: () => import('../views/Product/OrderDetail.vue')\r\n    },\r\n    {\r\n        path: '/personal',\r\n        name: 'personal',\r\n        component: () => import('../views/Personal/personal.vue')\r\n    },\r\n    {\r\n        path: '/userorder',\r\n        name: 'userorder',\r\n        component: () => import('../views/Ordermange/OrderView.vue')\r\n    },\r\n    {\r\n        path: '/console',\r\n        name: 'userorder',\r\n        component: () => import('../views/Console.vue')\r\n    }\r\n    //\r\n    // {\r\n    //     path: '/topup',\r\n    //     name: 'topup',\r\n    //     component: () => import('../views/TopupView.vue')\r\n    // }\r\n]\r\n\r\nconst router = new VueRouter({\r\n    routes\r\n})\r\n\r\nexport default router\r\n", "// 引入 axios\r\nimport axios from \"axios\";\r\n\r\n\r\n\r\nlet base = 'http://localhost:8087';\r\n\r\n//传送json格式的get请求\r\nexport const getRequest=(url,params)=>{\r\n    return axios({\r\n        method:'get',\r\n        url:`${base}${url}`,\r\n        params: params,\r\n    })\r\n}\r\n", "import Vue from 'vue'\r\nimport App from './App.vue'\r\nimport router from './router'\r\nimport './assets/css/style.css'\r\nimport Toast from 'vue-toastification';\r\nimport 'vue-toastification/dist/index.css';\r\nimport {getRequest} from \"@/api/api\";\r\n\r\nVue.prototype.getRequest = getRequest;\r\n\r\nVue.use(Toast, {\r\n  position: 'top-center',\r\n  timeout: 3000,\r\n  closeOnClick: true\r\n});\r\nVue.config.productionTip = false\r\n\r\nnew Vue({\r\n  router,\r\n  render: h => h(App)\r\n}).$mount('#app')\r\n", "import Cookies from 'js-cookie'\r\n\r\nconst TokenKey = 'Admin-Token'\r\n\r\nexport function getToken() {\r\n    return Cookies.get(TokenKey)\r\n}\r\n\r\nexport function setToken(token) {\r\n    return Cookies.set(To<PERSON><PERSON><PERSON>, token)\r\n}\r\n\r\nexport function removeToken() {\r\n    return Cookies.remove(TokenKey)\r\n}\r\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "var deferred = [];\n__webpack_require__.O = function(result, chunkIds, fn, priority) {\n\tif(chunkIds) {\n\t\tpriority = priority || 0;\n\t\tfor(var i = deferred.length; i > 0 && deferred[i - 1][2] > priority; i--) deferred[i] = deferred[i - 1];\n\t\tdeferred[i] = [chunkIds, fn, priority];\n\t\treturn;\n\t}\n\tvar notFulfilled = Infinity;\n\tfor (var i = 0; i < deferred.length; i++) {\n\t\tvar chunkIds = deferred[i][0];\n\t\tvar fn = deferred[i][1];\n\t\tvar priority = deferred[i][2];\n\t\tvar fulfilled = true;\n\t\tfor (var j = 0; j < chunkIds.length; j++) {\n\t\t\tif ((priority & 1 === 0 || notFulfilled >= priority) && Object.keys(__webpack_require__.O).every(function(key) { return __webpack_require__.O[key](chunkIds[j]); })) {\n\t\t\t\tchunkIds.splice(j--, 1);\n\t\t\t} else {\n\t\t\t\tfulfilled = false;\n\t\t\t\tif(priority < notFulfilled) notFulfilled = priority;\n\t\t\t}\n\t\t}\n\t\tif(fulfilled) {\n\t\t\tdeferred.splice(i--, 1)\n\t\t\tvar r = fn();\n\t\t\tif (r !== undefined) result = r;\n\t\t}\n\t}\n\treturn result;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = function(exports, definition) {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.f = {};\n// This file contains only the entry chunk.\n// The chunk loading function for additional chunks\n__webpack_require__.e = function(chunkId) {\n\treturn Promise.all(Object.keys(__webpack_require__.f).reduce(function(promises, key) {\n\t\t__webpack_require__.f[key](chunkId, promises);\n\t\treturn promises;\n\t}, []));\n};", "// This function allow to reference async chunks\n__webpack_require__.u = function(chunkId) {\n\t// return url for filenames based on template\n\treturn \"js/\" + ({\"32\":\"docs26\",\"166\":\"docs15\",\"370\":\"docs14\",\"371\":\"docs19\",\"762\":\"docs30\",\"1447\":\"docs10\",\"1912\":\"docs2\",\"2091\":\"docs18\",\"2261\":\"docs12\",\"2543\":\"docs20\",\"2576\":\"docs17\",\"2719\":\"docs6\",\"3168\":\"docs1\",\"3469\":\"docs7\",\"3959\":\"docs22\",\"4594\":\"docs0\",\"4675\":\"docs9\",\"4956\":\"docs8\",\"5042\":\"docs32\",\"5061\":\"docs3\",\"5838\":\"docs11\",\"6193\":\"docs27\",\"6202\":\"docs4\",\"7083\":\"docs25\",\"7231\":\"docs16\",\"8265\":\"docs24\",\"8295\":\"docs23\",\"8303\":\"docs31\",\"8449\":\"docs28\",\"8991\":\"docs21\",\"9198\":\"docs5\",\"9240\":\"docs29\",\"9802\":\"docs13\"}[chunkId] || chunkId) + \".\" + {\"32\":\"db8e30b7\",\"166\":\"75a21b55\",\"343\":\"cb406483\",\"358\":\"ee71c5f5\",\"370\":\"c92d7ec9\",\"371\":\"dd9232ba\",\"762\":\"154d783c\",\"1447\":\"7735e265\",\"1910\":\"de1689ab\",\"1912\":\"1e275d7a\",\"2091\":\"97d90bf2\",\"2261\":\"0b72583f\",\"2543\":\"64367a01\",\"2567\":\"bfe52095\",\"2576\":\"940a54fc\",\"2719\":\"45f2b1fc\",\"2842\":\"c3349973\",\"3168\":\"fe4e9699\",\"3469\":\"c5aa59b9\",\"3959\":\"aeb7868e\",\"4344\":\"435e5715\",\"4366\":\"354fe420\",\"4559\":\"772a3994\",\"4594\":\"ed3e1cd2\",\"4675\":\"50693aab\",\"4956\":\"5542fa90\",\"4971\":\"ba274ee9\",\"5042\":\"e382623b\",\"5061\":\"41fed339\",\"5566\":\"3cec087b\",\"5648\":\"0d39ed99\",\"5838\":\"22786028\",\"6193\":\"f08fedcf\",\"6202\":\"a39082f7\",\"6433\":\"faf3b498\",\"6742\":\"c4e51fc8\",\"7083\":\"9fd7b08a\",\"7231\":\"6a997ea0\",\"7415\":\"196f86b0\",\"7796\":\"315c3c31\",\"8265\":\"7a5cefec\",\"8295\":\"8e52600d\",\"8303\":\"2f59350e\",\"8449\":\"529ce5b7\",\"8991\":\"e9d867f8\",\"9198\":\"5fea48b6\",\"9240\":\"0c85809c\",\"9802\":\"70993c3b\",\"9860\":\"28df3f5f\"}[chunkId] + \".js\";\n};", "// This function allow to reference async chunks\n__webpack_require__.miniCssF = function(chunkId) {\n\t// return url for filenames based on template\n\treturn \"css/\" + chunkId + \".\" + {\"343\":\"ba4e9447\",\"358\":\"d9cd4fac\",\"2567\":\"f8185547\",\"2842\":\"7ab80c07\",\"4344\":\"b88fdf3d\",\"4366\":\"64e7bc8b\",\"4559\":\"b88fdf3d\",\"4971\":\"66b9638e\",\"5566\":\"c2c713e6\",\"5648\":\"19908f09\",\"6433\":\"3643d4b1\",\"6742\":\"a85164ee\",\"7415\":\"b88fdf3d\",\"7796\":\"1de55221\",\"9860\":\"66d5df10\"}[chunkId] + \".css\";\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "var inProgress = {};\nvar dataWebpackPrefix = \"portal-ui:\";\n// loadScript function to load a script via script tag\n__webpack_require__.l = function(url, done, key, chunkId) {\n\tif(inProgress[url]) { inProgress[url].push(done); return; }\n\tvar script, needAttach;\n\tif(key !== undefined) {\n\t\tvar scripts = document.getElementsByTagName(\"script\");\n\t\tfor(var i = 0; i < scripts.length; i++) {\n\t\t\tvar s = scripts[i];\n\t\t\tif(s.getAttribute(\"src\") == url || s.getAttribute(\"data-webpack\") == dataWebpackPrefix + key) { script = s; break; }\n\t\t}\n\t}\n\tif(!script) {\n\t\tneedAttach = true;\n\t\tscript = document.createElement('script');\n\n\t\tscript.charset = 'utf-8';\n\t\tscript.timeout = 120;\n\t\tif (__webpack_require__.nc) {\n\t\t\tscript.setAttribute(\"nonce\", __webpack_require__.nc);\n\t\t}\n\t\tscript.setAttribute(\"data-webpack\", dataWebpackPrefix + key);\n\t\tscript.src = url;\n\t}\n\tinProgress[url] = [done];\n\tvar onScriptComplete = function(prev, event) {\n\t\t// avoid mem leaks in IE.\n\t\tscript.onerror = script.onload = null;\n\t\tclearTimeout(timeout);\n\t\tvar doneFns = inProgress[url];\n\t\tdelete inProgress[url];\n\t\tscript.parentNode && script.parentNode.removeChild(script);\n\t\tdoneFns && doneFns.forEach(function(fn) { return fn(event); });\n\t\tif(prev) return prev(event);\n\t};\n\tvar timeout = setTimeout(onScriptComplete.bind(null, undefined, { type: 'timeout', target: script }), 120000);\n\tscript.onerror = onScriptComplete.bind(null, script.onerror);\n\tscript.onload = onScriptComplete.bind(null, script.onload);\n\tneedAttach && document.head.appendChild(script);\n};", "// define __esModule on exports\n__webpack_require__.r = function(exports) {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "__webpack_require__.p = \"/portal/\";", "if (typeof document === \"undefined\") return;\nvar createStylesheet = function(chunkId, fullhref, oldTag, resolve, reject) {\n\tvar linkTag = document.createElement(\"link\");\n\n\tlinkTag.rel = \"stylesheet\";\n\tlinkTag.type = \"text/css\";\n\tvar onLinkComplete = function(event) {\n\t\t// avoid mem leaks.\n\t\tlinkTag.onerror = linkTag.onload = null;\n\t\tif (event.type === 'load') {\n\t\t\tresolve();\n\t\t} else {\n\t\t\tvar errorType = event && (event.type === 'load' ? 'missing' : event.type);\n\t\t\tvar realHref = event && event.target && event.target.href || fullhref;\n\t\t\tvar err = new Error(\"Loading CSS chunk \" + chunkId + \" failed.\\n(\" + realHref + \")\");\n\t\t\terr.code = \"CSS_CHUNK_LOAD_FAILED\";\n\t\t\terr.type = errorType;\n\t\t\terr.request = realHref;\n\t\t\tif (linkTag.parentNode) linkTag.parentNode.removeChild(linkTag)\n\t\t\treject(err);\n\t\t}\n\t}\n\tlinkTag.onerror = linkTag.onload = onLinkComplete;\n\tlinkTag.href = fullhref;\n\n\tif (oldTag) {\n\t\toldTag.parentNode.insertBefore(linkTag, oldTag.nextSibling);\n\t} else {\n\t\tdocument.head.appendChild(linkTag);\n\t}\n\treturn linkTag;\n};\nvar findStylesheet = function(href, fullhref) {\n\tvar existingLinkTags = document.getElementsByTagName(\"link\");\n\tfor(var i = 0; i < existingLinkTags.length; i++) {\n\t\tvar tag = existingLinkTags[i];\n\t\tvar dataHref = tag.getAttribute(\"data-href\") || tag.getAttribute(\"href\");\n\t\tif(tag.rel === \"stylesheet\" && (dataHref === href || dataHref === fullhref)) return tag;\n\t}\n\tvar existingStyleTags = document.getElementsByTagName(\"style\");\n\tfor(var i = 0; i < existingStyleTags.length; i++) {\n\t\tvar tag = existingStyleTags[i];\n\t\tvar dataHref = tag.getAttribute(\"data-href\");\n\t\tif(dataHref === href || dataHref === fullhref) return tag;\n\t}\n};\nvar loadStylesheet = function(chunkId) {\n\treturn new Promise(function(resolve, reject) {\n\t\tvar href = __webpack_require__.miniCssF(chunkId);\n\t\tvar fullhref = __webpack_require__.p + href;\n\t\tif(findStylesheet(href, fullhref)) return resolve();\n\t\tcreateStylesheet(chunkId, fullhref, null, resolve, reject);\n\t});\n}\n// object to store loaded CSS chunks\nvar installedCssChunks = {\n\t4826: 0\n};\n\n__webpack_require__.f.miniCss = function(chunkId, promises) {\n\tvar cssChunks = {\"343\":1,\"358\":1,\"2567\":1,\"2842\":1,\"4344\":1,\"4366\":1,\"4559\":1,\"4971\":1,\"5566\":1,\"5648\":1,\"6433\":1,\"6742\":1,\"7415\":1,\"7796\":1,\"9860\":1};\n\tif(installedCssChunks[chunkId]) promises.push(installedCssChunks[chunkId]);\n\telse if(installedCssChunks[chunkId] !== 0 && cssChunks[chunkId]) {\n\t\tpromises.push(installedCssChunks[chunkId] = loadStylesheet(chunkId).then(function() {\n\t\t\tinstalledCssChunks[chunkId] = 0;\n\t\t}, function(e) {\n\t\t\tdelete installedCssChunks[chunkId];\n\t\t\tthrow e;\n\t\t}));\n\t}\n};\n\n// no hmr", "__webpack_require__.b = document.baseURI || self.location.href;\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t4826: 0\n};\n\n__webpack_require__.f.j = function(chunkId, promises) {\n\t\t// JSONP chunk loading for javascript\n\t\tvar installedChunkData = __webpack_require__.o(installedChunks, chunkId) ? installedChunks[chunkId] : undefined;\n\t\tif(installedChunkData !== 0) { // 0 means \"already installed\".\n\n\t\t\t// a Promise means \"currently loading\".\n\t\t\tif(installedChunkData) {\n\t\t\t\tpromises.push(installedChunkData[2]);\n\t\t\t} else {\n\t\t\t\tif(true) { // all chunks have JS\n\t\t\t\t\t// setup Promise in chunk cache\n\t\t\t\t\tvar promise = new Promise(function(resolve, reject) { installedChunkData = installedChunks[chunkId] = [resolve, reject]; });\n\t\t\t\t\tpromises.push(installedChunkData[2] = promise);\n\n\t\t\t\t\t// start chunk loading\n\t\t\t\t\tvar url = __webpack_require__.p + __webpack_require__.u(chunkId);\n\t\t\t\t\t// create error before stack unwound to get useful stacktrace later\n\t\t\t\t\tvar error = new Error();\n\t\t\t\t\tvar loadingEnded = function(event) {\n\t\t\t\t\t\tif(__webpack_require__.o(installedChunks, chunkId)) {\n\t\t\t\t\t\t\tinstalledChunkData = installedChunks[chunkId];\n\t\t\t\t\t\t\tif(installedChunkData !== 0) installedChunks[chunkId] = undefined;\n\t\t\t\t\t\t\tif(installedChunkData) {\n\t\t\t\t\t\t\t\tvar errorType = event && (event.type === 'load' ? 'missing' : event.type);\n\t\t\t\t\t\t\t\tvar realSrc = event && event.target && event.target.src;\n\t\t\t\t\t\t\t\terror.message = 'Loading chunk ' + chunkId + ' failed.\\n(' + errorType + ': ' + realSrc + ')';\n\t\t\t\t\t\t\t\terror.name = 'ChunkLoadError';\n\t\t\t\t\t\t\t\terror.type = errorType;\n\t\t\t\t\t\t\t\terror.request = realSrc;\n\t\t\t\t\t\t\t\tinstalledChunkData[1](error);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t};\n\t\t\t\t\t__webpack_require__.l(url, loadingEnded, \"chunk-\" + chunkId, chunkId);\n\t\t\t\t} else installedChunks[chunkId] = 0;\n\t\t\t}\n\t\t}\n};\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n__webpack_require__.O.j = function(chunkId) { return installedChunks[chunkId] === 0; };\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = function(parentChunkLoadingFunction, data) {\n\tvar chunkIds = data[0];\n\tvar moreModules = data[1];\n\tvar runtime = data[2];\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some(function(id) { return installedChunks[id] !== 0; })) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkId] = 0;\n\t}\n\treturn __webpack_require__.O(result);\n}\n\nvar chunkLoadingGlobal = self[\"webpackChunkportal_ui\"] = self[\"webpackChunkportal_ui\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));", "// startup\n// Load entry module and return exports\n// This entry module depends on other loaded chunks and execution need to be delayed\nvar __webpack_exports__ = __webpack_require__.O(undefined, [4998], function() { return __webpack_require__(3969); })\n__webpack_exports__ = __webpack_require__.O(__webpack_exports__);\n"], "names": ["render", "_vm", "this", "_c", "_self", "staticClass", "_t", "staticRenderFns", "name", "components", "Header", "component", "showComingSoon", "attrs", "notificationMessage", "on", "$event", "_e", "style", "height", "navHeight", "ref", "isMobile", "toggleMobileMenu", "class", "mobileMenuOpen", "navigateTo", "isLoggedIn", "_v", "handleConsoleNavigation", "toggleUserMenu", "_s", "userInitial", "isActive", "triggerComingSoon", "logout", "showUserMenu", "userName", "userPhone", "navigateToRecharge", "isConsoleLoading", "gotoPersonal", "isReal", "userBalance", "toFixed", "SlideNotification", "data", "notificationCount", "currentPath", "scrollThrottleTimer", "previousActivePath", "windowWidth", "cookieWatcher", "isConsoleReady", "hasTriggeredRefresh", "computed", "length", "char<PERSON>t", "toUpperCase", "watch", "to", "path", "methods", "route", "startsWith", "checkScreenSize", "window", "innerWidth", "closeAllMenus", "$router", "push", "query", "activeTab", "setTimeout", "getUserInfo", "startCookieWatcher", "handleScroll", "postAnyData", "then", "res", "code", "Object", "keys", "Cookies", "for<PERSON>ach", "cookieName", "removeToken", "catch", "err", "$nextTick", "navLinks", "document", "querySelectorAll", "link", "classList", "contains", "add", "remove", "$route", "JSON", "stringify", "scrollTo", "top", "behavior", "measureNavHeight", "$refs", "mainNav", "rect", "getBoundingClientRect", "handleTokenExpired", "handleVisibilityChange", "visibilityState", "nick<PERSON><PERSON>", "username", "userEmail", "email", "tenantId", "userId", "id", "balance", "postJsonData", "correlationId", "rsaPubk", "rsa_pubk", "token", "setInterval", "<PERSON><PERSON><PERSON><PERSON>", "pubKey", "clearInterval", "match", "cookie", "RegExp", "created", "addEventListener", "passive", "getToken", "parseFloat", "parseInt", "mounted", "resizeTimer", "clearTimeout", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "visible", "type", "minHeight", "message", "closable", "close", "props", "String", "required", "default", "validator", "value", "includes", "duration", "Number", "Boolean", "timer", "show", "startTimer", "$emit", "beforeUnmount", "base", "url", "params", "axios", "headers", "method", "parmas", "postLogin", "postNotAuth", "getNotAuth", "getAnyData", "showLayout", "key", "<PERSON><PERSON><PERSON>", "fullPath", "refreshHeader", "hidenLayout", "Layout", "console", "log", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "routes", "redirect", "router", "getRequest", "Toast", "position", "timeout", "closeOnClick", "h", "App", "$mount", "TokenKey", "setToken", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "exports", "module", "__webpack_modules__", "m", "deferred", "O", "result", "chunkIds", "fn", "priority", "notFulfilled", "Infinity", "i", "fulfilled", "j", "every", "splice", "r", "d", "definition", "o", "defineProperty", "enumerable", "get", "f", "e", "chunkId", "Promise", "all", "reduce", "promises", "u", "miniCssF", "g", "globalThis", "Function", "obj", "prop", "prototype", "hasOwnProperty", "call", "inProgress", "dataWebpackPrefix", "l", "done", "script", "<PERSON><PERSON><PERSON><PERSON>", "scripts", "getElementsByTagName", "s", "getAttribute", "createElement", "charset", "nc", "setAttribute", "src", "onScriptComplete", "prev", "event", "onerror", "onload", "doneFns", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "bind", "target", "head", "append<PERSON><PERSON><PERSON>", "Symbol", "toStringTag", "p", "createStylesheet", "fullhref", "oldTag", "resolve", "reject", "linkTag", "rel", "onLinkComplete", "errorType", "realHref", "href", "Error", "request", "insertBefore", "nextS<PERSON>ling", "find<PERSON><PERSON><PERSON><PERSON><PERSON>", "existingLinkTags", "tag", "dataHref", "existingStyleTags", "loadStylesheet", "installedCssChunks", "miniCss", "cssChunks", "b", "baseURI", "self", "location", "installedChunks", "installedChunkData", "promise", "error", "loadingEnded", "realSrc", "webpackJsonpCallback", "parentChunkLoadingFunction", "moreModules", "runtime", "some", "chunkLoadingGlobal", "__webpack_exports__"], "sourceRoot": ""}