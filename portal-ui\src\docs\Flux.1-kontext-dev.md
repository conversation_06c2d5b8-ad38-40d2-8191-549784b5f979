# 容器化部署 Flux.1 Kontext Dev 图片编辑模型应用
## 1 部署步骤
### 1.1 登录[天工开物控制台](https://tiangongkaiwu.top/portal/#/console)，在控制台首页点击“弹性部署服务”进入管理页面。
![](./imgs/universal1.png)

### 1.2 基于自身需要进行配置，参考配置为单卡 4090 和 1 个节点（初次使用进行调试）。
![](./imgs/universal2.png)

### 1.3 选择相应预制镜像
![](./imgs/kontextdev3.png)

### 1.4 点击部署服务，耐心等待节点拉取镜像并启动。
![](./imgs/kontextdev4.png)

### 1.5 节点启动后，你所在“任务详情页”中看到的内容可能如下：
![](./imgs/kontextdev5.png)

### 1.6 我们可以点击快速访问下方“8188”端口的链接，测试 comfyui 部署情况
<font style="color:rgb(2, 8, 23);">系统会自动分配一个可公网访问的域名，点击 8188 端口的链接。接下来我们即可自由地通过使用工作流在 comfyui 中使用 Flux 模型进行图像生成。</font>

<font style="color:rgb(2, 8, 23);">我们进入 8188 端口的 web ui 界面，选择左侧的“工作流“菜单，找到名为”flux_1_kontext_dev_basic.json“的工作流文件，鼠标双击进行插入。</font>

![](./imgs/kontextdev6.png)

<font style="color:rgb(2, 8, 23);">随后，我们可以看到工作流已经被正常载入 ComfyUI 了。接下来，我们找到图片上传节点，上传我们想要编辑的图片。我这里选取了一张动漫风格的图片作为示例。</font>

![](./imgs/kontextdev7.png)

<font style="color:rgb(2, 8, 23);">接下来，我们找到 prompt（提示词）的填写节点，输入“我们希望如何转变图像”的提示词。</font>

![](./imgs/kontextdev8.png)

<font style="color:rgb(2, 8, 23);">我希望将女孩的头发转变成蓝色，参考的 prompt 如下（仅支持英文）：</font>

convert the girl’s hair to blue

<font style="color:rgb(2, 8, 23);">稍等片刻，即可在后方的“保存图像”节点看到基于我们提示词生成的图片。</font>

<font style="color:rgb(2, 8, 23);">可以看到，图片的一致性保存得很好，原有图片的纹理和细节大都能复刻下来。</font>

<font style="color:rgb(2, 8, 23);">右键点击图片，选择“Save Image”即可保存图片。</font>

![](./imgs/kontextdev9.png)

### 1.7 通过 API 的形式来调用 comfyui 进行图像生成
<font style="color:rgb(2, 8, 23);">我们不推荐直接使用 comfyui 的默认 API 接口，因为多节点时需自行解决无状态问题。更推荐的做法是通过我们暴露的 3000 端口进行请求，其通过 [<font style="color:rgb(0, 102, 204);">comfyui-api</font>](https://github.com/SaladTechnologies/comfyui-api?tab=readme-ov-file) 进行包装，支持默认的同步生图请求和 webhook 实现。</font>

<font style="color:rgb(2, 8, 23);">以下以 POSTMAN 为例，简要描述如何向 3000 端口发送图片生成的 API 请求：</font>

#### 1.7.1 保存页面工作流
<font style="color:rgb(2, 8, 23);">点击”导航栏——工作流——导出（API）“菜单，浏览器会自动下载一个 json 文件。</font>

![](./imgs/kontextdev10.png)

#### 1.7.2 打开 POSTMAN，新建一个 POST 请求
<font style="color:rgb(2, 8, 23);">新建一个 POST 请求，并命名为”prompt“，如下图所示：</font>

![](./imgs/kontextdev11.png)

#### 1.7.3 完善请求信息
<font style="color:rgb(2, 8, 23);">需要完善的信息如下：</font>

+ **请求的 URL**

<font style="color:rgb(2, 8, 23);">在 3000 端口的回传链接后加上“/prompt”的路径，保证其格式类似于<font style="color:#2F8EF4;background-color:#EFF0F0;">https://xxx/prompt</font>。</font>

+ **将请求体参数格式设置为 raw 和 json**

<font style="color:rgb(2, 8, 23);">如图。</font>

+ **设置参数内容基本格式**

<font style="color:rgb(2, 8, 23);">如图。</font>

![](./imgs/kontextdev12.png)

#### 1.7.4 将我们下载好的工作流 json 文件粘贴为参数中<font style="color:#2F8EF4;background-color:#EFF0F0;">prompt</font>字段的值
<font style="color:rgb(2, 8, 23);">如下图所示，我们将鼠标移动至 prompt 字段的冒号后，粘贴工作流的内容。</font>

![](./imgs/kontextdev13.png)

#### 1.7.5 上传图片
<font style="color:rgb(2, 8, 23);">Flux.1 Kontext Dev 的工作流中，有个节点类型为“LoadImageOutput”</font>

#### 1.7.6 发送请求
<font style="color:rgb(2, 8, 23);">返回结果如下所示，<font style="color:#2F8EF4;background-color:#EFF0F0;">images</font>字段包含一个字符数组，其中的元素即为生成图片的 base64 编码。</font>

<br><br>
<font style="color:rgba(178,178,178,1);">最后更新于: 2025/7/1 15:35</font>
  