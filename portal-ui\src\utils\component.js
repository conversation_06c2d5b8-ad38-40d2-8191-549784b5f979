export function formatDateTime(inputStr) {
    // 1. 将 "+" 替换为空格（如果输入是URL编码后的格式）
    const normalizedStr = inputStr.replace('+', ' ');
    // 2. 解析为Date对象（注意时区问题）
    const date = new Date(normalizedStr);
    // 3. 格式化为 yyyy-MM-dd HH:mm:ss
    const pad = (num) => num.toString().padStart(2, '0');
    const year = date.getFullYear();
    const month = pad(date.getMonth() + 1); // 月份从0开始
    const day = pad(date.getDate());
    const hours = pad(date.getHours());
    const minutes = pad(date.getMinutes());
    const seconds = pad(date.getSeconds());
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}