<template>
    <div id="app">
	<!-- :key="$route.fullPath" 解决了路由前缀相同时跳转不刷新 -->
        <Layout v-if="showLayout"  :key="headerKey">
        </Layout>
        <router-view @refresh-header="refreshHeader" @hiden-layout="hidenLayout" :key="$route.fullPath"/>

    </div>
</template>
<script>
    import Layout from "@/components/common/Layout-header";
    export default {
        components: {Layout},
        data() {
            return {
                headerKey: 0,
                showLayout:true
            }
        },
        methods: {
            hidenLayout(){
                this.showLayout = !this.showLayout
            },
            // 需要刷新 Header 时调用
            refreshHeader() {
                console.log("刷新了")
                this.headerKey += 1
            }
        }
    }
</script>
<style>
/* 隐藏垂直滚动条但保留滚动功能 */
html {
  overflow-y: scroll; /* 强制显示滚动条占位防止内容跳动 */
  scrollbar-width: none; /* Firefox 隐藏滚动条 */
  -ms-overflow-style: none; /* IE 10+ 隐藏滚动条 */
}

/* Chrome/Safari/Edge 隐藏滚动条 */
html::-webkit-scrollbar,
body::-webkit-scrollbar {
  width: 0 !important;
  height: 0 !important;
  display: none !important;
}

body {
  overflow: -moz-scrollbars-none; /* Firefox 旧版 */
  -webkit-overflow-scrolling: touch; /* 启用惯性滚动 */
  scrollbar-width: none; /* 新版 Firefox */
}

/* 所有可滚动容器统一处理 */
* {
  scrollbar-width: none !important; /* Firefox */
  -ms-overflow-style: none !important; /* IE/Edge */
}

*::-webkit-scrollbar {
  display: none !important; /* WebKit 内核 */
}
</style>

