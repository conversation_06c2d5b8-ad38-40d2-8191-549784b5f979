/*! Amaze UI v2.7.2 | by Amaze UI Team | (c) 2016 AllMobilize, Inc. | Licensed under MIT | 2016-08-17T16:17:24+0800 */
/* ==========================================================================
   Component: Base
 ============================================================================ */
/**
 * Fix the flawed CSS box model - Yes, IE6's box model is better
 * Browser support: IE8+
 * via: http://paulirish.com/2012/box-sizing-border-box-ftw/
 */
*,
*:before,
*:after {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}
/* normalize.css v3.0.2 | git.io/normalize */
/* Set <html> and <body> to inherit the height of the viewport */
html,
body {
  min-height: 100%;
}
/**
 * 1. Prevent iOS text size adjust after orientation change, without disabling
 *    user zoom.
 *    https://developer.mozilla.org/en-US/docs/Web/CSS/text-size-adjust
 */
html {
  -ms-text-size-adjust: 100%;
  /* 1 */
  -webkit-text-size-adjust: 100%;
  /* 1 */
}
/**
 * Remove default margin.
 */
body {
  margin: 0;
}
/* HTML5 display definitions
   ========================================================================== */
/**
 * Correct `block` display not defined for any HTML5 element in IE 8/9.
 * Correct `block` display not defined for `details` or `summary` in IE 10/11 and Firefox.
 * Correct `block` display not defined for `main` in IE 11.
 * hgroup has been removed from the HTML5 (W3C) specification
 *        https://developer.mozilla.org/en-US/docs/Web/HTML/Element/hgroup
 */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
main,
nav,
section,
summary {
  display: block;
}
/**
 * 1. Correct `inline-block` display not defined in IE 8/9.
 * 2. Normalize vertical alignment of `progress` in Chrome, Firefox, and Opera.
 */
audio,
canvas,
progress,
video {
  display: inline-block;
  /* 1 */
  vertical-align: baseline;
  /* 2 */
}
/**
 * Prevent modern browsers from displaying `audio` without controls.
 * Remove excess height in iOS 5 devices.
 */
audio:not([controls]) {
  display: none;
  height: 0;
}
/**
 * Address `[hidden]` styling not present in IE 8/9/10.
 * Hide the `template` element in IE 8/9/11, Safari, and Firefox < 22.
 */
[hidden],
template,
script {
  display: none;
}
/* Links
   ========================================================================== */
/**
 * Remove the gray background color from active links in IE 10.
 */
a {
  background-color: transparent;
}
/**
 * Address `outline` inconsistency between Chrome and other browsers.
 */
a:focus {
  outline: thin dotted;
}
/**
 * Improve readability when focused and also mouse hovered in all browsers.
 */
a:active,
a:hover {
  outline: 0;
}
/* Text-level semantics
   ========================================================================== */
/**
 * Remove text-decoration
 */
ins,
a {
  text-decoration: none;
}
/**
 * Address styling not present in IE 8/9, Safari 5, and Chrome.
 */
abbr[title] {
  border-bottom: 1px dotted;
}
/**
 * Address style set to `bolder` in Firefox 4+, Safari 5, and Chrome.
 */
b,
strong {
  font-weight: bold;
}
/**
 * Improve readability of pre-formatted text in all browsers.
 */
pre {
  white-space: pre-wrap;
}
/**
 * Set consistent quote types.
 */
q {
  quotes: "\201C" "\201D" "\2018" "\2019";
}
/**
 * Address inconsistent and variable font size in all browsers.
 */
small {
  font-size: 80%;
}
/**
 * Prevent `sub` and `sup` affecting `line-height` in all browsers.
 */
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}
sup {
  top: -0.5em;
}
sub {
  bottom: -0.25em;
}
/* Embedded content
   ========================================================================== */
/**
  * 1. Corrects max-width behavior (2.) if padding and border are used
  * 2. Responsiveness: Sets a maxium width relative to the parent and auto scales the height
  * 3. Remove the gap between images and the bottom of their containers
  * 4. Remove border when inside `a` element in IE 8/9.
  * remove 2. for Issue #502
  */
img {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  /* 1 */
  vertical-align: middle;
  /* 3 */
  border: 0;
  /* 4 */
}
/**
 * Correct overflow displayed oddly in IE 9.
 */
svg:not(:root) {
  overflow: hidden;
}
/* Grouping content
   ========================================================================== */
/**
 * Address margin not present in IE 8/9 and Safari.
 */
figure {
  margin: 0;
}
/**
 * Correct font family set oddly in Safari 5 and Chrome.
 */
code,
kbd,
pre,
samp {
  font-family: Monaco, Menlo, Consolas, "Courier New", "FontAwesome", monospace;
  font-size: 1em;
}
/* Forms
   ========================================================================== */
/**
 * Define consistent border, margin, and padding.
 */
fieldset {
  border: 1px solid #c0c0c0;
  margin: 0 2px;
  padding: 0.35em 0.625em 0.75em;
}
/**
 * 1. Correct `color` not being inherited in IE 8/9.
 * 2. Remove padding so people aren't caught out if they zero out fieldsets.
 */
legend {
  border: 0;
  /* 1 */
  padding: 0;
  /* 2 */
}
/**
 * 1. Correct color not being inherited.
 *    Known issue: affects color of disabled elements.
 * 2. Correct font properties not being inherited.
 * 3. Address margins set differently in Firefox 4+, Safari 5, and Chrome.
 */
button,
input,
optgroup,
select,
textarea {
  color: inherit;
  /* 1 */
  font: inherit;
  /* 2 */
  margin: 0;
  /* 3 */
}
/**
 * Address `overflow` set to `hidden` in IE 8/9/10/11.
 */
button {
  overflow: visible;
}
/**
 * Address Firefox 4+ setting `line-height` on `input` using `!important` in
 * the UA stylesheet.
 */
button,
input {
  line-height: normal;
}
/**
 * Address inconsistent `text-transform` inheritance for `button` and `select`.
 * All other form control elements do not inherit `text-transform` values.
 * Correct `button` style inheritance in Firefox, IE 8/9/10/11, and Opera.
 * Correct `select` style inheritance in Firefox.
 */
button,
select {
  text-transform: none;
}
/**
 * 1. Avoid the WebKit bug in Android 4.0.* where (2) destroys native `audio`
 *    and `video` controls.
 * 2. Correct inability to style clickable `input` types in iOS.
 * 3. Improve usability and consistency of cursor style between image-type
 *    `input` and others.
 */
button,
html input[type="button"],
input[type="reset"],
input[type="submit"] {
  -webkit-appearance: button;
  /* 2 */
  cursor: pointer;
  /* 3 */
}
/*
 * 1. Improves consistency of cursor style for clickable elements
 * 2. Removes excess padding in IE 8/9.
 * 3. Address box sizing set to `content-box` in IE 8/9/10.
 */
input[type="radio"],
input[type="checkbox"] {
  cursor: pointer;
  /*1*/
  padding: 0;
  /*2*/
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  /* 3 */
}
/**
 * Re-set default cursor for disabled elements.
 */
button[disabled],
html input[disabled] {
  cursor: default;
}
/**
 * Remove inner padding and border in Firefox 4+.
 */
button::-moz-focus-inner,
input::-moz-focus-inner {
  border: 0;
  padding: 0;
}
/**
 * Fix the cursor style for Chrome's increment/decrement buttons. For certain
 * `font-size` values of the `input`, it causes the cursor style of the
 * decrement button to change from `default` to `text`.
 */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  height: auto;
}
/**
 * 1. Address `appearance` set to `searchfield` in Safari and Chrome.
 * 2. Address `box-sizing` set to `border-box` in Safari and Chrome
 *    (include `-moz` to future-proof).
 */
input[type="search"] {
  -webkit-appearance: textfield;
  /* 1 */
  -webkit-box-sizing: content-box;
          box-sizing: content-box;
  /* 2 */
}
/**
 * Remove inner padding and search cancel button in Safari 5 and Chrome
 * on OS X.
 */
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}
/**
 * 1. Remove default vertical scrollbar in IE 8/9.
 * 2. Improve readability and alignment in all browsers.
 */
textarea {
  overflow: auto;
  /* 1 */
  vertical-align: top;
  /* 2 */
  resize: vertical;
}
/**
 * Don't inherit the `font-weight` (applied by a rule above).
 * NOTE: the default cannot safely be changed in Chrome and Safari on OS X.
 */
optgroup {
  font-weight: bold;
}
/* Tables
   ========================================================================== */
/**
 * Remove most spacing between table cells.
 */
table {
  border-collapse: collapse;
  border-spacing: 0;
}
td,
th {
  padding: 0;
}
/* AMUI Base
   ========================================================================== */
/**
 * `font-size` is set in `html` element to support the `rem` unit for font-sizes
 * NOTE: IE9 & IE10 do not recognize `rem` units when used with the
   `font` shorthand property.
 */
html {
  font-size: 10px;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
body {
  position: relative;
  background: #fff;
  font-family: "Segoe UI", "Lucida Grande", Helvetica, Arial, "Microsoft YaHei", FreeSans, Arimo, "Droid Sans", "wenquanyi micro hei", "Hiragino Sans GB", "Hiragino Sans GB W3", "FontAwesome", sans-serif;
  font-weight: normal;
  line-height: 1.6;
  color: #333333;
  font-size: 1.6rem;
}
/**
 * 1. http://usabilitypost.com/2012/11/15/w-optimizelegibility-on-mobile-devices/
 * 2. http://maxvoltar.com/archive/-webkit-font-smoothing
 * NOTE: http://usabilitypost.com/2012/11/05/stop-fixing-font-smoothing/
 * 3. http://maximilianhoffmann.com/posts/better-font-rendering-on-osx
 *    http://jaydenseric.com/blog/css-font-smoothing-for-firefox-mac
 */
body,
input,
textarea,
select,
button {
  text-rendering: optimizeLegibility;
  /* 1 */
  -webkit-font-smoothing: antialiased;
  /* 2 */
  -moz-osx-font-smoothing: grayscale;
  /* 3 Default: auto */
  -moz-font-feature-settings: "liga", "kern";
}
/*Only phones */
@media only screen and (max-width: 640px) {
  /**
   * Break strings if their length exceeds the width of their container
   */
  body {
    word-wrap: break-word;
    -webkit-hyphens: auto;
    -ms-hyphens: auto;
    -moz-hyphens: auto;
    hyphens: auto;
  }
}
/**
 * links
 */
a {
  color: #0e90d2;
}
a:hover,
a:focus {
  color: #095f8a;
}
a:focus {
  outline: thin dotted;
  outline: 1px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}
/* Insert */
ins {
  background: #ffa;
  color: #333333;
}
/**
 * Mark
 * Note: Addresses styling not present in IE 8/9.
 */
mark {
  background: #ffa;
  color: #333333;
}
/**
 * Abbreviation and definition
 */
abbr[title],
dfn[title] {
  cursor: help;
}
dfn[title] {
  border-bottom: 1px dotted;
  font-style: normal;
}
/* Spacing for block elements */
p,
hr,
ul,
ol,
dl,
blockquote,
pre,
address,
fieldset,
figure {
  margin: 0 0 1.6rem 0;
}
* + p,
* + hr,
* + ul,
* + ol,
* + dl,
* + blockquote,
* + pre,
* + address,
* + fieldset,
* + figure {
  margin-top: 1.6rem;
}
/* Headings
   ========================================================================== */
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0 0 1.6rem 0;
  font-weight: 600;
  font-size: 100%;
}
h1 {
  font-size: 1.5em;
}
h2 {
  font-size: 1.25em;
}
* + h1,
* + h2,
* + h3,
* + h4,
* + h5,
* + h6 {
  margin-top: 2em;
}
/* Ordered and unordered lists */
ul,
ol {
  padding-left: 2em;
}
/* Reset margin for nested lists */
ul > li > ul,
ul > li > ol,
ol > li > ol,
ol > li > ul {
  margin: 1em 0;
}
/* Description lists */
dt {
  font-weight: bold;
}
dt + dd {
  margin-top: .5em;
}
dd {
  margin-left: 0;
}
dd + dt {
  margin-top: 1em;
}
/**
 * Horizontal rule
 * 1. Address differences between Firefox and other browsers.
 */
hr {
  display: block;
  padding: 0;
  border: 0;
  height: 0;
  border-top: 1px solid #eeeeee;
  /* 1 */
  -webkit-box-sizing: content-box;
          box-sizing: content-box;
  /* 1 */
}
/* Address */
address {
  font-style: normal;
}
/* Quotes */
blockquote {
  padding-top: 5px;
  padding-bottom: 5px;
  padding-left: 15px;
  border-left: 4px solid #ddd;
  font-family: Georgia, "Times New Roman", Times, Kai, "Kaiti SC", KaiTi, BiauKai, "FontAwesome", serif;
  /* Small print for identifying the source */
  /* Smaller margin if `small` follows */
}
blockquote small {
  display: block;
  color: #999999;
  font-family: "Segoe UI", "Lucida Grande", Helvetica, Arial, "Microsoft YaHei", FreeSans, Arimo, "Droid Sans", "wenquanyi micro hei", "Hiragino Sans GB", "Hiragino Sans GB W3", "FontAwesome", sans-serif;
  text-align: right;
}
blockquote p:last-of-type {
  margin-bottom: 0;
}
/* iframe */
iframe {
  border: 0;
}
/* Forms
   ========================================================================== */
/**
  * Vertical alignment
  * Exclude `radio` and `checkbox` elements because the default `baseline` value aligns better with text
  */
button,
input:not([type="radio"]):not([type="checkbox"]),
select {
  vertical-align: middle;
}
/**
 * Fix viewport for IE10 snap mode
 * http://timkadlec.com/2012/10/ie10-snap-mode-and-responsive-design/
 * http://msdn.microsoft.com/en-us/library/ie/hh869615%28v=vs.85%29.aspx
 * http://msdn.microsoft.com/zh-cn/library/ie/hh708740(v=vs.85).aspx
 * @2014.01.20: 璁剧疆涓€涓嬪睘鎬т互鍚庯紝windows Phone 8 涓婃樉绀虹殑瀛椾綋杩囧皬锛屾殏鏃舵敞閲婃帀
 * ========================================================================== */
/*
@-ms-viewport {
  width: device-width;
}
*/
.am-scrollbar-measure {
  width: 100px;
  height: 100px;
  overflow: scroll;
  position: absolute;
  top: -9999px;
}
/* ==========================================================================
   Component: Grid
 ============================================================================ */
.am-container {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
  width: 100%;
  max-width: 1000px;
}
.am-container:before,
.am-container:after {
  content: " ";
  display: table;
}
.am-container:after {
  clear: both;
}
@media only screen and (min-width:641px) {
  .am-container {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}
.am-container > .am-g {
  width: auto;
  margin-left: -1rem;
  margin-right: -1rem;
}
@media only screen and (min-width:641px) {
  .am-container > .am-g {
    margin-left: -1.5rem;
    margin-right: -1.5rem;
  }
}
.am-g {
  margin: 0 auto;
  width: 100%;
}
.am-g:before,
.am-g:after {
  content: " ";
  display: table;
}
.am-g:after {
  clear: both;
}
.am-g .am-g {
  margin-left: -1rem;
  margin-right: -1rem;
  width: auto;
}
.am-g .am-g.am-g-collapse {
  margin-left: 0;
  margin-right: 0;
  width: auto;
}
@media only screen and (min-width:641px) {
  .am-g .am-g {
    margin-left: -1.5rem;
    margin-right: -1.5rem;
  }
}
.am-g.am-g-collapse .am-g {
  margin-left: 0;
  margin-right: 0;
}
.am-g-collapse [class*="am-u-"] {
  padding-left: 0;
  padding-right: 0;
}
.am-g-fixed {
  max-width: 1000px;
}
[class*="am-u-"] {
  width: 100%;
  padding-left: 1rem;
  padding-right: 1rem;
  float: left;
  position: relative;
}
[class*="am-u-"] + [class*="am-u-"]:last-child {
  float: right;
}
[class*="am-u-"] + [class*="am-u-"].am-u-end {
  float: left;
}
@media only screen and (min-width:641px) {
  [class*="am-u-"] {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}
[class*="am-u-pull-"] {
  left: auto;
}
[class*="am-u-push-"] {
  right: auto;
}
@media only screen {
  .am-u-sm-1 {
    width: 8.33333333%;
  }
  .am-u-sm-2 {
    width: 16.66666667%;
  }
  .am-u-sm-3 {
    width: 25%;
  }
  .am-u-sm-4 {
    width: 33.33333333%;
  }
  .am-u-sm-5 {
    width: 41.66666667%;
  }
  .am-u-sm-6 {
    width: 50%;
  }
  .am-u-sm-7 {
    width: 58.33333333%;
  }
  .am-u-sm-8 {
    width: 66.66666667%;
  }
  .am-u-sm-9 {
    width: 75%;
  }
  .am-u-sm-10 {
    width: 83.33333333%;
  }
  .am-u-sm-11 {
    width: 91.66666667%;
  }
  .am-u-sm-12 {
    width: 100%;
  }
  .am-u-sm-pull-0 {
    right: 0;
  }
  .am-u-sm-pull-1 {
    right: 8.33333333%;
  }
  .am-u-sm-pull-2 {
    right: 16.66666667%;
  }
  .am-u-sm-pull-3 {
    right: 25%;
  }
  .am-u-sm-pull-4 {
    right: 33.33333333%;
  }
  .am-u-sm-pull-5 {
    right: 41.66666667%;
  }
  .am-u-sm-pull-6 {
    right: 50%;
  }
  .am-u-sm-pull-7 {
    right: 58.33333333%;
  }
  .am-u-sm-pull-8 {
    right: 66.66666667%;
  }
  .am-u-sm-pull-9 {
    right: 75%;
  }
  .am-u-sm-pull-10 {
    right: 83.33333333%;
  }
  .am-u-sm-pull-11 {
    right: 91.66666667%;
  }
  .am-u-sm-push-0 {
    left: 0;
  }
  .am-u-sm-push-1 {
    left: 8.33333333%;
  }
  .am-u-sm-push-2 {
    left: 16.66666667%;
  }
  .am-u-sm-push-3 {
    left: 25%;
  }
  .am-u-sm-push-4 {
    left: 33.33333333%;
  }
  .am-u-sm-push-5 {
    left: 41.66666667%;
  }
  .am-u-sm-push-6 {
    left: 50%;
  }
  .am-u-sm-push-7 {
    left: 58.33333333%;
  }
  .am-u-sm-push-8 {
    left: 66.66666667%;
  }
  .am-u-sm-push-9 {
    left: 75%;
  }
  .am-u-sm-push-10 {
    left: 83.33333333%;
  }
  .am-u-sm-push-11 {
    left: 91.66666667%;
  }
  .am-u-sm-offset-0 {
    margin-left: 0;
  }
  .am-u-sm-offset-1 {
    margin-left: 8.33333333%;
  }
  .am-u-sm-offset-2 {
    margin-left: 16.66666667%;
  }
  .am-u-sm-offset-3 {
    margin-left: 25%;
  }
  .am-u-sm-offset-4 {
    margin-left: 33.33333333%;
  }
  .am-u-sm-offset-5 {
    margin-left: 41.66666667%;
  }
  .am-u-sm-offset-6 {
    margin-left: 50%;
  }
  .am-u-sm-offset-7 {
    margin-left: 58.33333333%;
  }
  .am-u-sm-offset-8 {
    margin-left: 66.66666667%;
  }
  .am-u-sm-offset-9 {
    margin-left: 75%;
  }
  .am-u-sm-offset-10 {
    margin-left: 83.33333333%;
  }
  .am-u-sm-offset-11 {
    margin-left: 91.66666667%;
  }
  .am-u-sm-reset-order {
    margin-left: 0;
    margin-right: 0;
    left: auto;
    right: auto;
    float: left;
  }
  [class*="am-u-"].am-u-sm-centered {
    margin-left: auto;
    margin-right: auto;
    float: none;
  }
  [class*="am-u-"].am-u-sm-centered:last-child {
    float: none;
  }
  [class*="am-u-"].am-u-sm-uncentered {
    margin-left: 0;
    margin-right: 0;
    float: left;
  }
  [class*="am-u-"].am-u-sm-uncentered:last-child {
    float: left;
  }
}
@media only screen and (min-width:641px) {
  .am-u-md-1 {
    width: 8.33333333%;
  }
  .am-u-md-2 {
    width: 16.66666667%;
  }
  .am-u-md-3 {
    width: 25%;
  }
  .am-u-md-4 {
    width: 33.33333333%;
  }
  .am-u-md-5 {
    width: 41.66666667%;
  }
  .am-u-md-6 {
    width: 50%;
  }
  .am-u-md-7 {
    width: 58.33333333%;
  }
  .am-u-md-8 {
    width: 66.66666667%;
  }
  .am-u-md-9 {
    width: 75%;
  }
  .am-u-md-10 {
    width: 83.33333333%;
  }
  .am-u-md-11 {
    width: 91.66666667%;
  }
  .am-u-md-12 {
    width: 100%;
  }
  .am-u-md-pull-0 {
    right: 0;
  }
  .am-u-md-pull-1 {
    right: 8.33333333%;
  }
  .am-u-md-pull-2 {
    right: 16.66666667%;
  }
  .am-u-md-pull-3 {
    right: 25%;
  }
  .am-u-md-pull-4 {
    right: 33.33333333%;
  }
  .am-u-md-pull-5 {
    right: 41.66666667%;
  }
  .am-u-md-pull-6 {
    right: 50%;
  }
  .am-u-md-pull-7 {
    right: 58.33333333%;
  }
  .am-u-md-pull-8 {
    right: 66.66666667%;
  }
  .am-u-md-pull-9 {
    right: 75%;
  }
  .am-u-md-pull-10 {
    right: 83.33333333%;
  }
  .am-u-md-pull-11 {
    right: 91.66666667%;
  }
  .am-u-md-push-0 {
    left: 0;
  }
  .am-u-md-push-1 {
    left: 8.33333333%;
  }
  .am-u-md-push-2 {
    left: 16.66666667%;
  }
  .am-u-md-push-3 {
    left: 25%;
  }
  .am-u-md-push-4 {
    left: 33.33333333%;
  }
  .am-u-md-push-5 {
    left: 41.66666667%;
  }
  .am-u-md-push-6 {
    left: 50%;
  }
  .am-u-md-push-7 {
    left: 58.33333333%;
  }
  .am-u-md-push-8 {
    left: 66.66666667%;
  }
  .am-u-md-push-9 {
    left: 75%;
  }
  .am-u-md-push-10 {
    left: 83.33333333%;
  }
  .am-u-md-push-11 {
    left: 91.66666667%;
  }
  .am-u-md-offset-0 {
    margin-left: 0;
  }
  .am-u-md-offset-1 {
    margin-left: 8.33333333%;
  }
  .am-u-md-offset-2 {
    margin-left: 16.66666667%;
  }
  .am-u-md-offset-3 {
    margin-left: 25%;
  }
  .am-u-md-offset-4 {
    margin-left: 33.33333333%;
  }
  .am-u-md-offset-5 {
    margin-left: 41.66666667%;
  }
  .am-u-md-offset-6 {
    margin-left: 50%;
  }
  .am-u-md-offset-7 {
    margin-left: 58.33333333%;
  }
  .am-u-md-offset-8 {
    margin-left: 66.66666667%;
  }
  .am-u-md-offset-9 {
    margin-left: 75%;
  }
  .am-u-md-offset-10 {
    margin-left: 83.33333333%;
  }
  .am-u-md-offset-11 {
    margin-left: 91.66666667%;
  }
  .am-u-md-reset-order {
    margin-left: 0;
    margin-right: 0;
    left: auto;
    right: auto;
    float: left;
  }
  [class*="am-u-"].am-u-md-centered {
    margin-left: auto;
    margin-right: auto;
    float: none;
  }
  [class*="am-u-"].am-u-md-centered:last-child {
    float: none;
  }
  [class*="am-u-"].am-u-md-uncentered {
    margin-left: 0;
    margin-right: 0;
    float: left;
  }
  [class*="am-u-"].am-u-md-uncentered:last-child {
    float: left;
  }
}
@media only screen and (min-width:1025px) {
  .am-u-lg-1 {
    width: 8.33333333%;
  }
  .am-u-lg-2 {
    width: 16.66666667%;
  }
  .am-u-lg-3 {
    width: 25%;
  }
  .am-u-lg-4 {
    width: 33.33333333%;
  }
  .am-u-lg-5 {
    width: 41.66666667%;
  }
  .am-u-lg-6 {
    width: 50%;
  }
  .am-u-lg-7 {
    width: 58.33333333%;
  }
  .am-u-lg-8 {
    width: 66.66666667%;
  }
  .am-u-lg-9 {
    width: 75%;
  }
  .am-u-lg-10 {
    width: 83.33333333%;
  }
  .am-u-lg-11 {
    width: 91.66666667%;
  }
  .am-u-lg-12 {
    width: 100%;
  }
  .am-u-lg-pull-0 {
    right: 0;
  }
  .am-u-lg-pull-1 {
    right: 8.33333333%;
  }
  .am-u-lg-pull-2 {
    right: 16.66666667%;
  }
  .am-u-lg-pull-3 {
    right: 25%;
  }
  .am-u-lg-pull-4 {
    right: 33.33333333%;
  }
  .am-u-lg-pull-5 {
    right: 41.66666667%;
  }
  .am-u-lg-pull-6 {
    right: 50%;
  }
  .am-u-lg-pull-7 {
    right: 58.33333333%;
  }
  .am-u-lg-pull-8 {
    right: 66.66666667%;
  }
  .am-u-lg-pull-9 {
    right: 75%;
  }
  .am-u-lg-pull-10 {
    right: 83.33333333%;
  }
  .am-u-lg-pull-11 {
    right: 91.66666667%;
  }
  .am-u-lg-push-0 {
    left: 0;
  }
  .am-u-lg-push-1 {
    left: 8.33333333%;
  }
  .am-u-lg-push-2 {
    left: 16.66666667%;
  }
  .am-u-lg-push-3 {
    left: 25%;
  }
  .am-u-lg-push-4 {
    left: 33.33333333%;
  }
  .am-u-lg-push-5 {
    left: 41.66666667%;
  }
  .am-u-lg-push-6 {
    left: 50%;
  }
  .am-u-lg-push-7 {
    left: 58.33333333%;
  }
  .am-u-lg-push-8 {
    left: 66.66666667%;
  }
  .am-u-lg-push-9 {
    left: 75%;
  }
  .am-u-lg-push-10 {
    left: 83.33333333%;
  }
  .am-u-lg-push-11 {
    left: 91.66666667%;
  }
  .am-u-lg-offset-0 {
    margin-left: 0;
  }
  .am-u-lg-offset-1 {
    margin-left: 8.33333333%;
  }
  .am-u-lg-offset-2 {
    margin-left: 16.66666667%;
  }
  .am-u-lg-offset-3 {
    margin-left: 25%;
  }
  .am-u-lg-offset-4 {
    margin-left: 33.33333333%;
  }
  .am-u-lg-offset-5 {
    margin-left: 41.66666667%;
  }
  .am-u-lg-offset-6 {
    margin-left: 50%;
  }
  .am-u-lg-offset-7 {
    margin-left: 58.33333333%;
  }
  .am-u-lg-offset-8 {
    margin-left: 66.66666667%;
  }
  .am-u-lg-offset-9 {
    margin-left: 75%;
  }
  .am-u-lg-offset-10 {
    margin-left: 83.33333333%;
  }
  .am-u-lg-offset-11 {
    margin-left: 91.66666667%;
  }
  .am-u-lg-reset-order {
    margin-left: 0;
    margin-right: 0;
    left: auto;
    right: auto;
    float: left;
  }
  [class*="am-u-"].am-u-lg-centered {
    margin-left: auto;
    margin-right: auto;
    float: none;
  }
  [class*="am-u-"].am-u-lg-centered:last-child {
    float: none;
  }
  [class*="am-u-"].am-u-lg-uncentered {
    margin-left: 0;
    margin-right: 0;
    float: left;
  }
  [class*="am-u-"].am-u-lg-uncentered:last-child {
    float: left;
  }
}
/* ==========================================================================
   Component: AVG Grid
 ============================================================================ */
[class*="am-avg-"] {
  display: block;
  padding: 0;
  margin: 0;
  list-style: none;
}
[class*="am-avg-"]:before,
[class*="am-avg-"]:after {
  content: " ";
  display: table;
}
[class*="am-avg-"]:after {
  clear: both;
}
[class*="am-avg-"] > li {
  display: block;
  height: auto;
  float: left;
}
@media only screen {
  .am-avg-sm-1 > li {
    width: 100%;
  }
  .am-avg-sm-1 > li:nth-of-type(n) {
    clear: none;
  }
  .am-avg-sm-1 > li:nth-of-type(1n + 1) {
    clear: both;
  }
  .am-avg-sm-2 > li {
    width: 50%;
  }
  .am-avg-sm-2 > li:nth-of-type(n) {
    clear: none;
  }
  .am-avg-sm-2 > li:nth-of-type(2n + 1) {
    clear: both;
  }
  .am-avg-sm-3 > li {
    width: 33.33333333%;
  }
  .am-avg-sm-3 > li:nth-of-type(n) {
    clear: none;
  }
  .am-avg-sm-3 > li:nth-of-type(3n + 1) {
    clear: both;
  }
  .am-avg-sm-4 > li {
    width: 25%;
  }
  .am-avg-sm-4 > li:nth-of-type(n) {
    clear: none;
  }
  .am-avg-sm-4 > li:nth-of-type(4n + 1) {
    clear: both;
  }
  .am-avg-sm-5 > li {
    width: 20%;
  }
  .am-avg-sm-5 > li:nth-of-type(n) {
    clear: none;
  }
  .am-avg-sm-5 > li:nth-of-type(5n + 1) {
    clear: both;
  }
  .am-avg-sm-6 > li {
    width: 16.66666667%;
  }
  .am-avg-sm-6 > li:nth-of-type(n) {
    clear: none;
  }
  .am-avg-sm-6 > li:nth-of-type(6n + 1) {
    clear: both;
  }
  .am-avg-sm-7 > li {
    width: 14.28571429%;
  }
  .am-avg-sm-7 > li:nth-of-type(n) {
    clear: none;
  }
  .am-avg-sm-7 > li:nth-of-type(7n + 1) {
    clear: both;
  }
  .am-avg-sm-8 > li {
    width: 12.5%;
  }
  .am-avg-sm-8 > li:nth-of-type(n) {
    clear: none;
  }
  .am-avg-sm-8 > li:nth-of-type(8n + 1) {
    clear: both;
  }
  .am-avg-sm-9 > li {
    width: 11.11111111%;
  }
  .am-avg-sm-9 > li:nth-of-type(n) {
    clear: none;
  }
  .am-avg-sm-9 > li:nth-of-type(9n + 1) {
    clear: both;
  }
  .am-avg-sm-10 > li {
    width: 10%;
  }
  .am-avg-sm-10 > li:nth-of-type(n) {
    clear: none;
  }
  .am-avg-sm-10 > li:nth-of-type(10n + 1) {
    clear: both;
  }
  .am-avg-sm-11 > li {
    width: 9.09090909%;
  }
  .am-avg-sm-11 > li:nth-of-type(n) {
    clear: none;
  }
  .am-avg-sm-11 > li:nth-of-type(11n + 1) {
    clear: both;
  }
  .am-avg-sm-12 > li {
    width: 8.33333333%;
  }
  .am-avg-sm-12 > li:nth-of-type(n) {
    clear: none;
  }
  .am-avg-sm-12 > li:nth-of-type(12n + 1) {
    clear: both;
  }
}
@media only screen and (min-width:641px) {
  .am-avg-md-1 > li {
    width: 100%;
  }
  .am-avg-md-1 > li:nth-of-type(n) {
    clear: none;
  }
  .am-avg-md-1 > li:nth-of-type(1n + 1) {
    clear: both;
  }
  .am-avg-md-2 > li {
    width: 50%;
  }
  .am-avg-md-2 > li:nth-of-type(n) {
    clear: none;
  }
  .am-avg-md-2 > li:nth-of-type(2n + 1) {
    clear: both;
  }
  .am-avg-md-3 > li {
    width: 33.33333333%;
  }
  .am-avg-md-3 > li:nth-of-type(n) {
    clear: none;
  }
  .am-avg-md-3 > li:nth-of-type(3n + 1) {
    clear: both;
  }
  .am-avg-md-4 > li {
    width: 25%;
  }
  .am-avg-md-4 > li:nth-of-type(n) {
    clear: none;
  }
  .am-avg-md-4 > li:nth-of-type(4n + 1) {
    clear: both;
  }
  .am-avg-md-5 > li {
    width: 20%;
  }
  .am-avg-md-5 > li:nth-of-type(n) {
    clear: none;
  }
  .am-avg-md-5 > li:nth-of-type(5n + 1) {
    clear: both;
  }
  .am-avg-md-6 > li {
    width: 16.66666667%;
  }
  .am-avg-md-6 > li:nth-of-type(n) {
    clear: none;
  }
  .am-avg-md-6 > li:nth-of-type(6n + 1) {
    clear: both;
  }
  .am-avg-md-7 > li {
    width: 14.28571429%;
  }
  .am-avg-md-7 > li:nth-of-type(n) {
    clear: none;
  }
  .am-avg-md-7 > li:nth-of-type(7n + 1) {
    clear: both;
  }
  .am-avg-md-8 > li {
    width: 12.5%;
  }
  .am-avg-md-8 > li:nth-of-type(n) {
    clear: none;
  }
  .am-avg-md-8 > li:nth-of-type(8n + 1) {
    clear: both;
  }
  .am-avg-md-9 > li {
    width: 11.11111111%;
  }
  .am-avg-md-9 > li:nth-of-type(n) {
    clear: none;
  }
  .am-avg-md-9 > li:nth-of-type(9n + 1) {
    clear: both;
  }
  .am-avg-md-10 > li {
    width: 10%;
  }
  .am-avg-md-10 > li:nth-of-type(n) {
    clear: none;
  }
  .am-avg-md-10 > li:nth-of-type(10n + 1) {
    clear: both;
  }
  .am-avg-md-11 > li {
    width: 9.09090909%;
  }
  .am-avg-md-11 > li:nth-of-type(n) {
    clear: none;
  }
  .am-avg-md-11 > li:nth-of-type(11n + 1) {
    clear: both;
  }
  .am-avg-md-12 > li {
    width: 8.33333333%;
  }
  .am-avg-md-12 > li:nth-of-type(n) {
    clear: none;
  }
  .am-avg-md-12 > li:nth-of-type(12n + 1) {
    clear: both;
  }
}
@media only screen and (min-width:1025px) {
  .am-avg-lg-1 > li {
    width: 100%;
  }
  .am-avg-lg-1 > li:nth-of-type(n) {
    clear: none;
  }
  .am-avg-lg-1 > li:nth-of-type(1n + 1) {
    clear: both;
  }
  .am-avg-lg-2 > li {
    width: 50%;
  }
  .am-avg-lg-2 > li:nth-of-type(n) {
    clear: none;
  }
  .am-avg-lg-2 > li:nth-of-type(2n + 1) {
    clear: both;
  }
  .am-avg-lg-3 > li {
    width: 33.33333333%;
  }
  .am-avg-lg-3 > li:nth-of-type(n) {
    clear: none;
  }
  .am-avg-lg-3 > li:nth-of-type(3n + 1) {
    clear: both;
  }
  .am-avg-lg-4 > li {
    width: 25%;
  }
  .am-avg-lg-4 > li:nth-of-type(n) {
    clear: none;
  }
  .am-avg-lg-4 > li:nth-of-type(4n + 1) {
    clear: both;
  }
  .am-avg-lg-5 > li {
    width: 20%;
  }
  .am-avg-lg-5 > li:nth-of-type(n) {
    clear: none;
  }
  .am-avg-lg-5 > li:nth-of-type(5n + 1) {
    clear: both;
  }
  .am-avg-lg-6 > li {
    width: 16.66666667%;
  }
  .am-avg-lg-6 > li:nth-of-type(n) {
    clear: none;
  }
  .am-avg-lg-6 > li:nth-of-type(6n + 1) {
    clear: both;
  }
  .am-avg-lg-7 > li {
    width: 14.28571429%;
  }
  .am-avg-lg-7 > li:nth-of-type(n) {
    clear: none;
  }
  .am-avg-lg-7 > li:nth-of-type(7n + 1) {
    clear: both;
  }
  .am-avg-lg-8 > li {
    width: 12.5%;
  }
  .am-avg-lg-8 > li:nth-of-type(n) {
    clear: none;
  }
  .am-avg-lg-8 > li:nth-of-type(8n + 1) {
    clear: both;
  }
  .am-avg-lg-9 > li {
    width: 11.11111111%;
  }
  .am-avg-lg-9 > li:nth-of-type(n) {
    clear: none;
  }
  .am-avg-lg-9 > li:nth-of-type(9n + 1) {
    clear: both;
  }
  .am-avg-lg-10 > li {
    width: 10%;
  }
  .am-avg-lg-10 > li:nth-of-type(n) {
    clear: none;
  }
  .am-avg-lg-10 > li:nth-of-type(10n + 1) {
    clear: both;
  }
  .am-avg-lg-11 > li {
    width: 9.09090909%;
  }
  .am-avg-lg-11 > li:nth-of-type(n) {
    clear: none;
  }
  .am-avg-lg-11 > li:nth-of-type(11n + 1) {
    clear: both;
  }
  .am-avg-lg-12 > li {
    width: 8.33333333%;
  }
  .am-avg-lg-12 > li:nth-of-type(n) {
    clear: none;
  }
  .am-avg-lg-12 > li:nth-of-type(12n + 1) {
    clear: both;
  }
}
/* ==========================================================================
   Component: Code
 ============================================================================ */
/* Inline and block code styles */
code,
kbd,
pre,
samp {
  font-family: Monaco, Menlo, Consolas, "Courier New", "FontAwesome", monospace;
}
/* Inline code */
code {
  padding: 2px 4px;
  font-size: 1.3rem;
  color: #c7254e;
  background-color: #f8f8f8;
  white-space: nowrap;
  border-radius: 0;
}
/* Code block */
pre {
  display: block;
  padding: 1rem;
  margin: 1rem  0;
  font-size: 1.3rem;
  line-height: 1.6;
  word-break: break-all;
  word-wrap: break-word;
  color: #555555;
  background-color: #f8f8f8;
  border: 1px solid #dedede;
  border-radius: 0;
}
pre code {
  padding: 0;
  font-size: inherit;
  color: inherit;
  white-space: pre-wrap;
  background-color: transparent;
  border-radius: 0;
}
/* Enable scrollable blocks of code */
.am-pre-scrollable {
  max-height: 24rem;
  overflow-y: scroll;
}
/* ==========================================================================
   Component: Button
 ============================================================================ */
.am-btn {
  display: inline-block;
  margin-bottom: 0;
  padding: 0.5em 1em;
  vertical-align: middle;
  font-size: 1.6rem;
  font-weight: normal;
  line-height: 1.2;
  text-align: center;
  white-space: nowrap;
  background-image: none;
  border: 1px solid transparent;
  border-radius: 0;
  cursor: pointer;
  outline: none;
  -webkit-appearance: none;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  -webkit-transition: background-color 300ms ease-out, border-color 300ms ease-out;
  transition: background-color 300ms ease-out, border-color 300ms ease-out;
}
.am-btn:focus,
.am-btn:active:focus {
  outline: thin dotted;
  outline: 1px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}
.am-btn:hover,
.am-btn:focus {
  color: #444;
  text-decoration: none;
}
.am-btn:active,
.am-btn.am-active {
  background-image: none;
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.15);
          box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.15);
}
.am-btn.am-disabled,
.am-btn[disabled],
fieldset[disabled] .am-btn {
  pointer-events: none;
  border-color: transparent;
  cursor: not-allowed;
  opacity: 0.45;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.am-btn.am-round {
  border-radius: 1000px;
}
.am-btn.am-radius {
  border-radius: 2px;
}
.am-btn-default {
  color: #444;
  background-color: #e6e6e6;
  border-color: #e6e6e6;
}
a.am-btn-default:visited {
  color: #444;
}
.am-btn-default:hover,
.am-btn-default:focus,
.am-btn-default:active,
.am-btn-default.am-active,
.am-dropdown.am-active .am-btn-default.am-dropdown-toggle {
  color: #444;
  border-color: #c7c7c7;
}
.am-btn-default:hover,
.am-btn-default:focus {
  background-color: #d4d4d4;
}
.am-btn-default:active,
.am-btn-default.am-active,
.am-dropdown.am-active .am-btn-default.am-dropdown-toggle {
  background-image: none;
  background-color: #c2c2c2;
}
.am-btn-default.am-disabled,
.am-btn-default[disabled],
fieldset[disabled] .am-btn-default,
.am-btn-default.am-disabled:hover,
.am-btn-default[disabled]:hover,
fieldset[disabled] .am-btn-default:hover,
.am-btn-default.am-disabled:focus,
.am-btn-default[disabled]:focus,
fieldset[disabled] .am-btn-default:focus,
.am-btn-default.am-disabled:active,
.am-btn-default[disabled]:active,
fieldset[disabled] .am-btn-default:active,
.am-btn-default.am-disabled.am-active,
.am-btn-default[disabled].am-active,
fieldset[disabled] .am-btn-default.am-active {
  background-color: #e6e6e6;
  border-color: #e6e6e6;
}
.am-btn-group .am-btn-default,
.am-btn-group-stacked .am-btn-default {
  border-color: #d9d9d9;
}
.am-btn-primary {
  color: #fff;
  background-color: #0e90d2;
  border-color: #0e90d2;
}
a.am-btn-primary:visited {
  color: #fff;
}
.am-btn-primary:hover,
.am-btn-primary:focus,
.am-btn-primary:active,
.am-btn-primary.am-active,
.am-dropdown.am-active .am-btn-primary.am-dropdown-toggle {
  color: #fff;
  border-color: #0a6999;
}
.am-btn-primary:hover,
.am-btn-primary:focus {
  background-color: #0c79b1;
}
.am-btn-primary:active,
.am-btn-primary.am-active,
.am-dropdown.am-active .am-btn-primary.am-dropdown-toggle {
  background-image: none;
  background-color: #0a628f;
}
.am-btn-primary.am-disabled,
.am-btn-primary[disabled],
fieldset[disabled] .am-btn-primary,
.am-btn-primary.am-disabled:hover,
.am-btn-primary[disabled]:hover,
fieldset[disabled] .am-btn-primary:hover,
.am-btn-primary.am-disabled:focus,
.am-btn-primary[disabled]:focus,
fieldset[disabled] .am-btn-primary:focus,
.am-btn-primary.am-disabled:active,
.am-btn-primary[disabled]:active,
fieldset[disabled] .am-btn-primary:active,
.am-btn-primary.am-disabled.am-active,
.am-btn-primary[disabled].am-active,
fieldset[disabled] .am-btn-primary.am-active {
  background-color: #0e90d2;
  border-color: #0e90d2;
}
.am-btn-group .am-btn-primary,
.am-btn-group-stacked .am-btn-primary {
  border-color: #0c80ba;
}
.am-btn-secondary {
  color: #fff;
  background-color: #3bb4f2;
  border-color: #3bb4f2;
}
a.am-btn-secondary:visited {
  color: #fff;
}
.am-btn-secondary:hover,
.am-btn-secondary:focus,
.am-btn-secondary:active,
.am-btn-secondary.am-active,
.am-dropdown.am-active .am-btn-secondary.am-dropdown-toggle {
  color: #fff;
  border-color: #0f9ae0;
}
.am-btn-secondary:hover,
.am-btn-secondary:focus {
  background-color: #19a7f0;
}
.am-btn-secondary:active,
.am-btn-secondary.am-active,
.am-dropdown.am-active .am-btn-secondary.am-dropdown-toggle {
  background-image: none;
  background-color: #0e93d7;
}
.am-btn-secondary.am-disabled,
.am-btn-secondary[disabled],
fieldset[disabled] .am-btn-secondary,
.am-btn-secondary.am-disabled:hover,
.am-btn-secondary[disabled]:hover,
fieldset[disabled] .am-btn-secondary:hover,
.am-btn-secondary.am-disabled:focus,
.am-btn-secondary[disabled]:focus,
fieldset[disabled] .am-btn-secondary:focus,
.am-btn-secondary.am-disabled:active,
.am-btn-secondary[disabled]:active,
fieldset[disabled] .am-btn-secondary:active,
.am-btn-secondary.am-disabled.am-active,
.am-btn-secondary[disabled].am-active,
fieldset[disabled] .am-btn-secondary.am-active {
  background-color: #3bb4f2;
  border-color: #3bb4f2;
}
.am-btn-group .am-btn-secondary,
.am-btn-group-stacked .am-btn-secondary {
  border-color: #23abf0;
}
.am-btn-warning {
  color: #fff;
  background-color: #F37B1D;
  border-color: #F37B1D;
}
a.am-btn-warning:visited {
  color: #fff;
}
.am-btn-warning:hover,
.am-btn-warning:focus,
.am-btn-warning:active,
.am-btn-warning.am-active,
.am-dropdown.am-active .am-btn-warning.am-dropdown-toggle {
  color: #fff;
  border-color: #c85e0b;
}
.am-btn-warning:hover,
.am-btn-warning:focus {
  background-color: #e0690c;
}
.am-btn-warning:active,
.am-btn-warning.am-active,
.am-dropdown.am-active .am-btn-warning.am-dropdown-toggle {
  background-image: none;
  background-color: #be590a;
}
.am-btn-warning.am-disabled,
.am-btn-warning[disabled],
fieldset[disabled] .am-btn-warning,
.am-btn-warning.am-disabled:hover,
.am-btn-warning[disabled]:hover,
fieldset[disabled] .am-btn-warning:hover,
.am-btn-warning.am-disabled:focus,
.am-btn-warning[disabled]:focus,
fieldset[disabled] .am-btn-warning:focus,
.am-btn-warning.am-disabled:active,
.am-btn-warning[disabled]:active,
fieldset[disabled] .am-btn-warning:active,
.am-btn-warning.am-disabled.am-active,
.am-btn-warning[disabled].am-active,
fieldset[disabled] .am-btn-warning.am-active {
  background-color: #F37B1D;
  border-color: #F37B1D;
}
.am-btn-group .am-btn-warning,
.am-btn-group-stacked .am-btn-warning {
  border-color: #ea6e0c;
}
.am-btn-danger {
  color: #fff;
  background-color: #dd514c;
  border-color: #dd514c;
}
a.am-btn-danger:visited {
  color: #fff;
}
.am-btn-danger:hover,
.am-btn-danger:focus,
.am-btn-danger:active,
.am-btn-danger.am-active,
.am-dropdown.am-active .am-btn-danger.am-dropdown-toggle {
  color: #fff;
  border-color: #c62b26;
}
.am-btn-danger:hover,
.am-btn-danger:focus {
  background-color: #d7342e;
}
.am-btn-danger:active,
.am-btn-danger.am-active,
.am-dropdown.am-active .am-btn-danger.am-dropdown-toggle {
  background-image: none;
  background-color: #be2924;
}
.am-btn-danger.am-disabled,
.am-btn-danger[disabled],
fieldset[disabled] .am-btn-danger,
.am-btn-danger.am-disabled:hover,
.am-btn-danger[disabled]:hover,
fieldset[disabled] .am-btn-danger:hover,
.am-btn-danger.am-disabled:focus,
.am-btn-danger[disabled]:focus,
fieldset[disabled] .am-btn-danger:focus,
.am-btn-danger.am-disabled:active,
.am-btn-danger[disabled]:active,
fieldset[disabled] .am-btn-danger:active,
.am-btn-danger.am-disabled.am-active,
.am-btn-danger[disabled].am-active,
fieldset[disabled] .am-btn-danger.am-active {
  background-color: #dd514c;
  border-color: #dd514c;
}
.am-btn-group .am-btn-danger,
.am-btn-group-stacked .am-btn-danger {
  border-color: #d93c37;
}
.am-btn-success {
  color: #fff;
  background-color: #5eb95e;
  border-color: #5eb95e;
}
a.am-btn-success:visited {
  color: #fff;
}
.am-btn-success:hover,
.am-btn-success:focus,
.am-btn-success:active,
.am-btn-success.am-active,
.am-dropdown.am-active .am-btn-success.am-dropdown-toggle {
  color: #fff;
  border-color: #429842;
}
.am-btn-success:hover,
.am-btn-success:focus {
  background-color: #4aaa4a;
}
.am-btn-success:active,
.am-btn-success.am-active,
.am-dropdown.am-active .am-btn-success.am-dropdown-toggle {
  background-image: none;
  background-color: #3f913f;
}
.am-btn-success.am-disabled,
.am-btn-success[disabled],
fieldset[disabled] .am-btn-success,
.am-btn-success.am-disabled:hover,
.am-btn-success[disabled]:hover,
fieldset[disabled] .am-btn-success:hover,
.am-btn-success.am-disabled:focus,
.am-btn-success[disabled]:focus,
fieldset[disabled] .am-btn-success:focus,
.am-btn-success.am-disabled:active,
.am-btn-success[disabled]:active,
fieldset[disabled] .am-btn-success:active,
.am-btn-success.am-disabled.am-active,
.am-btn-success[disabled].am-active,
fieldset[disabled] .am-btn-success.am-active {
  background-color: #5eb95e;
  border-color: #5eb95e;
}
.am-btn-group .am-btn-success,
.am-btn-group-stacked .am-btn-success {
  border-color: #4db14d;
}
/* Style links like a button */
.am-btn-link {
  color: #0e90d2;
  font-weight: normal;
  cursor: pointer;
  border-radius: 0;
}
.am-btn-link,
.am-btn-link:active,
.am-btn-link[disabled],
fieldset[disabled] .am-btn-link {
  background-color: transparent;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.am-btn-link,
.am-btn-link:hover,
.am-btn-link:focus,
.am-btn-link:active {
  border-color: transparent;
}
.am-btn-link:hover,
.am-btn-link:focus {
  color: #095f8a;
  text-decoration: underline;
  background-color: transparent;
}
.am-btn-link[disabled]:hover,
fieldset[disabled] .am-btn-link:hover,
.am-btn-link[disabled]:focus,
fieldset[disabled] .am-btn-link:focus {
  color: #999999;
  text-decoration: none;
}
/* button size */
.am-btn-xs {
  font-size: 1.2rem;
}
.am-btn-sm {
  font-size: 1.4rem;
}
.am-btn-lg {
  font-size: 1.8rem;
}
.am-btn-xl {
  font-size: 2rem;
}
/* Block button  */
.am-btn-block {
  display: block;
  width: 100%;
  padding-left: 0;
  padding-right: 0;
}
/* Multiple block buttons vertically space */
.am-btn-block + .am-btn-block {
  margin-top: 5px;
}
/* Specificity overrides */
input[type="submit"].am-btn-block,
input[type="reset"].am-btn-block,
input[type="button"].am-btn-block {
  width: 100%;
}
/* Button with loading spinner */
.am-btn.am-btn-loading .am-icon-spin {
  margin-right: 5px;
}
/* ==========================================================================
   Component: Table
 ============================================================================ */
table {
  max-width: 100%;
  background-color: transparent;
  empty-cells: show;
}
table code {
  white-space: normal;
}
th {
  text-align: left;
}
.am-table {
  width: 100%;
  margin-bottom: 1.6rem;
  border-spacing: 0;
  border-collapse: separate;
}
.am-table > thead > tr > th,
.am-table > tbody > tr > th,
.am-table > tfoot > tr > th,
.am-table > thead > tr > td,
.am-table > tbody > tr > td,
.am-table > tfoot > tr > td {
  padding: 0.7rem;
  line-height: 1.6;
  vertical-align: top;
  border-top: 1px solid #ddd;
}
.am-table > thead > tr > th {
  vertical-align: bottom;
  border-bottom: 1px solid #ddd;
}
.am-table > caption + thead > tr:first-child > th,
.am-table > colgroup + thead > tr:first-child > th,
.am-table > thead:first-child > tr:first-child > th,
.am-table > caption + thead > tr:first-child > td,
.am-table > colgroup + thead > tr:first-child > td,
.am-table > thead:first-child > tr:first-child > td {
  border-top: 0;
}
.am-table > tbody + tbody tr:first-child td {
  border-top: 2px solid #ddd;
}
/* Bordered version */
.am-table-bordered {
  border: 1px solid #ddd;
  border-left: none;
}
.am-table-bordered > thead > tr > th,
.am-table-bordered > tbody > tr > th,
.am-table-bordered > tfoot > tr > th,
.am-table-bordered > thead > tr > td,
.am-table-bordered > tbody > tr > td,
.am-table-bordered > tfoot > tr > td {
  border-left: 1px solid #ddd;
  /*&:first-child {
          border-left: none;
        }*/
}
.am-table-bordered > tbody > tr:first-child > th,
.am-table-bordered > tbody > tr:first-child > td {
  border-top: none;
}
.am-table-bordered > thead + tbody > tr:first-child > th,
.am-table-bordered > thead + tbody > tr:first-child > td {
  border-top: 1px solid #ddd;
}
/* Border-radius version */
.am-table-radius {
  border: 1px solid #ddd;
  border-radius: 2px;
}
.am-table-radius > thead > tr:first-child > th:first-child,
.am-table-radius > thead > tr:first-child > td:first-child {
  border-top-left-radius: 2px;
  border-left: none;
}
.am-table-radius > thead > tr:first-child > th:last-child,
.am-table-radius > thead > tr:first-child > td:last-child {
  border-top-right-radius: 2px;
  border-right: none;
}
.am-table-radius > tbody > tr > th:first-child,
.am-table-radius > tbody > tr > td:first-child {
  border-left: none;
}
.am-table-radius > tbody > tr > th:last-child,
.am-table-radius > tbody > tr > td:last-child {
  border-right: none;
}
.am-table-radius > tbody > tr:last-child > th,
.am-table-radius > tbody > tr:last-child > td {
  border-bottom: none;
}
.am-table-radius > tbody > tr:last-child > th:first-child,
.am-table-radius > tbody > tr:last-child > td:first-child {
  border-bottom-left-radius: 2px;
}
.am-table-radius > tbody > tr:last-child > th:last-child,
.am-table-radius > tbody > tr:last-child > td:last-child {
  border-bottom-right-radius: 2px;
}
/* Zebra-striping */
.am-table-striped > tbody > tr:nth-child(odd) > td,
.am-table-striped > tbody > tr:nth-child(odd) > th {
  background-color: #f9f9f9;
}
/* Hover effect */
.am-table-hover > tbody > tr:hover > td,
.am-table-hover > tbody > tr:hover > th {
  background-color: #e9e9e9;
}
.am-table-compact > thead > tr > th,
.am-table-compact > tbody > tr > th,
.am-table-compact > tfoot > tr > th,
.am-table-compact > thead > tr > td,
.am-table-compact > tbody > tr > td,
.am-table-compact > tfoot > tr > td {
  padding: 0.4rem;
}
.am-table-centered > thead > tr > th,
.am-table-centered > tbody > tr > th,
.am-table-centered > tfoot > tr > th,
.am-table-centered > thead > tr > td,
.am-table-centered > tbody > tr > td,
.am-table-centered > tfoot > tr > td {
  text-align: center;
}
.am-table > thead > tr > td.am-active,
.am-table > tbody > tr > td.am-active,
.am-table > tfoot > tr > td.am-active,
.am-table > thead > tr > th.am-active,
.am-table > tbody > tr > th.am-active,
.am-table > tfoot > tr > th.am-active,
.am-table > thead > tr.am-active > td,
.am-table > tbody > tr.am-active > td,
.am-table > tfoot > tr.am-active > td,
.am-table > thead > tr.am-active > th,
.am-table > tbody > tr.am-active > th,
.am-table > tfoot > tr.am-active > th {
  background-color: #ffd;
}
.am-table > thead > tr > td.am-disabled,
.am-table > tbody > tr > td.am-disabled,
.am-table > tfoot > tr > td.am-disabled,
.am-table > thead > tr > th.am-disabled,
.am-table > tbody > tr > th.am-disabled,
.am-table > tfoot > tr > th.am-disabled,
.am-table > thead > tr.am-disabled > td,
.am-table > tbody > tr.am-disabled > td,
.am-table > tfoot > tr.am-disabled > td,
.am-table > thead > tr.am-disabled > th,
.am-table > tbody > tr.am-disabled > th,
.am-table > tfoot > tr.am-disabled > th {
  color: #999999;
}
.am-table > thead > tr > td.am-primary,
.am-table > tbody > tr > td.am-primary,
.am-table > tfoot > tr > td.am-primary,
.am-table > thead > tr > th.am-primary,
.am-table > tbody > tr > th.am-primary,
.am-table > tfoot > tr > th.am-primary,
.am-table > thead > tr.am-primary > td,
.am-table > tbody > tr.am-primary > td,
.am-table > tfoot > tr.am-primary > td,
.am-table > thead > tr.am-primary > th,
.am-table > tbody > tr.am-primary > th,
.am-table > tfoot > tr.am-primary > th {
  color: #0b76ac;
  background-color: rgba(14, 144, 210, 0.115);
}
.am-table > thead > tr > td.am-success,
.am-table > tbody > tr > td.am-success,
.am-table > tfoot > tr > td.am-success,
.am-table > thead > tr > th.am-success,
.am-table > tbody > tr > th.am-success,
.am-table > tfoot > tr > th.am-success,
.am-table > thead > tr.am-success > td,
.am-table > tbody > tr.am-success > td,
.am-table > tfoot > tr.am-success > td,
.am-table > thead > tr.am-success > th,
.am-table > tbody > tr.am-success > th,
.am-table > tfoot > tr.am-success > th {
  color: #5eb95e;
  background-color: rgba(94, 185, 94, 0.115);
}
.am-table > thead > tr > td.am-warning,
.am-table > tbody > tr > td.am-warning,
.am-table > tfoot > tr > td.am-warning,
.am-table > thead > tr > th.am-warning,
.am-table > tbody > tr > th.am-warning,
.am-table > tfoot > tr > th.am-warning,
.am-table > thead > tr.am-warning > td,
.am-table > tbody > tr.am-warning > td,
.am-table > tfoot > tr.am-warning > td,
.am-table > thead > tr.am-warning > th,
.am-table > tbody > tr.am-warning > th,
.am-table > tfoot > tr.am-warning > th {
  color: #F37B1D;
  background-color: rgba(243, 123, 29, 0.115);
}
.am-table > thead > tr > td.am-danger,
.am-table > tbody > tr > td.am-danger,
.am-table > tfoot > tr > td.am-danger,
.am-table > thead > tr > th.am-danger,
.am-table > tbody > tr > th.am-danger,
.am-table > tfoot > tr > th.am-danger,
.am-table > thead > tr.am-danger > td,
.am-table > tbody > tr.am-danger > td,
.am-table > tfoot > tr.am-danger > td,
.am-table > thead > tr.am-danger > th,
.am-table > tbody > tr.am-danger > th,
.am-table > tfoot > tr.am-danger > th {
  color: #dd514c;
  background-color: rgba(221, 81, 76, 0.115);
}
/* ==========================================================================
   Component: Form
 ============================================================================ */
/* Normalize */
fieldset {
  border: none;
}
legend {
  display: block;
  width: 100%;
  margin-bottom: 2rem;
  font-size: 2rem;
  line-height: inherit;
  color: #333333;
  border-bottom: 1px solid #e5e5e5;
  padding-bottom: 0.5rem;
}
label {
  display: inline-block;
  margin-bottom: 5px;
  font-weight: bold;
}
input[type="search"] {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}
input[type="radio"],
input[type="checkbox"] {
  margin: 4px 0 0;
  margin-top: 1px \9;
  /* IE8-9 */
  line-height: normal;
}
input[type="file"] {
  display: block;
}
select[multiple],
select[size] {
  height: auto;
}
select optgroup {
  font-size: inherit;
  font-style: inherit;
  font-family: inherit;
}
input[type="file"]:focus,
input[type="radio"]:focus,
input[type="checkbox"]:focus {
  outline: thin dotted;
  outline: 1px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}
input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  height: auto;
}
output {
  display: block;
  padding-top: 1.6rem;
  font-size: 1.6rem;
  line-height: 1.6;
  color: #555555;
  vertical-align: middle;
}
/* Common form controls */
.am-form select,
.am-form textarea,
.am-form input[type="text"],
.am-form input[type="password"],
.am-form input[type="datetime"],
.am-form input[type="datetime-local"],
.am-form input[type="date"],
.am-form input[type="month"],
.am-form input[type="time"],
.am-form input[type="week"],
.am-form input[type="number"],
.am-form input[type="email"],
.am-form input[type="url"],
.am-form input[type="search"],
.am-form input[type="tel"],
.am-form input[type="color"],
.am-form-field {
  display: block;
  width: 100%;
  padding: 0.5em;
  font-size: 1.6rem;
  line-height: 1.2;
  color: #555555;
  vertical-align: middle;
  background-color: #fff;
  background-image: none;
  border: 1px solid #ccc;
  border-radius: 0;
  -webkit-appearance: none;
  -webkit-transition: border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
  transition: border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
}
.am-form select:focus,
.am-form textarea:focus,
.am-form input[type="text"]:focus,
.am-form input[type="password"]:focus,
.am-form input[type="datetime"]:focus,
.am-form input[type="datetime-local"]:focus,
.am-form input[type="date"]:focus,
.am-form input[type="month"]:focus,
.am-form input[type="time"]:focus,
.am-form input[type="week"]:focus,
.am-form input[type="number"]:focus,
.am-form input[type="email"]:focus,
.am-form input[type="url"]:focus,
.am-form input[type="search"]:focus,
.am-form input[type="tel"]:focus,
.am-form input[type="color"]:focus,
.am-form-field:focus {
  outline: 0;
}
.am-form select:focus,
.am-form textarea:focus,
.am-form input[type="text"]:focus,
.am-form input[type="password"]:focus,
.am-form input[type="datetime"]:focus,
.am-form input[type="datetime-local"]:focus,
.am-form input[type="date"]:focus,
.am-form input[type="month"]:focus,
.am-form input[type="time"]:focus,
.am-form input[type="week"]:focus,
.am-form input[type="number"]:focus,
.am-form input[type="email"]:focus,
.am-form input[type="url"]:focus,
.am-form input[type="search"]:focus,
.am-form input[type="tel"]:focus,
.am-form input[type="color"]:focus,
.am-form-field:focus {
  background-color: #fefffe;
  border-color: #3bb4f2;
  outline: 0;
  -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 5px rgba(59, 180, 242, 0.3);
          box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 5px rgba(59, 180, 242, 0.3);
}
.am-form select::-webkit-input-placeholder,
.am-form textarea::-webkit-input-placeholder,
.am-form input[type="text"]::-webkit-input-placeholder,
.am-form input[type="password"]::-webkit-input-placeholder,
.am-form input[type="datetime"]::-webkit-input-placeholder,
.am-form input[type="datetime-local"]::-webkit-input-placeholder,
.am-form input[type="date"]::-webkit-input-placeholder,
.am-form input[type="month"]::-webkit-input-placeholder,
.am-form input[type="time"]::-webkit-input-placeholder,
.am-form input[type="week"]::-webkit-input-placeholder,
.am-form input[type="number"]::-webkit-input-placeholder,
.am-form input[type="email"]::-webkit-input-placeholder,
.am-form input[type="url"]::-webkit-input-placeholder,
.am-form input[type="search"]::-webkit-input-placeholder,
.am-form input[type="tel"]::-webkit-input-placeholder,
.am-form input[type="color"]::-webkit-input-placeholder,
.am-form-field::-webkit-input-placeholder {
  color: #999999;
}
.am-form select::-moz-placeholder,
.am-form textarea::-moz-placeholder,
.am-form input[type="text"]::-moz-placeholder,
.am-form input[type="password"]::-moz-placeholder,
.am-form input[type="datetime"]::-moz-placeholder,
.am-form input[type="datetime-local"]::-moz-placeholder,
.am-form input[type="date"]::-moz-placeholder,
.am-form input[type="month"]::-moz-placeholder,
.am-form input[type="time"]::-moz-placeholder,
.am-form input[type="week"]::-moz-placeholder,
.am-form input[type="number"]::-moz-placeholder,
.am-form input[type="email"]::-moz-placeholder,
.am-form input[type="url"]::-moz-placeholder,
.am-form input[type="search"]::-moz-placeholder,
.am-form input[type="tel"]::-moz-placeholder,
.am-form input[type="color"]::-moz-placeholder,
.am-form-field::-moz-placeholder {
  color: #999999;
}
.am-form select:-ms-input-placeholder,
.am-form textarea:-ms-input-placeholder,
.am-form input[type="text"]:-ms-input-placeholder,
.am-form input[type="password"]:-ms-input-placeholder,
.am-form input[type="datetime"]:-ms-input-placeholder,
.am-form input[type="datetime-local"]:-ms-input-placeholder,
.am-form input[type="date"]:-ms-input-placeholder,
.am-form input[type="month"]:-ms-input-placeholder,
.am-form input[type="time"]:-ms-input-placeholder,
.am-form input[type="week"]:-ms-input-placeholder,
.am-form input[type="number"]:-ms-input-placeholder,
.am-form input[type="email"]:-ms-input-placeholder,
.am-form input[type="url"]:-ms-input-placeholder,
.am-form input[type="search"]:-ms-input-placeholder,
.am-form input[type="tel"]:-ms-input-placeholder,
.am-form input[type="color"]:-ms-input-placeholder,
.am-form-field:-ms-input-placeholder {
  color: #999999;
}
.am-form select::placeholder,
.am-form textarea::placeholder,
.am-form input[type="text"]::placeholder,
.am-form input[type="password"]::placeholder,
.am-form input[type="datetime"]::placeholder,
.am-form input[type="datetime-local"]::placeholder,
.am-form input[type="date"]::placeholder,
.am-form input[type="month"]::placeholder,
.am-form input[type="time"]::placeholder,
.am-form input[type="week"]::placeholder,
.am-form input[type="number"]::placeholder,
.am-form input[type="email"]::placeholder,
.am-form input[type="url"]::placeholder,
.am-form input[type="search"]::placeholder,
.am-form input[type="tel"]::placeholder,
.am-form input[type="color"]::placeholder,
.am-form-field::placeholder {
  color: #999999;
}
.am-form select::-moz-placeholder,
.am-form textarea::-moz-placeholder,
.am-form input[type="text"]::-moz-placeholder,
.am-form input[type="password"]::-moz-placeholder,
.am-form input[type="datetime"]::-moz-placeholder,
.am-form input[type="datetime-local"]::-moz-placeholder,
.am-form input[type="date"]::-moz-placeholder,
.am-form input[type="month"]::-moz-placeholder,
.am-form input[type="time"]::-moz-placeholder,
.am-form input[type="week"]::-moz-placeholder,
.am-form input[type="number"]::-moz-placeholder,
.am-form input[type="email"]::-moz-placeholder,
.am-form input[type="url"]::-moz-placeholder,
.am-form input[type="search"]::-moz-placeholder,
.am-form input[type="tel"]::-moz-placeholder,
.am-form input[type="color"]::-moz-placeholder,
.am-form-field::-moz-placeholder {
  opacity: 1;
}
.am-form select[disabled],
.am-form textarea[disabled],
.am-form input[type="text"][disabled],
.am-form input[type="password"][disabled],
.am-form input[type="datetime"][disabled],
.am-form input[type="datetime-local"][disabled],
.am-form input[type="date"][disabled],
.am-form input[type="month"][disabled],
.am-form input[type="time"][disabled],
.am-form input[type="week"][disabled],
.am-form input[type="number"][disabled],
.am-form input[type="email"][disabled],
.am-form input[type="url"][disabled],
.am-form input[type="search"][disabled],
.am-form input[type="tel"][disabled],
.am-form input[type="color"][disabled],
.am-form-field[disabled],
.am-form select[readonly],
.am-form textarea[readonly],
.am-form input[type="text"][readonly],
.am-form input[type="password"][readonly],
.am-form input[type="datetime"][readonly],
.am-form input[type="datetime-local"][readonly],
.am-form input[type="date"][readonly],
.am-form input[type="month"][readonly],
.am-form input[type="time"][readonly],
.am-form input[type="week"][readonly],
.am-form input[type="number"][readonly],
.am-form input[type="email"][readonly],
.am-form input[type="url"][readonly],
.am-form input[type="search"][readonly],
.am-form input[type="tel"][readonly],
.am-form input[type="color"][readonly],
.am-form-field[readonly],
fieldset[disabled] .am-form select,
fieldset[disabled] .am-form textarea,
fieldset[disabled] .am-form input[type="text"],
fieldset[disabled] .am-form input[type="password"],
fieldset[disabled] .am-form input[type="datetime"],
fieldset[disabled] .am-form input[type="datetime-local"],
fieldset[disabled] .am-form input[type="date"],
fieldset[disabled] .am-form input[type="month"],
fieldset[disabled] .am-form input[type="time"],
fieldset[disabled] .am-form input[type="week"],
fieldset[disabled] .am-form input[type="number"],
fieldset[disabled] .am-form input[type="email"],
fieldset[disabled] .am-form input[type="url"],
fieldset[disabled] .am-form input[type="search"],
fieldset[disabled] .am-form input[type="tel"],
fieldset[disabled] .am-form input[type="color"],
fieldset[disabled] .am-form-field {
  cursor: not-allowed;
  background-color: #eeeeee;
}
.am-form select.am-radius,
.am-form textarea.am-radius,
.am-form input[type="text"].am-radius,
.am-form input[type="password"].am-radius,
.am-form input[type="datetime"].am-radius,
.am-form input[type="datetime-local"].am-radius,
.am-form input[type="date"].am-radius,
.am-form input[type="month"].am-radius,
.am-form input[type="time"].am-radius,
.am-form input[type="week"].am-radius,
.am-form input[type="number"].am-radius,
.am-form input[type="email"].am-radius,
.am-form input[type="url"].am-radius,
.am-form input[type="search"].am-radius,
.am-form input[type="tel"].am-radius,
.am-form input[type="color"].am-radius,
.am-form-field.am-radius {
  border-radius: 2px;
}
.am-form select.am-round,
.am-form textarea.am-round,
.am-form input[type="text"].am-round,
.am-form input[type="password"].am-round,
.am-form input[type="datetime"].am-round,
.am-form input[type="datetime-local"].am-round,
.am-form input[type="date"].am-round,
.am-form input[type="month"].am-round,
.am-form input[type="time"].am-round,
.am-form input[type="week"].am-round,
.am-form input[type="number"].am-round,
.am-form input[type="email"].am-round,
.am-form input[type="url"].am-round,
.am-form input[type="search"].am-round,
.am-form input[type="tel"].am-round,
.am-form input[type="color"].am-round,
.am-form-field.am-round {
  border-radius: 1000px;
}
.am-form textarea,
.am-form select[multiple],
.am-form select[size] {
  height: auto;
}
.am-form select {
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  -webkit-border-radius: 0;
  background: #fff url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZlcnNpb249IjEuMSIgeD0iMTJweCIgeT0iMHB4IiB3aWR0aD0iMjRweCIgaGVpZ2h0PSIzcHgiIHZpZXdCb3g9IjAgMCA2IDMiIGVuYWJsZS1iYWNrZ3JvdW5kPSJuZXcgMCAwIDYgMyIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+PHBvbHlnb24gcG9pbnRzPSI1Ljk5MiwwIDIuOTkyLDMgLTAuMDA4LDAgIi8+PC9zdmc+') no-repeat 100% center;
}
.am-form select[multiple="multiple"] {
  background-image: none;
}
.am-form input[type="datetime-local"],
.am-form input[type="date"],
input[type="datetime-local"].am-form-field,
input[type="date"].am-form-field {
  height: 37px;
}
.am-form input[type="datetime-local"].am-input-sm,
.am-form input[type="date"].am-input-sm,
input[type="datetime-local"].am-form-field.am-input-sm,
input[type="date"].am-form-field.am-input-sm {
  height: 32px;
}
.am-form input[type="datetime-local"] .am-input-lg,
.am-form input[type="date"] .am-input-lg,
input[type="datetime-local"].am-form-field .am-input-lg,
input[type="date"].am-form-field .am-input-lg {
  height: 41px;
}
/* help text */
.am-form-help {
  display: block;
  margin-top: 5px;
  margin-bottom: 10px;
  color: #999999;
  font-size: 1.3rem;
}
/* form group */
.am-form-group {
  margin-bottom: 1.5rem;
}
/* file field */
.am-form-file {
  position: relative;
  overflow: hidden;
}
.am-form-file input[type="file"] {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
  width: 100%;
  opacity: 0;
  cursor: pointer;
  font-size: 50rem;
}
/**
 * Checkboxes and radios
 * Indent the labels to position radios/checkboxes as hanging controls.
*/
.am-radio,
.am-checkbox {
  display: block;
  min-height: 1.92rem;
  margin-top: 10px;
  margin-bottom: 10px;
  padding-left: 20px;
  vertical-align: middle;
}
.am-radio label,
.am-checkbox label {
  display: inline;
  margin-bottom: 0;
  font-weight: normal;
  cursor: pointer;
}
.am-radio input[type="radio"],
.am-radio-inline input[type="radio"],
.am-checkbox input[type="checkbox"],
.am-checkbox-inline input[type="checkbox"] {
  float: left;
  margin-left: -20px;
  outline: none;
}
.am-radio + .am-radio,
.am-checkbox + .am-checkbox {
  margin-top: -5px;
}
/* Radios and checkboxes inline */
.am-radio-inline,
.am-checkbox-inline {
  display: inline-block;
  padding-left: 20px;
  margin-bottom: 0;
  vertical-align: middle;
  font-weight: normal;
  cursor: pointer;
}
.am-radio-inline + .am-radio-inline,
.am-checkbox-inline + .am-checkbox-inline {
  margin-top: 0;
  margin-left: 10px;
}
input[type="radio"][disabled],
input[type="checkbox"][disabled],
.am-radio[disabled],
.am-radio-inline[disabled],
.am-checkbox[disabled],
.am-checkbox-inline[disabled],
fieldset[disabled] input[type="radio"],
fieldset[disabled] input[type="checkbox"],
fieldset[disabled] .am-radio,
fieldset[disabled] .am-radio-inline,
fieldset[disabled] .am-checkbox,
fieldset[disabled] .am-checkbox-inline {
  cursor: not-allowed;
}
/* Form field feedback states */
.am-form-warning .am-form-help,
.am-form-warning .am-form-label,
.am-form-warning .am-radio,
.am-form-warning .am-checkbox,
.am-form-warning .am-radio-inline,
.am-form-warning .am-checkbox-inline,
.am-form-warning label {
  color: #F37B1D;
}
.am-form-warning [class*="icon-"] {
  color: #F37B1D;
}
.am-form-warning .am-form-field {
  border-color: #F37B1D !important;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
          box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}
.am-form-warning .am-form-field:focus {
  background-color: #fefffe;
  border-color: #d2620b;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 5px #f8b47e !important;
          box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 5px #f8b47e !important;
}
.am-form-error .am-form-help,
.am-form-error .am-form-label,
.am-form-error .am-radio,
.am-form-error .am-checkbox,
.am-form-error .am-radio-inline,
.am-form-error .am-checkbox-inline,
.am-form-error label {
  color: #dd514c;
}
.am-form-error [class*="icon-"] {
  color: #dd514c;
}
.am-form-error .am-form-field,
.am-field-error {
  border-color: #dd514c !important;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
          box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}
.am-form-error .am-form-field:focus,
.am-field-error:focus {
  background-color: #fefffe;
  border-color: #cf2d27;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 5px #eda4a2 !important;
          box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 5px #eda4a2 !important;
}
.am-form-success .am-form-help,
.am-form-success .am-form-label,
.am-form-success .am-radio,
.am-form-success .am-checkbox,
.am-form-success .am-radio-inline,
.am-form-success .am-checkbox-inline,
.am-form-success label {
  color: #5eb95e;
}
.am-form-success [class*="icon-"] {
  color: #5eb95e;
}
.am-form-success .am-form-field,
.am-field-valid {
  border-color: #5eb95e !important;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
          box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}
.am-form-success .am-form-field:focus,
.am-field-valid:focus {
  background-color: #fefffe;
  border-color: #459f45;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 5px #a5d8a5 !important;
          box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 5px #a5d8a5 !important;
}
/* Horizontal forms */
.am-form-horizontal .am-form-label,
.am-form-horizontal .am-radio,
.am-form-horizontal .am-checkbox,
.am-form-horizontal .am-radio-inline,
.am-form-horizontal .am-checkbox-inline {
  margin-top: 0;
  margin-bottom: 0;
  padding-top: 0.6em;
}
.am-form-horizontal .am-form-group:before,
.am-form-horizontal .am-form-group:after {
  content: " ";
  display: table;
}
.am-form-horizontal .am-form-group:after {
  clear: both;
}
@media only screen and (min-width:641px) {
  .am-form-horizontal .am-form-label {
    text-align: right;
  }
}
/* Inline form elements */
@media only screen and (min-width:641px) {
  .am-form-inline .am-form-group {
    display: inline-block;
    margin-bottom: 0;
    vertical-align: middle;
  }
  .am-form-inline .am-form-field {
    display: inline-block;
    width: auto;
    vertical-align: middle;
  }
  .am-form-inline .am-input-group {
    display: inline-table;
    vertical-align: middle;
  }
  .am-form-inline .am-input-group .am-input-group-label,
  .am-form-inline .am-input-group .am-input-group-btn,
  .am-form-inline .am-input-group .am-form-label {
    width: auto;
  }
  .am-form-inline .am-input-group > .am-form-field {
    width: 100%;
  }
  .am-form-inline .am-form-label {
    margin-bottom: 0;
    vertical-align: middle;
  }
  .am-form-inline .am-radio,
  .am-form-inline .am-checkbox {
    display: inline-block;
    margin-top: 0;
    margin-bottom: 0;
    padding-left: 0;
    vertical-align: middle;
  }
  .am-form-inline .am-radio input[type="radio"],
  .am-form-inline .am-checkbox input[type="checkbox"] {
    float: none;
    margin-left: 0;
  }
}
/* Form field size */
.am-input-sm {
  font-size: 1.4rem !important;
}
.am-input-lg {
  font-size: 1.8rem !important;
}
.am-form-group-sm .am-checkbox,
.am-form-group-sm .am-radio,
.am-form-group-sm .am-form-label,
.am-form-group-sm .am-form-field {
  font-size: 1.4rem !important;
}
.am-form-group-lg .am-checkbox,
.am-form-group-lg .am-radio,
.am-form-group-lg .am-form-label,
.am-form-group-lg .am-form-field {
  font-size: 1.8rem !important;
}
.am-form-group-lg input[type="radio"],
.am-form-group-lg input[type="checkbox"] {
  margin-top: 7px;
}
/* Form field feedback states */
.am-form-icon {
  position: relative;
}
.am-form-icon .am-form-field {
  padding-left: 1.75em !important;
}
.am-form-icon [class*='am-icon-'] {
  position: absolute;
  left: 0.5em;
  top: 50%;
  display: block;
  margin-top: -0.5em;
  line-height: 1;
  z-index: 2;
}
.am-form-icon label ~ [class*='am-icon-'] {
  top: 70%;
}
/* Feedback Icon */
.am-form-feedback {
  position: relative;
}
.am-form-feedback .am-form-field {
  padding-left: 0.5em !important;
  padding-right: 1.75em !important;
}
.am-form-feedback [class*='am-icon-'] {
  right: 0.5em;
  left: auto;
}
.am-form-horizontal .am-form-feedback [class*='am-icon-'] {
  right: 1.6em;
}
/* Form set */
.am-form-set {
  margin-bottom: 1.5rem;
  padding: 0;
}
.am-form-set > input {
  position: relative;
  top: -1px;
  border-radius: 0 !important;
}
.am-form-set > input:focus {
  z-index: 2;
}
.am-form-set > input:first-child {
  top: 1px;
  border-top-right-radius: 0 !important;
  border-top-left-radius: 0 !important;
}
.am-form-set > input:last-child {
  top: -2px;
  border-bottom-right-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}
/* ==========================================================================
   Component: Image
 ============================================================================ */
/* Image thumbnails */
.am-img-thumbnail {
  display: inline-block;
  max-width: 100%;
  height: auto;
  padding: 2px;
  line-height: 1.6;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 0;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}
.am-img-thumbnail.am-radius {
  border-radius: 2px;
}
.am-img-responsive {
  display: block;
  max-width: 100%;
  height: auto;
}
/* ==========================================================================
   Component: Nav
 ============================================================================ */
.am-nav {
  margin-bottom: 0;
  padding: 0;
  list-style: none;
}
.am-nav:before,
.am-nav:after {
  content: " ";
  display: table;
}
.am-nav:after {
  clear: both;
}
.am-nav > li {
  position: relative;
  display: block;
}
.am-nav > li + li {
  margin-top: 5px;
}
.am-nav > li + .am-nav-header {
  margin-top: 1em;
}
.am-nav > li > a {
  position: relative;
  display: block;
  padding: 0.4em 1em;
  border-radius: 0;
}
.am-nav > li > a:hover,
.am-nav > li > a:focus {
  text-decoration: none;
  background-color: #eeeeee;
}
.am-nav > li.am-active > a,
.am-nav > li.am-active > a:hover,
.am-nav > li.am-active > a:focus {
  color: #fff;
  background-color: #0e90d2;
  cursor: default;
}
.am-nav > li.am-disabled > a {
  color: #999999;
}
.am-nav > li.am-disabled > a:hover,
.am-nav > li.am-disabled > a:focus {
  color: #999999;
  text-decoration: none;
  background-color: transparent;
  cursor: not-allowed;
}
.am-nav-header {
  padding: 0.4em 1em;
  text-transform: uppercase;
  font-weight: bold;
  font-size: 100%;
  color: #555555;
}
.am-nav-divider {
  margin: 15px 1em !important;
  border-top: 1px solid #ddd;
  -webkit-box-shadow: 0 1px 0 #fff;
          box-shadow: 0 1px 0 #fff;
}
.am-nav-pills > li {
  float: left;
}
.am-nav-pills > li + li {
  margin-left: 5px;
  margin-top: 0;
}
.am-nav-tabs {
  border-bottom: 1px solid #ddd;
}
.am-nav-tabs > li {
  float: left;
  margin-bottom: -1px;
}
.am-nav-tabs > li + li {
  margin-top: 0;
}
.am-nav-tabs > li > a {
  margin-right: 5px;
  line-height: 1.6;
  border: 1px solid transparent;
  border-radius: 0 0 0 0;
}
.am-nav-tabs > li > a:hover {
  border-color: #eeeeee #eeeeee #ddd;
}
.am-nav-tabs > li.am-active > a,
.am-nav-tabs > li.am-active > a:hover,
.am-nav-tabs > li.am-active > a:focus {
  color: #555555;
  background-color: #fff;
  border: 1px solid #ddd;
  border-bottom-color: transparent;
  cursor: default;
}
.am-nav-tabs.am-nav-justify {
  border-bottom: 0;
  /*  > li > a {
      margin-right: 0;
      border-radius: @global-radius;
    }

    > .am-active > a {
      &,
      &:hover,
      &:focus {
        border: 1px solid @nav-tabs-justify-link-border-color;
      }
    }*/
}
.am-nav-tabs.am-nav-justify > li > a {
  margin-right: 0;
  border-bottom: 1px solid #ddd;
  border-radius: 0 0 0 0;
}
.am-nav-tabs.am-nav-justify > .am-active > a,
.am-nav-tabs.am-nav-justify > .am-active > a:hover,
.am-nav-tabs.am-nav-justify > .am-active > a:focus {
  border-bottom-color: #fff;
}
.am-nav-justify {
  width: 100%;
}
.am-nav-justify > li {
  float: none;
  display: table-cell;
  width: 1%;
}
.am-nav-justify > li > a {
  text-align: center;
  margin-bottom: 0;
}
.lte9 .am-nav-justify > li {
  display: table-cell;
  width: 1%;
}
/* ==========================================================================
   Component: Topbar
 ============================================================================ */
.am-topbar {
  position: relative;
  min-height: 50px;
  margin-bottom: 1.6rem;
  background: #f8f8f8;
  border-width: 0 0 1px;
  border-style: solid;
  border-color: #ddd;
  color: #666;
}
.am-topbar:before,
.am-topbar:after {
  content: " ";
  display: table;
}
.am-topbar:after {
  clear: both;
}
.am-topbar a {
  color: #666;
}
.am-topbar-brand {
  margin: 0;
}
@media only screen and (min-width:641px) {
  .am-topbar-brand {
    float: left;
  }
}
.am-topbar-brand a:hover {
  color: #4d4d4d;
}
.am-topbar-collapse {
  width: 100%;
  overflow-x: visible;
  padding: 10px;
  clear: both;
  -webkit-overflow-scrolling: touch;
}
.am-topbar-collapse:before,
.am-topbar-collapse:after {
  content: " ";
  display: table;
}
.am-topbar-collapse:after {
  clear: both;
}
.am-topbar-collapse.am-in {
  overflow-y: auto;
}
@media only screen and (min-width:641px) {
  .am-topbar-collapse {
    margin-top: 0;
    padding: 0;
    width: auto;
    clear: none;
  }
  .am-topbar-collapse.am-collapse {
    display: block !important;
    height: auto !important;
    padding: 0;
    overflow: visible !important;
  }
  .am-topbar-collapse.am-in {
    overflow-y: visible;
  }
}
.am-topbar-brand {
  padding: 0 10px;
  float: left;
  font-size: 1.8rem;
  height: 50px;
  line-height: 50px;
}
.am-topbar-toggle {
  position: relative;
  float: right;
  margin-right: 10px;
}
@media only screen and (min-width:641px) {
  .am-topbar-toggle {
    display: none;
  }
}
@media only screen and (max-width: 640px) {
  .am-topbar-nav {
    margin-bottom: 8px;
  }
  .am-topbar-nav > li {
    float: none;
  }
}
@media only screen and (max-width: 640px) {
  .am-topbar-nav > li + li {
    margin-left: 0;
    margin-top: 5px;
  }
}
@media only screen and (min-width:641px) {
  .am-topbar-nav {
    float: left;
  }
  .am-topbar-nav > li > a {
    position: relative;
    line-height: 50px;
    padding: 0 10px;
  }
  .am-topbar-nav > li > a:after {
    position: absolute;
    left: 50%;
    margin-left: -7px;
    bottom: -1px;
    content: "";
    display: inline-block;
    width: 0;
    height: 0;
    vertical-align: middle;
    border-bottom: 7px solid #f8f8f8;
    border-right: 7px solid transparent;
    border-left: 7px solid transparent;
    border-top: 0 dotted;
    -webkit-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
            transform: rotate(360deg);
    opacity: 0;
    -webkit-transition: opacity .1s;
    transition: opacity .1s;
  }
  .am-topbar-nav > li > a:hover:after {
    opacity: 1;
    border-bottom-color: #666;
  }
  .am-topbar-nav > li.am-dropdown > a:after {
    display: none;
  }
  .am-topbar-nav > li.am-active > a,
  .am-topbar-nav > li.am-active > a:hover,
  .am-topbar-nav > li.am-active > a:focus {
    border-radius: 0;
    color: #0e90d2;
    background: none;
  }
  .am-topbar-nav > li.am-active > a:after {
    opacity: 1;
    border-bottom-color: #0e90d2;
  }
}
@media only screen and (max-width: 640px) {
  .am-topbar-collapse .am-dropdown.am-active .am-dropdown-content {
    float: none;
    position: relative;
    width: 100%;
  }
}
@media only screen and (min-width:641px) {
  .am-topbar-left {
    float: left;
  }
  .am-topbar-right {
    float: right;
    margin-right: 10px;
  }
}
@media only screen and (max-width: 640px) {
  .am-topbar-form .am-form-group {
    margin-bottom: 5px;
  }
}
@media only screen and (min-width:641px) {
  .am-topbar-form {
    padding: 0 10px;
    margin-top: 8px;
  }
  .am-topbar-form .am-form-group + .am-btn {
    margin-left: 5px;
  }
}
.am-topbar-btn {
  margin-top: 8px;
}
@media only screen and (max-width: 640px) {
  .am-topbar-collapse .am-topbar-btn,
  .am-topbar-collapse .am-btn {
    display: block;
    width: 100%;
  }
}
.am-topbar-inverse {
  background-color: #0e90d2;
  border-color: #0b6fa2;
  color: #eeeeee;
}
.am-topbar-inverse a {
  color: #eeeeee;
}
.am-topbar-inverse .am-topbar-brand a {
  color: #fff;
}
.am-topbar-inverse .am-topbar-brand a:hover,
.am-topbar-inverse .am-topbar-brand a:focus {
  color: #fff;
  background-color: transparent;
}
.am-topbar-inverse .am-topbar-nav > li > a {
  color: #eeeeee;
}
.am-topbar-inverse .am-topbar-nav > li > a:hover,
.am-topbar-inverse .am-topbar-nav > li > a:focus {
  color: #fff;
  background-color: rgba(0, 0, 0, 0.05);
}
.am-topbar-inverse .am-topbar-nav > li > a:hover:after,
.am-topbar-inverse .am-topbar-nav > li > a:focus:after {
  border-bottom-color: #0b6fa2;
}
.am-topbar-inverse .am-topbar-nav > li > a:after {
  border-bottom-color: #0e90d2;
}
.am-topbar-inverse .am-topbar-nav > li.am-active > a,
.am-topbar-inverse .am-topbar-nav > li.am-active > a:hover,
.am-topbar-inverse .am-topbar-nav > li.am-active > a:focus {
  color: #fff;
  background-color: rgba(0, 0, 0, 0.1);
}
.am-topbar-inverse .am-topbar-nav > li.am-active > a:after,
.am-topbar-inverse .am-topbar-nav > li.am-active > a:hover:after,
.am-topbar-inverse .am-topbar-nav > li.am-active > a:focus:after {
  border-bottom-color: #fff;
}
.am-topbar-inverse .am-topbar-nav > li .disabled > a,
.am-topbar-inverse .am-topbar-nav > li .disabled > a:hover,
.am-topbar-inverse .am-topbar-nav > li .disabled > a:focus {
  color: #444;
  background-color: transparent;
}
.am-topbar-fixed-top,
.am-topbar-fixed-bottom {
  position: fixed;
  right: 0;
  left: 0;
  z-index: 1000;
  -webkit-transform: translate3d(0, 0, 0);
          transform: translate3d(0, 0, 0);
}
.am-topbar-fixed-top {
  top: 0;
}
.am-topbar-fixed-bottom {
  bottom: 0;
  margin-bottom: 0;
  border-width: 1px 0 0;
}
.am-with-topbar-fixed-top {
  padding-top: 51px;
}
.am-with-topbar-fixed-bottom {
  padding-bottom: 51px;
}
@media only screen and (max-width: 640px) {
  .am-topbar-fixed-bottom .am-topbar-collapse {
    position: absolute;
    bottom: 100%;
    margin-bottom: 1px;
    background-color: #f8f8f8;
  }
  .am-topbar-fixed-bottom .am-topbar-collapse .am-dropdown-content:before,
  .am-topbar-fixed-bottom .am-topbar-collapse .am-dropdown-content:after {
    display: none;
  }
  .am-topbar-fixed-bottom.am-topbar-inverse .am-topbar-collapse {
    background-color: #0e90d2;
  }
}
/* ==========================================================================
   Component: Breadcrumb
 ============================================================================ */
.am-breadcrumb {
  padding: .7em .5em;
  margin-bottom: 2rem;
  list-style: none;
  background-color: transparent;
  border-radius: 0;
  font-size: 85%;
}
.am-breadcrumb > li {
  display: inline-block;
}
.am-breadcrumb > li [class*="am-icon-"]:before {
  color: #999999;
  margin-right: 5px;
}
.am-breadcrumb > li + li:before {
  content: "\00bb\00a0";
  padding: 0 8px;
  color: #ccc;
}
.am-breadcrumb > .am-active {
  color: #999999;
}
.am-breadcrumb-slash > li + li:before {
  content: "/\00a0";
}
/* ==========================================================================
   Component: Pagination
 ============================================================================ */
.am-pagination {
  padding-left: 0;
  margin: 1.5rem 0;
  list-style: none;
  color: #999999;
  text-align: left;
}
.am-pagination:before,
.am-pagination:after {
  content: " ";
  display: table;
}
.am-pagination:after {
  clear: both;
}
.am-pagination > li {
  display: inline-block;
}
.am-pagination > li > a,
.am-pagination > li > span {
  position: relative;
  display: block;
  padding: 0.5em 1em;
  text-decoration: none;
  line-height: 1.2;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 0;
  margin-bottom: 5px;
  margin-right: 5px;
}
.am-pagination > li:last-child > a,
.am-pagination > li:last-child > span {
  margin-right: 0;
}
.am-pagination > li > a:hover,
.am-pagination > li > span:hover,
.am-pagination > li > a:focus,
.am-pagination > li > span:focus {
  background-color: #eeeeee;
}
.am-pagination > .am-active > a,
.am-pagination > .am-active > span,
.am-pagination > .am-active > a:hover,
.am-pagination > .am-active > span:hover,
.am-pagination > .am-active > a:focus,
.am-pagination > .am-active > span:focus {
  z-index: 2;
  color: #fff;
  background-color: #0e90d2;
  border-color: #0e90d2;
  cursor: default;
}
.am-pagination > .am-disabled > span,
.am-pagination > .am-disabled > span:hover,
.am-pagination > .am-disabled > span:focus,
.am-pagination > .am-disabled > a,
.am-pagination > .am-disabled > a:hover,
.am-pagination > .am-disabled > a:focus {
  color: #999999;
  background-color: #fff;
  border-color: #ddd;
  cursor: not-allowed;
  pointer-events: none;
}
.am-pagination .am-pagination-prev {
  float: left;
}
.am-pagination .am-pagination-prev a {
  border-radius: 0;
}
.am-pagination .am-pagination-next {
  float: right;
}
.am-pagination .am-pagination-next a {
  border-radius: 0;
}
.am-pagination-centered {
  text-align: center;
}
.am-pagination-right {
  text-align: right;
}
/* ==========================================================================
   Component: Aniamtion
 ============================================================================ */
[class*="am-animation-"] {
  -webkit-animation-duration: 0.5s;
          animation-duration: 0.5s;
  -webkit-animation-timing-function: ease-out;
          animation-timing-function: ease-out;
  -webkit-animation-fill-mode: both;
          animation-fill-mode: both;
}
/* Hide animated element if scrollSpy is used */
@media screen {
  .cssanimations [data-am-scrollspy*="animation"] {
    opacity: 0;
  }
}
/* Fade */
.am-animation-fade {
  -webkit-animation-name: am-fade;
          animation-name: am-fade;
  -webkit-animation-duration: 0.8s;
          animation-duration: 0.8s;
  -webkit-animation-timing-function: linear;
          animation-timing-function: linear;
}
/* Scale */
.am-animation-scale-up {
  -webkit-animation-name: am-scale-up;
          animation-name: am-scale-up;
}
.am-animation-scale-down {
  -webkit-animation-name: am-scale-down;
          animation-name: am-scale-down;
}
/* Slide */
.am-animation-slide-top {
  -webkit-animation-name: am-slide-top;
          animation-name: am-slide-top;
}
.am-animation-slide-bottom {
  -webkit-animation-name: am-slide-bottom;
          animation-name: am-slide-bottom;
}
.am-animation-slide-left {
  -webkit-animation-name: am-slide-left;
          animation-name: am-slide-left;
}
.am-animation-slide-right {
  -webkit-animation-name: am-slide-right;
          animation-name: am-slide-right;
}
.am-animation-slide-top-fixed {
  -webkit-animation-name: am-slide-top-fixed;
          animation-name: am-slide-top-fixed;
}
/* Shake */
.am-animation-shake {
  -webkit-animation-name: am-shake;
          animation-name: am-shake;
}
/* Spin */
.am-animation-spin {
  -webkit-animation: am-spin 2s infinite linear;
          animation: am-spin 2s infinite linear;
}
/* Spring */
.am-animation-left-spring {
  -webkit-animation: am-left-spring 0.3s ease-in-out;
          animation: am-left-spring 0.3s ease-in-out;
}
.am-animation-right-spring {
  -webkit-animation: am-right-spring 0.3s ease-in-out;
          animation: am-right-spring 0.3s ease-in-out;
}
.am-animation-reverse {
  -webkit-animation-direction: reverse;
          animation-direction: reverse;
}
.am-animation-paused {
  -webkit-animation-play-state: paused !important;
          animation-play-state: paused !important;
}
.am-animation-delay-1 {
  -webkit-animation-delay: 1s;
          animation-delay: 1s;
}
.am-animation-delay-2 {
  -webkit-animation-delay: 2s;
          animation-delay: 2s;
}
.am-animation-delay-3 {
  -webkit-animation-delay: 3s;
          animation-delay: 3s;
}
.am-animation-delay-4 {
  -webkit-animation-delay: 4s;
          animation-delay: 4s;
}
.am-animation-delay-5 {
  -webkit-animation-delay: 5s;
          animation-delay: 5s;
}
.am-animation-delay-6 {
  -webkit-animation-delay: 6s;
          animation-delay: 6s;
}
/* Keyframes
 ============================================================================ */
/* Fade */
@-webkit-keyframes am-fade {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes am-fade {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
/* Scale up */
@-webkit-keyframes am-scale-up {
  0% {
    opacity: 0;
    -webkit-transform: scale(0.2);
            transform: scale(0.2);
  }
  100% {
    opacity: 1;
    -webkit-transform: scale(1);
            transform: scale(1);
  }
}
@keyframes am-scale-up {
  0% {
    opacity: 0;
    -webkit-transform: scale(0.2);
            transform: scale(0.2);
  }
  100% {
    opacity: 1;
    -webkit-transform: scale(1);
            transform: scale(1);
  }
}
/* Scale down */
@-webkit-keyframes am-scale-down {
  0% {
    opacity: 0;
    -webkit-transform: scale(1.8);
            transform: scale(1.8);
  }
  100% {
    opacity: 1;
    -webkit-transform: scale(1);
            transform: scale(1);
  }
}
@keyframes am-scale-down {
  0% {
    opacity: 0;
    -webkit-transform: scale(1.8);
            transform: scale(1.8);
  }
  100% {
    opacity: 1;
    -webkit-transform: scale(1);
            transform: scale(1);
  }
}
/* Slide top */
@-webkit-keyframes am-slide-top {
  0% {
    opacity: 0;
    -webkit-transform: translateY(-100%);
            transform: translateY(-100%);
  }
  100% {
    opacity: 1;
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
}
@keyframes am-slide-top {
  0% {
    opacity: 0;
    -webkit-transform: translateY(-100%);
            transform: translateY(-100%);
  }
  100% {
    opacity: 1;
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
}
/* Slide bottom */
@-webkit-keyframes am-slide-bottom {
  0% {
    opacity: 0;
    -webkit-transform: translateY(100%);
            transform: translateY(100%);
  }
  100% {
    opacity: 1;
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
}
@keyframes am-slide-bottom {
  0% {
    opacity: 0;
    -webkit-transform: translateY(100%);
            transform: translateY(100%);
  }
  100% {
    opacity: 1;
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
}
/* Slide left */
@-webkit-keyframes am-slide-left {
  0% {
    opacity: 0;
    -webkit-transform: translateX(-100%);
            transform: translateX(-100%);
  }
  100% {
    opacity: 1;
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
}
@keyframes am-slide-left {
  0% {
    opacity: 0;
    -webkit-transform: translateX(-100%);
            transform: translateX(-100%);
  }
  100% {
    opacity: 1;
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
}
/* Slide right */
@-webkit-keyframes am-slide-right {
  0% {
    opacity: 0;
    -webkit-transform: translateX(100%);
            transform: translateX(100%);
  }
  100% {
    opacity: 1;
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
}
@keyframes am-slide-right {
  0% {
    opacity: 0;
    -webkit-transform: translateX(100%);
            transform: translateX(100%);
  }
  100% {
    opacity: 1;
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
}
/* Shake */
@-webkit-keyframes am-shake {
  0%,
  100% {
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
  10% {
    -webkit-transform: translateX(-9px);
            transform: translateX(-9px);
  }
  20% {
    -webkit-transform: translateX(8px);
            transform: translateX(8px);
  }
  30% {
    -webkit-transform: translateX(-7px);
            transform: translateX(-7px);
  }
  40% {
    -webkit-transform: translateX(6px);
            transform: translateX(6px);
  }
  50% {
    -webkit-transform: translateX(-5px);
            transform: translateX(-5px);
  }
  60% {
    -webkit-transform: translateX(4px);
            transform: translateX(4px);
  }
  70% {
    -webkit-transform: translateX(-3px);
            transform: translateX(-3px);
  }
  80% {
    -webkit-transform: translateX(2px);
            transform: translateX(2px);
  }
  90% {
    -webkit-transform: translateX(-1px);
            transform: translateX(-1px);
  }
}
@keyframes am-shake {
  0%,
  100% {
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
  10% {
    -webkit-transform: translateX(-9px);
            transform: translateX(-9px);
  }
  20% {
    -webkit-transform: translateX(8px);
            transform: translateX(8px);
  }
  30% {
    -webkit-transform: translateX(-7px);
            transform: translateX(-7px);
  }
  40% {
    -webkit-transform: translateX(6px);
            transform: translateX(6px);
  }
  50% {
    -webkit-transform: translateX(-5px);
            transform: translateX(-5px);
  }
  60% {
    -webkit-transform: translateX(4px);
            transform: translateX(4px);
  }
  70% {
    -webkit-transform: translateX(-3px);
            transform: translateX(-3px);
  }
  80% {
    -webkit-transform: translateX(2px);
            transform: translateX(2px);
  }
  90% {
    -webkit-transform: translateX(-1px);
            transform: translateX(-1px);
  }
}
/* Slide top fixed */
@-webkit-keyframes am-slide-top-fixed {
  0% {
    opacity: 0;
    -webkit-transform: translateY(-10px);
            transform: translateY(-10px);
  }
  100% {
    opacity: 1;
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
}
@keyframes am-slide-top-fixed {
  0% {
    opacity: 0;
    -webkit-transform: translateY(-10px);
            transform: translateY(-10px);
  }
  100% {
    opacity: 1;
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
}
/* Slide bottom fixed */
@-webkit-keyframes am-slide-bottom-fixed {
  0% {
    opacity: 0;
    -webkit-transform: translateY(10px);
            transform: translateY(10px);
  }
  100% {
    opacity: 1;
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
}
@keyframes am-slide-bottom-fixed {
  0% {
    opacity: 0;
    -webkit-transform: translateY(10px);
            transform: translateY(10px);
  }
  100% {
    opacity: 1;
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
}
/* Spin */
@-webkit-keyframes am-spin {
  0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
            transform: rotate(359deg);
  }
}
@keyframes am-spin {
  0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
            transform: rotate(359deg);
  }
}
/* Spring */
@-webkit-keyframes am-right-spring {
  0% {
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
  50% {
    -webkit-transform: translateX(-20%);
            transform: translateX(-20%);
  }
  100% {
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
}
@keyframes am-right-spring {
  0% {
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
  50% {
    -webkit-transform: translateX(-20%);
            transform: translateX(-20%);
  }
  100% {
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
}
@-webkit-keyframes am-left-spring {
  0% {
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
  50% {
    -webkit-transform: translateX(20%);
            transform: translateX(20%);
  }
  100% {
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
}
@keyframes am-left-spring {
  0% {
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
  50% {
    -webkit-transform: translateX(20%);
            transform: translateX(20%);
  }
  100% {
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
}
/* ==========================================================================
   Component: Article
 ============================================================================ */
.am-article:before,
.am-article:after {
  content: " ";
  display: table;
}
.am-article:after {
  clear: both;
}
.am-article > :last-child {
  margin-bottom: 0;
}
.am-article + .am-article {
  margin-top: 2.4rem;
}
/* Sub-object `.@{ns}article-title` */
.am-article-title {
  font-size: 2.8rem;
  line-height: 1.15;
  font-weight: normal;
}
.am-article-title a {
  color: inherit;
  text-decoration: none;
}
/* Sub-object `.@{ns}article-meta` */
.am-article-meta {
  font-size: 1.2rem;
  line-height: 1.5;
  color: #999999;
}
/* Sub-object `.@{ns}article-lead` */
.am-article-lead {
  color: #666;
  font-size: 1.4rem;
  line-height: 1.5;
  border: 1px solid #dedede;
  border-radius: 2px;
  background: #f9f9f9;
  padding: 10px;
}
/* Sub-object `.@{ns}article-divider` */
.am-article-divider {
  margin-bottom: 2.4rem;
  border-color: #eeeeee;
}
* + .am-article-divider {
  margin-top: 2.4rem;
}
/* Sub-object `.@{ns}article-bd` */
.am-article-bd blockquote {
  font-family: Georgia, "Times New Roman", Times, Kai, "Kaiti SC", KaiTi, BiauKai, "FontAwesome", serif;
}
.am-article-bd img {
  display: block;
  max-width: 100%;
}
/* ==========================================================================
   Component: Badge
 ============================================================================ */
.am-badge {
  display: inline-block;
  min-width: 10px;
  padding: 0.25em 0.625em;
  font-size: 1.2rem;
  font-weight: bold;
  color: #fff;
  line-height: 1;
  vertical-align: baseline;
  white-space: nowrap;
  text-align: center;
  background-color: #999999;
  border-radius: 0;
}
.am-badge:empty {
  display: none;
}
.am-badge.am-square {
  border-radius: 0;
}
.am-badge.am-radius {
  border-radius: 2px;
}
.am-badge.am-round {
  border-radius: 1000px;
}
a.am-badge:hover,
a.am-badge:focus {
  color: #fff;
  text-decoration: none;
  cursor: pointer;
}
.am-badge-primary {
  background-color: #0e90d2;
}
.am-badge-secondary {
  background-color: #3bb4f2;
}
.am-badge-success {
  background-color: #5eb95e;
}
.am-badge-warning {
  background-color: #F37B1D;
}
.am-badge-danger {
  background-color: #dd514c;
}
/* ==========================================================================
   Component: Comment
 ============================================================================ */
.am-comment:before,
.am-comment:after {
  content: " ";
  display: table;
}
.am-comment:after {
  clear: both;
}
.am-comment-avatar {
  float: left;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: 1px solid transparent;
}
@media only screen and (min-width:641px) {
  .am-comment-avatar {
    width: 48px;
    height: 48px;
  }
}
.am-comment-main {
  position: relative;
  margin-left: 42px;
  border: 1px solid #dedede;
  border-radius: 0;
}
.am-comment-main:before,
.am-comment-main:after {
  position: absolute;
  top: 10px;
  left: -8px;
  right: 100%;
  width: 0;
  height: 0;
  display: block;
  content: " ";
  border-color: transparent;
  border-style: solid solid outset;
  border-width: 8px 8px 8px 0;
  pointer-events: none;
}
.am-comment-main:before {
  border-right-color: #dedede;
  z-index: 1;
}
.am-comment-main:after {
  border-right-color: #f8f8f8;
  margin-left: 1px;
  z-index: 2;
}
@media only screen and (min-width:641px) {
  .am-comment-main {
    margin-left: 63px;
  }
}
.am-comment-hd {
  background: #f8f8f8;
  border-bottom: 1px solid #eee;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}
.am-comment-title {
  margin: 0 0 8px 0;
  font-size: 1.6rem;
  line-height: 1.2;
}
.am-comment-meta {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
      -ms-flex: 1;
          flex: 1;
  padding: 10px 15px;
  font-size: 13px;
  color: #999999;
  line-height: 1.2;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.am-comment-meta a {
  color: #999999;
}
.am-comment-author {
  font-weight: bold;
  color: #999999;
}
.am-comment-bd {
  padding: 15px;
  overflow: hidden;
}
.am-comment-bd > :last-child {
  margin-bottom: 0;
}
.am-comment-footer {
  padding: 0 15px 5px;
}
.am-comment-footer .am-comment-actions a + a {
  margin-left: 5px;
}
.am-comment-actions {
  font-size: 13px;
  color: #999999;
}
.am-comment-actions a {
  display: inline-block;
  padding: 10px 5px;
  line-height: 1;
  color: #999999;
  opacity: .7;
}
.am-comment-actions a:hover {
  color: #0e90d2;
  opacity: 1;
}
.am-comment-hd .am-comment-actions {
  padding-right: .5rem;
}
.am-comment-flip .am-comment-avatar {
  float: right;
}
.am-comment-flip .am-comment-main {
  margin-left: auto;
  margin-right: 42px;
}
@media only screen and (min-width:641px) {
  .am-comment-flip .am-comment-main {
    margin-right: 63px;
  }
}
.am-comment-flip .am-comment-main:before,
.am-comment-flip .am-comment-main:after {
  left: auto;
  right: -8px;
  border-width: 8px 0 8px 8px;
}
.am-comment-flip .am-comment-main:before {
  border-left-color: #dedede;
}
.am-comment-flip .am-comment-main:after {
  border-left-color: #f8f8f8;
  margin-right: 1px;
  margin-left: auto;
}
.am-comment-primary .am-comment-avatar {
  border-color: #0e90d2;
}
.am-comment-primary .am-comment-main {
  border-color: #0e90d2;
}
.am-comment-primary .am-comment-main:before {
  border-right-color: #0e90d2;
}
.am-comment-primary.am-comment-flip .am-comment-main:before {
  border-left-color: #0e90d2;
  border-right-color: transparent;
}
.am-comment-primary.am-comment-flip .am-comment-main:after {
  border-left-color: #f8f8f8;
}
.am-comment-secondary .am-comment-avatar,
.am-comment-highlight .am-comment-avatar {
  border-color: #3bb4f2;
}
.am-comment-secondary .am-comment-main,
.am-comment-highlight .am-comment-main {
  border-color: #3bb4f2;
}
.am-comment-secondary .am-comment-main:before,
.am-comment-highlight .am-comment-main:before {
  border-right-color: #3bb4f2;
}
.am-comment-secondary.am-comment-flip .am-comment-main:before,
.am-comment-highlight.am-comment-flip .am-comment-main:before {
  border-left-color: #3bb4f2;
  border-right-color: transparent;
}
.am-comment-secondary.am-comment-flip .am-comment-main:after,
.am-comment-highlight.am-comment-flip .am-comment-main:after {
  border-left-color: #f8f8f8;
}
.am-comment-success .am-comment-avatar {
  border-color: #5eb95e;
}
.am-comment-success .am-comment-main {
  border-color: #5eb95e;
}
.am-comment-success .am-comment-main:before {
  border-right-color: #5eb95e;
}
.am-comment-success.am-comment-flip .am-comment-main:before {
  border-left-color: #5eb95e;
  border-right-color: transparent;
}
.am-comment-success.am-comment-flip .am-comment-main:after {
  border-left-color: #f8f8f8;
}
.am-comment-warning .am-comment-avatar {
  border-color: #F37B1D;
}
.am-comment-warning .am-comment-main {
  border-color: #F37B1D;
}
.am-comment-warning .am-comment-main:before {
  border-right-color: #F37B1D;
}
.am-comment-warning.am-comment-flip .am-comment-main:before {
  border-left-color: #F37B1D;
  border-right-color: transparent;
}
.am-comment-warning.am-comment-flip .am-comment-main:after {
  border-left-color: #f8f8f8;
}
.am-comment-danger .am-comment-avatar {
  border-color: #dd514c;
}
.am-comment-danger .am-comment-main {
  border-color: #dd514c;
}
.am-comment-danger .am-comment-main:before {
  border-right-color: #dd514c;
}
.am-comment-danger.am-comment-flip .am-comment-main:before {
  border-left-color: #dd514c;
  border-right-color: transparent;
}
.am-comment-danger.am-comment-flip .am-comment-main:after {
  border-left-color: #f8f8f8;
}
.am-comments-list {
  padding: 0;
  list-style: none;
}
.am-comments-list .am-comment {
  margin: 1.6rem 0 0 0;
  list-style: none;
}
@media only screen and (min-width:641px) {
  .am-comments-list-flip .am-comment-main {
    margin-right: 64px;
  }
  .am-comments-list-flip .am-comment-flip .am-comment-main {
    margin-left: 64px;
  }
}
/* ==========================================================================
   Component: Button Group
 ============================================================================ */
.am-btn-group,
.am-btn-group-stacked {
  position: relative;
  display: inline-block;
  vertical-align: middle;
}
.am-btn-group > .am-btn,
.am-btn-group-stacked > .am-btn {
  position: relative;
  float: left;
}
.am-btn-group > .am-btn:hover,
.am-btn-group-stacked > .am-btn:hover,
.am-btn-group > .am-btn:focus,
.am-btn-group-stacked > .am-btn:focus,
.am-btn-group > .am-btn:active,
.am-btn-group-stacked > .am-btn:active,
.am-btn-group > .am-btn.active,
.am-btn-group-stacked > .am-btn.active {
  z-index: 2;
}
.am-btn-group > .am-btn:focus,
.am-btn-group-stacked > .am-btn:focus {
  outline: 0;
}
.am-btn-group .am-btn + .am-btn,
.am-btn-group .am-btn + .am-btn-group,
.am-btn-group .am-btn-group + .am-btn,
.am-btn-group .am-btn-group + .am-btn-group {
  margin-left: -1px;
}
.am-btn-toolbar {
  margin-left: -5px;
}
.am-btn-toolbar:before,
.am-btn-toolbar:after {
  content: " ";
  display: table;
}
.am-btn-toolbar:after {
  clear: both;
}
.am-btn-toolbar .am-btn-group,
.am-btn-toolbar .am-input-group {
  float: left;
}
.am-btn-toolbar > .am-btn,
.am-btn-toolbar > .am-btn-group,
.am-btn-toolbar > .am-input-group {
  margin-left: 5px;
}
.am-btn-group > .am-btn:not(:first-child):not(:last-child):not(.am-dropdown-toggle) {
  border-radius: 0;
}
.am-btn-group > .am-btn:first-child {
  margin-left: 0;
}
.am-btn-group > .am-btn:first-child:not(:last-child):not(.am-dropdown-toggle) {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}
.am-btn-group > .am-btn:last-child:not(:first-child),
.am-btn-group > .am-dropdown-toggle:not(:first-child) {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}
.am-btn-group > .am-btn-group {
  float: left;
}
.am-btn-group > .am-btn-group:not(:first-child):not(:last-child) > .am-btn {
  border-radius: 0;
}
.am-btn-group > .am-btn-group:first-child > .am-btn:last-child,
.am-btn-group > .am-btn-group:first-child > .am-dropdown-toggle {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}
.am-btn-group > .am-btn-group:last-child > .am-btn:first-child {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}
.am-btn-group-xs > .am-btn {
  font-size: 1.2rem;
}
.am-btn-group-sm > .am-btn {
  font-size: 1.4rem;
}
.am-btn-group-lg > .am-btn {
  font-size: 1.8rem;
}
.am-btn-group-stacked > .am-btn,
.am-btn-group-stacked > .am-btn-group,
.am-btn-group-stacked > .am-btn-group > .am-btn {
  display: block;
  float: none;
  width: 100%;
  max-width: 100%;
}
.am-btn-group-stacked > .am-btn-group:before,
.am-btn-group-stacked > .am-btn-group:after {
  content: " ";
  display: table;
}
.am-btn-group-stacked > .am-btn-group:after {
  clear: both;
}
.am-btn-group-stacked > .am-btn-group > .am-btn {
  float: none;
}
.am-btn-group-stacked > .am-btn + .am-btn,
.am-btn-group-stacked > .am-btn + .am-btn-group,
.am-btn-group-stacked > .am-btn-group + .am-btn,
.am-btn-group-stacked > .am-btn-group + .am-btn-group {
  margin-top: -1px;
  margin-left: 0;
}
.am-btn-group-stacked > .am-btn:not(:first-child):not(:last-child) {
  border-radius: 0;
}
.am-btn-group-stacked > .am-btn:first-child:not(:last-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.am-btn-group-stacked > .am-btn:last-child:not(:first-child) {
  border-bottom-left-radius: 0;
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}
.am-btn-group-stacked > .am-btn-group:not(:first-child):not(:last-child) > .am-btn {
  border-radius: 0;
}
.am-btn-group-stacked > .am-btn-group:first-child:not(:last-child) > .am-btn:last-child,
.am-btn-group-stacked > .am-btn-group:first-child:not(:last-child) > .am-dropdown-toggle {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.am-btn-group-stacked > .am-btn-group:last-child:not(:first-child) > .am-btn:first-child {
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}
.am-btn-group-justify {
  display: table;
  table-layout: fixed;
  border-collapse: separate;
  width: 100%;
}
.am-btn-group-justify > .am-btn,
.am-btn-group-justify > .am-btn-group {
  float: none;
  display: table-cell;
  width: 1%;
}
.am-btn-group-justify > .am-btn-group .am-btn {
  width: 100%;
}
.lte9 .am-btn-group-justify {
  display: table;
  table-layout: fixed;
  border-collapse: separate;
}
.lte9 .am-btn-group-justify > .am-btn,
.lte9 .am-btn-group-justify > .am-btn-group {
  float: none;
  display: table-cell;
  width: 1%;
}
.am-btn-group .am-dropdown {
  float: left;
  margin-left: -1px;
}
.am-btn-group .am-dropdown > .am-btn {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}
.am-btn-group .am-active .am-dropdown-toggle {
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
          box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}
.am-btn-group .am-active .am-dropdown-toggle.am-btn-link {
  -webkit-box-shadow: none;
          box-shadow: none;
}
.am-btn-group .am-dropdown-toggle:active,
.am-btn-group .am-active .am-dropdown-toggle {
  outline: 0;
}
[data-am-button] > .am-btn > input[type="radio"],
[data-am-button] > .am-btn > input[type="checkbox"],
.am-btn-group-check > .am-btn > input[type="radio"],
.am-btn-group-check > .am-btn > input[type="checkbox"] {
  position: absolute;
  z-index: -1;
  opacity: 0;
}
/* ==========================================================================
   Component: Close
 ============================================================================ */
.am-close {
  display: inline-block;
  text-align: center;
  width: 24px;
  font-size: 20px;
  font-weight: bold;
  line-height: 24px;
  color: #000;
  text-shadow: 0 1px 0 #fff;
  opacity: .2;
  -webkit-transition: all .3s;
  transition: all .3s;
}
.am-close:hover,
.am-close:focus {
  color: #000;
  text-decoration: none;
  cursor: pointer;
  opacity: .5;
  outline: none;
}
.am-close[class*="am-icon-"] {
  font-size: 16px;
}
button.am-close {
  padding: 0;
  cursor: pointer;
  background: transparent;
  /* Needed for Sarari */
  border: 0;
  -webkit-appearance: none;
}
a.am-close:hover {
  color: inherit;
  text-decoration: none;
  cursor: pointer;
}
/* Modifiers
 ============================================================================ */
.am-close-alt {
  border-radius: 50%;
  background: #eee;
  opacity: 0.7;
  -webkit-box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.25);
          box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.25);
  /* Hover */
}
.am-close-alt:hover,
.am-close-alt:focus {
  opacity: 1;
}
.am-close-spin:hover {
  -webkit-transform: rotate(360deg);
      -ms-transform: rotate(360deg);
          transform: rotate(360deg);
}
/* ==========================================================================
   Component: Icon
 ============================================================================ */
/* Font-face
============================================================================= */
@font-face {
  font-family: 'FontAwesome';
  font-weight: normal;
  font-style: normal;
}
/* Component
============================================================================= */
[class*="am-icon-"] {
  display: inline-block;
  font-style: normal;
}
[class*="am-icon-"]:before {
  display: inline-block;
  font: normal normal normal 1.6rem/1 "FontAwesome", sans-serif;
  /*font-weight: normal; // 2
  font-style: normal; // 2
  vertical-align: baseline; // 3
  line-height: 1; // 4*/
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-transform: translate(0, 0);
      -ms-transform: translate(0, 0);
          transform: translate(0, 0);
}
.am-icon-border {
  padding: .2em .25em .15em;
  border: solid 0.08em #eee;
  border-radius: .1em;
}
[class*="am-icon-"].am-fl {
  margin-right: .3em;
}
[class*="am-icon-"].am-fr {
  margin-left: .3em;
}
.am-icon-sm:before {
  font-size: 150%;
  vertical-align: -10%;
}
.am-icon-md:before {
  font-size: 200%;
  vertical-align: -16%;
}
.am-icon-lg:before {
  font-size: 250%;
  vertical-align: -22%;
}
/* Modifier: `.am-icon-btn`
============================================================================= */
.am-icon-btn {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  display: inline-block;
  width: 48px;
  height: 48px;
  font-size: 24px;
  line-height: 48px;
  border-radius: 50%;
  background-color: #eee;
  color: #555555;
  text-align: center;
  /**
   * Hover
   * 1. Apply hover style also to focus state
   * 2. Remove default focus style
   */
  /* Active */
}
.am-icon-btn:hover,
.am-icon-btn:focus {
  /* 1 */
  background-color: #f5f5f5;
  color: #333333;
  text-decoration: none;
  outline: none;
  /* 2 */
}
.am-icon-btn:active {
  background-color: #ddd;
  color: #333333;
}
.am-icon-btn.am-primary,
.am-icon-btn.am-secondary,
.am-icon-btn.am-success,
.am-icon-btn.am-warning,
.am-icon-btn.am-danger {
  color: #fff;
}
.am-icon-btn.am-primary {
  background-color: #0e90d2;
}
.am-icon-btn.am-secondary {
  background-color: #3bb4f2;
}
.am-icon-btn.am-success {
  background-color: #5eb95e;
}
.am-icon-btn.am-warning {
  background-color: #F37B1D;
}
.am-icon-btn.am-danger {
  background-color: #dd514c;
}
.am-icon-btn-sm {
  width: 32px;
  height: 32px;
  font-size: 16px;
  line-height: 32px;
}
.am-icon-btn-lg {
  width: 64px;
  height: 64px;
  font-size: 28px;
  line-height: 64px;
}
/* Modifier: `.am-icon-fw` Fixed width
============================================================================= */
.am-icon-fw {
  width: 1.25em;
  text-align: center;
}
/* Icon mapping
============================================================================= */
.am-icon-glass:before {
  content: "\f000";
}
.am-icon-music:before {
  content: "\f001";
}
.am-icon-search:before {
  content: "\f002";
}
.am-icon-envelope-o:before {
  content: "\f003";
}
.am-icon-heart:before {
  content: "\f004";
}
.am-icon-star:before {
  content: "\f005";
}
.am-icon-star-o:before {
  content: "\f006";
}
.am-icon-user:before {
  content: "\f007";
}
.am-icon-film:before {
  content: "\f008";
}
.am-icon-th-large:before {
  content: "\f009";
}
.am-icon-th:before {
  content: "\f00a";
}
.am-icon-th-list:before {
  content: "\f00b";
}
.am-icon-check:before {
  content: "\f00c";
}
.am-icon-remove:before,
.am-icon-close:before,
.am-icon-times:before {
  content: "\f00d";
}
.am-icon-search-plus:before {
  content: "\f00e";
}
.am-icon-search-minus:before {
  content: "\f010";
}
.am-icon-power-off:before {
  content: "\f011";
}
.am-icon-signal:before {
  content: "\f012";
}
.am-icon-gear:before,
.am-icon-cog:before {
  content: "\f013";
}
.am-icon-trash-o:before {
  content: "\f014";
}
.am-icon-home:before {
  content: "\f015";
}
.am-icon-file-o:before {
  content: "\f016";
}
.am-icon-clock-o:before {
  content: "\f017";
}
.am-icon-road:before {
  content: "\f018";
}
.am-icon-download:before {
  content: "\f019";
}
.am-icon-arrow-circle-o-down:before {
  content: "\f01a";
}
.am-icon-arrow-circle-o-up:before {
  content: "\f01b";
}
.am-icon-inbox:before {
  content: "\f01c";
}
.am-icon-play-circle-o:before {
  content: "\f01d";
}
.am-icon-rotate-right:before,
.am-icon-repeat:before {
  content: "\f01e";
}
.am-icon-refresh:before {
  content: "\f021";
}
.am-icon-list-alt:before {
  content: "\f022";
}
.am-icon-lock:before {
  content: "\f023";
}
.am-icon-flag:before {
  content: "\f024";
}
.am-icon-headphones:before {
  content: "\f025";
}
.am-icon-volume-off:before {
  content: "\f026";
}
.am-icon-volume-down:before {
  content: "\f027";
}
.am-icon-volume-up:before {
  content: "\f028";
}
.am-icon-qrcode:before {
  content: "\f029";
}
.am-icon-barcode:before {
  content: "\f02a";
}
.am-icon-tag:before {
  content: "\f02b";
}
.am-icon-tags:before {
  content: "\f02c";
}
.am-icon-book:before {
  content: "\f02d";
}
.am-icon-bookmark:before {
  content: "\f02e";
}
.am-icon-print:before {
  content: "\f02f";
}
.am-icon-camera:before {
  content: "\f030";
}
.am-icon-font:before {
  content: "\f031";
}
.am-icon-bold:before {
  content: "\f032";
}
.am-icon-italic:before {
  content: "\f033";
}
.am-icon-text-height:before {
  content: "\f034";
}
.am-icon-text-width:before {
  content: "\f035";
}
.am-icon-align-left:before {
  content: "\f036";
}
.am-icon-align-center:before {
  content: "\f037";
}
.am-icon-align-right:before {
  content: "\f038";
}
.am-icon-align-justify:before {
  content: "\f039";
}
.am-icon-list:before {
  content: "\f03a";
}
.am-icon-dedent:before,
.am-icon-outdent:before {
  content: "\f03b";
}
.am-icon-indent:before {
  content: "\f03c";
}
.am-icon-video-camera:before {
  content: "\f03d";
}
.am-icon-photo:before,
.am-icon-image:before,
.am-icon-picture-o:before {
  content: "\f03e";
}
.am-icon-pencil:before {
  content: "\f040";
}
.am-icon-map-marker:before {
  content: "\f041";
}
.am-icon-adjust:before {
  content: "\f042";
}
.am-icon-tint:before {
  content: "\f043";
}
.am-icon-edit:before,
.am-icon-pencil-square-o:before {
  content: "\f044";
}
.am-icon-share-square-o:before {
  content: "\f045";
}
.am-icon-check-square-o:before {
  content: "\f046";
}
.am-icon-arrows:before {
  content: "\f047";
}
.am-icon-step-backward:before {
  content: "\f048";
}
.am-icon-fast-backward:before {
  content: "\f049";
}
.am-icon-backward:before {
  content: "\f04a";
}
.am-icon-play:before {
  content: "\f04b";
}
.am-icon-pause:before {
  content: "\f04c";
}
.am-icon-stop:before {
  content: "\f04d";
}
.am-icon-forward:before {
  content: "\f04e";
}
.am-icon-fast-forward:before {
  content: "\f050";
}
.am-icon-step-forward:before {
  content: "\f051";
}
.am-icon-eject:before {
  content: "\f052";
}
.am-icon-chevron-left:before {
  content: "\f053";
}
.am-icon-chevron-right:before {
  content: "\f054";
}
.am-icon-plus-circle:before {
  content: "\f055";
}
.am-icon-minus-circle:before {
  content: "\f056";
}
.am-icon-times-circle:before {
  content: "\f057";
}
.am-icon-check-circle:before {
  content: "\f058";
}
.am-icon-question-circle:before {
  content: "\f059";
}
.am-icon-info-circle:before {
  content: "\f05a";
}
.am-icon-crosshairs:before {
  content: "\f05b";
}
.am-icon-times-circle-o:before {
  content: "\f05c";
}
.am-icon-check-circle-o:before {
  content: "\f05d";
}
.am-icon-ban:before {
  content: "\f05e";
}
.am-icon-arrow-left:before {
  content: "\f060";
}
.am-icon-arrow-right:before {
  content: "\f061";
}
.am-icon-arrow-up:before {
  content: "\f062";
}
.am-icon-arrow-down:before {
  content: "\f063";
}
.am-icon-mail-forward:before,
.am-icon-share:before {
  content: "\f064";
}
.am-icon-expand:before {
  content: "\f065";
}
.am-icon-compress:before {
  content: "\f066";
}
.am-icon-plus:before {
  content: "\f067";
}
.am-icon-minus:before {
  content: "\f068";
}
.am-icon-asterisk:before {
  content: "\f069";
}
.am-icon-exclamation-circle:before {
  content: "\f06a";
}
.am-icon-gift:before {
  content: "\f06b";
}
.am-icon-leaf:before {
  content: "\f06c";
}
.am-icon-fire:before {
  content: "\f06d";
}
.am-icon-eye:before {
  content: "\f06e";
}
.am-icon-eye-slash:before {
  content: "\f070";
}
.am-icon-warning:before,
.am-icon-exclamation-triangle:before {
  content: "\f071";
}
.am-icon-plane:before {
  content: "\f072";
}
.am-icon-calendar:before {
  content: "\f073";
}
.am-icon-random:before {
  content: "\f074";
}
.am-icon-comment:before {
  content: "\f075";
}
.am-icon-magnet:before {
  content: "\f076";
}
.am-icon-chevron-up:before {
  content: "\f077";
}
.am-icon-chevron-down:before {
  content: "\f078";
}
.am-icon-retweet:before {
  content: "\f079";
}
.am-icon-shopping-cart:before {
  content: "\f07a";
}
.am-icon-folder:before {
  content: "\f07b";
}
.am-icon-folder-open:before {
  content: "\f07c";
}
.am-icon-arrows-v:before {
  content: "\f07d";
}
.am-icon-arrows-h:before {
  content: "\f07e";
}
.am-icon-bar-chart-o:before,
.am-icon-bar-chart:before {
  content: "\f080";
}
.am-icon-twitter-square:before {
  content: "\f081";
}
.am-icon-facebook-square:before {
  content: "\f082";
}
.am-icon-camera-retro:before {
  content: "\f083";
}
.am-icon-key:before {
  content: "\f084";
}
.am-icon-gears:before,
.am-icon-cogs:before {
  content: "\f085";
}
.am-icon-comments:before {
  content: "\f086";
}
.am-icon-thumbs-o-up:before {
  content: "\f087";
}
.am-icon-thumbs-o-down:before {
  content: "\f088";
}
.am-icon-star-half:before {
  content: "\f089";
}
.am-icon-heart-o:before {
  content: "\f08a";
}
.am-icon-sign-out:before {
  content: "\f08b";
}
.am-icon-linkedin-square:before {
  content: "\f08c";
}
.am-icon-thumb-tack:before {
  content: "\f08d";
}
.am-icon-external-link:before {
  content: "\f08e";
}
.am-icon-sign-in:before {
  content: "\f090";
}
.am-icon-trophy:before {
  content: "\f091";
}
.am-icon-github-square:before {
  content: "\f092";
}
.am-icon-upload:before {
  content: "\f093";
}
.am-icon-lemon-o:before {
  content: "\f094";
}
.am-icon-phone:before {
  content: "\f095";
}
.am-icon-square-o:before {
  content: "\f096";
}
.am-icon-bookmark-o:before {
  content: "\f097";
}
.am-icon-phone-square:before {
  content: "\f098";
}
.am-icon-twitter:before {
  content: "\f099";
}
.am-icon-facebook-f:before,
.am-icon-facebook:before {
  content: "\f09a";
}
.am-icon-github:before {
  content: "\f09b";
}
.am-icon-unlock:before {
  content: "\f09c";
}
.am-icon-credit-card:before {
  content: "\f09d";
}
.am-icon-feed:before,
.am-icon-rss:before {
  content: "\f09e";
}
.am-icon-hdd-o:before {
  content: "\f0a0";
}
.am-icon-bullhorn:before {
  content: "\f0a1";
}
.am-icon-bell:before {
  content: "\f0f3";
}
.am-icon-certificate:before {
  content: "\f0a3";
}
.am-icon-hand-o-right:before {
  content: "\f0a4";
}
.am-icon-hand-o-left:before {
  content: "\f0a5";
}
.am-icon-hand-o-up:before {
  content: "\f0a6";
}
.am-icon-hand-o-down:before {
  content: "\f0a7";
}
.am-icon-arrow-circle-left:before {
  content: "\f0a8";
}
.am-icon-arrow-circle-right:before {
  content: "\f0a9";
}
.am-icon-arrow-circle-up:before {
  content: "\f0aa";
}
.am-icon-arrow-circle-down:before {
  content: "\f0ab";
}
.am-icon-globe:before {
  content: "\f0ac";
}
.am-icon-wrench:before {
  content: "\f0ad";
}
.am-icon-tasks:before {
  content: "\f0ae";
}
.am-icon-filter:before {
  content: "\f0b0";
}
.am-icon-briefcase:before {
  content: "\f0b1";
}
.am-icon-arrows-alt:before {
  content: "\f0b2";
}
.am-icon-group:before,
.am-icon-users:before {
  content: "\f0c0";
}
.am-icon-chain:before,
.am-icon-link:before {
  content: "\f0c1";
}
.am-icon-cloud:before {
  content: "\f0c2";
}
.am-icon-flask:before {
  content: "\f0c3";
}
.am-icon-cut:before,
.am-icon-scissors:before {
  content: "\f0c4";
}
.am-icon-copy:before,
.am-icon-files-o:before {
  content: "\f0c5";
}
.am-icon-paperclip:before {
  content: "\f0c6";
}
.am-icon-save:before,
.am-icon-floppy-o:before {
  content: "\f0c7";
}
.am-icon-square:before {
  content: "\f0c8";
}
.am-icon-navicon:before,
.am-icon-reorder:before,
.am-icon-bars:before {
  content: "\f0c9";
}
.am-icon-list-ul:before {
  content: "\f0ca";
}
.am-icon-list-ol:before {
  content: "\f0cb";
}
.am-icon-strikethrough:before {
  content: "\f0cc";
}
.am-icon-underline:before {
  content: "\f0cd";
}
.am-icon-table:before {
  content: "\f0ce";
}
.am-icon-magic:before {
  content: "\f0d0";
}
.am-icon-truck:before {
  content: "\f0d1";
}
.am-icon-pinterest:before {
  content: "\f0d2";
}
.am-icon-pinterest-square:before {
  content: "\f0d3";
}
.am-icon-google-plus-square:before {
  content: "\f0d4";
}
.am-icon-google-plus:before {
  content: "\f0d5";
}
.am-icon-money:before {
  content: "\f0d6";
}
.am-icon-caret-down:before {
  content: "\f0d7";
}
.am-icon-caret-up:before {
  content: "\f0d8";
}
.am-icon-caret-left:before {
  content: "\f0d9";
}
.am-icon-caret-right:before {
  content: "\f0da";
}
.am-icon-columns:before {
  content: "\f0db";
}
.am-icon-unsorted:before,
.am-icon-sort:before {
  content: "\f0dc";
}
.am-icon-sort-down:before,
.am-icon-sort-desc:before {
  content: "\f0dd";
}
.am-icon-sort-up:before,
.am-icon-sort-asc:before {
  content: "\f0de";
}
.am-icon-envelope:before {
  content: "\f0e0";
}
.am-icon-linkedin:before {
  content: "\f0e1";
}
.am-icon-rotate-left:before,
.am-icon-undo:before {
  content: "\f0e2";
}
.am-icon-legal:before,
.am-icon-gavel:before {
  content: "\f0e3";
}
.am-icon-dashboard:before,
.am-icon-tachometer:before {
  content: "\f0e4";
}
.am-icon-comment-o:before {
  content: "\f0e5";
}
.am-icon-comments-o:before {
  content: "\f0e6";
}
.am-icon-flash:before,
.am-icon-bolt:before {
  content: "\f0e7";
}
.am-icon-sitemap:before {
  content: "\f0e8";
}
.am-icon-umbrella:before {
  content: "\f0e9";
}
.am-icon-paste:before,
.am-icon-clipboard:before {
  content: "\f0ea";
}
.am-icon-lightbulb-o:before {
  content: "\f0eb";
}
.am-icon-exchange:before {
  content: "\f0ec";
}
.am-icon-cloud-download:before {
  content: "\f0ed";
}
.am-icon-cloud-upload:before {
  content: "\f0ee";
}
.am-icon-user-md:before {
  content: "\f0f0";
}
.am-icon-stethoscope:before {
  content: "\f0f1";
}
.am-icon-suitcase:before {
  content: "\f0f2";
}
.am-icon-bell-o:before {
  content: "\f0a2";
}
.am-icon-coffee:before {
  content: "\f0f4";
}
.am-icon-cutlery:before {
  content: "\f0f5";
}
.am-icon-file-text-o:before {
  content: "\f0f6";
}
.am-icon-building-o:before {
  content: "\f0f7";
}
.am-icon-hospital-o:before {
  content: "\f0f8";
}
.am-icon-ambulance:before {
  content: "\f0f9";
}
.am-icon-medkit:before {
  content: "\f0fa";
}
.am-icon-fighter-jet:before {
  content: "\f0fb";
}
.am-icon-beer:before {
  content: "\f0fc";
}
.am-icon-h-square:before {
  content: "\f0fd";
}
.am-icon-plus-square:before {
  content: "\f0fe";
}
.am-icon-angle-double-left:before {
  content: "\f100";
}
.am-icon-angle-double-right:before {
  content: "\f101";
}
.am-icon-angle-double-up:before {
  content: "\f102";
}
.am-icon-angle-double-down:before {
  content: "\f103";
}
.am-icon-angle-left:before {
  content: "\f104";
}
.am-icon-angle-right:before {
  content: "\f105";
}
.am-icon-angle-up:before {
  content: "\f106";
}
.am-icon-angle-down:before {
  content: "\f107";
}
.am-icon-desktop:before {
  content: "\f108";
}
.am-icon-laptop:before {
  content: "\f109";
}
.am-icon-tablet:before {
  content: "\f10a";
}
.am-icon-mobile-phone:before,
.am-icon-mobile:before {
  content: "\f10b";
}
.am-icon-circle-o:before {
  content: "\f10c";
}
.am-icon-quote-left:before {
  content: "\f10d";
}
.am-icon-quote-right:before {
  content: "\f10e";
}
.am-icon-spinner:before {
  content: "\f110";
}
.am-icon-circle:before {
  content: "\f111";
}
.am-icon-mail-reply:before,
.am-icon-reply:before {
  content: "\f112";
}
.am-icon-github-alt:before {
  content: "\f113";
}
.am-icon-folder-o:before {
  content: "\f114";
}
.am-icon-folder-open-o:before {
  content: "\f115";
}
.am-icon-smile-o:before {
  content: "\f118";
}
.am-icon-frown-o:before {
  content: "\f119";
}
.am-icon-meh-o:before {
  content: "\f11a";
}
.am-icon-gamepad:before {
  content: "\f11b";
}
.am-icon-keyboard-o:before {
  content: "\f11c";
}
.am-icon-flag-o:before {
  content: "\f11d";
}
.am-icon-flag-checkered:before {
  content: "\f11e";
}
.am-icon-terminal:before {
  content: "\f120";
}
.am-icon-code:before {
  content: "\f121";
}
.am-icon-mail-reply-all:before,
.am-icon-reply-all:before {
  content: "\f122";
}
.am-icon-star-half-empty:before,
.am-icon-star-half-full:before,
.am-icon-star-half-o:before {
  content: "\f123";
}
.am-icon-location-arrow:before {
  content: "\f124";
}
.am-icon-crop:before {
  content: "\f125";
}
.am-icon-code-fork:before {
  content: "\f126";
}
.am-icon-unlink:before,
.am-icon-chain-broken:before {
  content: "\f127";
}
.am-icon-question:before {
  content: "\f128";
}
.am-icon-info:before {
  content: "\f129";
}
.am-icon-exclamation:before {
  content: "\f12a";
}
.am-icon-superscript:before {
  content: "\f12b";
}
.am-icon-subscript:before {
  content: "\f12c";
}
.am-icon-eraser:before {
  content: "\f12d";
}
.am-icon-puzzle-piece:before {
  content: "\f12e";
}
.am-icon-microphone:before {
  content: "\f130";
}
.am-icon-microphone-slash:before {
  content: "\f131";
}
.am-icon-shield:before {
  content: "\f132";
}
.am-icon-calendar-o:before {
  content: "\f133";
}
.am-icon-fire-extinguisher:before {
  content: "\f134";
}
.am-icon-rocket:before {
  content: "\f135";
}
.am-icon-maxcdn:before {
  content: "\f136";
}
.am-icon-chevron-circle-left:before {
  content: "\f137";
}
.am-icon-chevron-circle-right:before {
  content: "\f138";
}
.am-icon-chevron-circle-up:before {
  content: "\f139";
}
.am-icon-chevron-circle-down:before {
  content: "\f13a";
}
.am-icon-html5:before {
  content: "\f13b";
}
.am-icon-css3:before {
  content: "\f13c";
}
.am-icon-anchor:before {
  content: "\f13d";
}
.am-icon-unlock-alt:before {
  content: "\f13e";
}
.am-icon-bullseye:before {
  content: "\f140";
}
.am-icon-ellipsis-h:before {
  content: "\f141";
}
.am-icon-ellipsis-v:before {
  content: "\f142";
}
.am-icon-rss-square:before {
  content: "\f143";
}
.am-icon-play-circle:before {
  content: "\f144";
}
.am-icon-ticket:before {
  content: "\f145";
}
.am-icon-minus-square:before {
  content: "\f146";
}
.am-icon-minus-square-o:before {
  content: "\f147";
}
.am-icon-level-up:before {
  content: "\f148";
}
.am-icon-level-down:before {
  content: "\f149";
}
.am-icon-check-square:before {
  content: "\f14a";
}
.am-icon-pencil-square:before {
  content: "\f14b";
}
.am-icon-external-link-square:before {
  content: "\f14c";
}
.am-icon-share-square:before {
  content: "\f14d";
}
.am-icon-compass:before {
  content: "\f14e";
}
.am-icon-toggle-down:before,
.am-icon-caret-square-o-down:before {
  content: "\f150";
}
.am-icon-toggle-up:before,
.am-icon-caret-square-o-up:before {
  content: "\f151";
}
.am-icon-toggle-right:before,
.am-icon-caret-square-o-right:before {
  content: "\f152";
}
.am-icon-euro:before,
.am-icon-eur:before {
  content: "\f153";
}
.am-icon-gbp:before {
  content: "\f154";
}
.am-icon-dollar:before,
.am-icon-usd:before {
  content: "\f155";
}
.am-icon-rupee:before,
.am-icon-inr:before {
  content: "\f156";
}
.am-icon-cny:before,
.am-icon-rmb:before,
.am-icon-yen:before,
.am-icon-jpy:before {
  content: "\f157";
}
.am-icon-ruble:before,
.am-icon-rouble:before,
.am-icon-rub:before {
  content: "\f158";
}
.am-icon-won:before,
.am-icon-krw:before {
  content: "\f159";
}
.am-icon-bitcoin:before,
.am-icon-btc:before {
  content: "\f15a";
}
.am-icon-file:before {
  content: "\f15b";
}
.am-icon-file-text:before {
  content: "\f15c";
}
.am-icon-sort-alpha-asc:before {
  content: "\f15d";
}
.am-icon-sort-alpha-desc:before {
  content: "\f15e";
}
.am-icon-sort-amount-asc:before {
  content: "\f160";
}
.am-icon-sort-amount-desc:before {
  content: "\f161";
}
.am-icon-sort-numeric-asc:before {
  content: "\f162";
}
.am-icon-sort-numeric-desc:before {
  content: "\f163";
}
.am-icon-thumbs-up:before {
  content: "\f164";
}
.am-icon-thumbs-down:before {
  content: "\f165";
}
.am-icon-youtube-square:before {
  content: "\f166";
}
.am-icon-youtube:before {
  content: "\f167";
}
.am-icon-xing:before {
  content: "\f168";
}
.am-icon-xing-square:before {
  content: "\f169";
}
.am-icon-youtube-play:before {
  content: "\f16a";
}
.am-icon-dropbox:before {
  content: "\f16b";
}
.am-icon-stack-overflow:before {
  content: "\f16c";
}
.am-icon-instagram:before {
  content: "\f16d";
}
.am-icon-flickr:before {
  content: "\f16e";
}
.am-icon-adn:before {
  content: "\f170";
}
.am-icon-bitbucket:before {
  content: "\f171";
}
.am-icon-bitbucket-square:before {
  content: "\f172";
}
.am-icon-tumblr:before {
  content: "\f173";
}
.am-icon-tumblr-square:before {
  content: "\f174";
}
.am-icon-long-arrow-down:before {
  content: "\f175";
}
.am-icon-long-arrow-up:before {
  content: "\f176";
}
.am-icon-long-arrow-left:before {
  content: "\f177";
}
.am-icon-long-arrow-right:before {
  content: "\f178";
}
.am-icon-apple:before {
  content: "\f179";
}
.am-icon-windows:before {
  content: "\f17a";
}
.am-icon-android:before {
  content: "\f17b";
}
.am-icon-linux:before {
  content: "\f17c";
}
.am-icon-dribbble:before {
  content: "\f17d";
}
.am-icon-skype:before {
  content: "\f17e";
}
.am-icon-foursquare:before {
  content: "\f180";
}
.am-icon-trello:before {
  content: "\f181";
}
.am-icon-female:before {
  content: "\f182";
}
.am-icon-male:before {
  content: "\f183";
}
.am-icon-gittip:before,
.am-icon-gratipay:before {
  content: "\f184";
}
.am-icon-sun-o:before {
  content: "\f185";
}
.am-icon-moon-o:before {
  content: "\f186";
}
.am-icon-archive:before {
  content: "\f187";
}
.am-icon-bug:before {
  content: "\f188";
}
.am-icon-vk:before {
  content: "\f189";
}
.am-icon-weibo:before {
  content: "\f18a";
}
.am-icon-renren:before {
  content: "\f18b";
}
.am-icon-pagelines:before {
  content: "\f18c";
}
.am-icon-stack-exchange:before {
  content: "\f18d";
}
.am-icon-arrow-circle-o-right:before {
  content: "\f18e";
}
.am-icon-arrow-circle-o-left:before {
  content: "\f190";
}
.am-icon-toggle-left:before,
.am-icon-caret-square-o-left:before {
  content: "\f191";
}
.am-icon-dot-circle-o:before {
  content: "\f192";
}
.am-icon-wheelchair:before {
  content: "\f193";
}
.am-icon-vimeo-square:before {
  content: "\f194";
}
.am-icon-turkish-lira:before,
.am-icon-try:before {
  content: "\f195";
}
.am-icon-plus-square-o:before {
  content: "\f196";
}
.am-icon-space-shuttle:before {
  content: "\f197";
}
.am-icon-slack:before {
  content: "\f198";
}
.am-icon-envelope-square:before {
  content: "\f199";
}
.am-icon-wordpress:before {
  content: "\f19a";
}
.am-icon-openid:before {
  content: "\f19b";
}
.am-icon-institution:before,
.am-icon-bank:before,
.am-icon-university:before {
  content: "\f19c";
}
.am-icon-mortar-board:before,
.am-icon-graduation-cap:before {
  content: "\f19d";
}
.am-icon-yahoo:before {
  content: "\f19e";
}
.am-icon-google:before {
  content: "\f1a0";
}
.am-icon-reddit:before {
  content: "\f1a1";
}
.am-icon-reddit-square:before {
  content: "\f1a2";
}
.am-icon-stumbleupon-circle:before {
  content: "\f1a3";
}
.am-icon-stumbleupon:before {
  content: "\f1a4";
}
.am-icon-delicious:before {
  content: "\f1a5";
}
.am-icon-digg:before {
  content: "\f1a6";
}
.am-icon-pied-piper-pp:before {
  content: "\f1a7";
}
.am-icon-pied-piper-alt:before {
  content: "\f1a8";
}
.am-icon-drupal:before {
  content: "\f1a9";
}
.am-icon-joomla:before {
  content: "\f1aa";
}
.am-icon-language:before {
  content: "\f1ab";
}
.am-icon-fax:before {
  content: "\f1ac";
}
.am-icon-building:before {
  content: "\f1ad";
}
.am-icon-child:before {
  content: "\f1ae";
}
.am-icon-paw:before {
  content: "\f1b0";
}
.am-icon-spoon:before {
  content: "\f1b1";
}
.am-icon-cube:before {
  content: "\f1b2";
}
.am-icon-cubes:before {
  content: "\f1b3";
}
.am-icon-behance:before {
  content: "\f1b4";
}
.am-icon-behance-square:before {
  content: "\f1b5";
}
.am-icon-steam:before {
  content: "\f1b6";
}
.am-icon-steam-square:before {
  content: "\f1b7";
}
.am-icon-recycle:before {
  content: "\f1b8";
}
.am-icon-automobile:before,
.am-icon-car:before {
  content: "\f1b9";
}
.am-icon-cab:before,
.am-icon-taxi:before {
  content: "\f1ba";
}
.am-icon-tree:before {
  content: "\f1bb";
}
.am-icon-spotify:before {
  content: "\f1bc";
}
.am-icon-deviantart:before {
  content: "\f1bd";
}
.am-icon-soundcloud:before {
  content: "\f1be";
}
.am-icon-database:before {
  content: "\f1c0";
}
.am-icon-file-pdf-o:before {
  content: "\f1c1";
}
.am-icon-file-word-o:before {
  content: "\f1c2";
}
.am-icon-file-excel-o:before {
  content: "\f1c3";
}
.am-icon-file-powerpoint-o:before {
  content: "\f1c4";
}
.am-icon-file-photo-o:before,
.am-icon-file-picture-o:before,
.am-icon-file-image-o:before {
  content: "\f1c5";
}
.am-icon-file-zip-o:before,
.am-icon-file-archive-o:before {
  content: "\f1c6";
}
.am-icon-file-sound-o:before,
.am-icon-file-audio-o:before {
  content: "\f1c7";
}
.am-icon-file-movie-o:before,
.am-icon-file-video-o:before {
  content: "\f1c8";
}
.am-icon-file-code-o:before {
  content: "\f1c9";
}
.am-icon-vine:before {
  content: "\f1ca";
}
.am-icon-codepen:before {
  content: "\f1cb";
}
.am-icon-jsfiddle:before {
  content: "\f1cc";
}
.am-icon-life-bouy:before,
.am-icon-life-buoy:before,
.am-icon-life-saver:before,
.am-icon-support:before,
.am-icon-life-ring:before {
  content: "\f1cd";
}
.am-icon-circle-o-notch:before {
  content: "\f1ce";
}
.am-icon-ra:before,
.am-icon-resistance:before,
.am-icon-rebel:before {
  content: "\f1d0";
}
.am-icon-ge:before,
.am-icon-empire:before {
  content: "\f1d1";
}
.am-icon-git-square:before {
  content: "\f1d2";
}
.am-icon-git:before {
  content: "\f1d3";
}
.am-icon-y-combinator-square:before,
.am-icon-yc-square:before,
.am-icon-hacker-news:before {
  content: "\f1d4";
}
.am-icon-tencent-weibo:before {
  content: "\f1d5";
}
.am-icon-qq:before {
  content: "\f1d6";
}
.am-icon-wechat:before,
.am-icon-weixin:before {
  content: "\f1d7";
}
.am-icon-send:before,
.am-icon-paper-plane:before {
  content: "\f1d8";
}
.am-icon-send-o:before,
.am-icon-paper-plane-o:before {
  content: "\f1d9";
}
.am-icon-history:before {
  content: "\f1da";
}
.am-icon-circle-thin:before {
  content: "\f1db";
}
.am-icon-header:before {
  content: "\f1dc";
}
.am-icon-paragraph:before {
  content: "\f1dd";
}
.am-icon-sliders:before {
  content: "\f1de";
}
.am-icon-share-alt:before {
  content: "\f1e0";
}
.am-icon-share-alt-square:before {
  content: "\f1e1";
}
.am-icon-bomb:before {
  content: "\f1e2";
}
.am-icon-soccer-ball-o:before,
.am-icon-futbol-o:before {
  content: "\f1e3";
}
.am-icon-tty:before {
  content: "\f1e4";
}
.am-icon-binoculars:before {
  content: "\f1e5";
}
.am-icon-plug:before {
  content: "\f1e6";
}
.am-icon-slideshare:before {
  content: "\f1e7";
}
.am-icon-twitch:before {
  content: "\f1e8";
}
.am-icon-yelp:before {
  content: "\f1e9";
}
.am-icon-newspaper-o:before {
  content: "\f1ea";
}
.am-icon-wifi:before {
  content: "\f1eb";
}
.am-icon-calculator:before {
  content: "\f1ec";
}
.am-icon-paypal:before {
  content: "\f1ed";
}
.am-icon-google-wallet:before {
  content: "\f1ee";
}
.am-icon-cc-visa:before {
  content: "\f1f0";
}
.am-icon-cc-mastercard:before {
  content: "\f1f1";
}
.am-icon-cc-discover:before {
  content: "\f1f2";
}
.am-icon-cc-amex:before {
  content: "\f1f3";
}
.am-icon-cc-paypal:before {
  content: "\f1f4";
}
.am-icon-cc-stripe:before {
  content: "\f1f5";
}
.am-icon-bell-slash:before {
  content: "\f1f6";
}
.am-icon-bell-slash-o:before {
  content: "\f1f7";
}
.am-icon-trash:before {
  content: "\f1f8";
}
.am-icon-copyright:before {
  content: "\f1f9";
}
.am-icon-at:before {
  content: "\f1fa";
}
.am-icon-eyedropper:before {
  content: "\f1fb";
}
.am-icon-paint-brush:before {
  content: "\f1fc";
}
.am-icon-birthday-cake:before {
  content: "\f1fd";
}
.am-icon-area-chart:before {
  content: "\f1fe";
}
.am-icon-pie-chart:before {
  content: "\f200";
}
.am-icon-line-chart:before {
  content: "\f201";
}
.am-icon-lastfm:before {
  content: "\f202";
}
.am-icon-lastfm-square:before {
  content: "\f203";
}
.am-icon-toggle-off:before {
  content: "\f204";
}
.am-icon-toggle-on:before {
  content: "\f205";
}
.am-icon-bicycle:before {
  content: "\f206";
}
.am-icon-bus:before {
  content: "\f207";
}
.am-icon-ioxhost:before {
  content: "\f208";
}
.am-icon-angellist:before {
  content: "\f209";
}
.am-icon-cc:before {
  content: "\f20a";
}
.am-icon-shekel:before,
.am-icon-sheqel:before,
.am-icon-ils:before {
  content: "\f20b";
}
.am-icon-meanpath:before {
  content: "\f20c";
}
.am-icon-buysellads:before {
  content: "\f20d";
}
.am-icon-connectdevelop:before {
  content: "\f20e";
}
.am-icon-dashcube:before {
  content: "\f210";
}
.am-icon-forumbee:before {
  content: "\f211";
}
.am-icon-leanpub:before {
  content: "\f212";
}
.am-icon-sellsy:before {
  content: "\f213";
}
.am-icon-shirtsinbulk:before {
  content: "\f214";
}
.am-icon-simplybuilt:before {
  content: "\f215";
}
.am-icon-skyatlas:before {
  content: "\f216";
}
.am-icon-cart-plus:before {
  content: "\f217";
}
.am-icon-cart-arrow-down:before {
  content: "\f218";
}
.am-icon-diamond:before {
  content: "\f219";
}
.am-icon-ship:before {
  content: "\f21a";
}
.am-icon-user-secret:before {
  content: "\f21b";
}
.am-icon-motorcycle:before {
  content: "\f21c";
}
.am-icon-street-view:before {
  content: "\f21d";
}
.am-icon-heartbeat:before {
  content: "\f21e";
}
.am-icon-venus:before {
  content: "\f221";
}
.am-icon-mars:before {
  content: "\f222";
}
.am-icon-mercury:before {
  content: "\f223";
}
.am-icon-intersex:before,
.am-icon-transgender:before {
  content: "\f224";
}
.am-icon-transgender-alt:before {
  content: "\f225";
}
.am-icon-venus-double:before {
  content: "\f226";
}
.am-icon-mars-double:before {
  content: "\f227";
}
.am-icon-venus-mars:before {
  content: "\f228";
}
.am-icon-mars-stroke:before {
  content: "\f229";
}
.am-icon-mars-stroke-v:before {
  content: "\f22a";
}
.am-icon-mars-stroke-h:before {
  content: "\f22b";
}
.am-icon-neuter:before {
  content: "\f22c";
}
.am-icon-genderless:before {
  content: "\f22d";
}
.am-icon-facebook-official:before {
  content: "\f230";
}
.am-icon-pinterest-p:before {
  content: "\f231";
}
.am-icon-whatsapp:before {
  content: "\f232";
}
.am-icon-server:before {
  content: "\f233";
}
.am-icon-user-plus:before {
  content: "\f234";
}
.am-icon-user-times:before {
  content: "\f235";
}
.am-icon-hotel:before,
.am-icon-bed:before {
  content: "\f236";
}
.am-icon-viacoin:before {
  content: "\f237";
}
.am-icon-train:before {
  content: "\f238";
}
.am-icon-subway:before {
  content: "\f239";
}
.am-icon-medium:before {
  content: "\f23a";
}
.am-icon-yc:before,
.am-icon-y-combinator:before {
  content: "\f23b";
}
.am-icon-optin-monster:before {
  content: "\f23c";
}
.am-icon-opencart:before {
  content: "\f23d";
}
.am-icon-expeditedssl:before {
  content: "\f23e";
}
.am-icon-battery-4:before,
.am-icon-battery-full:before {
  content: "\f240";
}
.am-icon-battery-3:before,
.am-icon-battery-three-quarters:before {
  content: "\f241";
}
.am-icon-battery-2:before,
.am-icon-battery-half:before {
  content: "\f242";
}
.am-icon-battery-1:before,
.am-icon-battery-quarter:before {
  content: "\f243";
}
.am-icon-battery-0:before,
.am-icon-battery-empty:before {
  content: "\f244";
}
.am-icon-mouse-pointer:before {
  content: "\f245";
}
.am-icon-i-cursor:before {
  content: "\f246";
}
.am-icon-object-group:before {
  content: "\f247";
}
.am-icon-object-ungroup:before {
  content: "\f248";
}
.am-icon-sticky-note:before {
  content: "\f249";
}
.am-icon-sticky-note-o:before {
  content: "\f24a";
}
.am-icon-cc-jcb:before {
  content: "\f24b";
}
.am-icon-cc-diners-club:before {
  content: "\f24c";
}
.am-icon-clone:before {
  content: "\f24d";
}
.am-icon-balance-scale:before {
  content: "\f24e";
}
.am-icon-hourglass-o:before {
  content: "\f250";
}
.am-icon-hourglass-1:before,
.am-icon-hourglass-start:before {
  content: "\f251";
}
.am-icon-hourglass-2:before,
.am-icon-hourglass-half:before {
  content: "\f252";
}
.am-icon-hourglass-3:before,
.am-icon-hourglass-end:before {
  content: "\f253";
}
.am-icon-hourglass:before {
  content: "\f254";
}
.am-icon-hand-grab-o:before,
.am-icon-hand-rock-o:before {
  content: "\f255";
}
.am-icon-hand-stop-o:before,
.am-icon-hand-paper-o:before {
  content: "\f256";
}
.am-icon-hand-scissors-o:before {
  content: "\f257";
}
.am-icon-hand-lizard-o:before {
  content: "\f258";
}
.am-icon-hand-spock-o:before {
  content: "\f259";
}
.am-icon-hand-pointer-o:before {
  content: "\f25a";
}
.am-icon-hand-peace-o:before {
  content: "\f25b";
}
.am-icon-trademark:before {
  content: "\f25c";
}
.am-icon-registered:before {
  content: "\f25d";
}
.am-icon-creative-commons:before {
  content: "\f25e";
}
.am-icon-gg:before {
  content: "\f260";
}
.am-icon-gg-circle:before {
  content: "\f261";
}
.am-icon-tripadvisor:before {
  content: "\f262";
}
.am-icon-odnoklassniki:before {
  content: "\f263";
}
.am-icon-odnoklassniki-square:before {
  content: "\f264";
}
.am-icon-get-pocket:before {
  content: "\f265";
}
.am-icon-wikipedia-w:before {
  content: "\f266";
}
.am-icon-safari:before {
  content: "\f267";
}
.am-icon-chrome:before {
  content: "\f268";
}
.am-icon-firefox:before {
  content: "\f269";
}
.am-icon-opera:before {
  content: "\f26a";
}
.am-icon-internet-explorer:before {
  content: "\f26b";
}
.am-icon-tv:before,
.am-icon-television:before {
  content: "\f26c";
}
.am-icon-contao:before {
  content: "\f26d";
}
.am-icon-500px:before {
  content: "\f26e";
}
.am-icon-amazon:before {
  content: "\f270";
}
.am-icon-calendar-plus-o:before {
  content: "\f271";
}
.am-icon-calendar-minus-o:before {
  content: "\f272";
}
.am-icon-calendar-times-o:before {
  content: "\f273";
}
.am-icon-calendar-check-o:before {
  content: "\f274";
}
.am-icon-industry:before {
  content: "\f275";
}
.am-icon-map-pin:before {
  content: "\f276";
}
.am-icon-map-signs:before {
  content: "\f277";
}
.am-icon-map-o:before {
  content: "\f278";
}
.am-icon-map:before {
  content: "\f279";
}
.am-icon-commenting:before {
  content: "\f27a";
}
.am-icon-commenting-o:before {
  content: "\f27b";
}
.am-icon-houzz:before {
  content: "\f27c";
}
.am-icon-vimeo:before {
  content: "\f27d";
}
.am-icon-black-tie:before {
  content: "\f27e";
}
.am-icon-fonticons:before {
  content: "\f280";
}
.am-icon-reddit-alien:before {
  content: "\f281";
}
.am-icon-edge:before {
  content: "\f282";
}
.am-icon-credit-card-alt:before {
  content: "\f283";
}
.am-icon-codiepie:before {
  content: "\f284";
}
.am-icon-modx:before {
  content: "\f285";
}
.am-icon-fort-awesome:before {
  content: "\f286";
}
.am-icon-usb:before {
  content: "\f287";
}
.am-icon-product-hunt:before {
  content: "\f288";
}
.am-icon-mixcloud:before {
  content: "\f289";
}
.am-icon-scribd:before {
  content: "\f28a";
}
.am-icon-pause-circle:before {
  content: "\f28b";
}
.am-icon-pause-circle-o:before {
  content: "\f28c";
}
.am-icon-stop-circle:before {
  content: "\f28d";
}
.am-icon-stop-circle-o:before {
  content: "\f28e";
}
.am-icon-shopping-bag:before {
  content: "\f290";
}
.am-icon-shopping-basket:before {
  content: "\f291";
}
.am-icon-hashtag:before {
  content: "\f292";
}
.am-icon-bluetooth:before {
  content: "\f293";
}
.am-icon-bluetooth-b:before {
  content: "\f294";
}
.am-icon-percent:before {
  content: "\f295";
}
.am-icon-gitlab:before {
  content: "\f296";
}
.am-icon-wpbeginner:before {
  content: "\f297";
}
.am-icon-wpforms:before {
  content: "\f298";
}
.am-icon-envira:before {
  content: "\f299";
}
.am-icon-universal-access:before {
  content: "\f29a";
}
.am-icon-wheelchair-alt:before {
  content: "\f29b";
}
.am-icon-question-circle-o:before {
  content: "\f29c";
}
.am-icon-blind:before {
  content: "\f29d";
}
.am-icon-audio-description:before {
  content: "\f29e";
}
.am-icon-volume-control-phone:before {
  content: "\f2a0";
}
.am-icon-braille:before {
  content: "\f2a1";
}
.am-icon-assistive-listening-systems:before {
  content: "\f2a2";
}
.am-icon-asl-interpreting:before,
.am-icon-american-sign-language-interpreting:before {
  content: "\f2a3";
}
.am-icon-deafness:before,
.am-icon-hard-of-hearing:before,
.am-icon-deaf:before {
  content: "\f2a4";
}
.am-icon-glide:before {
  content: "\f2a5";
}
.am-icon-glide-g:before {
  content: "\f2a6";
}
.am-icon-signing:before,
.am-icon-sign-language:before {
  content: "\f2a7";
}
.am-icon-low-vision:before {
  content: "\f2a8";
}
.am-icon-viadeo:before {
  content: "\f2a9";
}
.am-icon-viadeo-square:before {
  content: "\f2aa";
}
.am-icon-snapchat:before {
  content: "\f2ab";
}
.am-icon-snapchat-ghost:before {
  content: "\f2ac";
}
.am-icon-snapchat-square:before {
  content: "\f2ad";
}
.am-icon-pied-piper:before {
  content: "\f2ae";
}
.am-icon-first-order:before {
  content: "\f2b0";
}
.am-icon-yoast:before {
  content: "\f2b1";
}
.am-icon-themeisle:before {
  content: "\f2b2";
}
.am-icon-google-plus-circle:before,
.am-icon-google-plus-official:before {
  content: "\f2b3";
}
.am-icon-fa:before,
.am-icon-font-awesome:before {
  content: "\f2b4";
}
/* Modifier: `am-icon-spin`
============================================================================= */
@-webkit-keyframes icon-spin {
  0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
            transform: rotate(359deg);
  }
}
@keyframes icon-spin {
  0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
            transform: rotate(359deg);
  }
}
.am-icon-spin {
  -webkit-animation: icon-spin 2s infinite linear;
          animation: icon-spin 2s infinite linear;
}
.am-icon-pulse {
  -webkit-animation: icon-spin 1s infinite steps(8);
          animation: icon-spin 1s infinite steps(8);
}
.am-icon-ul {
  padding-left: 0;
  margin-left: 2.14285714em;
  list-style-type: none;
}
.am-icon-ul > li {
  position: relative;
}
.am-icon-li {
  position: absolute;
  left: -2.14285714em;
  width: 2.14285714em;
  top: 0.14285714em;
  text-align: center;
}
/* ==========================================================================
   Component: Input group
 ============================================================================ */
.am-input-group {
  position: relative;
  display: table;
  border-collapse: separate;
}
.am-input-group .am-form-field {
  position: relative;
  z-index: 2;
  float: left;
  width: 100%;
  margin-bottom: 0;
}
.am-input-group-label,
.am-input-group-btn,
.am-input-group .am-form-field {
  display: table-cell;
}
.am-input-group-label:not(:first-child):not(:last-child),
.am-input-group-btn:not(:first-child):not(:last-child),
.am-input-group .am-form-field:not(:first-child):not(:last-child) {
  border-radius: 0;
}
.am-input-group-label,
.am-input-group-btn {
  width: 1%;
  white-space: nowrap;
  vertical-align: middle;
}
.am-input-group-label {
  height: 38px;
  padding: 0 1em;
  font-size: 1.6rem;
  font-weight: normal;
  line-height: 36px;
  color: #555555;
  text-align: center;
  background-color: #eeeeee;
  border: 1px solid #ccc;
  border-radius: 0;
}
.am-input-group-label input[type="radio"],
.am-input-group-label input[type="checkbox"] {
  margin-top: 0;
}
.am-input-group .am-form-field:first-child,
.am-input-group-label:first-child,
.am-input-group-btn:first-child > .am-btn,
.am-input-group-btn:first-child > .am-btn-group > .am-btn,
.am-input-group-btn:first-child > .am-dropdown-toggle,
.am-input-group-btn:last-child > .am-btn:not(:last-child):not(.dropdown-toggle),
.am-input-group-btn:last-child > .am-btn-group:not(:last-child) > .am-btn {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}
.am-input-group-label:first-child {
  border-right: 0;
}
.am-input-group .am-form-field:last-child,
.am-input-group-label:last-child,
.am-input-group-btn:last-child > .am-btn,
.am-input-group-btn:last-child > .am-btn-group > .am-btn,
.am-input-group-btn:last-child > .am-dropdown-toggle,
.am-input-group-btn:first-child > .am-btn:not(:first-child),
.am-input-group-btn:first-child > .am-btn-group:not(:first-child) > .am-btn {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}
.am-input-group-label:last-child {
  border-left: 0;
}
.am-input-group-btn {
  position: relative;
  font-size: 0;
  white-space: nowrap;
}
.am-input-group-btn > .am-btn {
  position: relative;
  border-color: #ccc;
}
.am-input-group-btn > .am-btn + .am-btn {
  margin-left: -1px;
}
.am-input-group-btn > .am-btn:hover,
.am-input-group-btn > .am-btn:focus,
.am-input-group-btn > .am-btn:active {
  z-index: 2;
}
.am-input-group-btn:first-child > .am-btn,
.am-input-group-btn:first-child > .am-btn-group {
  margin-right: -2px;
}
.am-input-group-btn:last-child > .am-btn,
.am-input-group-btn:last-child > .am-btn-group {
  margin-left: -1px;
}
.am-input-group .am-form-field,
.am-input-group-btn > .am-btn {
  height: 38px;
  padding-bottom: auto;
}
.am-input-group-lg > .am-form-field,
.am-input-group-lg > .am-input-group-label,
.am-input-group-lg > .am-input-group-btn > .am-btn {
  height: 42px;
  font-size: 1.8rem !important;
}
.am-input-group-lg > .am-input-group-label {
  line-height: 40px;
}
.am-input-group-sm > .am-form-field,
.am-input-group-sm > .am-input-group-label,
.am-input-group-sm > .am-input-group-btn > .am-btn {
  height: 33px;
  font-size: 1.4rem !important;
}
.am-input-group-sm > .am-input-group-label {
  line-height: 31px;
}
.am-input-group-primary .am-input-group-label {
  background: #0e90d2;
  color: #fff;
}
.am-input-group-primary .am-input-group-label,
.am-input-group-primary .am-input-group-btn > .am-btn,
.am-input-group-primary.am-input-group .am-form-field {
  border-color: #0e90d2;
}
.am-input-group-secondary .am-input-group-label {
  background: #3bb4f2;
  color: #fff;
}
.am-input-group-secondary .am-input-group-label,
.am-input-group-secondary .am-input-group-btn > .am-btn,
.am-input-group-secondary.am-input-group .am-form-field {
  border-color: #3bb4f2;
}
.am-input-group-success .am-input-group-label {
  background: #5eb95e;
  color: #fff;
}
.am-input-group-success .am-input-group-label,
.am-input-group-success .am-input-group-btn > .am-btn,
.am-input-group-success.am-input-group .am-form-field {
  border-color: #5eb95e;
}
.am-input-group-warning .am-input-group-label {
  background: #F37B1D;
  color: #fff;
}
.am-input-group-warning .am-input-group-label,
.am-input-group-warning .am-input-group-btn > .am-btn,
.am-input-group-warning.am-input-group .am-form-field {
  border-color: #F37B1D;
}
.am-input-group-danger .am-input-group-label {
  background: #dd514c;
  color: #fff;
}
.am-input-group-danger .am-input-group-label,
.am-input-group-danger .am-input-group-btn > .am-btn,
.am-input-group-danger.am-input-group .am-form-field {
  border-color: #dd514c;
}
/* ==========================================================================
   Component: List
 ============================================================================ */
.am-list {
  margin-bottom: 1.6rem;
  padding-left: 0;
}
.am-list > li {
  position: relative;
  display: block;
  margin-bottom: -1px;
  background-color: #fff;
  border: 1px solid #dedede;
  border-width: 1px 0;
}
.am-list > li > a {
  display: block;
  padding: 1rem 0;
}
.am-list > li > a.am-active,
.am-list > li > a.am-active:hover,
.am-list > li > a.am-active:focus {
  z-index: 2;
  color: #fff;
  background-color: #0e90d2;
  border-color: #0e90d2;
}
.am-list > li > a.am-active .am-list-item-heading,
.am-list > li > a.am-active:hover .am-list-item-heading,
.am-list > li > a.am-active:focus .am-list-item-heading {
  color: inherit;
}
.am-list > li > a.am-active .am-list-item-text,
.am-list > li > a.am-active:hover .am-list-item-text,
.am-list > li > a.am-active:focus .am-list-item-text {
  color: #b2e2fa;
}
.am-list > li > .am-badge {
  float: right;
}
.am-list > li > .am-badge + .am-badge {
  margin-right: 5px;
}
/* Pure text list */
.am-list-static > li {
  padding: .8rem .2rem;
}
.am-list-static.am-list-border > li {
  padding: 1rem;
}
/* with border */
.am-list-border > li,
.am-list-bordered > li {
  border-width: 1px;
}
.am-list-border > li:first-child,
.am-list-bordered > li:first-child,
.am-list-border > li:first-child > a,
.am-list-bordered > li:first-child > a {
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}
.am-list-border > li:last-child,
.am-list-bordered > li:last-child,
.am-list-border > li:last-child > a,
.am-list-bordered > li:last-child > a {
  margin-bottom: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.am-list-border > li > a,
.am-list-bordered > li > a {
  padding: 1rem;
}
.am-list-border > li > a:hover,
.am-list-bordered > li > a:hover,
.am-list-border > li > a:focus,
.am-list-bordered > li > a:focus {
  background-color: #f5f5f5;
}
/* Striped */
.am-list-striped > li:nth-of-type(even) {
  background: #f5f5f5;
}
.am-list-item-hd {
  margin-top: 0;
}
.am-list-item-text {
  line-height: 1.4;
  font-size: 1.3rem;
  color: #999999;
  margin: 0;
}
/* ==========================================================================
   Component: Panel
 ============================================================================ */
.am-panel {
  margin-bottom: 20px;
  background-color: #fff;
  border: 1px solid transparent;
  border-radius: 0;
  -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
          box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
}
/* Panel header */
.am-panel-hd {
  padding: 0.6rem 1.25rem;
  border-bottom: 1px solid transparent;
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}
/* Panel content */
.am-panel-bd {
  padding: 1.25rem;
}
.am-panel-title {
  margin: 0;
  font-size: 100%;
  color: inherit;
}
.am-panel-title > a {
  color: inherit;
}
/* Panel footer */
.am-panel-footer {
  padding: 0.6rem 1.25rem;
  background-color: #f5f5f5;
  border-top: 1px solid #ddd;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.am-panel-default {
  border-color: #ddd;
}
.am-panel-default > .am-panel-hd {
  color: #444;
  background-color: #f5f5f5;
  border-color: #ddd;
}
.am-panel-default > .am-panel-hd + .am-panel-collapse > .am-panel-bd {
  border-top-color: #ddd;
}
.am-panel-default > .am-panel-footer + .am-panel-collapse > .am-panel-bd {
  border-bottom-color: #ddd;
}
.am-panel-primary {
  border-color: #10a0ea;
}
.am-panel-primary > .am-panel-hd {
  color: #fff;
  background-color: #0e90d2;
  border-color: #10a0ea;
}
.am-panel-primary > .am-panel-hd + .am-panel-collapse > .am-panel-bd {
  border-top-color: #10a0ea;
}
.am-panel-primary > .am-panel-footer + .am-panel-collapse > .am-panel-bd {
  border-bottom-color: #10a0ea;
}
.am-panel-secondary {
  border-color: #caebfb;
}
.am-panel-secondary > .am-panel-hd {
  color: #14a6ef;
  background-color: rgba(59, 180, 242, 0.15);
  border-color: #caebfb;
}
.am-panel-secondary > .am-panel-hd + .am-panel-collapse > .am-panel-bd {
  border-top-color: #caebfb;
}
.am-panel-secondary > .am-panel-footer + .am-panel-collapse > .am-panel-bd {
  border-bottom-color: #caebfb;
}
.am-panel-success {
  border-color: #c9e7c9;
}
.am-panel-success > .am-panel-hd {
  color: #5eb95e;
  background-color: rgba(94, 185, 94, 0.15);
  border-color: #c9e7c9;
}
.am-panel-success > .am-panel-hd + .am-panel-collapse > .am-panel-bd {
  border-top-color: #c9e7c9;
}
.am-panel-success > .am-panel-footer + .am-panel-collapse > .am-panel-bd {
  border-bottom-color: #c9e7c9;
}
.am-panel-warning {
  border-color: #fbd0ae;
}
.am-panel-warning > .am-panel-hd {
  color: #F37B1D;
  background-color: rgba(243, 123, 29, 0.15);
  border-color: #fbd0ae;
}
.am-panel-warning > .am-panel-hd + .am-panel-collapse > .am-panel-bd {
  border-top-color: #fbd0ae;
}
.am-panel-warning > .am-panel-footer + .am-panel-collapse > .am-panel-bd {
  border-bottom-color: #fbd0ae;
}
.am-panel-danger {
  border-color: #f5cecd;
}
.am-panel-danger > .am-panel-hd {
  color: #dd514c;
  background-color: rgba(221, 81, 76, 0.15);
  border-color: #f5cecd;
}
.am-panel-danger > .am-panel-hd + .am-panel-collapse > .am-panel-bd {
  border-top-color: #f5cecd;
}
.am-panel-danger > .am-panel-footer + .am-panel-collapse > .am-panel-bd {
  border-bottom-color: #f5cecd;
}
.am-panel > .am-table {
  margin-bottom: 0;
}
.am-panel > .am-table:first-child {
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}
.am-panel > .am-table:first-child > thead:first-child > tr:first-child td:first-child,
.am-panel > .am-table:first-child > tbody:first-child > tr:first-child td:first-child,
.am-panel > .am-table:first-child > thead:first-child > tr:first-child th:first-child,
.am-panel > .am-table:first-child > tbody:first-child > tr:first-child th:first-child {
  border-top-left-radius: 0;
}
.am-panel > .am-table:first-child > thead:first-child > tr:first-child td:last-child,
.am-panel > .am-table:first-child > tbody:first-child > tr:first-child td:last-child,
.am-panel > .am-table:first-child > thead:first-child > tr:first-child th:last-child,
.am-panel > .am-table:first-child > tbody:first-child > tr:first-child th:last-child {
  border-top-right-radius: 0;
}
.am-panel > .am-table:last-child {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.am-panel > .am-table:last-child > tbody:last-child > tr:last-child td:first-child,
.am-panel > .am-table:last-child > tfoot:last-child > tr:last-child td:first-child,
.am-panel > .am-table:last-child > tbody:last-child > tr:last-child th:first-child,
.am-panel > .am-table:last-child > tfoot:last-child > tr:last-child th:first-child {
  border-bottom-left-radius: 0;
}
.am-panel > .am-table:last-child > tbody:last-child > tr:last-child td:last-child,
.am-panel > .am-table:last-child > tfoot:last-child > tr:last-child td:last-child,
.am-panel > .am-table:last-child > tbody:last-child > tr:last-child th:last-child,
.am-panel > .am-table:last-child > tfoot:last-child > tr:last-child th:last-child {
  border-bottom-right-radius: 0;
}
.am-panel > .am-panel-bd + .am-table {
  border-top: 1px solid #ddd;
}
.am-panel > .am-table > tbody:first-child > tr:first-child th,
.am-panel > .am-table > tbody:first-child > tr:first-child td {
  border-top: 0;
}
.am-panel > .am-table-bd {
  border: 0;
}
.am-panel > .am-table-bd > thead > tr > th:first-child,
.am-panel > .am-table-bd > tbody > tr > th:first-child,
.am-panel > .am-table-bd > tfoot > tr > th:first-child,
.am-panel > .am-table-bd > thead > tr > td:first-child,
.am-panel > .am-table-bd > tbody > tr > td:first-child,
.am-panel > .am-table-bd > tfoot > tr > td:first-child {
  border-left: 0;
}
.am-panel > .am-table-bd > thead > tr > th:last-child,
.am-panel > .am-table-bd > tbody > tr > th:last-child,
.am-panel > .am-table-bd > tfoot > tr > th:last-child,
.am-panel > .am-table-bd > thead > tr > td:last-child,
.am-panel > .am-table-bd > tbody > tr > td:last-child,
.am-panel > .am-table-bd > tfoot > tr > td:last-child {
  border-right: 0;
}
.am-panel > .am-table-bd > thead > tr:first-child > td,
.am-panel > .am-table-bd > tbody > tr:first-child > td,
.am-panel > .am-table-bd > thead > tr:first-child > th,
.am-panel > .am-table-bd > tbody > tr:first-child > th {
  border-bottom: 0;
}
.am-panel > .am-table-bd > tbody > tr:last-child > td,
.am-panel > .am-table-bd > tfoot > tr:last-child > td,
.am-panel > .am-table-bd > tbody > tr:last-child > th,
.am-panel > .am-table-bd > tfoot > tr:last-child > th {
  border-bottom: 0;
}
/* Wrap list */
.am-panel > .am-list {
  margin: 0;
}
.am-panel > .am-list > li > a {
  padding-left: 1rem;
  padding-right: 1rem;
}
.am-panel > .am-list-static li {
  padding-left: 1rem;
  padding-right: 1rem;
}
/* Panel group */
.am-panel-group {
  margin-bottom: 2rem;
}
.am-panel-group .am-panel {
  margin-bottom: 0;
  border-radius: 0;
}
.am-panel-group .am-panel + .am-panel {
  margin-top: 6px;
}
.am-panel-group .am-panel-hd {
  border-bottom: 0;
}
.am-panel-group .am-panel-hd + .am-panel-collapse .am-panel-bd {
  border-top: 1px solid #ddd;
}
.am-panel-group .am-panel-footer {
  border-top: 0;
}
.am-panel-group .am-panel-footer + .am-panel-collapse .am-panel-bd {
  border-bottom: 1px solid #ddd;
}
/* ==========================================================================
   Component: Progress
 ============================================================================ */
/* Progress bar animation */
@-webkit-keyframes progress-bar-stripes {
  from {
    background-position: 36px 0;
  }
  to {
    background-position: 0 0;
  }
}
@keyframes progress-bar-stripes {
  from {
    background-position: 36px 0;
  }
  to {
    background-position: 0 0;
  }
}
/*  Progress container */
.am-progress {
  overflow: hidden;
  height: 2rem;
  margin-bottom: 2rem;
  background-color: #f5f5f5;
  border-radius: 0;
  -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
          box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}
/* Progress bar */
.am-progress-bar {
  float: left;
  width: 0;
  height: 100%;
  font-size: 1.2rem;
  line-height: 2rem;
  color: #fff;
  text-align: center;
  background-color: #0e90d2;
  -webkit-box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.15);
          box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  -webkit-transition: width .6s ease;
  transition: width .6s ease;
}
.am-progress-striped .am-progress-bar {
  background-image: -webkit-gradient(linear, 0 100%, 100% 0, color-stop(0.25, rgba(255, 255, 255, 0.15)), color-stop(0.25, transparent), color-stop(0.5, transparent), color-stop(0.5, rgba(255, 255, 255, 0.15)), color-stop(0.75, rgba(255, 255, 255, 0.15)), color-stop(0.75, transparent), to(transparent));
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  -webkit-background-size: 36px 36px;
          background-size: 36px 36px;
}
.am-progress.am-active .am-progress-bar {
  -webkit-animation: progress-bar-stripes 2s linear infinite;
          animation: progress-bar-stripes 2s linear infinite;
}
.am-progress-bar[aria-valuenow="1"],
.am-progress-bar[aria-valuenow="2"] {
  min-width: 30px;
}
.am-progress-bar[aria-valuenow="0"] {
  color: #999999;
  min-width: 30px;
  background: none;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.am-progress-bar-secondary {
  background-color: #3bb4f2;
}
.am-progress-striped .am-progress-bar-secondary {
  background-image: -webkit-gradient(linear, 0 100%, 100% 0, color-stop(0.25, rgba(255, 255, 255, 0.15)), color-stop(0.25, transparent), color-stop(0.5, transparent), color-stop(0.5, rgba(255, 255, 255, 0.15)), color-stop(0.75, rgba(255, 255, 255, 0.15)), color-stop(0.75, transparent), to(transparent));
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}
.am-progress-bar-success {
  background-color: #5eb95e;
}
.am-progress-striped .am-progress-bar-success {
  background-image: -webkit-gradient(linear, 0 100%, 100% 0, color-stop(0.25, rgba(255, 255, 255, 0.15)), color-stop(0.25, transparent), color-stop(0.5, transparent), color-stop(0.5, rgba(255, 255, 255, 0.15)), color-stop(0.75, rgba(255, 255, 255, 0.15)), color-stop(0.75, transparent), to(transparent));
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}
.am-progress-bar-warning {
  background-color: #F37B1D;
}
.am-progress-striped .am-progress-bar-warning {
  background-image: -webkit-gradient(linear, 0 100%, 100% 0, color-stop(0.25, rgba(255, 255, 255, 0.15)), color-stop(0.25, transparent), color-stop(0.5, transparent), color-stop(0.5, rgba(255, 255, 255, 0.15)), color-stop(0.75, rgba(255, 255, 255, 0.15)), color-stop(0.75, transparent), to(transparent));
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}
.am-progress-bar-danger {
  background-color: #dd514c;
}
.am-progress-striped .am-progress-bar-danger {
  background-image: -webkit-gradient(linear, 0 100%, 100% 0, color-stop(0.25, rgba(255, 255, 255, 0.15)), color-stop(0.25, transparent), color-stop(0.5, transparent), color-stop(0.5, rgba(255, 255, 255, 0.15)), color-stop(0.75, rgba(255, 255, 255, 0.15)), color-stop(0.75, transparent), to(transparent));
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}
.am-progress-xs {
  height: 0.6rem;
}
.am-progress-sm {
  height: 1.2rem;
}
/* ==========================================================================
   Component: Thumbnail
 ============================================================================ */
.am-thumbnail {
  display: block;
  padding: 2px;
  margin-bottom: 2rem;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 0;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}
.am-thumbnail > img,
.am-thumbnail a > img {
  margin-left: auto;
  margin-right: auto;
  display: block;
}
.am-thumbnail a.am-thumbnail:hover,
.am-thumbnail a.am-thumbnail:focus,
.am-thumbnail a.am-thumbnail.active {
  border-color: #0e90d2;
  background-color: #fff;
}
img.am-thumbnail,
.am-thumbnail > img,
.am-thumbnail a > img {
  max-width: 100%;
  height: auto;
}
/* Image caption */
.am-thumbnail-caption {
  margin: 0;
  padding: 0.8rem;
  color: #333;
  font-weight: normal;
}
.am-thumbnail-caption *:last-child {
  margin-bottom: 0;
}
.am-thumbnails {
  margin-left: -0.5rem;
  margin-right: -0.5rem;
}
.am-thumbnails > li {
  padding: 0 .5rem 1rem .5rem;
}
/* ==========================================================================
   Component: Utility
 ============================================================================ */
.am-scrollable-horizontal {
  width: 100%;
  overflow-y: hidden;
  overflow-x: auto;
  -ms-overflow-style: -ms-autohiding-scrollbar;
  -webkit-overflow-scrolling: touch;
}
.am-scrollable-vertical {
  height: 240px;
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch;
  resize: vertical;
}
/* Border-radius*/
.am-square {
  border-radius: 0;
}
.am-radius {
  border-radius: 2px;
}
.am-round {
  border-radius: 1000px;
}
.am-circle {
  border-radius: 50%;
}
/* Float blocks*/
.am-cf:before,
.am-cf:after {
  content: " ";
  display: table;
}
.am-cf:after {
  clear: both;
}
.am-fl {
  float: left;
}
.am-fr {
  float: right;
}
.am-nbfc {
  overflow: hidden;
}
.am-center {
  display: block;
  margin-left: auto;
  margin-right: auto;
}
/* Display */
.am-block {
  display: block !important;
}
.am-inline {
  display: inline !important;
}
.am-inline-block {
  display: inline-block !important;
}
.am-hide {
  display: none !important;
  visibility: hidden !important;
}
/*
* Remove whitespace between child elements when using `inline-block`
*/
.am-vertical-align {
  font-size: 0.001px;
}
/*
* The `@{ns}vertical-align` container needs a specific height
*/
.am-vertical-align:before {
  content: '';
  display: inline-block;
  height: 100%;
  vertical-align: middle;
}
/*
* Sub-object which can have any height
* 1. Reset whitespace hack
*/
.am-vertical-align-middle,
.am-vertical-align-bottom {
  display: inline-block;
  font-size: 1.6rem;
  /* 1 */
  max-width: 100%;
}
.am-vertical-align-middle {
  vertical-align: middle;
}
.am-vertical-align-bottom {
  vertical-align: bottom;
}
.am-responsive-width {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  max-width: 100%;
  height: auto;
}
/* Margin helpers */
.am-margin {
  margin: 1.6rem;
}
.am-margin-0 {
  margin: 0!important;
}
.am-margin-xs {
  margin: 0.5rem;
}
.am-margin-sm {
  margin: 1rem;
}
.am-margin-lg {
  margin: 2.4rem;
}
.am-margin-xl {
  margin: 3.2rem;
}
.am-margin-horizontal {
  margin-left: 1.6rem;
  margin-right: 1.6rem;
}
.am-margin-horizontal-0 {
  margin-left: 0!important;
  margin-right: 0!important;
}
.am-margin-horizontal-xs {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}
.am-margin-horizontal-sm {
  margin-left: 1rem;
  margin-right: 1rem;
}
.am-margin-horizontal-lg {
  margin-left: 2.4rem;
  margin-right: 2.4rem;
}
.am-margin-horizontal-xl {
  margin-left: 3.2rem;
  margin-right: 3.2rem;
}
.am-margin-vertical {
  margin-top: 1.6rem;
  margin-bottom: 1.6rem;
}
.am-margin-vertical-0 {
  margin-top: 0!important;
  margin-bottom: 0!important;
}
.am-margin-vertical-xs {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}
.am-margin-vertical-sm {
  margin-top: 1rem;
  margin-bottom: 1rem;
}
.am-margin-vertical-lg {
  margin-top: 2.4rem;
  margin-bottom: 2.4rem;
}
.am-margin-vertical-xl {
  margin-top: 3.2rem;
  margin-bottom: 3.2rem;
}
.am-margin-top {
  margin-top: 1.6rem;
}
.am-margin-top-0 {
  margin-top: 0!important;
}
.am-margin-top-xs {
  margin-top: 0.5rem;
}
.am-margin-top-sm {
  margin-top: 1rem;
}
.am-margin-top-lg {
  margin-top: 2.4rem;
}
.am-margin-top-xl {
  margin-top: 3.2rem;
}
.am-margin-bottom {
  margin-bottom: 1.6rem;
}
.am-margin-bottom-0 {
  margin-bottom: 0!important;
}
.am-margin-bottom-xs {
  margin-bottom: 0.5rem;
}
.am-margin-bottom-sm {
  margin-bottom: 1rem;
}
.am-margin-bottom-lg {
  margin-bottom: 2.4rem;
}
.am-margin-bottom-xl {
  margin-bottom: 3.2rem;
}
.am-margin-left {
  margin-left: 1.6rem;
}
.am-margin-left-0 {
  margin-left: 0!important;
}
.am-margin-left-xs {
  margin-left: 0.5rem;
}
.am-margin-left-sm {
  margin-left: 1rem;
}
.am-margin-left-lg {
  margin-left: 2.4rem;
}
.am-margin-left-xl {
  margin-left: 3.2rem;
}
.am-margin-right {
  margin-right: 1.6rem;
}
.am-margin-right-0 {
  margin-right: 0!important;
}
.am-margin-right-xs {
  margin-right: 0.5rem;
}
.am-margin-right-sm {
  margin-right: 1rem;
}
.am-margin-right-lg {
  margin-right: 2.4rem;
}
.am-margin-right-xl {
  margin-right: 3.2rem;
}
/* Padding helpers */
.am-padding {
  padding: 1.6rem;
}
.am-padding-0 {
  padding: 0!important;
}
.am-padding-xs {
  padding: 0.5rem;
}
.am-padding-sm {
  padding: 1rem;
}
.am-padding-lg {
  padding: 2.4rem;
}
.am-padding-xl {
  padding: 3.2rem;
}
.am-padding-horizontal {
  padding-left: 1.6rem;
  padding-right: 1.6rem;
}
.am-padding-horizontal-0 {
  padding-left: 0!important;
  padding-right: 0!important;
}
.am-padding-horizontal-xs {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.am-padding-horizontal-sm {
  padding-left: 1rem;
  padding-right: 1rem;
}
.am-padding-horizontal-lg {
  padding-left: 2.4rem;
  padding-right: 2.4rem;
}
.am-padding-horizontal-xl {
  padding-left: 3.2rem;
  padding-right: 3.2rem;
}
.am-padding-vertical {
  padding-top: 1.6rem;
  padding-bottom: 1.6rem;
}
.am-padding-vertical-0 {
  padding-top: 0!important;
  padding-bottom: 0!important;
}
.am-padding-vertical-xs {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.am-padding-vertical-sm {
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.am-padding-vertical-lg {
  padding-top: 2.4rem;
  padding-bottom: 2.4rem;
}
.am-padding-vertical-xl {
  padding-top: 3.2rem;
  padding-bottom: 3.2rem;
}
.am-padding-top {
  padding-top: 1.6rem;
}
.am-padding-top-0 {
  padding-top: 0!important;
}
.am-padding-top-xs {
  padding-top: 0.5rem;
}
.am-padding-top-sm {
  padding-top: 1rem;
}
.am-padding-top-lg {
  padding-top: 2.4rem;
}
.am-padding-top-xl {
  padding-top: 3.2rem;
}
.am-padding-bottom {
  padding-bottom: 1.6rem;
}
.am-padding-bottom-0 {
  padding-bottom: 0!important;
}
.am-padding-bottom-xs {
  padding-bottom: 0.5rem;
}
.am-padding-bottom-sm {
  padding-bottom: 1rem;
}
.am-padding-bottom-lg {
  padding-bottom: 2.4rem;
}
.am-padding-bottom-xl {
  padding-bottom: 3.2rem;
}
.am-padding-left {
  padding-left: 1.6rem;
}
.am-padding-left-0 {
  padding-left: 0!important;
}
.am-padding-left-xs {
  padding-left: 0.5rem;
}
.am-padding-left-sm {
  padding-left: 1rem;
}
.am-padding-left-lg {
  padding-left: 2.4rem;
}
.am-padding-left-xl {
  padding-left: 3.2rem;
}
.am-padding-right {
  padding-right: 1.6rem;
}
.am-padding-right-0 {
  padding-right: 0!important;
}
.am-padding-right-xs {
  padding-right: 0.5rem;
}
.am-padding-right-sm {
  padding-right: 1rem;
}
.am-padding-right-lg {
  padding-right: 2.4rem;
}
.am-padding-right-xl {
  padding-right: 3.2rem;
}
/* small displays */
@media only screen {
  .am-show-sm-only,
  .am-show-sm-up,
  .am-show-sm,
  .am-show-sm-down,
  .am-hide-md-only,
  .am-hide-md-up,
  .am-hide-md,
  .am-show-md-down,
  .am-hide-lg-only,
  .am-hide-lg-up,
  .am-hide-lg,
  .am-show-lg-down {
    display: inherit !important;
  }
  .am-hide-sm-only,
  .am-hide-sm-up,
  .am-hide-sm,
  .am-hide-sm-down,
  .am-show-md-only,
  .am-show-md-up,
  .am-show-md,
  .am-hide-md-down,
  .am-show-lg-only,
  .am-show-lg-up,
  .am-show-lg,
  .am-hide-lg-down {
    display: none !important;
  }
  /* table */
  table.am-show-sm-only,
  table.am-show-sm-up,
  table.am-show-sm,
  table.am-show-sm-down,
  table.am-hide-md-only,
  table.am-hide-md-up,
  table.am-hide-md,
  table.am-show-md-down,
  table.am-hide-lg-only,
  table.am-hide-lg-up,
  table.am-hide-lg,
  table.am-show-lg-down {
    display: table !important;
  }
  thead.am-show-sm-only,
  thead.am-show-sm-up,
  thead.am-show-sm,
  thead.am-show-sm-down,
  thead.am-hide-md-only,
  thead.am-hide-md-up,
  thead.am-hide-md,
  thead.am-show-md-down,
  thead.am-hide-lg-only,
  thead.am-hide-lg-up,
  thead.am-hide-lg,
  thead.am-show-lg-down {
    display: table-header-group !important;
  }
  tbody.am-show-sm-only,
  tbody.am-show-sm-up,
  tbody.am-show-sm,
  tbody.am-show-sm-down,
  tbody.am-hide-md-only,
  tbody.am-hide-md-up,
  tbody.am-hide-md,
  tbody.am-show-md-down,
  tbody.am-hide-lg-only,
  tbody.am-hide-lg-up,
  tbody.am-hide-lg,
  tbody.am-show-lg-down {
    display: table-row-group !important;
  }
  tr.am-show-sm-only,
  tr.am-show-sm-up,
  tr.am-show-sm,
  tr.am-show-sm-down,
  tr.am-hide-md-only,
  tr.am-hide-md-up,
  tr.am-hide-md,
  tr.am-show-md-down,
  tr.am-hide-lg-only,
  tr.am-hide-lg-up,
  tr.am-hide-lg,
  tr.am-show-lg-down {
    display: table-row !important;
  }
  th.am-show-sm-only,
  td.am-show-sm-only,
  th.am-show-sm-up,
  td.am-show-sm-up,
  th.am-show-sm,
  td.am-show-sm,
  th.am-show-sm-down,
  td.am-show-sm-down,
  th.am-hide-md-only,
  td.am-hide-md-only,
  th.am-hide-md-up,
  td.am-hide-md-up,
  th.am-hide-md,
  td.am-hide-md,
  th.am-show-md-down,
  td.am-show-md-down,
  th.am-hide-lg-only,
  td.am-hide-lg-only,
  th.am-hide-lg-up,
  td.am-hide-lg-up,
  th.am-hide-lg,
  td.am-hide-lg,
  th.am-show-lg-down,
  td.am-show-lg-down {
    display: table-cell !important;
  }
}
/* medium displays */
@media only screen and (min-width:641px) {
  .am-hide-sm-only,
  .am-show-sm-up,
  .am-hide-sm,
  .am-hide-sm-down,
  .am-show-md-only,
  .am-show-md-up,
  .am-show-md,
  .am-show-md-down,
  .am-hide-lg-only,
  .am-hide-lg-up,
  .am-hide-lg,
  .am-show-lg-down {
    display: inherit !important;
  }
  .am-show-sm-only,
  .am-hide-sm-up,
  .am-show-sm,
  .am-show-sm-down,
  .am-hide-md-only,
  .am-hide-md-up,
  .am-hide-md,
  .am-hide-md-down,
  .am-show-lg-only,
  .am-show-lg-up,
  .am-show-lg,
  .am-hide-lg-down {
    display: none !important;
  }
  table.am-hide-sm-only,
  table.am-show-sm-up,
  table.am-hide-sm,
  table.am-hide-sm-down,
  table.am-show-md-only,
  table.am-show-md-up,
  table.am-show-md,
  table.am-show-md-down,
  table.am-hide-lg-only,
  table.am-hide-lg-up,
  table.am-hide-lg,
  table.am-show-lg-down {
    display: table !important;
  }
  thead.am-hide-sm-only,
  thead.am-show-sm-up,
  thead.am-hide-sm,
  thead.am-hide-sm-down,
  thead.am-show-md-only,
  thead.am-show-md-up,
  thead.am-show-md,
  thead.am-show-md-down,
  thead.am-hide-lg-only,
  thead.am-hide-lg-up,
  thead.am-hide-lg,
  thead.am-show-lg-down {
    display: table-header-group !important;
  }
  tbody.am-hide-sm-only,
  tbody.am-show-sm-up,
  tbody.am-hide-sm,
  tbody.am-hide-sm-down,
  tbody.am-show-md-only,
  tbody.am-show-md-up,
  tbody.am-show-md,
  tbody.am-show-md-down,
  tbody.am-hide-lg-only,
  tbody.am-hide-lg-up,
  tbody.am-hide-lg,
  tbody.am-show-lg-down {
    display: table-row-group !important;
  }
  tr.am-hide-sm-only,
  tr.am-show-sm-up,
  tr.am-hide-sm,
  tr.am-hide-sm-down,
  tr.am-show-md-only,
  tr.am-show-md-up,
  tr.am-show-md,
  tr.am-show-md-down,
  tr.am-hide-lg-only,
  tr.am-hide-lg-up,
  tr.am-hide-lg,
  tr.am-show-lg-down {
    display: table-row !important;
  }
  th.am-hide-sm-only,
  td.am-hide-sm-only,
  th.am-show-sm-up,
  td.am-show-sm-up,
  th.am-hide-sm,
  td.am-hide-sm,
  th.am-hide-sm-down,
  td.am-hide-sm-down,
  th.am-show-md-only,
  td.am-show-md-only,
  th.am-show-md-up,
  td.am-show-md-up,
  th.am-show-md,
  td.am-show-md,
  th.am-show-md-down,
  td.am-show-md-down,
  th.am-hide-lg-only,
  td.am-hide-lg-only,
  th.am-hide-lg-up,
  td.am-hide-lg-up,
  th.am-hide-lg,
  td.am-hide-lg,
  th.am-show-lg-down,
  td.am-show-lg-down {
    display: table-cell !important;
  }
}
/* large displays */
@media only screen and (min-width:1025px) {
  .am-hide-sm-only,
  .am-show-sm-up,
  .am-hide-sm,
  .am-hide-sm-down,
  .am-hide-md-only,
  .am-show-md-up,
  .am-hide-md,
  .am-hide-md-down,
  .am-show-lg-only,
  .am-show-lg-up,
  .am-show-lg,
  .am-show-lg-down {
    display: inherit !important;
  }
  .am-show-sm-only,
  .am-hide-sm-up,
  .am-show-sm,
  .am-show-sm-down,
  .am-show-md-only,
  .am-hide-md-up,
  .am-show-md,
  .am-show-md-down,
  .am-hide-lg-only,
  .am-hide-lg-up,
  .am-hide-lg,
  .am-hide-lg-down {
    display: none !important;
  }
  table.am-hide-sm-only,
  table.am-show-sm-up,
  table.am-hide-sm,
  table.am-hide-sm-down,
  table.am-hide-md-only,
  table.am-show-md-up,
  table.am-hide-md,
  table.am-hide-md-down,
  table.am-show-lg-only,
  table.am-show-lg-up,
  table.am-show-lg,
  table.am-show-lg-down {
    display: table !important;
  }
  thead.am-hide-sm-only,
  thead.am-show-sm-up,
  thead.am-hide-sm,
  thead.am-hide-sm-down,
  thead.am-hide-md-only,
  thead.am-show-md-up,
  thead.am-hide-md,
  thead.am-hide-md-down,
  thead.am-show-lg-only,
  thead.am-show-lg-up,
  thead.am-show-lg,
  thead.am-show-lg-down {
    display: table-header-group !important;
  }
  tbody.am-hide-sm-only,
  tbody.am-show-sm-up,
  tbody.am-hide-sm,
  tbody.am-hide-sm-down,
  tbody.am-hide-md-only,
  tbody.am-show-md-up,
  tbody.am-hide-md,
  tbody.am-hide-md-down,
  tbody.am-show-lg-only,
  tbody.am-show-lg-up,
  tbody.am-show-lg,
  tbody.am-show-lg-down {
    display: table-row-group !important;
  }
  tr.am-hide-sm-only,
  tr.am-show-sm-up,
  tr.am-hide-sm,
  tr.am-hide-sm-down,
  tr.am-hide-md-only,
  tr.am-show-md-up,
  tr.am-hide-md,
  tr.am-hide-md-down,
  tr.am-show-lg-only,
  tr.am-show-lg-up,
  tr.am-show-lg,
  tr.am-show-lg-down {
    display: table-row !important;
  }
  th.am-hide-sm-only,
  td.am-hide-sm-only,
  th.am-show-sm-up,
  td.am-show-sm-up,
  th.am-hide-sm,
  td.am-hide-sm,
  th.am-hide-sm-down,
  td.am-hide-sm-down,
  th.am-hide-md-only,
  td.am-hide-md-only,
  th.am-show-md-up,
  td.am-show-md-up,
  th.am-hide-md,
  td.am-hide-md,
  th.am-hide-md-down,
  td.am-hide-md-down,
  th.am-show-lg-only,
  td.am-show-lg-only,
  th.am-show-lg-up,
  td.am-show-lg-up,
  th.am-show-lg,
  td.am-show-lg,
  th.am-show-lg-down,
  td.am-show-lg-down {
    display: table-cell !important;
  }
}
@media only screen and (orientation: landscape) {
  .am-show-landscape,
  .am-hide-portrait {
    display: inherit !important;
  }
  .am-hide-landscape,
  .am-show-portrait {
    display: none !important;
  }
}
@media only screen and (orientation: portrait) {
  .am-show-portrait,
  .am-hide-landscape {
    display: inherit !important;
  }
  .am-hide-portrait,
  .am-show-landscape {
    display: none !important;
  }
}
.am-sans-serif {
  font-family: "Segoe UI", "Lucida Grande", Helvetica, Arial, "Microsoft YaHei", FreeSans, Arimo, "Droid Sans", "wenquanyi micro hei", "Hiragino Sans GB", "Hiragino Sans GB W3", "FontAwesome", sans-serif;
}
.am-serif {
  font-family: Georgia, "Times New Roman", Times, SimSun, "FontAwesome", serif;
}
.am-kai {
  font-family: Georgia, "Times New Roman", Times, Kai, "Kaiti SC", KaiTi, BiauKai, "FontAwesome", serif;
}
.am-monospace {
  font-family: Monaco, Menlo, Consolas, "Courier New", "FontAwesome", monospace;
}
.am-text-primary {
  color: #0e90d2;
}
.am-text-secondary {
  color: #3bb4f2;
}
.am-text-success {
  color: #5eb95e;
}
.am-text-warning {
  color: #F37B1D;
}
.am-text-danger {
  color: #dd514c;
}
.am-link-muted {
  color: #666;
}
.am-link-muted a {
  color: #666;
}
.am-link-muted:hover,
.am-link-muted a:hover {
  color: #555;
}
.am-text-default {
  font-size: 1.6rem;
}
/*
.@{ns}text-xxs {
  font-size: @font-size-xxs;
}
*/
.am-text-xs {
  font-size: 1.2rem;
}
.am-text-sm {
  font-size: 1.4rem;
}
.am-text-lg {
  font-size: 1.8rem;
}
.am-text-xl {
  font-size: 2.4rem;
}
.am-text-xxl {
  font-size: 3.2rem;
}
.am-text-xxxl {
  font-size: 4.2rem;
}
.am-ellipsis,
.am-text-truncate {
  word-wrap: normal;
  /* for IE */
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.am-text-break {
  word-wrap: break-word;
  -webkit-hyphens: auto;
  -ms-hyphens: auto;
  -moz-hyphens: auto;
  hyphens: auto;
}
.am-text-nowrap {
  white-space: nowrap;
}
[class*='am-align-'] {
  margin-bottom: 1rem;
}
.am-align-left {
  margin-right: 1rem;
  float: left;
}
.am-align-right {
  margin-left: 1rem;
  float: right;
}
/** Only display content to screen readers
 * See: http://a11yproject.com/posts/how-to-hide-content/
 */
.am-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}
/* Text Image Replacement */
.am-text-ir {
  font: 0/0 a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0;
}
/* Text align */
@media only screen {
  .am-text-left {
    text-align: left !important;
  }
  .am-text-right {
    text-align: right !important;
  }
  .am-text-center {
    text-align: center !important;
  }
  .am-text-justify {
    text-align: justify !important;
  }
}
@media only screen and (max-width: 640px) {
  .am-sm-only-text-left {
    text-align: left !important;
  }
  .am-sm-only-text-right {
    text-align: right !important;
  }
  .am-sm-only-text-center {
    text-align: center !important;
  }
  .am-sm-only-text-justify {
    text-align: justify !important;
  }
}
@media only screen and (min-width:641px) and (max-width:1024px) {
  .am-md-only-text-left {
    text-align: left !important;
  }
  .am-md-only-text-right {
    text-align: right !important;
  }
  .am-md-only-text-center {
    text-align: center !important;
  }
  .am-md-only-text-justify {
    text-align: justify !important;
  }
}
@media only screen and (min-width:641px) {
  .am-md-text-left {
    text-align: left !important;
  }
  .am-md-text-right {
    text-align: right !important;
  }
  .am-md-text-center {
    text-align: center !important;
  }
  .am-md-text-justify {
    text-align: justify !important;
  }
}
@media only screen and (min-width:1025px) {
  .am-lg-text-left {
    text-align: left !important;
  }
  .am-lg-text-right {
    text-align: right !important;
  }
  .am-lg-text-center {
    text-align: center !important;
  }
  .am-lg-text-justify {
    text-align: justify !important;
  }
}
.am-text-top {
  vertical-align: top !important;
}
.am-text-middle {
  vertical-align: middle !important;
}
.am-text-bottom {
  vertical-align: bottom !important;
}
.am-angle {
  position: absolute;
}
.am-angle:before,
.am-angle:after {
  position: absolute;
  display: block;
  content: "";
  width: 0;
  height: 0;
  border: 8px dashed transparent;
  z-index: 1;
}
.am-angle-up {
  top: 0;
}
.am-angle-up:before,
.am-angle-up:after {
  border-bottom-style: solid;
  border-width: 0 8px 8px;
}
.am-angle-up:before {
  border-bottom-color: #ddd;
  bottom: 0;
}
.am-angle-up:after {
  border-bottom-color: #fff;
  bottom: -1px;
}
.am-angle-down {
  bottom: -9px;
}
.am-angle-down:before,
.am-angle-down:after {
  border-top-style: solid;
  border-width: 8px 8px 0;
}
.am-angle-down:before {
  border-top-color: #ddd;
  bottom: 0;
}
.am-angle-down:after {
  border-top-color: #fff;
  bottom: 1px;
}
.am-angle-left {
  left: -9px;
}
.am-angle-left:before,
.am-angle-left:after {
  border-right-style: solid;
  border-width: 8px 8px 8px 0;
}
.am-angle-left:before {
  border-right-color: #ddd;
  left: 0;
}
.am-angle-left:after {
  border-right-color: #fff;
  left: 1px;
}
.am-angle-right {
  right: 0;
}
.am-angle-right:before,
.am-angle-right:after {
  border-left-style: solid;
  border-width: 8px 0 8px 8px;
}
.am-angle-right:before {
  border-left-color: #ddd;
  left: 0;
}
.am-angle-right:after {
  border-left-color: #fff;
  left: -1px;
}
/* ==========================================================================
   Component: Alert Plugin
 ============================================================================ */
.am-alert {
  margin-bottom: 1em;
  padding: 0.625em;
  background: #0e90d2;
  color: #fff;
  border: 1px solid #0c7cb5;
  border-radius: 0;
}
.am-alert a {
  color: #fff;
}
.am-alert h1,
.am-alert h2,
.am-alert h3,
.am-alert h4,
.am-alert h5,
.am-alert h6 {
  color: inherit;
}
.am-alert .am-close {
  opacity: .4;
}
.am-alert .am-close:hover {
  opacity: .6;
}
/* Add margin if adjacent element */
* + .am-alert {
  margin-top: 1em;
}
/* Remove margin from the last-child */
.am-alert > :last-child {
  margin-bottom: 0;
}
/* within am-form-group */
.am-form-group .am-alert {
  margin: 5px 0 0;
  padding: 0.25em 0.625em;
  font-size: 1.3rem;
}
/* Close in alert */
.am-alert > .am-close:first-child {
  float: right;
  height: auto;
  margin: -3px -5px auto auto;
}
/* Remove margin from adjacent element */
.am-alert > .am-close:first-child + * {
  margin-top: 0;
}
.am-alert-secondary {
  background-color: #eeeeee;
  border-color: #dfdfdf;
  color: #555555;
}
.am-alert-success {
  background-color: #5eb95e;
  border-color: #4bad4b;
  color: #fff;
}
.am-alert-warning {
  background-color: #F37B1D;
  border-color: #e56c0c;
  color: #fff;
}
.am-alert-danger {
  background-color: #dd514c;
  border-color: #d83832;
  color: #fff;
}
.am-dropdown {
  position: relative;
  display: inline-block;
}
.am-dropdown-toggle:focus {
  outline: 0;
}
.am-dropdown-content {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1020;
  display: none;
  float: left;
  min-width: 160px;
  padding: 15px;
  margin: 9px 0 0;
  text-align: left;
  line-height: 1.6;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 0;
  -webkit-background-clip: padding-box;
          background-clip: padding-box;
  -webkit-animation-duration: .15s;
          animation-duration: .15s;
}
.am-dropdown-content:before,
.am-dropdown-content:after {
  position: absolute;
  display: block;
  content: "";
  width: 0;
  height: 0;
  border: 8px dashed transparent;
  z-index: 1;
}
.am-dropdown-content:before,
.am-dropdown-content:after {
  border-bottom-style: solid;
  border-width: 0 8px 8px;
}
.am-dropdown-content:before {
  border-bottom-color: #ddd;
  bottom: 0;
}
.am-dropdown-content:after {
  border-bottom-color: #fff;
  bottom: -1px;
}
.am-dropdown-content:before,
.am-dropdown-content:after {
  left: 10px;
  top: -8px;
  pointer-events: none;
}
.am-dropdown-content:after {
  top: -7px;
}
.am-active > .am-dropdown-content {
  display: block;
}
.am-dropdown-content :first-child {
  margin-top: 0;
}
.am-dropdown-up .am-dropdown-content {
  top: auto;
  bottom: 100%;
  margin: 0 0 9px;
}
.am-dropdown-up .am-dropdown-content:before,
.am-dropdown-up .am-dropdown-content:after {
  border-bottom: none;
  border-top: 8px solid #ddd;
  top: auto;
  bottom: -8px;
}
.am-dropdown-up .am-dropdown-content:after {
  bottom: -7px;
  border-top-color: #fff;
}
.am-dropdown-flip .am-dropdown-content {
  left: auto;
  right: 0;
}
.am-dropdown-flip .am-dropdown-content:before,
.am-dropdown-flip .am-dropdown-content:after {
  left: auto;
  right: 10px;
}
ul.am-dropdown-content {
  list-style: none;
  padding: 5px 0;
}
ul.am-dropdown-content.am-fr {
  right: 0;
  left: auto;
}
ul.am-dropdown-content .am-divider {
  height: 1px;
  margin: 0rem 0;
  overflow: hidden;
  background-color: #e5e5e5;
}
ul.am-dropdown-content > li > a {
  display: block;
  padding: 6px 20px;
  clear: both;
  font-weight: normal;
  color: #333333;
  white-space: nowrap;
}
ul.am-dropdown-content > li > a:hover,
ul.am-dropdown-content > li > a:focus {
  text-decoration: none;
  color: #262626;
  background-color: #f5f5f5;
}
ul.am-dropdown-content > .am-active > a,
ul.am-dropdown-content > .am-active > a:hover,
ul.am-dropdown-content > .am-active > a:focus {
  color: #fff;
  text-decoration: none;
  outline: 0;
  background-color: #0e90d2;
}
ul.am-dropdown-content > .am-disabled > a,
ul.am-dropdown-content > .am-disabled > a:hover,
ul.am-dropdown-content > .am-disabled > a:focus {
  color: #999999;
}
ul.am-dropdown-content > .am-disabled > a:hover,
ul.am-dropdown-content > .am-disabled > a:focus {
  text-decoration: none;
  background-color: transparent;
  background-image: none;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  cursor: not-allowed;
}
.am-dropdown-header {
  display: block;
  padding: 6px 20px;
  font-size: 1.2rem;
  color: #999999;
}
.am-fr > .am-dropdown-content {
  right: 0;
  left: auto;
}
.am-fr > .am-dropdown-content:before {
  right: 10px;
  left: auto;
}
.am-dropdown-animation {
  -webkit-animation: am-dropdown-animation 0.15s ease-out;
          animation: am-dropdown-animation 0.15s ease-out;
}
@-webkit-keyframes am-dropdown-animation {
  0% {
    opacity: 1;
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
  100% {
    opacity: 0;
    -webkit-transform: translateY(-10px);
            transform: translateY(-10px);
  }
}
@keyframes am-dropdown-animation {
  0% {
    opacity: 1;
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
  100% {
    opacity: 0;
    -webkit-transform: translateY(-10px);
            transform: translateY(-10px);
  }
}
/* ==========================================================================
   Component: Flex Slider Plugin
 ============================================================================ */
.am-slider a:hover,
.am-slider a:focus {
  outline: none;
}
.am-slides,
.am-control-nav,
.am-direction-nav {
  margin: 0;
  padding: 0;
  list-style: none;
}
.am-slider {
  margin: 0;
  padding: 0;
}
.am-slider .am-slides:before,
.am-slider .am-slides:after {
  content: " ";
  display: table;
}
.am-slider .am-slides:after {
  clear: both;
}
.am-slider .am-slides > li {
  display: none;
  -webkit-backface-visibility: hidden;
  position: relative;
}
.no-js .am-slider .am-slides > li:first-child {
  display: block;
}
.am-slider .am-slides img {
  width: 100%;
  display: block;
}
.am-pauseplay span {
  text-transform: capitalize;
}
.am-slider {
  position: relative;
}
.am-viewport {
  -webkit-transition: all 1s ease;
  transition: all 1s ease;
}
.am-slider-carousel li {
  margin-right: 5px;
}
.am-control-nav {
  position: absolute;
}
.am-control-nav li {
  display: inline-block;
}
.am-control-thumbs {
  position: static;
  overflow: hidden;
}
.am-control-thumbs img {
  -webkit-transition: all 1s ease;
  transition: all 1s ease;
}
.am-slider-slide .am-slides > li {
  display: none;
  position: relative;
}
@media all and (transform-3d), (-webkit-transform-3d) {
  .am-slider-slide .am-slides > li {
    -webkit-transition: -webkit-transform 0.6s ease-in-out;
    transition: -webkit-transform 0.6s ease-in-out;
    transition: transform 0.6s ease-in-out;
    transition: transform 0.6s ease-in-out, -webkit-transform 0.6s ease-in-out;
    -webkit-backface-visibility: hidden;
            backface-visibility: hidden;
    -webkit-perspective: 1000px;
            perspective: 1000px;
  }
  .am-slider-slide .am-slides > li.next,
  .am-slider-slide .am-slides > li.active.right {
    -webkit-transform: translate3d(100%, 0, 0);
            transform: translate3d(100%, 0, 0);
    left: 0;
  }
  .am-slider-slide .am-slides > li.prev,
  .am-slider-slide .am-slides > li.active.left {
    -webkit-transform: translate3d(-100%, 0, 0);
            transform: translate3d(-100%, 0, 0);
    left: 0;
  }
  .am-slider-slide .am-slides > li.next.left,
  .am-slider-slide .am-slides > li.prev.right,
  .am-slider-slide .am-slides > li.active {
    -webkit-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0);
    left: 0;
  }
}
.am-slider-slide .am-slides > .active,
.am-slider-slide .am-slides > .next,
.am-slider-slide .am-slides > .prev {
  display: block;
}
.am-slider-slide .am-slides > .active {
  left: 0;
}
.am-slider-slide .am-slides > .next,
.am-slider-slide .am-slides > .prev {
  position: absolute;
  top: 0;
  width: 100%;
}
.am-slider-slide .am-slides > .next {
  left: 100%;
}
.am-slider-slide .am-slides > .prev {
  left: -100%;
}
.am-slider-slide .am-slides > .next.left,
.am-slider-slide .am-slides > .prev.right {
  left: 0;
}
.am-slider-slide .am-slides > .active.left {
  left: -100%;
}
.am-slider-slide .am-slides > .active.right {
  left: 100%;
}
/**
  * Slider Theme: Default
  */
.am-slider-default {
  margin: 0 0 20px;
  background-color: #fff;
  border-radius: 2px;
  -webkit-box-shadow: 0 0 2px rgba(0, 0, 0, 0.15);
          box-shadow: 0 0 2px rgba(0, 0, 0, 0.15);
  /* Direction Nav */
  /* Pause/Play */
  /* Control Nav */
}
.am-slider-default .am-viewport {
  max-height: 2000px;
  -webkit-transition: all 1s ease;
  transition: all 1s ease;
}
.loading .am-slider-default .am-viewport {
  max-height: 300px;
}
.am-slider-default .carousel li {
  margin-right: 5px;
}
.am-slider-default .am-direction-nav a {
  position: absolute;
  top: 50%;
  z-index: 10;
  display: block;
  width: 36px;
  height: 36px;
  margin: -18px 0 0;
  overflow: hidden;
  opacity: 0.45;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.65);
  -webkit-transition: all .3s ease;
  transition: all .3s ease;
}
.am-slider-default .am-direction-nav a:before {
  display: inline-block;
  font: normal normal normal 1.6rem/1 "FontAwesome", sans-serif;
  /*font-weight: normal; // 2
  font-style: normal; // 2
  vertical-align: baseline; // 3
  line-height: 1; // 4*/
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-transform: translate(0, 0);
      -ms-transform: translate(0, 0);
          transform: translate(0, 0);
  width: 100%;
  color: #333333;
  content: "\f137";
  font-size: 24px!important;
  text-align: center;
  line-height: 36px!important;
  height: 36px;
}
.am-slider-default .am-direction-nav a.am-next:before {
  content: "\f138";
}
.am-slider-default .am-direction-nav .am-prev {
  left: 10px;
}
.am-slider-default .am-direction-nav .am-next {
  right: 10px;
  text-align: right;
}
.am-slider-default .am-direction-nav .am-disabled {
  opacity: 0!important;
  cursor: default;
}
.am-slider-default:hover .am-prev {
  opacity: 0.7;
  left: 10px;
}
.am-slider-default:hover .am-prev:hover {
  opacity: 1;
}
.am-slider-default:hover .am-next {
  opacity: 0.7;
  right: 10px;
}
.am-slider-default:hover .am-next:hover {
  opacity: 1;
}
.am-slider-default .am-pauseplay a {
  display: block;
  width: 20px;
  height: 20px;
  position: absolute;
  bottom: 5px;
  left: 10px;
  opacity: 0.8;
  z-index: 10;
  overflow: hidden;
  cursor: pointer;
  color: #000;
}
.am-slider-default .am-pauseplay a::before {
  font: normal normal normal 1.6rem/1 "FontAwesome", sans-serif;
  /*font-weight: normal; // 2
  font-style: normal; // 2
  vertical-align: baseline; // 3
  line-height: 1; // 4*/
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-transform: translate(0, 0);
      -ms-transform: translate(0, 0);
          transform: translate(0, 0);
  font-size: 20px;
  display: inline-block;
  content: "\f04c";
}
.am-slider-default .am-pauseplay a:hover {
  opacity: 1;
}
.am-slider-default .am-pauseplay a.am-play::before {
  content: "\f04b";
}
.am-slider-default .am-slider-desc {
  background-color: rgba(0, 0, 0, 0.7);
  position: absolute;
  bottom: 0;
  padding: 10px;
  width: 100%;
  color: #fff;
}
.am-slider-default .am-control-nav {
  width: 100%;
  position: absolute;
  bottom: -15px;
  text-align: center;
}
.am-slider-default .am-control-nav li {
  margin: 0 6px;
  display: inline-block;
}
.am-slider-default .am-control-nav li a {
  width: 8px;
  height: 8px;
  display: block;
  background-color: #666;
  background-color: rgba(0, 0, 0, 0.5);
  line-height: 0;
  font-size: 0;
  cursor: pointer;
  text-indent: -9999px;
  border-radius: 20px;
  -webkit-box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.3);
          box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.3);
}
.am-slider-default .am-control-nav li a:hover {
  background-color: #333;
  background-color: rgba(0, 0, 0, 0.7);
}
.am-slider-default .am-control-nav li a.am-active {
  background-color: #000;
  background-color: #0e90d2;
  cursor: default;
}
.am-slider-default .am-control-thumbs {
  margin: 5px 0 0;
  position: static;
  overflow: hidden;
}
.am-slider-default .am-control-thumbs li {
  width: 25%;
  float: left;
  margin: 0;
}
.am-slider-default .am-control-thumbs img {
  width: 100%;
  height: auto;
  display: block;
  opacity: .7;
  cursor: pointer;
}
.am-slider-default .am-control-thumbs img:hover {
  opacity: 1;
}
.am-slider-default .am-control-thumbs .am-active {
  opacity: 1;
  cursor: default;
}
.am-slider-default .am-control-thumbs i {
  position: absolute;
}
/*
TODO:
  1. 鍔ㄧ敾搴旇鏀惧湪 dialog 涓婏紝涓嶅啀鏄暣涓� modal锛屾秹鍙� JS 閫昏緫锛岄渶瑕佺粺绛逛慨鏀�
  2. modal 婊氬姩鏉″鐞嗭紝鏄惁闇€瑕佹坊鍔犱竴涓粴鍔ㄦ潯瀹藉害鐨勬按骞� padding锛�
*/
/* ==========================================================================
   Component: Modal Plugin
 ============================================================================ */
.am-modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1110;
  display: none;
  opacity: 0;
  outline: 0;
  text-align: center;
  -webkit-transform: scale(1.185);
      -ms-transform: scale(1.185);
          transform: scale(1.185);
  -webkit-transition-property: opacity, -webkit-transform;
  transition-property: opacity, -webkit-transform;
  transition-property: transform, opacity;
  transition-property: transform, opacity, -webkit-transform;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  -webkit-perspective: 1000px;
          perspective: 1000px;
}
.am-modal:focus {
  outline: 0;
}
.am-modal.am-modal-active {
  opacity: 1;
  -webkit-transition-duration: 300ms;
          transition-duration: 300ms;
  -webkit-transform: scale(1);
      -ms-transform: scale(1);
          transform: scale(1);
  overflow-x: hidden;
  overflow-y: auto;
}
.am-modal.am-modal-out {
  opacity: 0;
  z-index: 1109;
  -webkit-transition-duration: 300ms;
          transition-duration: 300ms;
  -webkit-transform: scale(0.815);
      -ms-transform: scale(0.815);
          transform: scale(0.815);
}
.am-modal:before {
  content: "\200B";
  display: inline-block;
  height: 100%;
  vertical-align: middle;
}
.am-modal-dialog {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  margin-left: auto;
  margin-right: auto;
  width: 270px;
  max-width: 100%;
  border-radius: 0;
  background: #f8f8f8;
  /*@media @large-up {
    width: @modal-lg;
    margin-left: -@modal-lg/2;
  }*/
}
@media only screen and (min-width:641px) {
  .am-modal-dialog {
    width: 540px;
  }
}
.am-modal-hd {
  padding: 15px 10px 5px 10px;
  font-size: 1.8rem;
  font-weight: 500;
}
.am-modal-hd + .am-modal-bd {
  padding-top: 0;
}
.am-modal-hd .am-close {
  position: absolute;
  top: 4px;
  right: 4px;
}
.am-modal-bd {
  padding: 15px 10px;
  text-align: center;
  border-bottom: 1px solid #dedede;
  border-radius: 2px 2px 0 0;
}
.am-modal-bd + .am-modal-bd {
  margin-top: 5px;
}
.am-modal-prompt-input {
  display: block;
  margin: 5px auto 0 auto;
  border-radius: 0;
  padding: 5px;
  line-height: 1.8rem;
  width: 80%;
  border: 1px solid #dedede;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
}
.am-modal-prompt-input:focus {
  outline: none;
  border-color: #d6d6d6;
}
.am-modal-footer {
  height: 44px;
  overflow: hidden;
  display: table;
  width: 100%;
  border-collapse: collapse;
}
.am-modal-btn {
  display: table-cell !important;
  padding: 0 5px;
  height: 44px;
  -webkit-box-sizing: border-box !important;
          box-sizing: border-box !important;
  font-size: 1.6rem;
  line-height: 44px;
  text-align: center;
  color: #0e90d2;
  display: block;
  word-wrap: normal;
  /* for IE */
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  cursor: pointer;
  border-right: 1px solid #dedede;
}
.am-modal-btn:first-child {
  border-radius: 0 0 0 0;
}
.am-modal-btn:last-child {
  border-right: none;
  border-radius: 0 0 0 0;
}
.am-modal-btn:first-child:last-child {
  border-radius: 0 0 0 0;
}
.am-modal-btn.am-modal-btn-bold {
  font-weight: 500;
}
.am-modal-btn:active {
  background: #d4d4d4;
}
.am-modal-btn + .am-modal-btn {
  border-left: 1px solid #dedede;
}
.am-modal-no-btn .am-modal-dialog {
  border-radius: 0;
  border-bottom: none;
}
.am-modal-no-btn .am-modal-bd {
  border-bottom: none;
}
.am-modal-no-btn .am-modal-footer {
  display: none;
}
.am-modal-loading .am-modal-bd {
  border-bottom: none;
}
.am-modal-loading .am-icon-spin {
  display: inline-block;
  font-size: 2.4rem;
}
.am-modal-loading .am-modal-footer {
  display: none;
}
.am-modal-actions {
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 1110;
  width: 100%;
  max-height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  text-align: center;
  border-radius: 0;
  -webkit-transform: translateY(100%);
      -ms-transform: translateY(100%);
          transform: translateY(100%);
  -webkit-transition: -webkit-transform 300ms;
  transition: -webkit-transform 300ms;
  transition: transform 300ms;
  transition: transform 300ms, -webkit-transform 300ms;
}
.am-modal-actions.am-modal-active {
  -webkit-transform: translateY(0);
      -ms-transform: translateY(0);
          transform: translateY(0);
}
.am-modal-actions.am-modal-out {
  z-index: 1109;
  -webkit-transform: translateY(100%);
      -ms-transform: translateY(100%);
          transform: translateY(100%);
}
.am-modal-actions-group {
  margin: 10px;
}
.am-modal-actions-group .am-list {
  margin: 0;
  border-radius: 0;
}
.am-modal-actions-group .am-list > li {
  margin-bottom: 0;
  border-bottom: none;
  display: block;
  word-wrap: normal;
  /* for IE */
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  -webkit-box-shadow: inset 0 1px 0 rgba(0, 0, 0, 0.015);
          box-shadow: inset 0 1px 0 rgba(0, 0, 0, 0.015);
}
.am-modal-actions-group .am-list > li > a {
  padding: 1rem;
  display: block;
  word-wrap: normal;
  /* for IE */
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.am-modal-actions-group .am-list > li:first-child {
  border-top: none;
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}
.am-modal-actions-group .am-list > li:last-child {
  border-bottom: none;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.am-modal-actions-header {
  padding: 1rem;
  color: #999999;
  font-size: 1.4rem;
}
.am-modal-actions-danger {
  color: #dd514c;
}
.am-modal-actions-danger a {
  color: inherit;
}
.am-popup {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 1110;
  background: #fff;
  display: none;
  overflow: hidden;
  -webkit-transition-property: -webkit-transform;
  transition-property: -webkit-transform;
  transition-property: transform;
  transition-property: transform, -webkit-transform;
  -webkit-transform: translateY(100%);
      -ms-transform: translateY(100%);
          transform: translateY(100%);
}
.am-popup.am-modal-active,
.am-popup.am-modal-out {
  -webkit-transition-duration: 300ms;
          transition-duration: 300ms;
}
.am-popup.am-modal-active {
  -webkit-transform: translateY(0);
      -ms-transform: translateY(0);
          transform: translateY(0);
}
.am-popup.am-modal-out {
  -webkit-transform: translateY(100%);
      -ms-transform: translateY(100%);
          transform: translateY(100%);
}
@media all and (min-width: 630px) and (min-height: 630px) {
  .am-popup {
    width: 630px;
    height: 630px;
    left: 50%;
    top: 50%;
    margin-left: -315px;
    margin-top: -315px;
    -webkit-transform: translateY(1024px);
        -ms-transform: translateY(1024px);
            transform: translateY(1024px);
  }
  .am-popup.am-modal-active {
    -webkit-transform: translateY(0);
        -ms-transform: translateY(0);
            transform: translateY(0);
  }
  .am-popup.am-modal-out {
    -webkit-transform: translateY(1024px);
        -ms-transform: translateY(1024px);
            transform: translateY(1024px);
  }
}
.am-popup-inner {
  padding-top: 44px;
  height: 100%;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
}
.am-popup-hd {
  position: absolute;
  top: 0;
  z-index: 1000;
  width: 100%;
  height: 43px;
  border-bottom: 1px solid #dedede;
  background-color: #fff;
}
.am-popup-hd .am-popup-title {
  font-size: 1.8rem;
  font-weight: bold;
  line-height: 43px;
  text-align: center;
  margin: 0 30px;
  color: #333333;
  display: block;
  word-wrap: normal;
  /* for IE */
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.am-popup-hd .am-close {
  position: absolute;
  right: 10px;
  top: 8px;
  cursor: pointer;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  color: #999999;
}
.am-popup-hd .am-close:hover {
  -webkit-transform: rotate(360deg);
      -ms-transform: rotate(360deg);
          transform: rotate(360deg);
  color: #555555;
}
.am-popup-bd {
  padding: 15px;
  background: #f8f8f8;
  color: #555555;
}
/* ==========================================================================
   Component: OffCanvas Plugin
 ============================================================================ */
/* Off-canvas overlay and bar container */
.am-offcanvas {
  display: none;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1090;
  background: rgba(0, 0, 0, 0.15);
}
.am-offcanvas.am-active {
  display: block;
}
/**
 * .@{ns}offcanvas-page
 *
 * Prepares the whole HTML page to slide-out
 * 1. Fix the main page and disallow scrolling
 * 2. Side-out transition
 */
.am-offcanvas-page {
  position: fixed;
  /* 1 */
  -webkit-transition: margin-left 0.3s ease-in-out;
  transition: margin-left 0.3s ease-in-out;
  /* 2 */
}
/* Sub-object .@{ns}offcanvas-bar */
.am-offcanvas-bar {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 1091;
  width: 270px;
  max-width: 100%;
  background: #333;
  overflow-y: auto;
  /* scrollable */
  -webkit-overflow-scrolling: touch;
  /* scrollable */
  -webkit-transition: -webkit-transform 0.3s ease-in-out;
  transition: -webkit-transform 0.3s ease-in-out;
  transition: transform 0.3s ease-in-out;
  transition: transform 0.3s ease-in-out, -webkit-transform 0.3s ease-in-out;
  -webkit-transform: translateX(-100%);
      -ms-transform: translateX(-100%);
          transform: translateX(-100%);
}
.am-offcanvas-bar:after {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  width: 1px;
  background: #262626;
}
.am-offcanvas.am-active .am-offcanvas-bar.am-offcanvas-bar-active {
  -webkit-transform: translateX(0);
      -ms-transform: translateX(0);
          transform: translateX(0);
}
/* .@{ns}offcanvas-bar-flip */
.am-offcanvas-bar-flip {
  left: auto;
  right: 0;
  -webkit-transform: translateX(100%);
      -ms-transform: translateX(100%);
          transform: translateX(100%);
}
.am-offcanvas-bar-flip:after {
  right: auto;
  left: 0;
}
.am-offcanvas-content {
  padding: 15px;
  color: #999;
}
.am-offcanvas-content a {
  color: #ccc;
}
/* ==========================================================================
   Component: Popover Plugin
 ============================================================================ */
.am-popover {
  position: absolute;
  top: 0;
  left: 0;
  margin: 0;
  border-radius: 0;
  background: #333333;
  color: #fff;
  border: 1px solid #333333;
  display: none;
  font-size: 1.6rem;
  z-index: 1150;
  opacity: 0;
  -webkit-transition: opacity 300ms;
  transition: opacity 300ms;
}
.am-popover.am-active {
  display: block !important;
  opacity: 1;
}
.am-popover-inner {
  position: relative;
  background: #333333;
  padding: 8px;
  z-index: 110;
}
.am-popover-caret {
  position: absolute;
  top: 0;
  z-index: 100;
  display: inline-block;
  width: 0;
  height: 0;
  vertical-align: middle;
  border-bottom: 8px solid #333333;
  border-right: 8px solid transparent;
  border-left: 8px solid transparent;
  border-top: 0 dotted;
  -webkit-transform: rotate(360deg);
      -ms-transform: rotate(360deg);
          transform: rotate(360deg);
  overflow: hidden;
}
.am-popover-top .am-popover-caret {
  top: auto;
  bottom: -8px;
  -webkit-transform: rotate(180deg);
      -ms-transform: rotate(180deg);
          transform: rotate(180deg);
}
.am-popover-bottom .am-popover-caret {
  top: -8px;
}
.am-popover-top .am-popover-caret,
.am-popover-bottom .am-popover-caret {
  left: 50%;
  margin-left: -8px;
}
.am-popover-left .am-popover-caret {
  top: auto;
  left: auto;
  right: -12px;
  -webkit-transform: rotate(90deg);
      -ms-transform: rotate(90deg);
          transform: rotate(90deg);
}
.am-popover-right .am-popover-caret {
  right: auto;
  left: -12px;
  -webkit-transform: rotate(-90deg);
      -ms-transform: rotate(-90deg);
          transform: rotate(-90deg);
}
.am-popover-left .am-popover-caret,
.am-popover-right .am-popover-caret {
  top: 50%;
  margin-top: -4px;
}
.am-popover-sm {
  font-size: 1.4rem;
}
.am-popover-sm .am-popover-inner {
  padding: 5px;
}
.am-popover-lg {
  font-size: 1.8rem;
}
.am-popover-primary {
  border-color: #0e90d2;
}
.am-popover-primary .am-popover-inner {
  background: #0e90d2;
}
.am-popover-primary .am-popover-caret {
  border-bottom-color: #0e90d2;
}
.am-popover-secondary {
  border-color: #3bb4f2;
}
.am-popover-secondary .am-popover-inner {
  background: #3bb4f2;
}
.am-popover-secondary .am-popover-caret {
  border-bottom-color: #3bb4f2;
}
.am-popover-success {
  border-color: #5eb95e;
}
.am-popover-success .am-popover-inner {
  background: #5eb95e;
}
.am-popover-success .am-popover-caret {
  border-bottom-color: #5eb95e;
}
.am-popover-warning {
  border-color: #F37B1D;
}
.am-popover-warning .am-popover-inner {
  background: #F37B1D;
}
.am-popover-warning .am-popover-caret {
  border-bottom-color: #F37B1D;
}
.am-popover-danger {
  border-color: #dd514c;
}
.am-popover-danger .am-popover-inner {
  background: #dd514c;
}
.am-popover-danger .am-popover-caret {
  border-bottom-color: #dd514c;
}
/* ==========================================================================
   Component: Progress Plugin
 ============================================================================ */
#nprogress {
  /* Make clicks pass-through */
  pointer-events: none;
  /* Fancy blur effect */
}
#nprogress .nprogress-bar {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 2000;
  width: 100%;
  height: 2px;
  background: #5eb95e;
}
#nprogress .nprogress-peg {
  display: block;
  position: absolute;
  right: 0;
  width: 100px;
  height: 100%;
  -webkit-box-shadow: 0 0 10px #5eb95e, 0 0 5px #5eb95e;
          box-shadow: 0 0 10px #5eb95e, 0 0 5px #5eb95e;
  opacity: 1;
  -webkit-transform: rotate(3deg) translate(0px, -4px);
      -ms-transform: rotate(3deg) translate(0px, -4px);
          transform: rotate(3deg) translate(0px, -4px);
}
#nprogress .nprogress-spinner {
  position: fixed;
  top: 15px;
  right: 15px;
  z-index: 2000;
  display: block;
}
#nprogress .nprogress-spinner-icon {
  width: 18px;
  height: 18px;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  border: solid 2px transparent;
  border-top-color: #5eb95e;
  border-left-color: #5eb95e;
  border-radius: 50%;
  -webkit-animation: nprogress-spinner 400ms linear infinite;
          animation: nprogress-spinner 400ms linear infinite;
}
@-webkit-keyframes nprogress-spinner {
  0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
  }
}
@keyframes nprogress-spinner {
  0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
  }
}
/* ==========================================================================
   Component: Tabs Plugin
 ============================================================================ */
.am-tabs-bd {
  position: relative;
  overflow: hidden;
  border: 1px solid #ddd;
  border-top: none;
  z-index: 100;
  -webkit-transition: height .3s;
  transition: height .3s;
}
.am-tabs-bd:before,
.am-tabs-bd:after {
  content: " ";
  display: table;
}
.am-tabs-bd:after {
  clear: both;
}
.am-tabs-bd .am-tab-panel {
  position: absolute;
  top: 0;
  z-index: 99;
  float: left;
  width: 100%;
  padding: 10px 10px 15px;
  visibility: hidden;
  -webkit-transition: -webkit-transform 0.3s;
  transition: -webkit-transform 0.3s;
  transition: transform 0.3s;
  transition: transform 0.3s, -webkit-transform 0.3s;
  -webkit-transform: translateX(-100%);
      -ms-transform: translateX(-100%);
          transform: translateX(-100%);
}
.am-tabs-bd .am-tab-panel * {
  -webkit-user-drag: none;
}
.am-tabs-bd .am-tab-panel.am-active {
  position: relative;
  z-index: 100;
  visibility: visible;
  -webkit-transform: translateX(0);
      -ms-transform: translateX(0);
          transform: translateX(0);
}
.am-tabs-bd .am-tab-panel.am-active ~ .am-tab-panel {
  -webkit-transform: translateX(100%);
      -ms-transform: translateX(100%);
          transform: translateX(100%);
}
.am-tabs-bd .am-tabs-bd {
  border: none;
}
.am-tabs-bd-ofv {
  overflow: visible;
}
.am-tabs-bd-ofv > .am-tab-panel {
  display: none;
}
.am-tabs-bd-ofv > .am-tab-panel.am-active {
  display: block;
}
.am-tabs-fade .am-tab-panel {
  opacity: 0;
  -webkit-transition: opacity .25s linear;
  transition: opacity .25s linear;
}
.am-tabs-fade .am-tab-panel.am-in {
  opacity: 1;
}
/* ==========================================================================
   Component: Share Plugin
 ============================================================================ */
.am-share {
  font-size: 14px;
}
.am-share-title {
  padding: 10px 0 0;
  margin: 0 10px;
  font-weight: normal;
  text-align: center;
  color: #555555;
  background-color: #f8f8f8;
  border-bottom: 1px solid #fff;
  border-top-right-radius: 2px;
  border-top-left-radius: 2px;
}
.am-share-title:after {
  content: "";
  display: block;
  width: 100%;
  height: 0;
  margin-top: 10px;
  border-bottom: 1px solid #dfdfdf;
}
.am-share-sns {
  margin: 0 10px;
  padding-top: 15px;
  background-color: #f8f8f8;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 2px;
}
.am-share-sns li {
  margin-bottom: 15px;
}
.am-share-sns a {
  display: block;
  color: #555555;
}
.am-share-sns span {
  display: block;
}
.am-share-sns [class*='am-icon'] {
  background-color: #3bb4f2;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  line-height: 36px;
  color: #fff;
  margin-bottom: 5px;
  font-size: 18px;
}
.am-share-sns .am-icon-weibo {
  background-color: #ea1328;
}
.am-share-sns .am-icon-qq {
  background-color: #009cda;
}
.am-share-sns .am-icon-star {
  background-color: #ffc028;
}
.am-share-sns .am-icon-tencent-weibo {
  background-color: #23ccfe;
}
.am-share-sns .am-icon-wechat,
.am-share-sns .am-icon-weixin {
  background-color: #44b549;
}
.am-share-sns .am-icon-renren {
  background-color: #105ba3;
}
.am-share-sns .am-icon-comment {
  background-color: #5eb95e;
}
.am-share-footer {
  margin: 10px;
}
.am-share-footer .am-btn {
  color: #555555;
}
.am-share-wechat-qr {
  font-size: 14px;
  color: #777;
}
.am-share-wechat-qr .am-modal-dialog {
  background-color: #fff;
  border: 1px solid #dedede;
}
.am-share-wechat-qr .am-modal-hd {
  padding-top: 10px;
  text-align: left;
  margin-bottom: 10px;
}
.am-share-wechat-qr .am-share-wx-qr {
  margin-bottom: 10px;
}
.am-share-wechat-qr .am-share-wechat-tip {
  text-align: left;
}
.am-share-wechat-qr .am-share-wechat-tip em {
  color: #dd514c;
  font-weight: bold;
  font-style: normal;
  margin-left: 3px;
  margin-right: 3px;
}
/* ==========================================================================
   Component: PureView Plugin
 ============================================================================ */
.am-pureview {
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  z-index: 1120;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.95);
  display: none;
  overflow: hidden;
  -webkit-transition: -webkit-transform .3s;
  transition: -webkit-transform .3s;
  transition: transform .3s;
  transition: transform .3s, -webkit-transform .3s;
  -webkit-transform: translate(0, 100%);
      -ms-transform: translate(0, 100%);
          transform: translate(0, 100%);
}
.am-pureview.am-active {
  -webkit-transform: translate(0, 0);
      -ms-transform: translate(0, 0);
          transform: translate(0, 0);
}
.am-pureview ul,
.am-pureview ol {
  list-style: none;
  padding: 0;
  margin: 0;
  width: 100%;
}
.am-pureview-slider {
  overflow: hidden;
  height: 100%;
}
.am-pureview-slider li {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  vertical-align: middle;
  -webkit-transition: all .3s linear;
  transition: all .3s linear;
  z-index: 100;
  visibility: hidden;
}
.am-pureview-slider li.am-pureview-slide-prev {
  -webkit-transform: translate(-100%, 0);
      -ms-transform: translate(-100%, 0);
          transform: translate(-100%, 0);
  z-index: 109;
}
.am-pureview-slider li.am-pureview-slide-next {
  -webkit-transform: translate(100%, 0);
      -ms-transform: translate(100%, 0);
          transform: translate(100%, 0);
  z-index: 109;
}
.am-pureview-slider li.am-active {
  position: relative;
  z-index: 110;
  -webkit-transform: translate(0, 0);
      -ms-transform: translate(0, 0);
          transform: translate(0, 0);
  visibility: visible;
}
.am-pureview-slider .pinch-zoom-container {
  width: 100%;
  z-index: 1121;
}
.am-pureview-slider .am-pinch-zoom {
  position: relative;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
}
.am-pureview-slider .am-pinch-zoom:after {
  display: inline-block;
  font: normal normal normal 1.6rem/1 "FontAwesome", sans-serif;
  /*font-weight: normal; // 2
  font-style: normal; // 2
  vertical-align: baseline; // 3
  line-height: 1; // 4*/
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-transform: translate(0, 0);
      -ms-transform: translate(0, 0);
          transform: translate(0, 0);
  content: "\f110";
  -webkit-animation: icon-spin 2s infinite linear;
          animation: icon-spin 2s infinite linear;
  font-size: 24px;
  line-height: 24px;
  color: #eee;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -12px;
  margin-top: -12px;
  z-index: 1;
}
.am-pureview-slider .am-pinch-zoom.am-pureview-loaded:after {
  display: none;
}
.am-pureview-slider img {
  position: relative;
  display: block;
  max-width: 100%;
  max-height: 100%;
  opacity: 0;
  z-index: 200;
  -webkit-user-drag: none;
  -webkit-transition: opacity 0.2s ease-in;
  transition: opacity 0.2s ease-in;
}
.am-pureview-slider img.am-img-loaded {
  opacity: 1;
}
.am-pureview-direction {
  position: absolute;
  top: 50%;
  width: 100%;
  margin-top: -18px !important;
  z-index: 1122;
}
.am-touch .am-pureview-direction,
.am-pureview-only .am-pureview-direction {
  display: none;
}
.am-pureview-direction li {
  position: absolute;
  width: 36px;
  height: 36px;
}
.am-pureview-direction a {
  display: block;
  height: 36px;
  border: none;
  color: #ccc;
  opacity: 0.5;
  cursor: pointer;
  text-align: center;
  z-index: 1125;
}
.am-pureview-direction a:before {
  content: "\f137";
  line-height: 36px;
  font-size: 24px;
}
.am-pureview-direction a:hover {
  opacity: 1;
}
.am-pureview-direction .am-pureview-prev {
  left: 15px;
}
.am-pureview-direction .am-pureview-next {
  right: 15px;
}
.am-pureview-direction .am-pureview-next a:before {
  content: "\f138";
}
.am-pureview-bar {
  position: absolute;
  bottom: 0;
  height: 45px;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.35);
  color: #eeeeee;
  line-height: 45px;
  padding: 0 10px;
  font-size: 14px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}
.am-pureview-bar .am-pureview-title {
  display: block;
  word-wrap: normal;
  /* for IE */
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  margin-left: 6px;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
      -ms-flex: 1;
          flex: 1;
}
.am-pureview-bar .am-pureview-total {
  font-size: 10px;
  line-height: 48px;
}
.am-pureview-actions {
  position: absolute;
  z-index: 1130;
  left: 0;
  right: 0;
  top: 0;
  height: 45px;
  background-color: rgba(0, 0, 0, 0.35);
}
.am-pureview-actions a {
  position: absolute;
  left: 10px;
  color: #ccc;
  display: block;
  width: 45px;
  line-height: 45px;
  text-align: left;
  font-size: 16px;
}
.am-pureview-actions a:hover {
  color: #fff;
}
.am-pureview-actions [data-am-toggle="share"] {
  left: auto;
  right: 10px;
}
.am-pureview-bar,
.am-pureview-actions {
  opacity: 0;
  -webkit-transition: all .15s;
  transition: all .15s;
  z-index: 1130;
}
.am-pureview-bar-active .am-pureview-bar,
.am-pureview-bar-active .am-pureview-actions {
  opacity: 1;
}
.am-pureview-nav {
  position: absolute;
  bottom: 15px;
  left: 0;
  right: 0;
  text-align: center;
  z-index: 1131;
}
.am-pureview-bar-active .am-pureview-nav {
  display: none;
}
.am-pureview-nav li {
  display: inline-block;
  background: #ccc;
  background: rgba(255, 255, 255, 0.5);
  width: 8px;
  height: 8px;
  margin: 0 3px;
  border-radius: 50%;
  text-indent: -9999px;
  overflow: hidden;
  cursor: pointer;
}
.am-pureview-nav .am-active {
  background: #fff;
  background: rgba(255, 255, 255, 0.9);
}
[data-am-pureview] img {
  cursor: pointer;
}
.am-pureview-active {
  overflow: hidden;
}
.ath-viewport * {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}
.ath-viewport {
  position: relative;
  z-index: 2147483641;
  pointer-events: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  -webkit-touch-callout: none;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  -webkit-text-size-adjust: none;
      -ms-text-size-adjust: none;
          text-size-adjust: none;
}
.ath-modal {
  pointer-events: auto !important;
  background: rgba(0, 0, 0, 0.6);
}
.ath-mandatory {
  background: #000;
}
.ath-container {
  pointer-events: auto !important;
  position: absolute;
  z-index: 2147483641;
  padding: 0.7em 0.6em;
  width: 18em;
  background: #eee;
  -webkit-background-size: 100% auto;
          background-size: 100% auto;
  -webkit-box-shadow: 0 0.2em 0 #d1d1d1;
          box-shadow: 0 0.2em 0 #d1d1d1;
  font-family: sans-serif;
  font-size: 15px;
  line-height: 1.5em;
  text-align: center;
}
.ath-container small {
  font-size: 0.8em;
  line-height: 1.3em;
  display: block;
  margin-top: 0.5em;
}
.ath-ios.ath-phone {
  bottom: 1.8em;
  left: 50%;
  margin-left: -9em;
}
.ath-ios6.ath-tablet {
  left: 5em;
  top: 1.8em;
}
.ath-ios7.ath-tablet {
  left: 0.7em;
  top: 1.8em;
}
.ath-ios8.ath-tablet {
  right: 0.4em;
  top: 1.8em;
}
.ath-android {
  bottom: 1.8em;
  left: 50%;
  margin-left: -9em;
}
/* close icon */
.ath-container:before {
  content: '';
  position: relative;
  display: block;
  float: right;
  margin: -0.7em -0.6em 0 0.5em;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAIQAAACECAMAAABmmnOVAAAAdVBMVEUAAAA5OTkzMzM7Ozs3NzdBQUFAQEA/Pz8+Pj5BQUFAQEA/Pz8+Pj5BQUFAQEA/Pz9BQUE+Pj4/Pz8/Pz8+Pj4/Pz8/Pz8/Pz8+Pj4/Pz8+Pj4/Pz8/Pz8/Pz8/Pz8/Pz8+Pj4/Pz8/Pz8/Pz8/Pz9AQEA/Pz+fdCaPAAAAJnRSTlMACQoNDjM4OTo7PEFCQ0RFS6ytsbS1tru8vcTFxu7x8vX19vf4+C5yomAAAAJESURBVHgBvdzLTsJAGEfxr4C2KBcVkQsIDsK8/yPaqIsPzVlyzrKrX/5p0kkXEz81L23otc9NpIbbWia2YVLqdnhlqFlhGWpSDHe1aopsSIpRb8gK0dC3G30b9rVmhWZIimTICsvQtx/FsuYOrWHoDjX3Gu31gzJxdki934WrAIOsAIOsAIOiAMPhPsJTgKGN0BVsYIVsYIVpYIVpYIVpYIVpYIVpYIVpYIVpYIVlAIVgEBRs8BRs8BRs8BRs8BRs8BRs8BRs8BRTNmgKNngKNngKNngKNngKNhiKGxgiOlZoBlaYBlaYBlaYBlaYBlaYBlaYBlaYBlZIBlBMfQMrVAMr2KAqBENSHFHhGEABhi5CV6gGUKgGUKgGUKgGUFwuqgEUvoEVsoEVpoEUpgEUggF+gKTKY+h1fxSlC7/Z+RrxOQ3fcEoAPPHZBlaYBlaYBlaYBlZYBlYIhvLBCstw7PgM7hkiWOEZWGEaWGEaWGEaIsakEAysmHkGVpxmvoEVqoEVpoEVpoEVpoEVpoEVpoEVkoEVgkFQsEFSsEFQsGEcoSvY4CnY4CnY4CnY4CnY4CnY4CnY4CnY4CnY4CnY4CnY4CnY4CnY4CnY4CnY4CnY4CnY4CnY4CnmbNAUT2c2WAo2eAo2eAo2eAo2eAo2eArNEPFACjZ4CjZ4CjZ4CjaIird/rBvFH6llNCvewdli1URWCIakSIZesUaDoFg36dKFWk9zCZDei3TtwmCj7pC22AwikiIZPEU29IpFNliKxa/hC9DFITjQPYhcAAAAAElFTkSuQmCC);
  background-color: rgba(255, 255, 255, 0.8);
  -webkit-background-size: 50% 50%;
          background-size: 50%;
  background-repeat: no-repeat;
  background-position: 50%;
  width: 2.7em;
  height: 2.7em;
  text-align: center;
  overflow: hidden;
  color: #a33;
  z-index: 2147483642;
}
.ath-container.ath-icon:before {
  position: absolute;
  top: 0;
  right: 0;
  margin: 0;
  float: none;
}
.ath-mandatory .ath-container:before {
  display: none;
}
.ath-container.ath-android:before {
  float: left;
  margin: -0.7em 0.5em 0 -0.6em;
}
.ath-container.ath-android.ath-icon:before {
  position: absolute;
  right: auto;
  left: 0;
  margin: 0;
  float: none;
}
/* applied only if the application icon is shown */
.ath-action-icon {
  display: inline-block;
  vertical-align: middle;
  background-position: 50%;
  background-repeat: no-repeat;
  text-indent: -9999em;
  overflow: hidden;
}
.ath-ios7 .ath-action-icon,
.ath-ios8 .ath-action-icon {
  width: 1.6em;
  height: 1.6em;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHgAAACtCAYAAAB7l7tOAAAF6UlEQVR4AezZWWxUZRiH8VcQEdxZEFFiUZBFUCIa1ABBDARDcCciYGKMqTEGww3SOcNSAwQTjOBiiIpEhRjAhRgXRC8MFxojEhAFZUGttVhaoSxlaW3n8W3yXZxm6vTrOMM5Q98n+V9MMu1pvl++uZhKuypghu49KaaTWGdZSYoVN6VD95nMpLNYZ9XNbdQR2od2k88O3Gm6Bh0t7H0p5Vwp2Ax3ajpu2tYbciFWwkTFO63DY6+JcI4USFaSyYpWp8N7SVZJKR3EinkBk9JxvZFXxhnZSjBaoWp1ZL0ES8WKYXMZp0AndORgy8WKFe5Yf1zvvSBWDEpys2LU6MjD5kmEWQlGKsJRHXlcqUSQVcItEnDEA6gAb7LhjvD9WO6yIEfICQI5A1nzGCYB1T4og5bBiFcyv2f6ujYhl4iVxwKG6qp8MK55HsqPwK0rMr9v/yEo3uCPrJstVh5KMER30Aeh31Ioq0FrHfjXw9CYghnrvYFTuqfEymFzGSwBlT4ARYr7u+K6GLmCVGvAGg2NMG0d/sgJnpScZLjXSkC5z8H3eQ72/k24Q8NfzvwFyK4qtuJSZKaubRPyE/K/Mtx+EvCHL+7uasId1t10w0scz/RzSzYzAfgKV30D3LPaG7lRkR8RK4tKKJKAMp+D7r0EfmmOe0x3m2itAc/ZxBjgAt1mXHWKPPkdb+QGSTJdrDaU5EoJ2OtzwD0WwY7KNNzbRfMFFg24WPdtGHnS221Cflgsj56hjwTs8TnY7oq7/QDhjutGicsb2AVcovsO18l6uPPNNiE/JFaGAq7Q7fY50G4LYVtz3FrdaNGyBXbIl+q24DqhyHes9EaulwR3SwtZs+ktAT/7HORliru1gnCndONFyx44Dfn7MPLYN7yR6yTJZAllJeguAT/4HOBFz8I3ZWm4E0TLFbBD7qn7EVdtHYx53R9ZN0ksrZRuErDN5+AuLIWvm+Oe1k0ULdfADrmX7idcR0/DyBXeyCdlLuMMOGCBz4F1ng+f7yFcve5e0fIFHELeiav6BAx70Rt5p0yhY3u/wR0kyarW/uX35b403PtFyzewQ75ctwtXzSkY8WqruHslSV8RscrL6TJ1bcvfWJ0/HzbtIdw/ugdFyzdwOOAq3T6fmzxwGQ3vbmO8iFioIWqYSsHMj9M/ljfuTsOdItoZBXYBfXX7cVXVwvXLm/8+fU3lcdCqdEMNGBbgUmRmfQISQKd5sGEn4VK6YtEiAXYBA3QVuA4q8hCHrDcafR1ul65jewfuovsCl7vJrNlOuEbdo6JFCuwCrtb9hqusBu56Cw4cI1y1briIWEBn3Ue0XKPuMdGiBg4H9NdV0HJ/6QZLOEPmPN0GmpfSPS5arIBdwHUtIFfoBsl/ZsgfhHCfFi2WwC5goO4AmvanbqBkzJA76tboZokWa2AXMEi3RTdAvDLkDqJFAhzB32xFD2wZsGXA0WfAlgFbBmwZsGXAlgFbBpzk04JaKb0iA9ZnF9x5SQAFtRKKIgPWZxfaeRmwAZ/BGbAB37eaG6MCbnq2Aed5czYyKirgpmcbsAHHZAZswN0Wwo7KeG1fFf2jAm56dtzOQ42yB+65mDhWFBUwUETMUiMDNmADbp/APRaTAh6I2bpGCNw1bufRZJQ1cPdF/NueHZsgDEBBGLbMGoIu4AZu5gLOZeEaYmEXeznF3jRPyEv4frgJvvJe3qTefY0AAwYMGDBgwIABAwYMGDBgwIABAwYMGDBgwIABAwYMGDBgwIABAwYMGDBgwIABAwYMGDBgwIABAwb8rwADBgwYMGDAgAEDBgwYMGDAgAEDBgwYMGDAgAEDBgz4/sz1Nia/9hizA7zgklwy3RYwYMBzBRjw4bPjxAbAAizAAtwgwAIswAIswAIMGDBgARZgARZgAS4FWIAFWIAFWIABAwYswAIswAIswIUAC7AAC7AACzBgwIAFWIAFWIAFuBBgARZgARZgAQYMGPApQ99ZCdgWtzqwATbABtgAG2DbnxNb7zbRimsMLMACrDf2wMWI/WasfQAAAABJRU5ErkJggg==);
  margin-top: -0.3em;
  -webkit-background-size: auto 100%;
          background-size: auto 100%;
}
.ath-ios6 .ath-action-icon {
  width: 1.8em;
  height: 1.8em;
  background-image: url(data:image/png;base64,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);
  margin-bottom: 0.4em;
  -webkit-background-size: 100% auto;
          background-size: 100% auto;
}
.ath-android .ath-action-icon {
  width: 1.4em;
  height: 1.4em;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAMAAABg3Am1AAAANlBMVEVmZmb///9mZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZW6fJrAAAAEXRSTlMAAAYHG21ub8fLz9DR8/T4+RrZ9owAAAB3SURBVHja7dNLDoAgDATQWv4gKve/rEajJOJiWLgg6WzpSyB0aHqHiNj6nL1lovb4C+hYzkSNAT7mryQFAVOeGAj4CjwEtgrWXpD/uZKtwEJApXt+Vn0flzRhgNiFZQkOXY0aADQZCOCPlsZJ46Rx0jhp3IiN2wGDHhxtldrlwQAAAABJRU5ErkJggg==);
  -webkit-background-size: 100% auto;
          background-size: 100% auto;
}
.ath-container p {
  margin: 0;
  padding: 0;
  position: relative;
  z-index: 2147483642;
  text-shadow: 0 0.1em 0 #fff;
  font-size: 1.1em;
}
.ath-ios.ath-phone:after {
  content: '';
  background: #eee;
  position: absolute;
  width: 2em;
  height: 2em;
  bottom: -0.9em;
  left: 50%;
  margin-left: -1em;
  -webkit-transform: scaleX(0.9) rotate(45deg);
  -ms-transform: scaleX(0.9) rotate(45deg);
      transform: scaleX(0.9) rotate(45deg);
  -webkit-box-shadow: 0.2em 0.2em 0 #d1d1d1;
          box-shadow: 0.2em 0.2em 0 #d1d1d1;
}
.ath-ios.ath-tablet:after {
  content: '';
  background: #eee;
  position: absolute;
  width: 2em;
  height: 2em;
  top: -0.9em;
  left: 50%;
  margin-left: -1em;
  -webkit-transform: scaleX(0.9) rotate(45deg);
  -ms-transform: scaleX(0.9) rotate(45deg);
      transform: scaleX(0.9) rotate(45deg);
  z-index: 2147483641;
}
.ath-application-icon {
  position: relative;
  padding: 0;
  border: 0;
  margin: 0 auto 0.2em auto;
  height: 6em;
  width: 6em;
  z-index: 2147483642;
}
.ath-container.ath-ios .ath-application-icon {
  border-radius: 1em;
  -webkit-box-shadow: 0 0.2em 0.4em rgba(0, 0, 0, 0.3), inset 0 0.07em 0 rgba(255, 255, 255, 0.5);
          box-shadow: 0 0.2em 0.4em rgba(0, 0, 0, 0.3), inset 0 0.07em 0 rgba(255, 255, 255, 0.5);
  margin: 0 auto 0.4em auto;
}
@media only screen and (orientation: landscape) {
  .ath-container.ath-phone {
    width: 24em;
  }
  .ath-android.ath-phone {
    margin-left: -12em;
  }
  .ath-ios.ath-phone {
    margin-left: -12em;
  }
  .ath-ios6:after {
    left: 39%;
  }
  .ath-ios8.ath-phone {
    left: auto;
    bottom: auto;
    right: 0.4em;
    top: 1.8em;
  }
  .ath-ios8.ath-phone:after {
    bottom: auto;
    top: -0.9em;
    left: 68%;
    z-index: 2147483641;
    -webkit-box-shadow: none;
            box-shadow: none;
  }
}
/* ==========================================================================
   Component: uCheck Plugin
 ============================================================================ */
.am-checkbox,
.am-radio,
.am-checkbox-inline,
.am-radio-inline {
  padding-left: 22px;
  position: relative;
  -webkit-transition: color .25s linear;
  transition: color .25s linear;
  font-size: 14px;
  line-height: 1.5;
}
label.am-checkbox,
label.am-radio {
  font-weight: normal;
}
.am-ucheck-icons {
  color: #999999;
  display: block;
  height: 20px;
  top: 0;
  left: 0;
  position: absolute;
  width: 20px;
  text-align: center;
  line-height: 21px;
  font-size: 18px;
  cursor: pointer;
}
.am-checkbox .am-icon-checked,
.am-radio .am-icon-checked,
.am-checkbox-inline .am-icon-checked,
.am-radio-inline .am-icon-checked,
.am-checkbox .am-icon-unchecked,
.am-radio .am-icon-unchecked,
.am-checkbox-inline .am-icon-unchecked,
.am-radio-inline .am-icon-unchecked {
  position: absolute;
  left: 0;
  top: 0;
  display: inline-table;
  margin: 0;
  background-color: transparent;
  -webkit-transition: color .25s linear;
  transition: color .25s linear;
}
.am-checkbox .am-icon-checked:before,
.am-radio .am-icon-checked:before,
.am-checkbox-inline .am-icon-checked:before,
.am-radio-inline .am-icon-checked:before,
.am-checkbox .am-icon-unchecked:before,
.am-radio .am-icon-unchecked:before,
.am-checkbox-inline .am-icon-unchecked:before,
.am-radio-inline .am-icon-unchecked:before {
  display: inline-block;
  font: normal normal normal 1.6rem/1 "FontAwesome", sans-serif;
  /*font-weight: normal; // 2
  font-style: normal; // 2
  vertical-align: baseline; // 3
  line-height: 1; // 4*/
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-transform: translate(0, 0);
      -ms-transform: translate(0, 0);
          transform: translate(0, 0);
}
.am-checkbox .am-icon-checked,
.am-radio .am-icon-checked,
.am-checkbox-inline .am-icon-checked,
.am-radio-inline .am-icon-checked {
  opacity: 0;
}
.am-checkbox .am-icon-checked:before,
.am-checkbox-inline .am-icon-checked:before {
  content: "\f046";
}
.am-checkbox .am-icon-unchecked:before,
.am-checkbox-inline .am-icon-unchecked:before {
  content: "\f096";
}
.am-radio .am-icon-checked:before,
.am-radio-inline .am-icon-checked:before {
  content: "\f192";
}
.am-radio .am-icon-unchecked:before,
.am-radio-inline .am-icon-unchecked:before {
  content: "\f10c";
}
.am-ucheck-checkbox,
.am-ucheck-radio {
  position: absolute;
  left: 0;
  top: 0;
  margin: 0;
  padding: 0;
  width: 20px;
  height: 20px;
  opacity: 0;
  outline: none !important;
}
.am-ucheck-checkbox:hover:not(.am-nohover):not(:disabled) + .am-ucheck-icons,
.am-ucheck-radio:hover:not(.am-nohover):not(:disabled) + .am-ucheck-icons {
  color: #0e90d2;
}
.am-ucheck-checkbox:checked + .am-ucheck-icons,
.am-ucheck-radio:checked + .am-ucheck-icons {
  color: #0e90d2;
}
.am-ucheck-checkbox:checked + .am-ucheck-icons .am-icon-unchecked,
.am-ucheck-radio:checked + .am-ucheck-icons .am-icon-unchecked {
  opacity: 0;
}
.am-ucheck-checkbox:checked + .am-ucheck-icons .am-icon-checked,
.am-ucheck-radio:checked + .am-ucheck-icons .am-icon-checked {
  opacity: 1;
}
.am-ucheck-checkbox:disabled + .am-ucheck-icons,
.am-ucheck-radio:disabled + .am-ucheck-icons {
  cursor: default;
  color: #d8d8d8;
}
.am-ucheck-checkbox:disabled:checked + .am-ucheck-icons .am-icon-unchecked,
.am-ucheck-radio:disabled:checked + .am-ucheck-icons .am-icon-unchecked {
  opacity: 0;
}
.am-ucheck-checkbox:disabled:checked + .am-ucheck-icons .am-icon-checked,
.am-ucheck-radio:disabled:checked + .am-ucheck-icons .am-icon-checked {
  opacity: 1;
  color: #d8d8d8;
}
.am-checkbox.am-secondary .am-ucheck-checkbox:hover:not(.am-nohover):not(:disabled) + .am-ucheck-icons,
.am-radio.am-secondary .am-ucheck-checkbox:hover:not(.am-nohover):not(:disabled) + .am-ucheck-icons,
.am-checkbox-inline.am-secondary .am-ucheck-checkbox:hover:not(.am-nohover):not(:disabled) + .am-ucheck-icons,
.am-radio-inline.am-secondary .am-ucheck-checkbox:hover:not(.am-nohover):not(:disabled) + .am-ucheck-icons,
.am-checkbox.am-secondary .am-ucheck-radio:hover:not(.am-nohover):not(:disabled) + .am-ucheck-icons,
.am-radio.am-secondary .am-ucheck-radio:hover:not(.am-nohover):not(:disabled) + .am-ucheck-icons,
.am-checkbox-inline.am-secondary .am-ucheck-radio:hover:not(.am-nohover):not(:disabled) + .am-ucheck-icons,
.am-radio-inline.am-secondary .am-ucheck-radio:hover:not(.am-nohover):not(:disabled) + .am-ucheck-icons {
  color: #3bb4f2;
}
.am-checkbox.am-secondary .am-ucheck-checkbox:checked + .am-ucheck-icons,
.am-radio.am-secondary .am-ucheck-checkbox:checked + .am-ucheck-icons,
.am-checkbox-inline.am-secondary .am-ucheck-checkbox:checked + .am-ucheck-icons,
.am-radio-inline.am-secondary .am-ucheck-checkbox:checked + .am-ucheck-icons,
.am-checkbox.am-secondary .am-ucheck-radio:checked + .am-ucheck-icons,
.am-radio.am-secondary .am-ucheck-radio:checked + .am-ucheck-icons,
.am-checkbox-inline.am-secondary .am-ucheck-radio:checked + .am-ucheck-icons,
.am-radio-inline.am-secondary .am-ucheck-radio:checked + .am-ucheck-icons {
  color: #3bb4f2;
}
.am-checkbox.am-success .am-ucheck-checkbox:hover:not(.am-nohover):not(:disabled) + .am-ucheck-icons,
.am-radio.am-success .am-ucheck-checkbox:hover:not(.am-nohover):not(:disabled) + .am-ucheck-icons,
.am-checkbox-inline.am-success .am-ucheck-checkbox:hover:not(.am-nohover):not(:disabled) + .am-ucheck-icons,
.am-radio-inline.am-success .am-ucheck-checkbox:hover:not(.am-nohover):not(:disabled) + .am-ucheck-icons,
.am-checkbox.am-success .am-ucheck-radio:hover:not(.am-nohover):not(:disabled) + .am-ucheck-icons,
.am-radio.am-success .am-ucheck-radio:hover:not(.am-nohover):not(:disabled) + .am-ucheck-icons,
.am-checkbox-inline.am-success .am-ucheck-radio:hover:not(.am-nohover):not(:disabled) + .am-ucheck-icons,
.am-radio-inline.am-success .am-ucheck-radio:hover:not(.am-nohover):not(:disabled) + .am-ucheck-icons {
  color: #5eb95e;
}
.am-checkbox.am-success .am-ucheck-checkbox:checked + .am-ucheck-icons,
.am-radio.am-success .am-ucheck-checkbox:checked + .am-ucheck-icons,
.am-checkbox-inline.am-success .am-ucheck-checkbox:checked + .am-ucheck-icons,
.am-radio-inline.am-success .am-ucheck-checkbox:checked + .am-ucheck-icons,
.am-checkbox.am-success .am-ucheck-radio:checked + .am-ucheck-icons,
.am-radio.am-success .am-ucheck-radio:checked + .am-ucheck-icons,
.am-checkbox-inline.am-success .am-ucheck-radio:checked + .am-ucheck-icons,
.am-radio-inline.am-success .am-ucheck-radio:checked + .am-ucheck-icons {
  color: #5eb95e;
}
.am-checkbox.am-warning .am-ucheck-checkbox:hover:not(.am-nohover):not(:disabled) + .am-ucheck-icons,
.am-radio.am-warning .am-ucheck-checkbox:hover:not(.am-nohover):not(:disabled) + .am-ucheck-icons,
.am-checkbox-inline.am-warning .am-ucheck-checkbox:hover:not(.am-nohover):not(:disabled) + .am-ucheck-icons,
.am-radio-inline.am-warning .am-ucheck-checkbox:hover:not(.am-nohover):not(:disabled) + .am-ucheck-icons,
.am-checkbox.am-warning .am-ucheck-radio:hover:not(.am-nohover):not(:disabled) + .am-ucheck-icons,
.am-radio.am-warning .am-ucheck-radio:hover:not(.am-nohover):not(:disabled) + .am-ucheck-icons,
.am-checkbox-inline.am-warning .am-ucheck-radio:hover:not(.am-nohover):not(:disabled) + .am-ucheck-icons,
.am-radio-inline.am-warning .am-ucheck-radio:hover:not(.am-nohover):not(:disabled) + .am-ucheck-icons {
  color: #F37B1D;
}
.am-checkbox.am-warning .am-ucheck-checkbox:checked + .am-ucheck-icons,
.am-radio.am-warning .am-ucheck-checkbox:checked + .am-ucheck-icons,
.am-checkbox-inline.am-warning .am-ucheck-checkbox:checked + .am-ucheck-icons,
.am-radio-inline.am-warning .am-ucheck-checkbox:checked + .am-ucheck-icons,
.am-checkbox.am-warning .am-ucheck-radio:checked + .am-ucheck-icons,
.am-radio.am-warning .am-ucheck-radio:checked + .am-ucheck-icons,
.am-checkbox-inline.am-warning .am-ucheck-radio:checked + .am-ucheck-icons,
.am-radio-inline.am-warning .am-ucheck-radio:checked + .am-ucheck-icons {
  color: #F37B1D;
}
.am-checkbox.am-danger .am-ucheck-checkbox:hover:not(.am-nohover):not(:disabled) + .am-ucheck-icons,
.am-radio.am-danger .am-ucheck-checkbox:hover:not(.am-nohover):not(:disabled) + .am-ucheck-icons,
.am-checkbox-inline.am-danger .am-ucheck-checkbox:hover:not(.am-nohover):not(:disabled) + .am-ucheck-icons,
.am-radio-inline.am-danger .am-ucheck-checkbox:hover:not(.am-nohover):not(:disabled) + .am-ucheck-icons,
.am-checkbox.am-danger .am-ucheck-radio:hover:not(.am-nohover):not(:disabled) + .am-ucheck-icons,
.am-radio.am-danger .am-ucheck-radio:hover:not(.am-nohover):not(:disabled) + .am-ucheck-icons,
.am-checkbox-inline.am-danger .am-ucheck-radio:hover:not(.am-nohover):not(:disabled) + .am-ucheck-icons,
.am-radio-inline.am-danger .am-ucheck-radio:hover:not(.am-nohover):not(:disabled) + .am-ucheck-icons {
  color: #dd514c;
}
.am-checkbox.am-danger .am-ucheck-checkbox:checked + .am-ucheck-icons,
.am-radio.am-danger .am-ucheck-checkbox:checked + .am-ucheck-icons,
.am-checkbox-inline.am-danger .am-ucheck-checkbox:checked + .am-ucheck-icons,
.am-radio-inline.am-danger .am-ucheck-checkbox:checked + .am-ucheck-icons,
.am-checkbox.am-danger .am-ucheck-radio:checked + .am-ucheck-icons,
.am-radio.am-danger .am-ucheck-radio:checked + .am-ucheck-icons,
.am-checkbox-inline.am-danger .am-ucheck-radio:checked + .am-ucheck-icons,
.am-radio-inline.am-danger .am-ucheck-radio:checked + .am-ucheck-icons {
  color: #dd514c;
}
.am-field-error + .am-ucheck-icons {
  color: #dd514c;
}
.am-field-valid + .am-ucheck-icons {
  color: #5eb95e;
}
/*
// Group Addon
.@{ns}input-group-label {
  .@{ns}radio,
  .@{ns}checkbox {
    margin: -2px 0;
    padding-left: 15px;
  }
}

// Form inline style
.@{ns}form-inline .@{ns}checkbox,
.@{ns}form-inline .@{ns}radio {
  padding-left: 24px;
}
*/
/* ==========================================================================
   Component: Selected Plugin
 ============================================================================ */
.am-selected {
  width: 200px;
}
.am-selected-btn {
  width: 100%;
  padding-left: 10px;
  text-align: right;
}
.am-selected-btn.am-btn-default {
  background: none;
}
.am-invalid .am-selected-btn {
  border-color: #dd514c;
}
.am-selected-header {
  height: 45px;
  background-color: #f2f2f2;
  border-bottom: 1px solid #ddd;
  display: none;
}
.am-selected-status {
  text-align: left;
  width: 100%;
  display: block;
  word-wrap: normal;
  /* for IE */
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.am-selected-content {
  padding: 10px 0;
}
.am-selected-search {
  padding: 0 10px 10px;
}
.am-selected-search .am-form-field {
  padding: .5em;
}
.am-selected-list {
  margin: 0;
  padding: 0;
  list-style: none;
  font-size: 1.5rem;
}
.am-selected-list li {
  position: relative;
  cursor: pointer;
  padding: 5px 10px;
  -webkit-transition: background-color 0.15s;
  transition: background-color 0.15s;
}
.am-selected-list li:hover {
  background-color: #f8f8f8;
}
.am-selected-list li:hover .am-icon-check {
  opacity: .6;
}
.am-selected-list li.am-checked .am-icon-check {
  opacity: 1;
  color: #0e90d2;
}
.am-selected-list li.am-disabled {
  opacity: .5;
  pointer-events: none;
  cursor: not-allowed;
}
.am-selected-list .am-selected-list-header {
  margin-top: 8px;
  font-size: 1.3rem;
  color: #999999;
  border-bottom: 1px solid #e5e5e5;
  cursor: default;
}
.am-selected-list .am-selected-list-header:hover {
  background: none;
}
.am-selected-list .am-selected-list-header:first-child {
  margin-top: 0;
}
.am-selected-list .am-selected-text {
  display: block;
  word-wrap: normal;
  /* for IE */
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  margin-right: 30px;
}
.am-selected-list .am-icon-check {
  position: absolute;
  right: 8px;
  top: 5px;
  color: #999999;
  opacity: 0;
  -webkit-transition: opacity .15s;
  transition: opacity .15s;
}
.am-selected-hint {
  line-height: 1.2;
  color: #dd514c;
}
.am-selected-hint:not(:empty) {
  margin-top: 10px;
  border-top: 1px solid #e5e5e5;
  padding: 10px 10px 0;
}
.am-selected-placeholder {
  opacity: .65;
}
/* ==========================================================================
   Component: JS Plugins helpers
 ============================================================================ */
.am-fade {
  opacity: 0;
  -webkit-transition: opacity .2s linear;
  transition: opacity .2s linear;
}
.am-fade.am-in {
  opacity: 1;
}
.am-collapse {
  display: none;
}
.am-collapse.am-in {
  display: block;
}
tr.am-collapse.am-in {
  display: table-row;
}
tbody.am-collapse.am-in {
  display: table-row-group;
}
.am-collapsing {
  position: relative;
  height: 0;
  overflow: hidden;
  -webkit-transition: height .3s ease;
  transition: height .3s ease;
}
.am-sticky {
  position: fixed !important;
  z-index: 1010;
  -webkit-transform-origin: 0 0;
      -ms-transform-origin: 0 0;
          transform-origin: 0 0;
}
[data-am-sticky][class*="am-animation-"] {
  -webkit-animation-duration: .2s;
          animation-duration: .2s;
}
.am-dimmer-active {
  overflow: hidden;
}
.am-dimmer {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: none;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1100;
  opacity: 0;
}
.am-dimmer.am-active {
  opacity: 1;
}
[data-am-collapse] {
  cursor: pointer;
}
.am-datepicker {
  top: 0;
  left: 0;
  border-radius: 0;
  background: #fff;
  -webkit-box-shadow: 0 0 10px #ccc;
          box-shadow: 0 0 10px #ccc;
  padding-bottom: 10px;
  margin-top: 10px;
  width: 238px;
  color: #555;
  display: none;
}
.am-datepicker > div {
  display: none;
}
.am-datepicker table {
  width: 100%;
}
.am-datepicker tr.am-datepicker-header {
  font-size: 1.6rem;
  color: #fff;
  background: #3bb4f2;
}
.am-datepicker td,
.am-datepicker th {
  text-align: center;
  font-weight: normal;
  cursor: pointer;
}
.am-datepicker th {
  height: 48px;
}
.am-datepicker td {
  font-size: 1.4rem;
}
.am-datepicker td.am-datepicker-day {
  height: 34px;
  width: 34px;
}
.am-datepicker td.am-datepicker-day:hover {
  background: #F0F0F0;
  height: 34px;
  width: 34px;
}
.am-datepicker td.am-datepicker-day.am-disabled {
  cursor: no-drop;
  color: #999;
  background: #fafafa;
}
.am-datepicker td.am-datepicker-old,
.am-datepicker td.am-datepicker-new {
  color: #89d7ff;
}
.am-datepicker td.am-active,
.am-datepicker td.am-active:hover {
  border-radius: 0;
  color: #0084c7;
  background: #F0F0F0;
}
.am-datepicker td span {
  display: block;
  width: 79.33333333px;
  height: 40px;
  line-height: 40px;
  float: left;
  cursor: pointer;
}
.am-datepicker td span:hover {
  background: #F0F0F0;
}
.am-datepicker td span.am-active {
  color: #0084c7;
  background: #F0F0F0;
}
.am-datepicker td span.am-disabled {
  cursor: no-drop;
  color: #999;
  background: #fafafa;
}
.am-datepicker td span.am-datepicker-old {
  color: #89d7ff;
}
.am-datepicker .am-datepicker-dow {
  height: 40px;
  color: #0c80ba;
}
.am-datepicker-caret {
  display: block!important;
  display: inline-block;
  width: 0;
  height: 0;
  vertical-align: middle;
  border-bottom: 7px solid #3bb4f2;
  border-right: 7px solid transparent;
  border-left: 7px solid transparent;
  border-top: 0 dotted;
  -webkit-transform: rotate(360deg);
      -ms-transform: rotate(360deg);
          transform: rotate(360deg);
  position: absolute;
  top: -7px;
  left: 6px;
}
.am-datepicker-right .am-datepicker-caret {
  left: auto;
  right: 7px;
}
.am-datepicker-up .am-datepicker-caret {
  top: auto;
  bottom: -7px;
  display: inline-block;
  width: 0;
  height: 0;
  vertical-align: middle;
  border-top: 7px solid #fff;
  border-right: 7px solid transparent;
  border-left: 7px solid transparent;
  border-bottom: 0 dotted;
  -webkit-transform: rotate(360deg);
      -ms-transform: rotate(360deg);
          transform: rotate(360deg);
}
.am-datepicker-select {
  height: 34px;
  line-height: 34px;
  text-align: center;
  -webkit-transition: background-color 300ms ease-out;
  transition: background-color 300ms ease-out;
}
.am-datepicker-select:hover {
  background: rgba(154, 217, 248, 0.5);
  color: #0c80ba;
}
.am-datepicker-prev,
.am-datepicker-next {
  width: 34px;
  height: 34px;
}
.am-datepicker-prev-icon,
.am-datepicker-next-icon {
  width: 34px;
  height: 34px;
  line-height: 34px;
  display: inline-block;
  -webkit-transition: background-color 300ms ease-out;
  transition: background-color 300ms ease-out;
}
.am-datepicker-prev-icon:hover,
.am-datepicker-next-icon:hover {
  background: rgba(154, 217, 248, 0.5);
  color: #0c80ba;
}
.am-datepicker-prev-icon:before {
  display: inline-block;
  font: normal normal normal 1.6rem/1 "FontAwesome", sans-serif;
  /*font-weight: normal; // 2
  font-style: normal; // 2
  vertical-align: baseline; // 3
  line-height: 1; // 4*/
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-transform: translate(0, 0);
      -ms-transform: translate(0, 0);
          transform: translate(0, 0);
  content: "\f053";
}
.am-datepicker-next-icon:before {
  display: inline-block;
  font: normal normal normal 1.6rem/1 "FontAwesome", sans-serif;
  /*font-weight: normal; // 2
  font-style: normal; // 2
  vertical-align: baseline; // 3
  line-height: 1; // 4*/
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-transform: translate(0, 0);
      -ms-transform: translate(0, 0);
          transform: translate(0, 0);
  content: "\f054";
}
.am-datepicker-dropdown {
  position: absolute;
  z-index: 1120;
}
@media only screen and (max-width: 640px) {
  .am-datepicker {
    width: 100%;
  }
  .am-datepicker td span {
    width: 33.33%;
  }
  .am-datepicker-caret {
    display: none!important;
  }
  .am-datepicker-prev,
  .am-datepicker-next {
    width: 44px;
    height: 44px;
  }
}
.am-datepicker-success tr.am-datepicker-header {
  background: #5eb95e;
}
.am-datepicker-success td.am-datepicker-day.am-disabled {
  color: #999;
}
.am-datepicker-success td.am-datepicker-old,
.am-datepicker-success td.am-datepicker-new {
  color: #94df94;
}
.am-datepicker-success td.am-active,
.am-datepicker-success td.am-active:hover {
  color: #1b961b;
}
.am-datepicker-success td span.am-datepicker-old {
  color: #94df94;
}
.am-datepicker-success td span.am-active {
  color: #1b961b;
}
.am-datepicker-success .am-datepicker-caret {
  border-bottom-color: #5eb95e;
}
.am-datepicker-success .am-datepicker-dow {
  color: #367b36;
}
.am-datepicker-success .am-datepicker-select:hover,
.am-datepicker-success .am-datepicker-prev-icon:hover,
.am-datepicker-success .am-datepicker-next-icon:hover {
  background: rgba(165, 216, 165, 0.5);
  color: #367b36;
}
.am-datepicker-danger tr.am-datepicker-header {
  background: #dd514c;
}
.am-datepicker-danger td.am-datepicker-day.am-disabled {
  color: #999;
}
.am-datepicker-danger td.am-datepicker-old,
.am-datepicker-danger td.am-datepicker-new {
  color: #f59490;
}
.am-datepicker-danger td.am-active,
.am-datepicker-danger td.am-active:hover {
  color: #c10802;
}
.am-datepicker-danger td span.am-datepicker-old {
  color: #f59490;
}
.am-datepicker-danger td span.am-active {
  color: #c10802;
}
.am-datepicker-danger .am-datepicker-caret {
  border-bottom-color: #dd514c;
}
.am-datepicker-danger .am-datepicker-dow {
  color: #a4241f;
}
.am-datepicker-danger .am-datepicker-select:hover,
.am-datepicker-danger .am-datepicker-prev-icon:hover,
.am-datepicker-danger .am-datepicker-next-icon:hover {
  background: rgba(237, 164, 162, 0.5);
  color: #a4241f;
}
.am-datepicker-warning tr.am-datepicker-header {
  background: #F37B1D;
}
.am-datepicker-warning td.am-datepicker-day.am-disabled {
  color: #999;
}
.am-datepicker-warning td.am-datepicker-old,
.am-datepicker-warning td.am-datepicker-new {
  color: #ffad6d;
}
.am-datepicker-warning td.am-active,
.am-datepicker-warning td.am-active:hover {
  color: #aa4b00;
}
.am-datepicker-warning td span.am-datepicker-old {
  color: #ffad6d;
}
.am-datepicker-warning td span.am-active {
  color: #aa4b00;
}
.am-datepicker-warning .am-datepicker-caret {
  border-bottom-color: #F37B1D;
}
.am-datepicker-warning .am-datepicker-dow {
  color: #a14c09;
}
.am-datepicker-warning .am-datepicker-select:hover,
.am-datepicker-warning .am-datepicker-prev-icon:hover,
.am-datepicker-warning .am-datepicker-next-icon:hover {
  background: rgba(248, 180, 126, 0.5);
  color: #a14c09;
}
.am-datepicker > div {
  display: block;
}
.am-datepicker > div span.am-datepicker-hour {
  width: 59.5px;
}
.am-datepicker-date {
  display: block;
}
.am-datepicker-date.am-input-group {
  display: table;
}
.am-datepicker-time-box {
  padding: 30px 0 30px 0;
}
.am-datepicker-time-box strong {
  font-size: 5.2rem;
  display: inline-block;
  height: 70px;
  width: 70px;
  line-height: 70px;
  font-weight: normal;
}
.am-datepicker-time-box strong:hover {
  border-radius: 4px;
  background: #ECECEC;
}
.am-datepicker-time-box em {
  display: inline-block;
  height: 70px;
  width: 20px;
  line-height: 70px;
  font-size: 5.2rem;
  font-style: normal;
}
.am-datepicker-toggle {
  text-align: center;
  cursor: pointer;
  padding: 10px 0;
}
.am-datepicker-toggle:hover {
  background: #f0f0f0;
}
/* ==========================================================================
   Component: Print
 ============================================================================ */
@media print {
  *,
  *:before,
  *:after {
    background: transparent !important;
    color: #000 !important;
    /* Black prints faster: h5bp.com/s */
    -webkit-box-shadow: none !important;
            box-shadow: none !important;
    text-shadow: none !important;
  }
  a,
  a:visited {
    text-decoration: underline;
  }
  a[href]:after {
    content: " (" attr(href) ")";
  }
  abbr[title]:after {
    content: " [" attr(title) "] ";
  }
  /**
   * Don't show links that are fragment identifiers,
   * or use the `javascript:` pseudo protocol
   */
  a[href^="javascript:"]:after,
  a[href^="#"]:after {
    content: "";
  }
  pre,
  blockquote {
    border: 1px solid #999;
    page-break-inside: avoid;
  }
  thead {
    display: table-header-group;
    /* h5bp.com/t */
  }
  tr,
  img {
    page-break-inside: avoid;
  }
  img {
    max-width: 100% !important;
  }
  p,
  h2,
  h3 {
    orphans: 3;
    widows: 3;
  }
  h2,
  h3 {
    page-break-after: avoid;
  }
  @page {
    margin: 0.5cm;
  }
  select {
    background: #fff !important;
  }
  .am-topbar {
    display: none;
  }
  .am-table td,
  .am-table th {
    background-color: #fff !important;
  }
  .am-table {
    border-collapse: collapse !important;
  }
  .am-table-bordered th,
  .am-table-bordered td {
    border: 1px solid #ddd !important;
  }
}
/* print helper classes */
.am-print-block {
  display: none !important;
}
@media print {
  .am-print-block {
    display: block !important;
  }
}
.am-print-inline {
  display: none !important;
}
@media print {
  .am-print-inline {
    display: inline !important;
  }
}
.am-print-inline-block {
  display: none !important;
}
@media print {
  .am-print-inline-block {
    display: inline-block !important;
  }
}
@media print {
  .am-print-hide {
    display: none !important;
  }
}
.lte9 #nprogress .nprogress-spinner {
  display: none !important;
}
.lte8 .am-dimmer {
  background-color: #000;
  filter: alpha(opacity=60);
}
.lte8 .am-modal-actions {
  display: none;
}
.lte8 .am-modal-actions.am-modal-active {
  display: block;
}
.lte8 .am-offcanvas.am-active {
  background: #000;
}
.lte8 .am-popover .am-popover-caret {
  border: 8px solid transparent;
}
.lte8 .am-popover-top .am-popover-caret {
  border-top: 8px solid #333333;
  border-bottom: none;
}
.lte8 .am-popover-left .am-popover-caret {
  right: -8px;
  margin-top: -6px;
  border-left: 8px solid #333333;
  border-right: none;
}
.lte8 .am-popover-right .am-popover-caret {
  left: -8px;
  margin-top: -6px;
  border-right: 8px solid #333333;
  border-left: none;
}
.am-accordion-item {
  margin: 0;
}
.am-accordion-title {
  font-weight: normal;
  cursor: pointer;
}
.am-accordion-item.am-disabled .am-accordion-title {
  cursor: default;
  pointer-events: none;
}
.am-accordion-bd {
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
}
.am-accordion-content {
  margin-top: 0;
  padding: 0.8rem 1rem 1.2rem;
  font-size: 1.4rem;
}
/**
  * Accordion Theme: default
  * Author: Minwe (minwe@XXX)
  */
.am-accordion-default {
  margin: 1rem;
  border-radius: 2px;
  -webkit-box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);
          box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);
}
.am-accordion-default .am-accordion-item {
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}
.am-accordion-default .am-accordion-item:first-child {
  border-top: none;
}
.am-accordion-default .am-accordion-title {
  color: rgba(0, 0, 0, 0.6);
  -webkit-transition: background-color 0.2s ease-out;
  transition: background-color 0.2s ease-out;
  padding: .8rem 1rem;
}
.am-accordion-default .am-accordion-title:before {
  content: "\f0da";
  display: inline-block;
  font: normal normal normal 1.6rem/1 "FontAwesome", sans-serif;
  /*font-weight: normal; // 2
  font-style: normal; // 2
  vertical-align: baseline; // 3
  line-height: 1; // 4*/
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-transform: translate(0, 0);
      -ms-transform: translate(0, 0);
          transform: translate(0, 0);
  -webkit-transition: -webkit-transform .2s ease;
  transition: -webkit-transform .2s ease;
  transition: transform .2s ease;
  transition: transform .2s ease, -webkit-transform .2s ease;
  -webkit-transform: rotate(0deg);
      -ms-transform: rotate(0deg);
          transform: rotate(0deg);
  margin-right: 5px;
}
.am-accordion-default .am-accordion-title:hover {
  color: #0e90d2;
}
.am-accordion-default .am-accordion-content {
  color: #666;
}
.am-accordion-default .am-active .am-accordion-title {
  background-color: #eeeeee;
  color: #0e90d2;
}
.am-accordion-default .am-active .am-accordion-title:before {
  -webkit-transform: rotate(90deg);
      -ms-transform: rotate(90deg);
          transform: rotate(90deg);
}
/**
  * Accordion Theme: basic
  * Author: Minwe (minwe@XXX)
  */
.am-accordion-basic {
  margin: 1rem;
}
.am-accordion-basic .am-accordion-title {
  color: #333333;
  -webkit-transition: background-color 0.2s ease-out;
  transition: background-color 0.2s ease-out;
  padding: .8rem 0 0;
}
.am-accordion-basic .am-accordion-title:before {
  display: inline-block;
  font: normal normal normal 1.6rem/1 "FontAwesome", sans-serif;
  /*font-weight: normal; // 2
  font-style: normal; // 2
  vertical-align: baseline; // 3
  line-height: 1; // 4*/
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-transform: translate(0, 0);
      -ms-transform: translate(0, 0);
          transform: translate(0, 0);
  content: "\f0da";
  -webkit-transition: -webkit-transform .2s ease;
  transition: -webkit-transform .2s ease;
  transition: transform .2s ease;
  transition: transform .2s ease, -webkit-transform .2s ease;
  -webkit-transform: rotate(0deg);
      -ms-transform: rotate(0deg);
          transform: rotate(0deg);
  margin-right: .5rem;
}
.am-accordion-basic .am-accordion-content {
  color: #666;
}
.am-accordion-basic .am-active .am-accordion-title {
  color: #0e90d2;
}
.am-accordion-basic .am-active .am-accordion-title:before {
  -webkit-transform: rotate(90deg);
      -ms-transform: rotate(90deg);
          transform: rotate(90deg);
}
/**
  * Accordion Theme: gapped
  * Author: Minwe (minwe@XXX)
  */
.am-accordion-gapped {
  margin: .5rem 1rem;
}
.am-accordion-gapped .am-accordion-item {
  border: 1px solid #dedede;
  border-bottom: none;
  margin: .5rem 0;
}
.am-accordion-gapped .am-accordion-item.am-active {
  border-bottom: 1px solid #dedede;
}
.am-accordion-gapped .am-accordion-title {
  color: rgba(0, 0, 0, 0.6);
  -webkit-transition: background-color 0.15s ease-out;
  transition: background-color 0.15s ease-out;
  border-bottom: 1px solid #dedede;
  padding: 0.8rem 2rem 0.8rem 1rem;
  position: relative;
}
.am-accordion-gapped .am-accordion-title:after {
  display: inline-block;
  font: normal normal normal 1.6rem/1 "FontAwesome", sans-serif;
  /*font-weight: normal; // 2
  font-style: normal; // 2
  vertical-align: baseline; // 3
  line-height: 1; // 4*/
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-transform: translate(0, 0);
      -ms-transform: translate(0, 0);
          transform: translate(0, 0);
  content: "\f105";
  -webkit-transition: -webkit-transform .2s linear;
  transition: -webkit-transform .2s linear;
  transition: transform .2s linear;
  transition: transform .2s linear, -webkit-transform .2s linear;
  position: absolute;
  right: 10px;
  top: 50%;
  margin-top: -0.8rem;
}
.am-accordion-gapped .am-accordion-title:hover {
  color: rgba(0, 0, 0, 0.8);
}
.am-accordion-gapped .am-accordion-content {
  color: #666;
}
.am-accordion-gapped .am-active .am-accordion-title {
  background-color: #f5f5f5;
  color: rgba(0, 0, 0, 0.8);
}
.am-accordion-gapped .am-active .am-accordion-title:after {
  -webkit-transform: rotate(90deg);
      -ms-transform: rotate(90deg);
          transform: rotate(90deg);
}
.am-divider {
  height: 0;
  margin: 1.5rem auto;
  overflow: hidden;
  clear: both;
}
/**
  * Divider Theme: default
  */
.am-divider-default {
  border-top: 1px solid #ddd;
}
/**
  * Divider Theme: dotted
  */
.am-divider-dotted {
  border-top: 1px dotted #ccc;
}
/**
  * Divider Theme: dashed
  */
.am-divider-dashed {
  border-top: 1px dashed #ccc;
}
.am-figure-zoomable {
  position: relative;
  cursor: pointer;
}
.am-figure-zoomable:after {
  display: inline-block;
  font: normal normal normal 1.6rem/1 "FontAwesome", sans-serif;
  /*font-weight: normal; // 2
  font-style: normal; // 2
  vertical-align: baseline; // 3
  line-height: 1; // 4*/
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-transform: translate(0, 0);
      -ms-transform: translate(0, 0);
          transform: translate(0, 0);
  content: "\f00e";
  position: absolute;
  top: 1rem;
  right: 1rem;
  color: #999999;
  font-size: 1.6rem;
  -webkit-transition: all 0.2s;
  transition: all 0.2s;
  pointer-events: none;
}
.am-figure-zoomable:hover:after {
  color: #eeeeee;
}
.am-figure-default {
  margin: 10px;
}
.am-figure-default img {
  display: block;
  max-width: 100%;
  height: auto;
  padding: 2px;
  border: 1px solid #eeeeee;
  margin: 10px auto;
}
.am-figure-default figcaption {
  text-align: center;
  font-size: 1.4rem;
  margin-bottom: 15px;
  color: #333333;
}
.am-footer {
  text-align: center;
  padding: 1em 0;
  font-size: 1.6rem;
}
.am-footer .am-switch-mode-ysp {
  cursor: pointer;
}
.am-footer .am-footer-text {
  margin-top: 10px;
  font-size: 14px;
}
.am-footer .am-footer-text-left {
  text-align: left;
  padding-left: 10px;
}
.am-modal-footer-hd {
  padding-bottom: 10px;
}
/**
  * Footer Theme: default
  */
.am-footer-default {
  background-color: #fff;
}
.am-footer-default a {
  color: #555555;
}
.am-footer-default .am-footer-switch {
  margin-bottom: 10px;
  font-weight: bold;
}
.am-footer-default .am-footer-ysp {
  color: #555555;
  cursor: pointer;
}
.am-footer-default .am-footer-divider {
  color: #ccc;
}
.am-footer-default .am-footer-desktop {
  color: #0e90d2;
}
.am-footer-default .am-footer-miscs {
  color: #999999;
  font-size: 13px;
}
.am-footer-default .am-footer-miscs p {
  margin: 5px 0;
}
@media only screen and (min-width:641px) {
  .am-footer-default .am-footer-miscs p {
    display: inline-block;
    margin: 5px;
  }
}
.am-gallery {
  padding: 5px 5px 0 5px;
  list-style: none;
}
.am-gallery h3 {
  margin: 0;
}
[data-am-gallery*='pureview'] img {
  cursor: pointer;
}
/**
  * Gallery Theme: default
  * Author: Minwe (minwe@XXX)
  */
.am-gallery-default > li {
  padding: 5px;
}
.am-gallery-default .am-gallery-item img {
  width: 100%;
  height: auto;
}
.am-gallery-default .am-gallery-title {
  margin-top: 10px;
  font-weight: normal;
  font-size: 1.4rem;
  display: block;
  word-wrap: normal;
  /* for IE */
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  color: #555555;
}
.am-gallery-default .am-gallery-desc {
  color: #999999;
  font-size: 1.2rem;
}
/**
  * Gallery Theme: overlay
  * Author: Minwe (minwe@XXX)
  */
.am-gallery-overlay > li {
  padding: 5px;
}
.am-gallery-overlay .am-gallery-item {
  position: relative;
}
.am-gallery-overlay .am-gallery-item img {
  width: 100%;
  height: auto;
}
.am-gallery-overlay .am-gallery-title {
  font-weight: normal;
  font-size: 1.4rem;
  color: #FFF;
  position: absolute;
  bottom: 0;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  text-indent: 5px;
  height: 30px;
  line-height: 30px;
  display: block;
  word-wrap: normal;
  /* for IE */
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.am-gallery-overlay .am-gallery-desc {
  display: none;
}
/**
  * Accordion Theme: bordered
  * Author: Minwe (minwe@XXX)
  */
.am-gallery-bordered > li {
  padding: 5px;
}
.am-gallery-bordered .am-gallery-item {
  -webkit-box-shadow: 0 0 3px rgba(0, 0, 0, 0.35);
          box-shadow: 0 0 3px rgba(0, 0, 0, 0.35);
  padding: 5px;
}
.am-gallery-bordered .am-gallery-item img {
  width: 100%;
  height: auto;
}
.am-gallery-bordered .am-gallery-title {
  margin-top: 10px;
  font-weight: normal;
  font-size: 1.4rem;
  color: #555555;
  display: block;
  word-wrap: normal;
  /* for IE */
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.am-gallery-bordered .am-gallery-desc {
  color: #999999;
  font-size: 1.2rem;
}
/**
  * Gallery Theme: imgbordered
  * Author: Minwe (minwe@XXX)
  */
.am-gallery-imgbordered > li {
  padding: 5px;
}
.am-gallery-imgbordered .am-gallery-item img {
  width: 100%;
  height: auto;
  border: 3px solid #FFF;
  -webkit-box-shadow: 0 0 3px rgba(0, 0, 0, 0.35);
          box-shadow: 0 0 3px rgba(0, 0, 0, 0.35);
}
.am-gallery-imgbordered .am-gallery-title {
  margin-top: 10px;
  font-weight: normal;
  font-size: 1.4rem;
  color: #555555;
  display: block;
  word-wrap: normal;
  /* for IE */
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.am-gallery-imgbordered .am-gallery-desc {
  color: #999999;
  font-size: 1.2rem;
}
.am-gotop a {
  display: inline-block;
  text-decoration: none;
}
/**
  * Gotop Theme: default
  */
.am-gotop-default {
  text-align: center;
  margin: 10px 0;
}
.am-gotop-default a {
  background-color: #0e90d2;
  padding: .5em 1.5em;
  border-radius: 0;
  color: #fff;
}
.am-gotop-default a img {
  display: none;
}
/**
  * Gotop Theme: fixed
  */
.am-gotop-fixed {
  position: fixed;
  right: 10px;
  bottom: 10px;
  z-index: 1010;
  opacity: 0;
  width: 32px;
  min-height: 32px;
  overflow: hidden;
  border-radius: 0;
  text-align: center;
}
.am-gotop-fixed.am-active {
  opacity: .9;
}
.am-gotop-fixed.am-active:hover {
  opacity: 1;
}
.am-gotop-fixed a {
  display: block;
}
.am-gotop-fixed .am-gotop-title {
  display: none;
}
.am-gotop-fixed .am-gotop-icon-custom {
  display: inline-block;
  max-width: 30px;
  vertical-align: middle;
}
.am-gotop-fixed .am-gotop-icon {
  width: 100%;
  line-height: 32px;
  background-color: #555555;
  vertical-align: middle;
  color: #ddd;
}
.am-gotop-fixed .am-gotop-icon:hover {
  color: #fff;
}
.am-with-fixed-navbar .am-gotop-fixed {
  bottom: 60px;
}
.am-header {
  position: relative;
  width: 100%;
  height: 49px;
  line-height: 49px;
  padding: 0 10px;
}
.am-header h1 {
  margin-top: 0;
  margin-bottom: 0;
}
.am-header .am-header-title {
  margin: 0 30%;
  font-size: 2rem;
  font-weight: normal;
  text-align: center;
  display: block;
  word-wrap: normal;
  /* for IE */
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.am-header .am-header-title img {
  margin-top: 12px;
  height: 25px;
  vertical-align: top;
}
.am-header .am-header-nav {
  position: absolute;
  top: 0;
}
.am-header .am-header-nav img {
  height: 16px;
  width: auto;
  vertical-align: middle;
}
.am-header .am-header-left {
  left: 10px;
}
.am-header .am-header-right {
  right: 10px;
}
.am-header-fixed {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 1010;
}
.am-with-fixed-header {
  padding-top: 49px;
}
/**
  * Header Theme: default
  */
.am-header-default {
  background-color: #0e90d2;
}
.am-header-default .am-header-title {
  color: #fff;
}
.am-header-default .am-header-title a {
  color: #fff;
}
.am-header-default .am-header-icon {
  font-size: 20px;
}
.am-header-default .am-header-nav {
  color: #eeeeee;
}
.am-header-default .am-header-nav > a {
  display: inline-block;
  min-width: 36px;
  text-align: center;
  color: #eeeeee;
}
.am-header-default .am-header-nav > a + a {
  margin-left: 5px;
}
.am-header-default .am-header-nav .am-btn {
  margin-top: 9px;
  height: 31px;
  padding: 0 0.5em;
  line-height: 30px;
  font-size: 14px;
  vertical-align: top;
}
.am-header-default .am-header-nav .am-btn .am-header-icon {
  font-size: inherit;
}
.am-header-default .am-header-nav .am-btn-default {
  color: #999999;
}
.am-header-default .am-header-nav-title,
.am-header-default .am-header-nav-title + .am-header-icon {
  font-size: 14px;
}
.am-intro {
  position: relative;
}
.am-intro img {
  max-width: 100%;
}
.am-intro-hd {
  position: relative;
  height: 45px;
  line-height: 45px;
}
.am-intro-title {
  font-size: 18px;
  margin: 0;
  font-weight: bold;
}
.am-intro-more-top {
  position: absolute;
  right: 10px;
  top: 0;
  font-size: 1.4rem;
}
.am-intro-bd {
  padding-top: 15px;
  padding-bottom: 15px;
  font-size: 1.4rem;
}
.am-intro-bd p:last-child {
  margin-bottom: 0;
}
.am-intro-more-bottom {
  clear: both;
  text-align: center;
}
.am-intro-more-bottom .am-btn {
  font-size: 14px;
}
/**
  * Intro Theme: default
  */
.am-intro-default .am-intro-hd {
  background-color: #0e90d2;
  color: #fff;
  padding: 0 10px;
}
.am-intro-default .am-intro-hd a {
  color: #eee;
}
.am-intro-default .am-intro-right {
  padding-left: 0;
}
.am-list-news-hd {
  padding-top: 1.2rem;
  padding-bottom: 0.8rem;
}
.am-list-news-hd a {
  display: block;
}
.am-list-news-hd h2 {
  font-size: 1.6rem;
  float: left;
  margin: 0;
  height: 2rem;
  line-height: 2rem;
}
.am-list-news-hd h3 {
  margin: 0;
}
.am-list-news-hd .am-list-news-more {
  font-size: 1.3rem;
  height: 2rem;
  line-height: 2rem;
}
.am-list .am-list-item-dated a {
  padding-right: 80px;
  display: block;
  word-wrap: normal;
  /* for IE */
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.am-list .am-list-item-dated a::after {
  display: none;
}
.am-list .am-list-item-desced a,
.am-list .am-list-item-thumbed a {
  padding-right: 0;
}
.am-list-news .am-list-item-hd {
  margin: 0;
}
.am-list-date {
  position: absolute;
  right: 5px;
  font-size: 1.3rem;
  top: 1.3rem;
}
.am-list-item-desced {
  padding-bottom: 1rem;
}
.am-list-item-desced > a {
  padding: 1rem 0;
}
.am-list-item-desced .am-list-date {
  position: static;
}
.am-list-item-thumbed {
  padding-top: 1em;
}
.am-list-news-ft {
  text-align: center;
}
.am-list-news .am-titlebar {
  margin-left: 0;
  margin-right: 0;
}
.am-list-news .am-titlebar ~ .am-list-news-bd .am-list > li:first-child {
  border-top: none;
}
/**
  * list_news Theme: default
  */
.am-list-news-default {
  margin: 10px;
}
.am-list-news-default .am-g {
  margin-left: auto;
  margin-right: auto;
}
.am-list-news-default .am-list-item-hd {
  font-weight: normal;
}
.am-list-news-default .am-list-date {
  color: #999999;
}
.am-list-news-default .am-list > li {
  border-color: #dedede;
}
.am-list-news-default .am-list .am-list-item-desced {
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.am-list-news-default .am-list .am-list-item-desced > a {
  padding: 0;
}
.am-list-news-default .am-list .am-list-item-desced .am-list-item-text {
  margin-top: 0.5rem;
  color: #757575;
}
.am-list-news-default .am-list .am-list-item-text {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  line-height: 1.3em;
  -webkit-line-clamp: 2;
  /* number of lines to show */
  max-height: 2.6em;
}
.am-list-news-default .am-list .am-list-item-thumb-top .am-list-thumb {
  padding: 0;
  margin-bottom: 0.8rem;
}
.am-list-news-default .am-list .am-list-item-thumb-top .am-list-main {
  padding: 0;
}
.am-list-news-default .am-list .am-list-item-thumb-left .am-list-thumb {
  padding-left: 0;
}
.am-list-news-default .am-list .am-list-item-desced .am-list-main {
  padding: 0;
}
.am-list-news-default .am-list .am-list-item-thumb-right .am-list-thumb {
  padding-right: 0;
}
.am-list-news-default .am-list .am-list-item-thumb-bottom-left .am-list-item-hd {
  clear: both;
  padding-bottom: 0.5rem;
}
.am-list-news-default .am-list .am-list-item-thumb-bottom-left .am-list-thumb {
  padding-left: 0;
}
.am-list-news-default .am-list .am-list-item-thumb-bottom-right .am-list-item-hd {
  clear: both;
  padding-bottom: 0.5rem;
}
.am-list-news-default .am-list .am-list-item-thumb-bottom-right .am-list-thumb {
  padding-right: 0;
}
.am-list-news-default .am-list .am-list-thumb img {
  width: 100%;
  display: block;
}
@media only screen and (max-width: 640px) {
  .am-list-news-default .am-list-item-thumb-left .am-list-thumb,
  .am-list-news-default .am-list-item-thumb-right .am-list-thumb {
    max-height: 80px;
    overflow: hidden;
  }
  .am-list-news-default .am-list-item-thumb-bottom-left .am-list-item-text,
  .am-list-news-default .am-list-item-thumb-bottom-right .am-list-item-text {
    -webkit-line-clamp: 3;
    /* number of lines to show */
    max-height: 3.9em;
  }
  .am-list-news-default .am-list-item-thumb-bottom-left .am-list-thumb,
  .am-list-news-default .am-list-item-thumb-bottom-right .am-list-thumb {
    max-height: 60px;
    overflow: hidden;
  }
}
.am-map {
  width: 100%;
  height: 300px;
}
/**
  * Accordion Theme: default
  * Author: Hzp (hzp@XXX)
  */
.am-map-default #bd-map {
  width: 100%;
  height: 100%;
  overflow: hidden;
  margin: 0;
  font-size: 14px;
  line-height: 1.4!important;
}
.am-map-default .BMap_bubble_title {
  font-weight: bold;
}
.am-map-default #BMap_mask {
  width: 100%;
}
.am-mechat {
  margin: 1rem;
}
.am-mechat .section-cbox-wap .cbox-post-wap .post-action-wap .action-function-wap .function-list-wap .list-upload-wap .upload-mutual-wap {
  -webkit-box-sizing: content-box;
          box-sizing: content-box;
}
.am-menu {
  position: relative;
  padding: 0;
  margin: 0;
}
.am-menu ul {
  padding: 0;
  margin: 0;
}
.am-menu li {
  list-style: none;
}
.am-menu a:after,
.am-menu a:before {
  display: inline-block;
  font: normal normal normal 1.6rem/1 "FontAwesome", sans-serif;
  /*font-weight: normal; // 2
  font-style: normal; // 2
  vertical-align: baseline; // 3
  line-height: 1; // 4*/
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-transform: translate(0, 0);
      -ms-transform: translate(0, 0);
          transform: translate(0, 0);
}
.am-menu-sub {
  z-index: 1050;
}
.am-menu-toggle {
  display: none;
  z-index: 1015;
}
.am-menu-toggle img {
  display: inline-block;
  height: 16px;
  width: auto;
  vertical-align: middle;
}
.am-menu-nav a {
  display: block;
  padding: 0.8rem 0;
  -webkit-transition: all 0.45s;
  transition: all 0.45s;
}
/**
  * Menu Theme: default
  * Author: Minwe (minwe@XXX)
  */
.am-menu-default .am-menu-nav {
  padding-top: 8px;
  padding-bottom: 8px;
}
.am-menu-default .am-menu-nav a {
  text-align: center;
  height: 36px;
  line-height: 36px;
  display: block;
  word-wrap: normal;
  /* for IE */
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  padding: 0;
  color: #0e90d2;
}
.am-menu-default .am-menu-nav > .am-parent > a {
  position: relative;
  -webkit-transition: .15s;
  transition: .15s;
}
.am-menu-default .am-menu-nav > .am-parent > a:after {
  content: "\f107";
  margin-left: 5px;
  -webkit-transition: .15s;
  transition: .15s;
}
.am-menu-default .am-menu-nav > .am-parent > a:before {
  position: absolute;
  top: 100%;
  margin-top: -16px;
  left: 50%;
  margin-left: -12px;
  content: "\f0d8";
  display: none;
  color: #f1f1f1;
  font-size: 24px;
}
.am-menu-default .am-menu-nav > .am-parent.am-open > a {
  color: #095f8a;
}
.am-menu-default .am-menu-nav > .am-parent.am-open > a:before {
  display: block;
}
.am-menu-default .am-menu-nav > .am-parent.am-open > a:after {
  -webkit-transform: rotate(-180deg);
      -ms-transform: rotate(-180deg);
          transform: rotate(-180deg);
}
.am-menu-default .am-menu-sub {
  position: absolute;
  left: 5px;
  right: 5px;
  background-color: #f1f1f1;
  border-radius: 0;
  padding-top: 8px;
  padding-bottom: 8px;
}
.am-menu-default .am-menu-sub > li > a {
  color: #555555;
}
@media only screen and (min-width:641px) {
  .am-menu-default .am-menu-nav li {
    width: auto;
    float: left;
    clear: none;
    display: inline;
  }
  .am-menu-default .am-menu-nav a {
    padding-left: 1.5rem;
    padding-right: .5rem;
  }
}
/**
/**
  * Menu Theme: dropdown1
  * Author: Minwe (minwe@XXX)
  */
.am-menu-dropdown1 {
  position: relative;
  /*@media @medium-up {
    .am-menu-toggle {
      display: none!important;
    }

    .am-menu-nav {
      &.am-collapse {
        display: block;
      }

      .am-menu-sub {
        background-color: ;//@menu-sub-bg;
        display: block;
        & > li {
          clear: none;

        }
      }

      .am-menu-lv2 {
        & > li {
          clear: none;
          width: auto;
        }
      }
    }
  }*/
}
.am-menu-dropdown1 .am-menu-toggle {
  position: absolute;
  right: 5px;
  top: -47px;
  display: block;
  width: 44px;
  height: 44px;
  line-height: 44px;
  text-align: center;
  color: #fff;
}
.am-menu-dropdown1 a {
  -webkit-transition: all .4s;
  transition: all .4s;
  display: block;
  word-wrap: normal;
  /* for IE */
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.am-menu-dropdown1 .am-menu-nav {
  position: absolute;
  left: 0;
  right: 0;
  z-index: 1050;
}
.am-menu-dropdown1 .am-menu-nav a {
  padding: 0.8rem;
}
.am-menu-dropdown1 .am-menu-nav > li {
  width: 100%;
}
.am-menu-dropdown1 .am-menu-nav > li.am-parent > a {
  position: relative;
}
.am-menu-dropdown1 .am-menu-nav > li.am-parent > a::before {
  content: "\f067";
  position: absolute;
  right: 1rem;
  top: 1.4rem;
}
.am-menu-dropdown1 .am-menu-nav > li.am-parent.am-open > a {
  background-color: #0c80ba;
  border-bottom: none;
  color: #fff;
}
.am-menu-dropdown1 .am-menu-nav > li.am-parent.am-open > a:before {
  content: "\f068";
}
.am-menu-dropdown1 .am-menu-nav > li.am-parent.am-open > a:after {
  content: "";
  display: inline-block;
  width: 0;
  height: 0;
  vertical-align: middle;
  border-top: 8px solid #0c80ba;
  border-right: 8px solid transparent;
  border-left: 8px solid transparent;
  border-bottom: 0 dotted;
  -webkit-transform: rotate(360deg);
      -ms-transform: rotate(360deg);
          transform: rotate(360deg);
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -4px;
}
.am-menu-dropdown1 .am-menu-nav > li > a {
  border-bottom: 1px solid #0b76ac;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.05);
          box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.05);
  background-color: #0e90d2;
  color: #fff;
  height: 49px;
  line-height: 49px;
  padding: 0;
  text-indent: 10px;
}
.am-menu-dropdown1 .am-menu-sub {
  background-color: #fff;
}
.am-menu-dropdown1 .am-menu-sub a {
  color: #555;
  height: 44px;
  line-height: 44px;
  text-indent: 5px;
  padding: 0;
}
.am-menu-dropdown1 .am-menu-sub a:before {
  content: "\f105";
  color: #aaa;
  font-size: 16px;
  margin-right: 5px;
}
/**
  * Menu Theme: dropdown2
  * Author: Minwe (minwe@XXX)
  */
.am-menu-dropdown2 .am-menu-toggle {
  position: absolute;
  right: 5px;
  top: -47px;
  display: block;
  width: 44px;
  height: 44px;
  line-height: 44px;
  text-align: center;
  color: #fff;
}
.am-menu-dropdown2 .am-menu-nav {
  position: absolute;
  left: 0;
  right: 0;
  background-color: #f5f5f5;
  -webkit-box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
          box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  z-index: 1050;
  padding-top: 8px;
  padding-bottom: 8px;
}
.am-menu-dropdown2 .am-menu-nav a {
  height: 38px;
  line-height: 38px;
  padding: 0;
  text-align: center;
}
.am-menu-dropdown2 .am-menu-nav > li > a {
  color: #333333;
}
.am-menu-dropdown2 .am-menu-nav > li.am-parent > a {
  position: relative;
}
.am-menu-dropdown2 .am-menu-nav > li.am-parent > a:after {
  content: "\f107";
  margin-left: 5px;
  -webkit-transition: -webkit-transform .2s;
  transition: -webkit-transform .2s;
  transition: transform .2s;
  transition: transform .2s, -webkit-transform .2s;
}
.am-menu-dropdown2 .am-menu-nav > li.am-parent.am-open > a {
  position: relative;
}
.am-menu-dropdown2 .am-menu-nav > li.am-parent.am-open > a:after {
  color: #0e90d2;
  -webkit-transform: rotate(180deg);
      -ms-transform: rotate(180deg);
          transform: rotate(180deg);
}
.am-menu-dropdown2 .am-menu-nav > li.am-parent.am-open > a:before {
  position: absolute;
  top: 100%;
  margin-top: -16px;
  left: 50%;
  margin-left: -12px;
  font-size: 24px;
  content: "\f0d8";
  color: rgba(0, 0, 0, 0.2);
}
.am-menu-dropdown2 .am-menu-sub {
  position: absolute;
  left: 5px;
  right: 5px;
  padding: 8px 0;
  border-radius: 2px;
  -webkit-box-shadow: 0 0 3px rgba(0, 0, 0, 0.15);
          box-shadow: 0 0 3px rgba(0, 0, 0, 0.15);
  background-color: #fff;
  z-index: 1055;
}
.am-menu-dropdown2 .am-menu-sub a {
  padding: 0;
  height: 35px;
  color: #555555;
  line-height: 35px;
}
@media only screen and (min-width:641px) {
  .am-menu-dropdown2 .am-menu-toggle {
    display: none !important;
  }
  .am-menu-dropdown2 .am-menu-nav {
    position: static;
    display: block;
  }
  .am-menu-dropdown2 .am-menu-nav > li {
    float: none;
    width: auto;
    display: inline-block;
  }
  .am-menu-dropdown2 .am-menu-nav > li a {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
  .am-menu-dropdown2 .am-menu-sub {
    left: auto;
    right: auto;
  }
  .am-menu-dropdown2 .am-menu-sub > li {
    float: none;
    width: auto;
  }
  .am-menu-dropdown2 .am-menu-sub a {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}
/**
  * Menu Theme: slide1
  * Author: Minwe (minwe@XXX)
  */
.am-menu-slide1 .am-menu-toggle {
  position: absolute;
  right: 5px;
  top: -47px;
  display: block;
  width: 44px;
  height: 44px;
  line-height: 44px;
  text-align: center;
  color: #fff;
}
.am-menu-slide1 .am-menu-nav {
  background-color: #f5f5f5;
  padding-top: 8px;
  padding-bottom: 8px;
}
.am-menu-slide1 .am-menu-nav.am-in:before {
  display: inline-block;
  font: normal normal normal 1.6rem/1 "FontAwesome", sans-serif;
  /*font-weight: normal; // 2
  font-style: normal; // 2
  vertical-align: baseline; // 3
  line-height: 1; // 4*/
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-transform: translate(0, 0);
      -ms-transform: translate(0, 0);
          transform: translate(0, 0);
  content: "\f0d8";
  font-size: 24px;
  color: #f5f5f5;
  position: absolute;
  right: 16px;
  top: -16px;
}
.am-menu-slide1 .am-menu-nav a {
  line-height: 38px;
  height: 38px;
  display: block;
  padding: 0;
  text-align: center;
}
.am-menu-slide1 .am-menu-nav > li > a {
  color: #333333;
  display: block;
  word-wrap: normal;
  /* for IE */
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.am-menu-slide1 .am-menu-nav > .am-parent > a {
  position: relative;
  -webkit-transition: .15s;
  transition: .15s;
}
.am-menu-slide1 .am-menu-nav > .am-parent > a:after {
  content: "\f107";
  margin-left: 5px;
  -webkit-transition: .15s;
  transition: .15s;
}
.am-menu-slide1 .am-menu-nav > .am-parent > a:before {
  position: absolute;
  top: 100%;
  margin-top: -16px;
  left: 50%;
  margin-left: -12px;
  content: "\f0d8";
  display: none;
  color: #0e90d2;
  font-size: 24px;
}
.am-menu-slide1 .am-menu-nav > .am-parent.am-open > a {
  color: #0e90d2;
}
.am-menu-slide1 .am-menu-nav > .am-parent.am-open > a:before {
  display: block;
}
.am-menu-slide1 .am-menu-nav > .am-parent.am-open > a:after {
  -webkit-transform: rotate(-180deg);
      -ms-transform: rotate(-180deg);
          transform: rotate(-180deg);
}
.am-menu-slide1 .am-menu-sub {
  position: absolute;
  left: 5px;
  right: 5px;
  background-color: #0e90d2;
  border-radius: 0;
  padding-top: 8px;
  padding-bottom: 8px;
}
.am-menu-slide1 .am-menu-sub > li > a {
  color: #fff;
}
@media only screen and (min-width:641px) {
  .am-menu-slide1 .am-menu-toggle {
    display: none !important;
  }
  .am-menu-slide1 .am-menu-nav {
    background-color: #f5f5f5;
    display: block;
  }
  .am-menu-slide1 .am-menu-nav.am-in:before {
    display: none;
  }
  .am-menu-slide1 .am-menu-nav li {
    width: auto;
    clear: none;
  }
  .am-menu-slide1 .am-menu-nav li a {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}
/**
  * Menu Theme: offcanvas1
  * Author: Minwe (minwe@XXX)
  */
.am-menu-offcanvas1 .am-menu-toggle {
  position: absolute;
  right: 5px;
  top: -47px;
  display: block;
  width: 44px;
  height: 44px;
  line-height: 44px;
  text-align: center;
  color: #fff;
}
.am-menu-offcanvas1 .am-menu-nav {
  border-bottom: 1px solid rgba(0, 0, 0, 0.3);
  -webkit-box-shadow: 0 1px 0 rgba(255, 255, 255, 0.05);
          box-shadow: 0 1px 0 rgba(255, 255, 255, 0.05);
}
.am-menu-offcanvas1 .am-menu-nav > li > a {
  height: 44px;
  line-height: 44px;
  text-indent: 15px;
  padding: 0;
  position: relative;
  color: #ccc;
  border-top: 1px solid rgba(0, 0, 0, 0.3);
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.05);
          box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.05);
  text-shadow: 0 1px 0 rgba(0, 0, 0, 0.5);
}
.am-menu-offcanvas1 .am-menu-nav > .am-open > a,
.am-menu-offcanvas1 .am-menu-nav > li > a:hover,
.am-menu-offcanvas1 .am-menu-nav > li > a:focus {
  background-color: #474747;
  color: #fff;
  outline: none;
}
.am-menu-offcanvas1 .am-menu-nav > .am-active > a {
  background-color: #1a1a1a;
  color: #fff;
}
.am-menu-offcanvas1 .am-menu-nav > .am-parent > a {
  -webkit-transition: all .3s;
  transition: all .3s;
}
.am-menu-offcanvas1 .am-menu-nav > .am-parent > a:after {
  content: "\f104";
  position: absolute;
  right: 1.5rem;
  top: 1.3rem;
}
.am-menu-offcanvas1 .am-menu-nav > .am-parent.am-open > a:after {
  content: "\f107";
}
.am-menu-offcanvas1 .am-menu-sub {
  border-top: 1px solid rgba(0, 0, 0, 0.3);
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.05);
          box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.05);
  padding: 5px 0 5px 15px;
  background-color: #1a1a1a;
  font-size: 1.4rem;
}
.am-menu-offcanvas1 .am-menu-sub a {
  color: #eee;
}
.am-menu-offcanvas1 .am-menu-sub a:hover {
  color: #fff;
}
.am-menu-offcanvas1 .am-nav-divider {
  border-top: 1px solid #1a1a1a;
}
/**
  * Menu Theme: offcanvas2
  * Author: Minwe (minwe@XXX)
  */
.am-menu-offcanvas2 .am-menu-toggle {
  position: absolute;
  right: 5px;
  top: -47px;
  display: block;
  width: 44px;
  height: 44px;
  line-height: 44px;
  text-align: center;
  color: #fff;
}
.am-menu-offcanvas2 .am-menu-nav {
  padding: 10px 5px;
}
.am-menu-offcanvas2 .am-menu-nav > li {
  padding: 5px;
}
.am-menu-offcanvas2 .am-menu-nav > li > a {
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  background-color: #404040;
  color: #ccc;
  display: block;
  word-wrap: normal;
  /* for IE */
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.3);
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.05);
          box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.05);
  text-shadow: 0 1px 0 rgba(0, 0, 0, 0.5);
  height: 44px;
  line-height: 44px;
  padding: 0;
  text-align: center;
}
.am-menu-offcanvas2 .am-menu-nav > li > a:hover,
.am-menu-offcanvas2 .am-menu-nav > li > a:focus {
  background-color: #262626;
  color: #fff;
  outline: none;
}
.am-menu-offcanvas2 .am-menu-nav > .am-active > a {
  background-color: #262626;
  color: #fff;
}
/**
  * Menu Theme: stack
  * Author: Minwe (minwe@XXX)
  */
.am-menu-stack .am-menu-nav {
  border-bottom: 1px solid #dedede;
  -webkit-box-shadow: 0 1px 0 rgba(255, 255, 255, 0.05);
          box-shadow: 0 1px 0 rgba(255, 255, 255, 0.05);
}
.am-menu-stack .am-menu-nav > .am-parent > a {
  -webkit-transition: all .3s;
  transition: all .3s;
}
.am-menu-stack .am-menu-nav > .am-parent > a:after {
  content: "\f105";
  position: absolute;
  right: 1.5rem;
  top: 1.3rem;
  -webkit-transition: all .15s;
  transition: all .15s;
}
.am-menu-stack .am-menu-nav > .am-parent.am-open > a:after {
  -webkit-transform: rotate(90deg);
      -ms-transform: rotate(90deg);
          transform: rotate(90deg);
}
.am-menu-stack .am-menu-nav > li > a {
  position: relative;
  color: #333;
  background-color: #f5f5f5;
  border-top: 1px solid #dedede;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.05);
          box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.05);
  height: 49px;
  line-height: 49px;
  text-indent: 10px;
  padding: 0;
}
.am-menu-stack .am-menu-nav > .am-open > a,
.am-menu-stack .am-menu-nav > li > a:hover,
.am-menu-stack .am-menu-nav > li > a:focus {
  background-color: #e5e5e5;
  color: #222;
  outline: none;
}
.am-menu-stack .am-menu-sub {
  padding: 0;
  font-size: 1.4rem;
  border-top: 1px solid #dedede;
}
.am-menu-stack .am-menu-sub a {
  border-bottom: 1px solid #dedede;
  padding-left: 2rem;
  color: #444;
}
.am-menu-stack .am-menu-sub a:hover {
  color: #333;
}
.am-menu-stack .am-menu-sub li:last-child a {
  border-bottom: none;
}
.am-menu-stack .am-menu-sub > li > a {
  height: 44px;
  line-height: 44px;
  text-indent: 15px;
  padding: 0;
}
@media only screen and (min-width:641px) {
  .am-menu-stack .am-menu-nav {
    background-color: #f5f5f5;
  }
  .am-menu-stack .am-menu-nav > li {
    float: left;
    width: auto;
    clear: none !important;
    display: inline-block;
  }
  .am-menu-stack .am-menu-nav > li a {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
  .am-menu-stack .am-menu-nav > li.am-parent > a:after {
    position: static;
    content: "\f107";
  }
  .am-menu-stack .am-menu-nav > li.am-parent.am-open a {
    border-bottom: none;
  }
  .am-menu-stack .am-menu-nav > li.am-parent.am-open a:after {
    -webkit-transform: rotateX(-180deg);
            transform: rotateX(-180deg);
  }
  .am-menu-stack .am-menu-nav > li.am-parent.am-open .am-menu-sub {
    background-color: #e5e5e5;
  }
  .am-menu-stack .am-menu-sub {
    position: absolute;
    left: 0;
    right: 0;
    background-color: #ddd;
    border-top: none;
  }
  .am-menu-stack .am-menu-sub li {
    width: auto;
    float: left;
    clear: none;
  }
}
.am-navbar {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 49px;
  line-height: 49px;
  z-index: 1010;
}
.am-navbar ul {
  padding-left: 0;
  margin: 0;
  list-style: none;
  width: 100%;
}
.am-navbar .am-navbar-nav {
  padding-left: 8px;
  padding-right: 8px;
  text-align: center;
  overflow: hidden;
}
.am-navbar .am-navbar-nav li {
  display: table-cell;
  width: 1%;
  float: none;
}
.am-navbar-nav {
  position: relative;
  z-index: 1015;
}
.am-navbar-nav a {
  display: inline-block;
  width: 100%;
  height: 49px;
  line-height: 20px;
}
.am-navbar-nav a img {
  display: block;
  vertical-align: middle;
  height: 24px;
  width: 24px;
  margin: 4px auto 0;
}
.am-navbar-nav a [class*="am-icon"] {
  width: 24px;
  height: 24px;
  margin: 4px auto 0;
  display: block;
  line-height: 24px;
}
.am-navbar-nav a [class*="am-icon"]:before {
  font-size: 22px;
  vertical-align: middle;
}
.am-navbar-nav a .am-navbar-label {
  padding-top: 2px;
  line-height: 1;
  font-size: 12px;
  display: block;
  word-wrap: normal;
  /* for IE */
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.am-navbar-more [class*="am-icon-"] {
  -webkit-transition: 0.15s;
  transition: 0.15s;
}
.am-navbar-more.am-active [class*="am-icon-"] {
  -webkit-transform: rotateX(-180deg);
          transform: rotateX(-180deg);
}
.am-navbar-actions {
  position: absolute;
  bottom: 49px;
  right: 0;
  left: 0;
  z-index: 1009;
  opacity: 0;
  -webkit-transition: .3s;
  transition: .3s;
  -webkit-transform: translate(0, 100%);
      -ms-transform: translate(0, 100%);
          transform: translate(0, 100%);
}
.am-navbar-actions.am-active {
  opacity: 1;
  -webkit-transform: translate(0, 0);
      -ms-transform: translate(0, 0);
          transform: translate(0, 0);
}
.am-navbar-actions li {
  line-height: 42px;
  position: relative;
}
.am-navbar-actions li a {
  display: block;
  width: 100%;
  height: 40px;
  -webkit-box-shadow: inset 0 1px rgba(220, 220, 220, 0.25);
          box-shadow: inset 0 1px rgba(220, 220, 220, 0.25);
  padding-left: 20px;
  padding-right: 36px;
}
.am-navbar-actions li a :after {
  font-family: "FontAwesome", sans-serif;
  content: "\f105";
  display: inline-block;
  position: absolute;
  top: 0;
  right: 20px;
}
.am-navbar-actions li a img {
  vertical-align: middle;
  height: 20px;
  width: 20px;
  display: inline;
}
#am-navbar-qrcode {
  width: 220px;
  height: 220px;
  margin-left: -110px;
}
#am-navbar-qrcode .am-modal-bd {
  padding: 10px;
}
#am-navbar-qrcode canvas {
  display: block;
  width: 200px;
  height: 200px;
}
.am-with-fixed-navbar {
  padding-bottom: 54px;
}
/**
  * Navbar Theme: default
  * Author: hzp (hzp@XXX)
  */
.am-navbar-default a {
  color: #fff;
}
.am-navbar-default .am-navbar-nav {
  background-color: #0e90d2;
}
.am-navbar-default .am-navbar-actions {
  background-color: #0d86c4;
}
.am-navbar-default .am-navbar-actions a {
  border-bottom: 1px solid #0b6fa2;
}
.am-pagination {
  position: relative;
}
/**
  * Pagination Theme: default
  */
.am-pagination-default {
  margin-left: 10px;
  margin-right: 10px;
  font-size: 1.6rem;
}
.am-pagination-default .am-pagination-prev,
.am-pagination-default .am-pagination-next {
  float: none;
}
/**
  * Pagination Theme: select
  */
.am-pagination-select {
  margin-left: 10px;
  margin-right: 10px;
  font-size: 1.6rem;
}
.am-pagination-select > li > a {
  line-height: 36px;
  background-color: #eeeeee;
  padding: 0 15px;
  border: 0;
  color: #555555;
}
.am-pagination-select .am-pagination-select {
  position: absolute;
  top: 0;
  left: 50%;
  margin-left: -35px;
  width: 70px;
  height: 36px;
  text-align: center;
  border-radius: 0;
}
.am-pagination-select .am-pagination-select select {
  display: block;
  border: 0;
  line-height: 36px;
  width: 70px;
  height: 36px;
  border-radius: 0;
  color: #555555;
  background-color: #eeeeee;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  padding-left: 18px;
}
.am-paragraph p {
  margin: 10px 0;
}
.am-paragraph img {
  max-width: 100%;
}
.am-paragraph h1,
.am-paragraph h2,
.am-paragraph h3,
.am-paragraph h4,
.am-paragraph h5,
.am-paragraph h6 {
  color: #222222;
}
.am-paragraph table {
  max-width: none;
}
.am-paragraph-table-container {
  overflow: hidden;
  background: #eeeeee;
  max-width: none;
}
.am-paragraph-table-container table {
  width: 100%;
  max-width: none;
}
.am-paragraph-table-container table th {
  background: #bce5fb;
  height: 40px;
  border: 1px solid #999999;
  text-align: center;
}
.am-paragraph-table-container table td {
  border: 1px solid #999999;
  text-align: center;
  vertical-align: middle;
  background: #fff;
}
.am-paragraph-table-container table td p {
  text-indent: 0;
  font-size: 1.4rem;
}
.am-paragraph-table-container table td a {
  font-size: 1.4rem;
}
/**
  * Paragraph Theme: default
  */
.am-paragraph-default {
  margin: 0 10px;
  color: #333333;
  background-color: transparent;
}
.am-paragraph-default p {
  font-size: 1.4rem;
}
.am-paragraph-default img {
  max-width: 98%;
  display: block;
  margin: 5px auto;
  border: 1px solid #eeeeee;
  padding: 2px;
}
.am-paragraph-default a {
  color: #0e90d2;
}
/**
  * Slider Theme: a1
  */
.am-slider-a1 {
  -webkit-box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
          box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
  /* Control Nav */
}
.am-slider-a1 .am-viewport {
  max-height: 2000px;
  -webkit-transition: all 1s ease;
  transition: all 1s ease;
}
.loading .am-slider-a1 .am-viewport {
  max-height: 300px;
}
.am-slider-a1 .am-control-nav {
  width: 100%;
  position: absolute;
  bottom: 5px;
  text-align: center;
  line-height: 0;
}
.am-slider-a1 .am-control-nav li {
  margin: 0 6px;
  display: inline-block;
}
.am-slider-a1 .am-control-nav li a {
  width: 8px;
  height: 8px;
  display: block;
  background-color: rgba(0, 0, 0, 0.5);
  cursor: pointer;
  text-indent: -9999px;
  border-radius: 50%;
  -webkit-box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.3);
          box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.3);
}
.am-slider-a1 .am-control-nav li a:hover {
  background-color: rgba(0, 0, 0, 0.7);
}
.am-slider-a1 .am-control-nav li a.am-active {
  background-color: #0e90d2;
  cursor: default;
}
.am-slider-a1 .am-direction-nav,
.am-slider-a1 .am-pauseplay {
  display: none;
}
/**
  * Slider Theme: a2
  */
.am-slider-a2 {
  -webkit-box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
          box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
  /* Control Nav */
}
.am-slider-a2 .am-viewport {
  max-height: 2000px;
  -webkit-transition: all 1s ease;
  transition: all 1s ease;
}
.loading .am-slider-a2 .am-viewport {
  max-height: 300px;
}
.am-slider-a2 .am-control-nav {
  width: 100%;
  position: absolute;
  bottom: 5px;
  text-align: center;
  line-height: 0;
}
.am-slider-a2 .am-control-nav li {
  margin: 0 6px;
  display: inline-block;
}
.am-slider-a2 .am-control-nav li a {
  width: 8px;
  height: 8px;
  display: block;
  background-color: rgba(0, 0, 0, 0.5);
  cursor: pointer;
  text-indent: -9999px;
  -webkit-box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.3);
          box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.3);
}
.am-slider-a2 .am-control-nav li a:hover {
  background-color: rgba(0, 0, 0, 0.7);
}
.am-slider-a2 .am-control-nav li a.am-active {
  background: #0e93d7;
  cursor: default;
}
.am-slider-a2 .am-direction-nav,
.am-slider-a2 .am-pauseplay {
  display: none;
}
/**
  * Slider Theme: a3
  */
.am-slider-a3 {
  margin-bottom: 20px;
  -webkit-box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
          box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
  /* Control Nav */
}
.am-slider-a3 .am-viewport {
  max-height: 2000px;
  -webkit-transition: all 1s ease;
  transition: all 1s ease;
}
.loading .am-slider-a3 .am-viewport {
  max-height: 300px;
}
.am-slider-a3 .am-control-nav {
  width: 100%;
  position: absolute;
  bottom: -20px;
  text-align: center;
  height: 20px;
  background-color: #000;
  padding-top: 5px;
  line-height: 0;
}
.am-slider-a3 .am-control-nav li {
  margin: 0 6px;
  display: inline-block;
}
.am-slider-a3 .am-control-nav li a {
  width: 8px;
  height: 8px;
  display: block;
  background-color: rgba(0, 0, 0, 0.5);
  cursor: pointer;
  text-indent: -9999px;
  border-radius: 50%;
  -webkit-box-shadow: inset 0 0 3px rgba(200, 200, 200, 0.3);
          box-shadow: inset 0 0 3px rgba(200, 200, 200, 0.3);
}
.am-slider-a3 .am-control-nav li a:hover {
  background-color: rgba(0, 0, 0, 0.7);
}
.am-slider-a3 .am-control-nav li a.am-active {
  background: #0e90d2;
  cursor: default;
}
.am-slider-a3 .am-direction-nav,
.am-slider-a3 .am-pauseplay {
  display: none;
}
/**
  * Slider Theme: a4
  */
.am-slider-a4 {
  margin-bottom: 30px;
  -webkit-box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
          box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
  /* Control Nav */
}
.am-slider-a4 .am-viewport {
  max-height: 2000px;
  -webkit-transition: all 1s ease;
  transition: all 1s ease;
}
.loading .am-slider-a4 .am-viewport {
  max-height: 300px;
}
.am-slider-a4 .am-control-nav {
  width: 100%;
  position: absolute;
  bottom: -15px;
  text-align: center;
  line-height: 0;
}
.am-slider-a4 .am-control-nav li {
  margin: 0 6px;
  display: inline-block;
}
.am-slider-a4 .am-control-nav li a {
  width: 8px;
  height: 8px;
  display: block;
  background-color: rgba(0, 0, 0, 0.5);
  cursor: pointer;
  text-indent: -9999px;
  border-radius: 50%;
  -webkit-box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.3);
          box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.3);
}
.am-slider-a4 .am-control-nav li a:hover {
  background-color: rgba(0, 0, 0, 0.7);
}
.am-slider-a4 .am-control-nav li a.am-active {
  background-color: #0e90d2;
  cursor: default;
}
.am-slider-a4 .am-direction-nav,
.am-slider-a4 .am-pauseplay {
  display: none;
}
/**
  * Slider Theme: a5
  */
.am-slider-a5 {
  -webkit-box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
          box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
  /* Control Nav */
}
.am-slider-a5 .am-viewport {
  max-height: 2000px;
  -webkit-transition: all 1s ease;
  transition: all 1s ease;
}
.loading .am-slider-a5 .am-viewport {
  max-height: 300px;
}
.am-slider-a5 .am-control-nav {
  width: 100%;
  position: absolute;
  text-align: center;
  height: 6px;
  display: table;
  bottom: 0;
  font-size: 0;
  line-height: 0;
}
.am-slider-a5 .am-control-nav li {
  display: table-cell;
}
.am-slider-a5 .am-control-nav li a {
  width: 100%;
  height: 6px;
  display: block;
  background-color: rgba(0, 0, 0, 0.5);
  cursor: pointer;
  text-indent: -9999px;
}
.am-slider-a5 .am-control-nav li a:hover {
  background-color: rgba(0, 0, 0, 0.7);
}
.am-slider-a5 .am-control-nav li a.am-active {
  background-color: #0e90d2;
  cursor: default;
}
.am-slider-a5 .am-direction-nav,
.am-slider-a5 .am-pauseplay {
  display: none;
}
/**
  * Slider Theme: b1
  */
.am-slider-b1 {
  -webkit-box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
          box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
  /* Direction Nav */
}
.am-slider-b1 .am-viewport {
  max-height: 2000px;
  -webkit-transition: all 1s ease;
  transition: all 1s ease;
}
.loading .am-slider-b1 .am-viewport {
  max-height: 300px;
}
.am-slider-b1 .am-direction-nav a {
  -webkit-box-sizing: content-box;
          box-sizing: content-box;
  display: block;
  width: 24px;
  height: 24px;
  padding: 8px 0;
  margin: -20px 0 0;
  position: absolute;
  top: 50%;
  z-index: 10;
  overflow: hidden;
  opacity: 0.45;
  cursor: pointer;
  color: #fff;
  text-shadow: 1px 1px 0 rgba(255, 255, 255, 0.3);
  background-color: rgba(0, 0, 0, 0.5);
  font-size: 0;
  text-align: center;
  -webkit-transition: all .3s ease;
  transition: all .3s ease;
}
.am-slider-b1 .am-direction-nav a:before {
  display: inline-block;
  font: normal normal normal 1.6rem/1 "FontAwesome", sans-serif;
  /*font-weight: normal; // 2
  font-style: normal; // 2
  vertical-align: baseline; // 3
  line-height: 1; // 4*/
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-transform: translate(0, 0);
      -ms-transform: translate(0, 0);
          transform: translate(0, 0);
  content: "\f053";
  font-size: 24px;
}
.am-slider-b1 .am-direction-nav a.am-prev {
  left: 0;
  padding-right: 5px;
  border-bottom-right-radius: 5px;
  border-top-right-radius: 5px;
}
.am-slider-b1 .am-direction-nav a.am-next {
  right: 0;
  padding-left: 5px;
  border-bottom-left-radius: 5px;
  border-top-left-radius: 5px;
}
.am-slider-b1 .am-direction-nav a.am-next:before {
  content: "\f054";
}
.am-slider-b1 .am-direction-nav .am-disabled {
  opacity: 0!important;
  cursor: default;
}
.am-slider-b1:hover .am-prev {
  opacity: 0.7;
}
.am-slider-b1:hover .am-prev:hover {
  opacity: 1;
}
.am-slider-b1:hover .am-next {
  opacity: 0.7;
}
.am-slider-b1:hover .am-next:hover {
  opacity: 1;
}
.am-slider-b1 .am-control-nav,
.am-slider-b1 .am-pauseplay {
  display: none;
}
/**
  * Slider Theme: b2
  */
.am-slider-b2 {
  -webkit-box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
          box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
  /* Direction Nav */
}
.am-slider-b2 .am-viewport {
  max-height: 2000px;
  -webkit-transition: all 1s ease;
  transition: all 1s ease;
}
.loading .am-slider-b2 .am-viewport {
  max-height: 300px;
}
.am-slider-b2 .am-direction-nav a {
  -webkit-box-sizing: content-box;
          box-sizing: content-box;
  display: block;
  width: 24px;
  height: 24px;
  padding: 4px;
  margin: -16px 0 0;
  position: absolute;
  top: 50%;
  z-index: 10;
  overflow: hidden;
  opacity: 0.45;
  cursor: pointer;
  color: #fff;
  text-shadow: 1px 1px 0 rgba(255, 255, 255, 0.3);
  background-color: rgba(0, 0, 0, 0.5);
  font-size: 0;
  text-align: center;
  border-radius: 50%;
  -webkit-transition: all .3s ease;
  transition: all .3s ease;
}
.am-slider-b2 .am-direction-nav a:before {
  display: inline-block;
  font: normal normal normal 1.6rem/1 "FontAwesome", sans-serif;
  /*font-weight: normal; // 2
  font-style: normal; // 2
  vertical-align: baseline; // 3
  line-height: 1; // 4*/
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-transform: translate(0, 0);
      -ms-transform: translate(0, 0);
          transform: translate(0, 0);
  content: "\f053";
  font-size: 16px;
  line-height: 24px;
}
.am-slider-b2 .am-direction-nav a.am-prev {
  left: 5px;
}
.am-slider-b2 .am-direction-nav a.am-next {
  right: 5px;
}
.am-slider-b2 .am-direction-nav a.am-next:before {
  content: "\f054";
}
.am-slider-b2 .am-direction-nav .am-disabled {
  opacity: 0!important;
  cursor: default;
}
.am-slider-b2:hover .am-prev {
  opacity: 0.7;
}
.am-slider-b2:hover .am-prev:hover {
  opacity: 1;
}
.am-slider-b2:hover .am-next {
  opacity: 0.7;
}
.am-slider-b2:hover .am-next:hover {
  opacity: 1;
}
.am-slider-b2 .am-control-nav,
.am-slider-b2 .am-pauseplay {
  display: none;
}
/**
  * Slider Theme: b3
  */
.am-slider-b3 {
  margin: 15px 30px;
  -webkit-box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
          box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
  /* Direction Nav */
}
.am-slider-b3 .am-viewport {
  max-height: 2000px;
  -webkit-transition: all 1s ease;
  transition: all 1s ease;
}
.loading .am-slider-b3 .am-viewport {
  max-height: 300px;
}
.am-slider-b3 .am-direction-nav a {
  -webkit-box-sizing: content-box;
          box-sizing: content-box;
  display: block;
  width: 24px;
  height: 24px;
  padding: 4px;
  margin: -16px 0 0;
  position: absolute;
  top: 50%;
  z-index: 10;
  overflow: hidden;
  opacity: 0.45;
  cursor: pointer;
  color: #333333;
  text-shadow: 1px 1px 0 rgba(255, 255, 255, 0.3);
  font-size: 0;
  -webkit-transition: all .3s ease;
  transition: all .3s ease;
}
.am-slider-b3 .am-direction-nav a:before {
  display: inline-block;
  font: normal normal normal 1.6rem/1 "FontAwesome", sans-serif;
  /*font-weight: normal; // 2
  font-style: normal; // 2
  vertical-align: baseline; // 3
  line-height: 1; // 4*/
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-transform: translate(0, 0);
      -ms-transform: translate(0, 0);
          transform: translate(0, 0);
  content: "\f053";
  font-size: 24px;
}
.am-slider-b3 .am-direction-nav a.am-prev {
  left: -25px;
}
.am-slider-b3 .am-direction-nav a.am-next {
  right: -25px;
  text-align: right;
}
.am-slider-b3 .am-direction-nav a.am-next:before {
  content: "\f054";
}
.am-slider-b3 .am-direction-nav .am-disabled {
  opacity: 0!important;
  cursor: default;
}
.am-slider-b3:hover .am-prev {
  opacity: 0.7;
}
.am-slider-b3:hover .am-prev:hover {
  opacity: 1;
}
.am-slider-b3:hover .am-next {
  opacity: 0.7;
}
.am-slider-b3:hover .am-next:hover {
  opacity: 1;
}
.am-slider-b3 .am-control-nav,
.am-slider-b3 .am-pauseplay {
  display: none;
}
/**
  * Slider Theme: b4
  */
.am-slider-b4 {
  margin: 15px 20px;
  -webkit-box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
          box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
  /* Direction Nav */
}
.am-slider-b4 .am-viewport {
  max-height: 2000px;
  -webkit-transition: all 1s ease;
  transition: all 1s ease;
}
.loading .am-slider-b4 .am-viewport {
  max-height: 300px;
}
.am-slider-b4 .am-direction-nav a {
  position: absolute;
  top: 50%;
  z-index: 10;
  display: block;
  -webkit-box-sizing: content-box;
          box-sizing: content-box;
  width: 24px;
  height: 24px;
  margin: -16px 0 0;
  padding: 4px;
  overflow: hidden;
  opacity: 0.45;
  background-color: rgba(0, 0, 0, 0.8);
  cursor: pointer;
  text-shadow: 1px 1px 0 rgba(255, 255, 255, 0.3);
  font-size: 0;
  border-radius: 50%;
  text-align: center;
  color: #fff;
  -webkit-transition: all .3s ease;
  transition: all .3s ease;
}
.am-slider-b4 .am-direction-nav a:before {
  display: inline-block;
  font: normal normal normal 1.6rem/1 "FontAwesome", sans-serif;
  /*font-weight: normal; // 2
  font-style: normal; // 2
  vertical-align: baseline; // 3
  line-height: 1; // 4*/
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-transform: translate(0, 0);
      -ms-transform: translate(0, 0);
          transform: translate(0, 0);
  content: "\f053";
  font-size: 20px;
  line-height: 24px;
}
.am-slider-b4 .am-direction-nav a.am-prev {
  left: -15px;
}
.am-slider-b4 .am-direction-nav a.am-next {
  right: -15px;
}
.am-slider-b4 .am-direction-nav a.am-next:before {
  content: "\f054";
}
.am-slider-b4 .am-direction-nav .am-disabled {
  opacity: 0!important;
  cursor: default;
}
.am-slider-b4:hover .am-prev {
  opacity: 0.7;
}
.am-slider-b4:hover .am-prev:hover {
  opacity: 0.9;
}
.am-slider-b4:hover .am-next {
  opacity: 0.7;
}
.am-slider-b4:hover .am-next:hover {
  opacity: 0.9;
}
.am-slider-b4 .am-control-nav,
.am-slider-b4 .am-pauseplay {
  display: none;
}
/**
  * Slider Theme: c1
  */
.am-slider-c1 {
  -webkit-box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
          box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
  /* Control Nav */
}
.am-slider-c1 .am-viewport {
  max-height: 2000px;
  -webkit-transition: all 1s ease;
  transition: all 1s ease;
}
.loading .am-slider-c1 .am-viewport {
  max-height: 300px;
}
.am-slider-c1 .am-control-nav {
  position: absolute;
  bottom: 0;
  display: table;
  width: 100%;
  height: 6px;
  font-size: 0;
  line-height: 0;
  text-align: center;
}
.am-slider-c1 .am-control-nav li {
  display: table-cell;
  width: 1%;
}
.am-slider-c1 .am-control-nav li a {
  width: 100%;
  height: 6px;
  display: block;
  background-color: rgba(0, 0, 0, 0.7);
  cursor: pointer;
  text-indent: -9999px;
}
.am-slider-c1 .am-control-nav li a:hover {
  background: rgba(0, 0, 0, 0.8);
}
.am-slider-c1 .am-control-nav li a.am-active {
  background-color: #0e90d2;
  cursor: default;
}
.am-slider-c1 .am-slider-desc {
  background-color: rgba(0, 0, 0, 0.6);
  position: absolute;
  bottom: 6px;
  padding: 8px;
  width: 100%;
  color: #fff;
  display: block;
  word-wrap: normal;
  /* for IE */
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.am-slider-c1 .am-direction-nav,
.am-slider-c1 .am-pauseplay {
  display: none;
}
/**
  * Slider Theme: c2
  */
.am-slider-c2 {
  -webkit-box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
          box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
  /* Control Nav */
}
.am-slider-c2 .am-viewport {
  max-height: 2000px;
  -webkit-transition: all 1s ease;
  transition: all 1s ease;
}
.loading .am-slider-c2 .am-viewport {
  max-height: 300px;
}
.am-slider-c2 .am-control-nav {
  position: absolute;
  bottom: 15px;
  right: 0;
  height: 6px;
  text-align: center;
  font-size: 0;
  line-height: 0;
}
.am-slider-c2 .am-control-nav li {
  display: inline-block;
  margin-right: 6px;
}
.am-slider-c2 .am-control-nav li a {
  width: 6px;
  height: 6px;
  display: block;
  background-color: rgba(255, 255, 255, 0.4);
  cursor: pointer;
  text-indent: -9999px;
}
.am-slider-c2 .am-control-nav li a:hover {
  background: rgba(230, 230, 230, 0.4);
}
.am-slider-c2 .am-control-nav li a.am-active {
  background-color: #0e90d2;
  cursor: default;
}
.am-slider-c2 .am-slider-desc {
  background-color: rgba(0, 0, 0, 0.6);
  position: absolute;
  bottom: 0;
  padding: 8px 60px 8px 8px;
  width: 100%;
  color: #fff;
  display: block;
  word-wrap: normal;
  /* for IE */
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.am-slider-c2 .am-direction-nav,
.am-slider-c2 .am-pauseplay {
  display: none;
}
/**
  * Slider Theme: c3
  */
.am-slider-c3 {
  -webkit-box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
          box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
  /* Direction Nav */
}
.am-slider-c3 .am-viewport {
  max-height: 2000px;
  -webkit-transition: all 1s ease;
  transition: all 1s ease;
}
.loading .am-slider-c3 .am-viewport {
  max-height: 300px;
}
.am-slider-c3 .am-slider-desc {
  background-color: rgba(0, 0, 0, 0.6);
  position: absolute;
  bottom: 10px;
  right: 60px;
  height: 30px;
  left: 0;
  padding-right: 5px;
  color: #fff;
  display: block;
  word-wrap: normal;
  /* for IE */
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.am-slider-c3 .am-slider-counter {
  margin-right: 5px;
  display: inline-block;
  height: 30px;
  background-color: #0e90d2;
  width: 40px;
  text-align: center;
  line-height: 30px;
  color: #eee;
  font-size: 1rem;
}
.am-slider-c3 .am-slider-counter .am-active {
  font-size: 1.8rem;
  font-weight: bold;
  color: #fff;
}
.am-slider-c3 .am-direction-nav a {
  -webkit-box-sizing: content-box;
          box-sizing: content-box;
  display: block;
  width: 24px;
  height: 24px;
  padding: 4px 0;
  margin: -16px 0 0;
  position: absolute;
  top: 50%;
  z-index: 10;
  overflow: hidden;
  opacity: 0.45;
  cursor: pointer;
  color: #fff;
  text-shadow: 1px 1px 0 rgba(255, 255, 255, 0.3);
  background-color: rgba(0, 0, 0, 0.5);
  font-size: 0;
  text-align: center;
  -webkit-transition: all .3s ease;
  transition: all .3s ease;
}
.am-slider-c3 .am-direction-nav a:before {
  display: inline-block;
  font: normal normal normal 1.6rem/1 "FontAwesome", sans-serif;
  /*font-weight: normal; // 2
  font-style: normal; // 2
  vertical-align: baseline; // 3
  line-height: 1; // 4*/
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-transform: translate(0, 0);
      -ms-transform: translate(0, 0);
          transform: translate(0, 0);
  content: "\f053";
  font-size: 16px;
  line-height: 24px;
}
.am-slider-c3 .am-direction-nav a.am-prev {
  left: 0;
  padding-right: 5px;
}
.am-slider-c3 .am-direction-nav a.am-next {
  right: 0;
  padding-left: 5px;
}
.am-slider-c3 .am-direction-nav a.am-next:before {
  content: "\f054";
}
.am-slider-c3 .am-direction-nav .am-disabled {
  opacity: 0!important;
  cursor: default;
}
.am-slider-c3:hover .am-prev {
  opacity: 0.7;
}
.am-slider-c3:hover .am-prev:hover {
  opacity: 1;
}
.am-slider-c3:hover .am-next {
  opacity: 0.7;
}
.am-slider-c3:hover .am-next:hover {
  opacity: 1;
}
.am-slider-c3 .am-control-nav,
.am-slider-c3 .am-pauseplay {
  display: none;
}
/**
  * Slider Theme: c4
  */
.am-slider-c4 {
  -webkit-box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
          box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
  /* Direction Nav */
}
.am-slider-c4 .am-viewport {
  max-height: 2000px;
  -webkit-transition: all 1s ease;
  transition: all 1s ease;
}
.loading .am-slider-c4 .am-viewport {
  max-height: 300px;
}
.am-slider-c4 .am-slider-desc {
  width: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  position: absolute;
  bottom: 0;
  right: 0;
  left: 0;
  padding: 8px 40px;
  color: #fff;
  display: block;
  word-wrap: normal;
  /* for IE */
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.am-slider-c4 .am-direction-nav a {
  -webkit-box-sizing: content-box;
          box-sizing: content-box;
  display: block;
  width: 24px;
  height: 24px;
  padding: 4px 0;
  margin: 0;
  position: absolute;
  bottom: 4px;
  z-index: 10;
  overflow: hidden;
  opacity: 0.45;
  cursor: pointer;
  text-shadow: 1px 1px 0 rgba(255, 255, 255, 0.3);
  font-size: 0;
  text-align: center;
  color: rgba(0, 0, 0, 0.7);
  -webkit-transition: all .3s ease;
  transition: all .3s ease;
}
.am-slider-c4 .am-direction-nav a:before {
  display: inline-block;
  font: normal normal normal 1.6rem/1 "FontAwesome", sans-serif;
  /*font-weight: normal; // 2
  font-style: normal; // 2
  vertical-align: baseline; // 3
  line-height: 1; // 4*/
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-transform: translate(0, 0);
      -ms-transform: translate(0, 0);
          transform: translate(0, 0);
  content: "\f053";
  font-size: 24px;
}
.am-slider-c4 .am-direction-nav a.am-prev {
  left: 0;
  padding-right: 5px;
}
.am-slider-c4 .am-direction-nav a.am-next {
  right: 0;
  padding-left: 5px;
}
.am-slider-c4 .am-direction-nav a.am-next:before {
  content: "\f054";
}
.am-slider-c4 .am-direction-nav .am-disabled {
  opacity: 0!important;
  cursor: default;
}
.am-slider-c4:hover .am-prev {
  opacity: 0.7;
}
.am-slider-c4:hover .am-prev:hover {
  opacity: 1;
}
.am-slider-c4:hover .am-next {
  opacity: 0.7;
}
.am-slider-c4:hover .am-next:hover {
  opacity: 1;
}
.am-slider-c4 .am-control-nav,
.am-slider-c4 .am-pauseplay {
  display: none;
}
/**
  * Slider Theme: d1
  */
.am-slider-d1 {
  -webkit-box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
          box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
  /* Direction Nav */
}
.am-slider-d1 .am-viewport {
  max-height: 2000px;
  -webkit-transition: all 1s ease;
  transition: all 1s ease;
}
.loading .am-slider-d1 .am-viewport {
  max-height: 300px;
}
.am-slider-d1 .am-slider-desc {
  padding: 8px 35px;
  width: 100%;
  color: #fff;
  background-color: #0e90d2;
}
.am-slider-d1 .am-slider-title {
  font-weight: normal;
  margin-bottom: 2px;
  display: block;
  word-wrap: normal;
  /* for IE */
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.am-slider-d1 .am-slider-more {
  color: #eeeeee;
  font-size: 1.3rem;
}
.am-slider-d1 .am-direction-nav a {
  -webkit-box-sizing: content-box;
          box-sizing: content-box;
  display: block;
  width: 24px;
  height: 24px;
  margin: 0;
  position: absolute;
  bottom: 18px;
  z-index: 10;
  overflow: hidden;
  opacity: 0.45;
  cursor: pointer;
  text-shadow: 1px 1px 0 rgba(255, 255, 255, 0.3);
  font-size: 0;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.9);
  color: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  -webkit-transition: all 03s ease;
  transition: all 03s ease;
}
.am-slider-d1 .am-direction-nav a:before {
  display: inline-block;
  font: normal normal normal 1.6rem/1 "FontAwesome", sans-serif;
  /*font-weight: normal; // 2
  font-style: normal; // 2
  vertical-align: baseline; // 3
  line-height: 1; // 4*/
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-transform: translate(0, 0);
      -ms-transform: translate(0, 0);
          transform: translate(0, 0);
  content: "\f053";
  font-size: 16px;
  line-height: 24px;
}
.am-slider-d1 .am-direction-nav a.am-prev {
  left: 5px;
}
.am-slider-d1 .am-direction-nav a.am-next {
  right: 5px;
}
.am-slider-d1 .am-direction-nav a.am-next:before {
  content: "\f054";
}
.am-slider-d1 .am-direction-nav .am-disabled {
  opacity: 0!important;
  cursor: default;
}
.am-slider-d1:hover .am-prev {
  opacity: 0.7;
}
.am-slider-d1:hover .am-prev:hover {
  opacity: 1;
}
.am-slider-d1:hover .am-next {
  opacity: 0.7;
}
.am-slider-d1:hover .am-next:hover {
  opacity: 1;
}
.am-slider-d1 .am-control-nav,
.am-slider-d1 .am-pauseplay {
  display: none;
}
/**
  * Slider Theme: d2
  */
.am-slider-d2 {
  margin-bottom: 20px;
  -webkit-box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
          box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
  /* Control Nav */
}
.am-slider-d2 .am-viewport {
  max-height: 2000px;
  -webkit-transition: all 1s ease;
  transition: all 1s ease;
}
.loading .am-slider-d2 .am-viewport {
  max-height: 300px;
}
.am-slider-d2 .am-slider-desc {
  position: absolute;
  left: 10px;
  bottom: 20px;
  right: 50px;
  color: #fff;
}
.am-slider-d2 .am-slider-content {
  background-color: rgba(0, 0, 0, 0.7);
  padding: 10px 6px;
  margin-bottom: 10px;
}
.am-slider-d2 .am-slider-content p {
  margin: 0;
  display: block;
  word-wrap: normal;
  /* for IE */
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  font-size: 1.4rem;
}
.am-slider-d2 .am-slider-title {
  font-weight: normal;
  margin-bottom: 5px;
  display: block;
  word-wrap: normal;
  /* for IE */
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.am-slider-d2 .am-slider-more {
  color: #eeeeee;
  font-size: 1.3rem;
  background-color: #0e90d2;
  padding: 2px 10px;
}
.am-slider-d2 .am-control-nav {
  width: 100%;
  position: absolute;
  bottom: -15px;
  text-align: center;
}
.am-slider-d2 .am-control-nav li {
  margin: 0 6px;
  display: inline-block;
}
.am-slider-d2 .am-control-nav li a {
  width: 8px;
  height: 8px;
  display: block;
  background-color: rgba(0, 0, 0, 0.5);
  cursor: pointer;
  text-indent: -9999px;
  border-radius: 50%;
  font-size: 0;
  line-height: 0;
  -webkit-box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.3);
          box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.3);
}
.am-slider-d2 .am-control-nav li a:hover {
  background: rgba(0, 0, 0, 0.5);
}
.am-slider-d2 .am-control-nav li a.am-active {
  background: #0e90d2;
  cursor: default;
}
.am-slider-d2 .am-direction-nav,
.am-slider-d2 .am-pauseplay {
  display: none;
}
/**
  * Slider Theme: d3
  */
.am-slider-d3 {
  margin-bottom: 10px;
  -webkit-box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
          box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
  /* Control Nav */
}
.am-slider-d3 .am-viewport {
  max-height: 2000px;
  -webkit-transition: all 1s ease;
  transition: all 1s ease;
}
.loading .am-slider-d3 .am-viewport {
  max-height: 300px;
}
.am-slider-d3 .am-slider-desc {
  position: absolute;
  bottom: 0;
  color: #fff;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  padding: 8px 5px;
}
.am-slider-d3 .am-slider-desc p {
  margin: 0;
  font-size: 1.3rem;
  display: block;
  word-wrap: normal;
  /* for IE */
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.am-slider-d3 .am-slider-title {
  font-weight: normal;
  margin-bottom: 5px;
  display: block;
  word-wrap: normal;
  /* for IE */
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.am-slider-d3 .am-control-thumbs {
  position: static;
  overflow: hidden;
}
.am-slider-d3 .am-control-thumbs li {
  padding: 12px 4px 4px;
  position: relative;
}
.am-slider-d3 .am-control-thumbs img {
  width: 100%;
  display: block;
  opacity: .85;
  cursor: pointer;
}
.am-slider-d3 .am-control-thumbs img:hover {
  opacity: 1;
}
.am-slider-d3 .am-control-thumbs .am-active {
  opacity: 1;
  cursor: default;
}
.am-slider-d3 .am-control-thumbs .am-active + i {
  position: absolute;
  top: 0;
  left: 50%;
  content: "";
  display: inline-block;
  width: 0;
  height: 0;
  vertical-align: middle;
  border-top: 8px solid rgba(0, 0, 0, 0.7);
  border-right: 8px solid transparent;
  border-left: 8px solid transparent;
  border-bottom: 0 dotted;
  -webkit-transform: rotate(360deg);
      -ms-transform: rotate(360deg);
          transform: rotate(360deg);
  margin-left: -4px;
  -webkit-transition: all 0.2s;
  transition: all 0.2s;
}
.am-slider-d3 .am-direction-nav,
.am-slider-d3 .am-pauseplay {
  display: none;
}
.am-slider-d3 .am-control-thumbs {
  display: table;
}
.am-slider-d3 .am-control-thumbs li {
  display: table-cell;
  width: 1%;
}
[data-am-widget='tabs'] {
  margin: 10px;
}
[data-am-widget='tabs'] .am-tabs-nav {
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}
[data-am-widget='tabs'] .am-tabs-nav li {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
      -ms-flex: 1;
          flex: 1;
}
[data-am-widget='tabs'] .am-tabs-nav a {
  display: block;
  word-wrap: normal;
  /* for IE */
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
/**
  * Tabs Theme: default
  */
.am-tabs-default .am-tabs-nav {
  line-height: 40px;
  background-color: #eeeeee;
}
.am-tabs-default .am-tabs-nav a {
  color: #222222;
  line-height: 42px;
}
.am-tabs-default .am-tabs-nav > .am-active a {
  background-color: #0e90d2;
  color: #fff;
}
/**
  * Tabs Theme: d2
  */
.am-tabs-d2 .am-tabs-nav {
  background-color: #eeeeee;
}
.am-tabs-d2 .am-tabs-nav li {
  height: 42px;
}
.am-tabs-d2 .am-tabs-nav a {
  color: #222222;
  line-height: 42px;
}
.am-tabs-d2 .am-tabs-nav > .am-active {
  position: relative;
  background-color: #fcfcfc;
  border-bottom: 2px solid #0e90d2;
}
.am-tabs-d2 .am-tabs-nav > .am-active a {
  line-height: 40px;
  color: #0e90d2;
}
.am-tabs-d2 .am-tabs-nav > .am-active:after {
  position: absolute;
  width: 0;
  height: 0;
  bottom: 0px;
  left: 50%;
  margin-left: -5px;
  border: 6px rgba(0, 0, 0, 0) solid;
  content: "";
  z-index: 1;
  border-bottom-color: #0e90d2;
}
.am-titlebar {
  margin-top: 20px;
  height: 45px;
  font-size: 100%;
}
.am-titlebar h2 {
  margin-top: 0;
  margin-bottom: 0;
  font-size: 1.6rem;
}
.am-titlebar .am-titlebar-title img {
  height: 24px;
  width: auto;
}
/**
  * Titlebar Theme: default
  */
.am-titlebar-default {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  margin-left: 10px;
  margin-right: 10px;
  background-color: transparent;
  border-bottom: 1px solid #dedede;
  line-height: 44px;
}
.am-titlebar-default a {
  color: #0e90d2;
}
.am-titlebar-default .am-titlebar-title {
  position: relative;
  padding-left: 12px;
  color: #0e90d2;
  font-size: 1.8rem;
  text-align: left;
  font-weight: bold;
}
.am-titlebar-default .am-titlebar-title:before {
  content: "";
  position: absolute;
  left: 2px;
  top: 8px;
  bottom: 8px;
  border-left: 3px solid #0e90d2;
}
.am-titlebar-default .am-titlebar-nav {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
      -ms-flex: 1;
          flex: 1;
  text-align: right;
}
.am-titlebar-default .am-titlebar-nav a {
  margin-right: 10px;
}
.am-titlebar-default .am-titlebar-nav a:last-child {
  margin-right: 5px;
}
/**
  * Titlebar Theme: multi
  */
.am-titlebar-multi {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  background-color: #f5f5f5;
  border-top: 2px solid #3bb4f2;
  border-bottom: 1px solid #e8e8e8;
}
.am-titlebar-multi a {
  color: #0e90d2;
}
.am-titlebar-multi .am-titlebar-title {
  padding-left: 10px;
  color: #0e90d2;
  font-size: 1.8rem;
  text-align: left;
  font-weight: bold;
  line-height: 42px;
}
.am-titlebar-multi .am-titlebar-nav {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
      -ms-flex: 1;
          flex: 1;
  text-align: right;
  line-height: 42px;
}
.am-titlebar-multi .am-titlebar-nav a {
  margin-right: 10px;
}
/**
  * Titlebar Theme: cols
  */
.am-titlebar-cols {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding-left: 10px;
  background-color: #f5f5f5;
  color: #555555;
  font-size: 18px;
  border-top: 2px solid #e1e1e1;
  line-height: 41px;
}
.am-titlebar-cols a {
  color: #555555;
}
.am-titlebar-cols .am-titlebar-title {
  color: #0e90d2;
  margin-right: 15px;
  border-bottom: 2px solid #0e90d2;
  font-weight: bold;
}
.am-titlebar-cols .am-titlebar-title a {
  color: #0e90d2;
}
.am-titlebar-cols .am-titlebar-nav {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
      -ms-flex: 1;
          flex: 1;
}
.am-titlebar-cols .am-titlebar-nav a {
  display: inline-block;
  margin-right: 15px;
  line-height: 41px;
  border-bottom: 2px solid transparent;
}
.am-titlebar-cols .am-titlebar-nav a:hover {
  color: #3c3c3c;
  border-bottom-color: #0e90d2;
}
.am-titlebar-cols .am-titlebar-nav a:last-child {
  margin-right: 10px;
}
.am-wechatpay .am-wechatpay-btn {
  margin-top: 1rem;
  margin-bottom: 1rem;
}
