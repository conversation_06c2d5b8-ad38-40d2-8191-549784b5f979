# <font style="color:rgb(2, 8, 23);">镜像仓库</font>
---
## <font style="color:rgb(2, 8, 23);">1. 服务选择建议</font>
1. **<font style="color:rgb(2, 8, 23);">阿里云容器镜像服务（ACR）</font>**
+ **<font style="color:rgb(2, 8, 23);">功能特性</font>**<font style="color:rgb(2, 8, 23);">：支持镜像托管、安全扫描、多地域分发，提供企业版与默认实例版，企业版适用于大规模多地域场景。</font>
+ **<font style="color:rgb(2, 8, 23);">推荐场景</font>**<font style="color:rgb(2, 8, 23);">：生产环境建议使用企业版，同时也提供免费版。</font>
+ **<font style="color:rgb(2, 8, 23);">服务地址</font>**<font style="color:rgb(2, 8, 23);">：</font>[<font style="color:#2F8EF4;">https://www.aliyun.com/product/acr</font>](https://www.aliyun.com/product/acr)
2. **<font style="color:rgb(2, 8, 23);">我们提供的暂时免费的镜像仓库</font>**
+ **<font style="color:rgb(2, 8, 23);">适用场景</font>**<font style="color:rgb(2, 8, 23);">：临时测试或小规模使用，需注意账号独立（与主账号密码不互通）。</font>

> <font style="color:rgb(31, 41, 55);">镜像仓库的账号密码和用户的账号密码不互通。</font>
>

## **<font style="color:rgb(2, 8, 23);">2. 我们提供的暂时免费的镜像仓库</font>**<font style="color:rgb(2, 8, 23);">使用指南</font>
### <font style="color:rgb(2, 8, 23);">2.1 步骤 1: 登录天工开物算力镜像站</font>
1. <font style="color:rgb(2, 8, 23);">访问控制台的 </font>[https://tiangongkaiwu.top/#/console](https://tiangongkaiwu.top/#/console) <font style="color:rgb(2, 8, 23);">页面</font>
2. <font style="color:rgb(2, 8, 23);">点击"访问凭证"</font>
3. <font style="color:rgb(2, 8, 23);">按照页面上的登录指引操作（运行命令->输入密码）</font>

**<font style="color:rgb(103, 103, 108);">凭证的核心作用：</font>**

+ **<font style="color:rgb(103, 103, 108);">身份验证</font>**<font style="color:rgb(103, 103, 108);"> </font><font style="color:rgb(103, 103, 108);">凭证相当于平台访问的"数字钥匙"，用于验证用户身份，确保只有授权用户才能访问私有镜像库。</font>
+ **<font style="color:rgb(103, 103, 108);">权限控制</font>**<font style="color:rgb(103, 103, 108);"> </font><font style="color:rgb(103, 103, 108);">通过凭证关联账户权限，控制用户对镜像资源的拉取（pull）、推送（push）等操作。</font>
+ **<font style="color:rgb(103, 103, 108);">安全通信</font>**<font style="color:rgb(103, 103, 108);"> </font><font style="color:rgb(103, 103, 108);">加密客户端与镜像站之间的数据传输，防止敏感信息泄露。</font>

![](./imgs/mirror1.png)

<font style="color:rgb(2, 8, 23);">针对</font><font style="color:rgb(2, 8, 23);"> </font>**<font style="color:rgb(2, 8, 23);">Windows</font>**<font style="color:rgb(2, 8, 23);"> </font><font style="color:rgb(2, 8, 23);">和</font><font style="color:rgb(2, 8, 23);"> </font>**<font style="color:rgb(2, 8, 23);">Mac</font>**<font style="color:rgb(2, 8, 23);"> </font><font style="color:rgb(2, 8, 23);">系统的终端操作指南：</font>

#### **<font style="color:rgb(2, 8, 23);">2.1.1 Windows 系统操作步骤</font>**
**<font style="color:rgb(2, 8, 23);">第一步：打开终端</font>**<font style="color:rgb(2, 8, 23);"> 按下 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">Win + S</font><font style="color:rgb(2, 8, 23);"> 搜索 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">PowerShell</font><font style="color:rgb(2, 8, 23);"> 或 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">CMD</font><font style="color:rgb(2, 8, 23);">，右键选择</font>**<font style="color:rgb(2, 8, 23);"> “以管理员身份运行”</font>**<font style="color:rgb(2, 8, 23);">（避免权限问题）</font>

**<font style="color:rgb(2, 8, 23);">第二步：执行登录命令</font>**<font style="color:rgb(2, 8, 23);"> (复制凭证中的登录镜像仓库命令) </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">docker login harbor.suanleme.cn --username=XXX（换成用户所对应的仓库名称）</font>![](./imgs/mirror2.png)<font style="color:rgb(2, 8, 23);">系统会提示输入密码：</font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">Password:#输入你的仓库密码（输入时不会显示字符，直接盲输后按回车）</font><font style="color:rgb(2, 8, 23);"> </font>![](./imgs/mirror3.png)<font style="color:rgb(2, 8, 23);">成功提示：</font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">Login Succeeded</font><font style="color:rgb(2, 8, 23);"> </font>![](./imgs/mirror4.png)

**<font style="color:rgb(2, 8, 23);">常见问题：</font>**

<font style="color:rgb(2, 8, 23);">如果提示 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">docker command not found</font><font style="color:rgb(2, 8, 23);">：需先安装</font><font style="color:#2F8EF4;"> </font>[<font style="color:#2F8EF4;">Docker Desktop for Windows</font>](https://www.docker.com/products/docker-desktop)<font style="color:#2F8EF4;"> </font><font style="color:rgb(2, 8, 23);">并确保服务已启动。</font>

<font style="color:rgb(2, 8, 23);">密码错误或过期：通过网页端</font><font style="color:rgb(2, 8, 23);"> </font>**<font style="color:rgb(2, 8, 23);">"重置密码"</font>**<font style="color:rgb(2, 8, 23);"> </font><font style="color:rgb(2, 8, 23);">后重试。</font>

---

#### **<font style="color:rgb(2, 8, 23);">2.1.2 Mac 系统操作步骤</font>**
**<font style="color:rgb(2, 8, 23);">第一步：打开终端</font>**<font style="color:rgb(2, 8, 23);"> 通过 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">Spotlight</font><font style="color:rgb(2, 8, 23);">（</font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">Command + Space</font><font style="color:rgb(2, 8, 23);">）搜索</font><font style="color:#2F8EF4;"> </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">终端</font><font style="color:#2F8EF4;"> </font><font style="color:rgb(2, 8, 23);">并打开</font>

**<font style="color:rgb(2, 8, 23);">第二步：执行登录命令</font>**<font style="color:#2F8EF4;"> </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">docker login harbor.suanleme.cn --username=XXX（换成用户所对应的仓库名称）</font><font style="color:rgb(2, 8, 23);">输入密码：</font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">Password: # 输入密码（无回显，输完直接按回车）</font><font style="color:rgb(2, 8, 23);"> 成功提示：</font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">Login Succeeded</font><font style="color:#2F8EF4;"> </font>

**<font style="color:rgb(2, 8, 23);">常见问题：</font>**<font style="color:rgb(2, 8, 23);">  
</font><font style="color:rgb(2, 8, 23);">如果提示权限不足：在命令前加 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">sudo</font><font style="color:rgb(2, 8, 23);">（需输入 Mac 用户密码）：</font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">sudo docker login harbor.suanleme.cn --username=XXX（换成用户所对应的仓库名称）</font><font style="color:rgb(2, 8, 23);"> Docker 未运行：需启动 </font>[<font style="color:#2F8EF4;">Docker Desktop for Mac</font>](https://www.docker.com/products/docker-desktop)<font style="color:#2F8EF4;"> </font><font style="color:rgb(2, 8, 23);">并等待图标变绿。</font>

---

#### **<font style="color:rgb(2, 8, 23);">2.1.3 密码重置指引</font>**
1. <font style="color:rgb(2, 8, 23);">访问镜像仓库控制台页面，点击</font><font style="color:rgb(2, 8, 23);"> </font>**<font style="color:rgb(2, 8, 23);">"重置密码"</font>**<font style="color:rgb(2, 8, 23);">。</font>
2. <font style="color:rgb(2, 8, 23);">按提示通过邮箱或手机验证身份。</font>
3. <font style="color:rgb(2, 8, 23);">设置新密码后，</font>**<font style="color:rgb(2, 8, 23);">重新执行登录命令</font>**<font style="color:rgb(2, 8, 23);">。</font>

---

### <font style="color:rgb(2, 8, 23);">2.2 步骤 2: 为镜像添加标签</font>
#### **<font style="color:rgb(2, 8, 23);">2.2.1 为何需要添加标签？</font>**
<font style="color:rgb(2, 8, 23);">镜像标签（Tag）相当于镜像的"地址 + 版本标识"，目的是将本地镜像与远程仓库路径绑定。天工开物算力镜像站要求镜像名称必须包含完整的仓库地址和账户信息，否则无法识别推送目标。</font>

---

#### **<font style="color:rgb(2, 8, 23);">2.2.2 完整操作步骤</font>**
##### **<font style="color:rgb(2, 8, 23);">第一步：查看本地镜像列表</font>**
<font style="color:rgb(2, 8, 23);">在终端执行以下命令，确认要推送的本地镜像名称和版本：</font>

###### **<font style="color:rgb(2, 8, 23);">查看本地所有的镜像</font>**
```plain
#查看本地所有的镜像
docker images
```

###### <font style="color:rgb(2, 8, 23);">非 root 用户使用</font>
```plain
#查看本地所有的镜像
sudo docker images
```

<font style="color:rgb(2, 8, 23);">输出示例：</font>

```plain
REPOSITORY    TAG       IMAGE ID       CREATED         SIZE
my-image     latest    a1b2c3d4e5f6   2 hours ago     1.2GB
```

###### **<font style="color:rgb(2, 8, 23);">Docker 镜像字段解析：</font>**
**<font style="color:rgb(2, 8, 23);">镜像仓库/名称 (REPOSITORY)</font>**

<font style="color:rgb(2, 8, 23);">表示镜像的完整来源路径，由仓库地址、账户名和镜像名称组成。例如：</font>

+ <font style="color:rgb(2, 8, 23);">公共仓库镜像：</font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">my-image</font><font style="color:rgb(2, 8, 23);">（默认从 Docker Hub 拉取，格式为</font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">仓库名/镜像名</font><font style="color:rgb(2, 8, 23);">）。</font>
+ <font style="color:rgb(2, 8, 23);">私有仓库镜像：</font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">harbor.suanleme.cn/xiditgkw/my-app</font><font style="color:rgb(2, 8, 23);">（需包含仓库域名、账户名及镜像名）。</font>

**<font style="color:rgb(2, 8, 23);">标签 (TAG)</font>**

<font style="color:rgb(2, 8, 23);">用于标识镜像版本，支持自定义语义化命名规则：</font>

+ <font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">latest</font><font style="color:rgb(82, 183, 136);background-color:rgba(142, 150, 170, 0.14);"> </font><font style="color:rgb(2, 8, 23);">是默认标签，通常指向最新构建的镜像（生产环境慎用）。</font>
+ <font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">v1.2.0 </font><font style="color:rgb(2, 8, 23);">是推荐的自定义标签，可通过版本号区分不同功能阶段的镜像（如开发版、稳定版）。</font>

**<font style="color:rgb(2, 8, 23);">镜像唯一 ID (IMAGE ID)</font>**<font style="color:rgb(2, 8, 23);"> 由镜像内容生成的哈希值，是镜像的唯一标识符。实际显示为完整 64 位 ID 的前 12 位（例如 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">a1b2c3d4e5f</font><font style="color:rgb(2, 8, 23);">），可通过 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">docker inspect </font><font style="color:rgb(2, 8, 23);">命令查看完整 ID。</font>

**<font style="color:rgb(2, 8, 23);">创建时间 (CREATED)</font>**<font style="color:rgb(2, 8, 23);"> 记录镜像的构建时间，格式为可读的相对时间（如 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">2 weeks ago</font><font style="color:rgb(2, 8, 23);">）。此时间不随镜像更新而改变，可用于判断镜像版本的新旧程度。</font>

**<font style="color:rgb(2, 8, 23);">镜像大小 (SIZE)</font>**<font style="color:rgb(2, 8, 23);"> 表示镜像的虚拟存储空间，包含所有分层文件的总和。由于 Docker 采用分层存储机制，不同镜像可能共享基础层，因此实际磁盘占用通常小于各镜像 SIZE 的累加值（例如多个镜像共享同一 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">Ubuntu</font><font style="color:rgb(2, 8, 23);"> 基础层时）。</font>

##### **<font style="color:rgb(2, 8, 23);">第二步：执行标签添加命令</font>**
**<font style="color:rgb(2, 8, 23);">命令格式</font>**<font style="color:rgb(2, 8, 23);">：</font>

```plain
docker tag <原镜像名称>:<原标签> harbor.suanleme.cn/<你的账户>/<镜像名称>:<自定义标签>
```

**<font style="color:rgb(2, 8, 23);">参数解释</font>**<font style="color:rgb(2, 8, 23);">：</font>

+ <font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);"><原镜像名称>:<原标签></font><font style="color:rgb(2, 8, 23);">：本地已有的镜像名称和标签（如 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">my-image:latest</font><font style="color:rgb(2, 8, 23);">）</font>
+ <font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);"><你的账户></font><font style="color:rgb(2, 8, 23);">：登录镜像仓库的账号（需在控制台 </font>**<font style="color:rgb(2, 8, 23);">[镜像仓库 > 访问凭证]</font>**<font style="color:rgb(2, 8, 23);"> 中确认）</font>
+ <font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);"><镜像名称></font><font style="color:rgb(2, 8, 23);">：推送到仓库后的镜像名称（可自定义，建议与本地镜像同名）</font>
+ <font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);"><自定义标签></font><font style="color:rgb(2, 8, 23);">：镜像版本标识（如 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">v1.0</font><font style="color:rgb(2, 8, 23);">、</font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">prod</font><font style="color:rgb(2, 8, 23);">）</font>

---

##### **<font style="color:rgb(2, 8, 23);">第三步：具体操作示例</font>**
<font style="color:rgb(2, 8, 23);">假设你的账户是 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">xiditgkw</font><font style="color:rgb(2, 8, 23);">，本地镜像为 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">my-image:latest</font><font style="color:rgb(2, 8, 23);">，目标标签设为 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">v1.0</font><font style="color:rgb(2, 8, 23);">：</font>

```plain
docker tag my-image:latest harbor.suanleme.cn/xiditgkw/my-image:v1.0
```

<font style="color:rgb(2, 8, 23);">验证是否成功：</font>

```plain
docker images
```

<font style="color:rgb(2, 8, 23);">输出中应出现新标签的镜像：</font>

```plain
REPOSITORY                            TAG       IMAGE ID       CREATED         SIZE
my-image                              latest    a1b2c3d4e5f6   2 hours ago     1.2GB
harbor.suanleme.cn/xiditgkw/my-image v1.0      a1b2c3d4e5f6   2 hours ago     1.2GB
```

---

#### **<font style="color:rgb(2, 8, 23);">2.2.3 关键注意事项</font>**
1. **<font style="color:rgb(2, 8, 23);">账户名必须精确匹配</font>**<font style="color:rgb(2, 8, 23);"> </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);"><你的账户> </font><font style="color:rgb(2, 8, 23);">必须与镜像仓库控制台中显示的</font>**<font style="color:rgb(2, 8, 23);">登录账号</font>**<font style="color:rgb(2, 8, 23);">完全一致（区分大小写）</font>
+ <font style="color:rgb(2, 8, 23);">可通过控制台</font><font style="color:rgb(2, 8, 23);"> </font>**<font style="color:rgb(2, 8, 23);">[镜像仓库 > 访问凭证]</font>**<font style="color:rgb(2, 8, 23);"> </font><font style="color:rgb(2, 8, 23);">查看确认账号</font>
2. **<font style="color:rgb(2, 8, 23);">镜像层级结构规则</font>**
+ <font style="color:rgb(2, 8, 23);">完整路径格式：</font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">harbor.suanleme.cn/<账户>/<项目>/<镜像名>:<标签></font>

<font style="color:rgb(2, 8, 23);">如果仓库有项目层级（如团队协作），需包含项目名：</font>

<font style="color:rgb(2, 8, 23);">例如：</font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">docker tag my-image harbor.suanleme.cn/xiditgkw/project-a/my-image:v1.0</font>

3. **<font style="color:rgb(2, 8, 23);">标签命名建议</font>**<font style="color:rgb(2, 8, 23);"> - 使用语义化版本（如 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">v1.0.2</font><font style="color:rgb(2, 8, 23);">）</font>
+ <font style="color:rgb(2, 8, 23);">避免使用默认的</font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">latest</font><font style="color:rgb(2, 8, 23);">标签（易导致版本混乱）</font>
+ <font style="color:rgb(2, 8, 23);">可包含环境标识（如</font><font style="color:#2F8EF4;"> </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">dev</font><font style="color:rgb(2, 8, 23);">、</font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">prod</font><font style="color:rgb(2, 8, 23);">）</font>

---

#### **<font style="color:rgb(2, 8, 23);">2.2.4 常见问题排查</font>**
+ **<font style="color:rgb(2, 8, 23);">错误提示 "Repository does not exist"</font>**<font style="color:rgb(2, 8, 23);"> </font><font style="color:rgb(2, 8, 23);">➠ 检查账户名是否拼写错误 ➠ 确认仓库中是否已手动创建对应项目（部分平台需先创建仓库目录）</font>
+ **<font style="color:rgb(2, 8, 23);">镜像列表未显示新标签</font>**<font style="color:rgb(2, 8, 23);"> ➠ 确保 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">docker tag</font><font style="color:rgb(82, 183, 136);background-color:rgba(142, 150, 170, 0.14);"> </font><font style="color:rgb(2, 8, 23);">命令参数顺序正确 ➠ 验证原镜像是否存在（通过 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">docker images</font><font style="color:rgb(2, 8, 23);">）</font>
+ **<font style="color:rgb(2, 8, 23);">推送时提示权限不足</font>**<font style="color:rgb(2, 8, 23);"> ➠ 重新执行 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">docker login</font><font style="color:rgb(82, 183, 136);background-color:rgba(142, 150, 170, 0.14);"> </font><font style="color:rgb(2, 8, 23);">确保登录状态 ➠ 检查账户是否有该仓库路径的写入权限</font>

---

### <font style="color:rgb(2, 8, 23);">2.3 步骤 3: 推送镜像</font>
<font style="color:rgb(2, 8, 23);">标签添加完成后，使用以下命令推送镜像：</font>

```plain
docker push harbor.suanleme.cn/<your-account>/my-image:my-tag
```

<font style="color:rgb(2, 8, 23);">参数解释：</font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">docker push harbor.suanleme.cn/<your-account>/my-image:my-tag</font>

+ <font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">harbor.suanleme.cn</font><font style="color:rgb(2, 8, 23);"> 镜像仓库的域名地址，指向天工开物算力平台的私有镜像仓库存储服务。</font>
+ <font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);"><your-account></font><font style="color:rgb(2, 8, 23);"> 你的仓库账号（需替换为实际账号）。 ➠ 通过控制台 </font>**<font style="color:rgb(2, 8, 23);">[镜像仓库 > 访问凭证]</font>**<font style="color:rgb(2, 8, 23);"> 查看确认账号名称（区分大小写）。</font>
+ <font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">my-image</font><font style="color:#2F8EF4;"> </font><font style="color:rgb(2, 8, 23);">推送到仓库后的镜像名称，支持以下两种形式：</font>
    - **<font style="color:rgb(2, 8, 23);">直接命名</font>**<font style="color:rgb(2, 8, 23);">：与本地镜像同名（如 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">my-image</font><font style="color:rgb(2, 8, 23);">）</font>
    - **<font style="color:rgb(2, 8, 23);">层级命名</font>**<font style="color:rgb(2, 8, 23);">：包含项目/分类（如 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">project-a/my-image</font><font style="color:rgb(2, 8, 23);">，需提前在仓库创建对应目录）</font>
+ <font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">my-tag</font><font style="color:rgb(2, 8, 23);"> 镜像的版本标签，用于标识不同版本或环境：</font>
    - <font style="color:rgb(2, 8, 23);">示例：</font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">v1.0</font><font style="color:rgb(2, 8, 23);">（语义化版本）、</font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">prod</font><font style="color:rgb(2, 8, 23);">（生产环境）、</font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">20251001</font><font style="color:rgb(2, 8, 23);">（日期版本）</font>
    - <font style="color:rgb(2, 8, 23);">避免使用默认的 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">latest</font><font style="color:#2F8EF4;"> </font><font style="color:rgb(2, 8, 23);">标签（易导致版本混乱）。</font>

<font style="color:rgb(2, 8, 23);">在推送过程中，会显示上传进度。当推送成功后，可以在“镜像仓库”页面查看该镜像。如果镜像过大，那么推送所需时间可能会比较长。</font>

![](./imgs/mirror5.png)

---

## **<font style="color:rgb(2, 8, 23);">3. 阿里云容器镜像服务（ACR）使用指南</font>**
### <font style="color:rgb(2, 8, 23);">3.1 服务准备与实例选择</font>
1. **<font style="color:rgb(2, 8, 23);">实例类型选择</font>**
+ **<font style="color:rgb(2, 8, 23);">企业版</font>**<font style="color:rgb(2, 8, 23);">：适用于生产环境，支持多地域分发、安全扫描、镜像加签、全球同步加速、P2P 分发等功能，提供 99.95% SLA 保障。推荐选择标准版或高级版以支持交付链与大规模分发。</font>
+ **<font style="color:rgb(2, 8, 23);">个人版</font>**<font style="color:rgb(2, 8, 23);">：仅限测试或小规模使用，无 SLA 保障，功能受限（如不支持 Helm Chart 管理）。</font>
2. **<font style="color:rgb(2, 8, 23);">创建实例</font>**
+ **<font style="color:rgb(2, 8, 23);">操作路径</font>**<font style="color:rgb(2, 8, 23);">：登录</font>[<font style="color:#2F8EF4;">容器镜像服务 ACR</font>](https://www.aliyun.com/product/acr)<font style="color:rgb(2, 8, 23);">，选择地域后创建实例，需配置实例名称、存储类型（默认 OSS 或自定义 Bucket）、安全扫描引擎（Trivy 或云安全引擎）及资源配额。</font>
+ **<font style="color:rgb(2, 8, 23);">注意事项</font>**<font style="color:rgb(2, 8, 23);">：</font>
    - <font style="color:rgb(2, 8, 23);">企业版需绑定 OSS 服务并设置 VPC 访问控制配额。</font>
    - <font style="color:rgb(2, 8, 23);">命名空间与仓库数量受配额限制，建议按团队或项目划分命名空间。</font>![](./imgs/mirror6.png)**<font style="color:rgb(2, 8, 23);">企业版：</font>**<font style="color:rgb(2, 8, 23);">容器镜像服务</font>![](./imgs/mirror7.png)

### <font style="color:rgb(2, 8, 23);">3.2 镜像仓库配置</font>
**<font style="color:rgb(2, 8, 23);">官方教程：阿里云【试用教程】在 Dockerfile 中使用构建打包镜像并运行</font>**
1. **<font style="color:rgb(2, 8, 23);">命名空间与仓库创建</font>**
    1. **<font style="color:rgb(2, 8, 23);">命名空间</font>**<font style="color:rgb(2, 8, 23);">：逻辑隔离单元，建议按组织命名（如</font><font style="color:#2F8EF4;"> </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">dev-team</font><font style="color:rgb(2, 8, 23);">），一个账号最多创建 3 个（个人版）或更多（企业版）。</font> 
![](./imgs/mirror8.png)![](./imgs/mirror9.png)
    2. **<font style="color:rgb(2, 8, 23);">镜像仓库</font>**<font style="color:rgb(2, 8, 23);">：</font><font style="color:rgb(2, 8, 23);"> </font>**<font style="color:rgb(2, 8, 23);">创建步骤</font>**<font style="color:rgb(2, 8, 23);">：在控制台选择命名空间后，填写仓库名称、类型（私有/公开）、代码源（本地仓库或 Git 平台），并配置构建设置（自动构建、缓存策略）。</font>
    3. **<font style="color:rgb(2, 8, 23);">命名规范</font>**<font style="color:rgb(2, 8, 23);">：需包含完整路径（如 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">registry.cn-hangzhou.aliyuncs.com/<命名空间>/<仓库名>:<标签></font><font style="color:rgb(2, 8, 23);">），标签建议使用语义化版本（如</font><font style="color:#2F8EF4;"> </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">v1.0-prod</font><font style="color:rgb(2, 8, 23);">）。</font>![](./imgs/mirror10.png)
2. **<font style="color:rgb(2, 8, 23);">访问控制</font>**
+ **<font style="color:rgb(2, 8, 23);">公网/VPC 访问</font>**<font style="color:rgb(2, 8, 23);">：企业版需在控制台开启公网入口并配置白名单，或通过专有网络实现内网加速。</font>
+ **<font style="color:rgb(2, 8, 23);">权限管理</font>**<font style="color:rgb(2, 8, 23);">：通过 RAM 子账号授权，区分命名空间管理员（全权限）、普通成员（读写）和只读用户。</font>

### <font style="color:rgb(2, 8, 23);">3.3 镜像操作指南</font>
1. **<font style="color:rgb(2, 8, 23);">登录凭证配置</font>**
+ **<font style="color:rgb(2, 8, 23);">凭证类型</font>**<font style="color:rgb(2, 8, 23);">：支持阿里云账号 AccessKey 或临时密码，企业版需在控制台[访问凭证]页面设置固定密码。</font>

![](./imgs/mirror11.png)

+ **<font style="color:rgb(2, 8, 23);">登录命令</font>**<font style="color:rgb(2, 8, 23);">：</font>

```plain
docker login --username=<账号名> registry.<region>.aliyuncs.com
```

<font style="color:rgb(2, 8, 23);">输入密码后显示 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">Login Succeeded</font><font style="color:rgb(2, 8, 23);"> 即为成功。</font>

2. **<font style="color:rgb(2, 8, 23);">镜像推送与拉取</font>**
+ **<font style="color:rgb(2, 8, 23);">标签与推送</font>**<font style="color:rgb(2, 8, 23);">：</font>

```plain
docker tag <本地镜像> registry.cn-hangzhou.aliyuncs.com/<命名空间>/<仓库名>:<标签>
docker push registry.cn-hangzhou.aliyuncs.com/<命名空间>/<仓库名>:<标签>
```

+ **<font style="color:rgb(2, 8, 23);">拉取镜像</font>**<font style="color:rgb(2, 8, 23);">：</font>

```plain
docker pull registry.cn-hangzhou.aliyuncs.com/<命名空间>/<仓库名>:<标签>
```

+ **<font style="color:rgb(2, 8, 23);">注意事项</font>**<font style="color:rgb(2, 8, 23);">：企业版实例名称需替换为 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">企业版实例名称-region.cr.aliyuncs.com</font><font style="color:rgb(2, 8, 23);">。</font>
3. **<font style="color:rgb(2, 8, 23);">多架构镜像构建</font>**
+ **<font style="color:rgb(2, 8, 23);">适用场景</font>**<font style="color:rgb(2, 8, 23);">：需支持 x86、ARM 等不同架构。</font>
+ **<font style="color:rgb(2, 8, 23);">操作步骤</font>**<font style="color:rgb(2, 8, 23);">：</font>
        1. <font style="color:rgb(2, 8, 23);">在代码仓库配置多架构构建规则（如 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">linux/amd64</font><font style="color:rgb(2, 8, 23);"> 和 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">linux/arm64</font><font style="color:rgb(2, 8, 23);">）。</font>
        2. <font style="color:rgb(2, 8, 23);">触发构建后，同一标签将包含多架构镜像，客户端自动拉取适配版本。</font>

---

### <font style="color:rgb(2, 8, 23);">3.4 高级功能与优化</font>
1. **<font style="color:rgb(2, 8, 23);">镜像加速器配置</font>**
+ **<font style="color:rgb(2, 8, 23);">作用</font>**<font style="color:rgb(2, 8, 23);">：加速 Docker Hub 官方镜像拉取，需在 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">/etc/docker/daemon.json </font><font style="color:rgb(2, 8, 23);">中添加加速器地址（如 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">https://<加速器地址>.mirror.aliyuncs.com</font><font style="color:rgb(2, 8, 23);">）并重启 Docker 服务。</font>
+ **<font style="color:rgb(2, 8, 23);">限制</font>**<font style="color:rgb(2, 8, 23);">：仅限阿里云公网产品使用，无法保证拉取最新版本，建议指定镜像版本。</font>
2. **<font style="color:rgb(2, 8, 23);">安全与合规</font>**
+ **<font style="color:rgb(2, 8, 23);">安全扫描</font>**<font style="color:rgb(2, 8, 23);">：企业版支持自动扫描漏洞（CVE、恶意代码），并生成修复建议。</font>
+ **<font style="color:rgb(2, 8, 23);">镜像加签</font>**<font style="color:rgb(2, 8, 23);">：通过密钥对镜像签名，防止篡改，可配置自动加签规则。</font>
+ **<font style="color:rgb(2, 8, 23);">操作审计</font>**<font style="color:rgb(2, 8, 23);">：记录所有镜像操作日志，便于追溯与合规审查。</font>
3. **<font style="color:rgb(2, 8, 23);">全球分发与同步</font>**
+ **<font style="color:rgb(2, 8, 23);">多地域复制</font>**<font style="color:rgb(2, 8, 23);">：企业版支持一键同步镜像至全球地域，通过内网专线降低延迟。</font>
+ **<font style="color:rgb(2, 8, 23);">P2P 加速</font>**<font style="color:rgb(2, 8, 23);">：千节点级分发时启用 P2P 技术，提升效率 4 倍以上。</font>
4. **<font style="color:rgb(2, 8, 23);">生命周期管理</font>**
+ **<font style="color:rgb(2, 8, 23);">自动清理策略</font>**<font style="color:rgb(2, 8, 23);">：设置保留规则（如保留最近 10 个版本），自动清理旧镜像释放存储空间。</font>
+ **<font style="color:rgb(2, 8, 23);">交付链集成</font>**<font style="color:rgb(2, 8, 23);">：高级版支持构建→扫描→加签→分发的自动化流水线，减少人工干预。</font>

---

### <font style="color:rgb(2, 8, 23);">3.5 常见问题与排查</font>
**<font style="color:rgb(2, 8, 23);">问题现象：unauthorized或权限不足</font>**

+ **<font style="color:rgb(2, 8, 23);">原因</font>**<font style="color:rgb(2, 8, 23);">：Docker 客户端未携带有效身份凭证，或用户对目标仓库无操作权限。</font>
+ **<font style="color:rgb(2, 8, 23);">解决方案</font>**<font style="color:rgb(2, 8, 23);">：</font>
    1. <font style="color:rgb(2, 8, 23);">执行 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">docker logout</font><font style="color:rgb(2, 8, 23);">退出当前登录状态，重新运行</font><font style="color:#2F8EF4;"> </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">docker login</font><font style="color:#2F8EF4;"> </font><font style="color:rgb(2, 8, 23);">输入账户密码。</font>
    2. <font style="color:rgb(2, 8, 23);">若使用私有仓库，需在登录时指定仓库地址（如</font><font style="color:#2F8EF4;"> </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">docker login harbor.example.com</font><font style="color:rgb(2, 8, 23);">）。</font>
    3. <font style="color:rgb(2, 8, 23);">检查账户是否被移出仓库权限组，联系管理员确认访问权限。</font>

**<font style="color:rgb(2, 8, 23);">问题现象：推送失败（Repository does not exist）</font>**

+ **<font style="color:rgb(2, 8, 23);">原因</font>**<font style="color:rgb(2, 8, 23);">：镜像名称中的命名空间或仓库路径不符合仓库规则，或目标仓库尚未创建。</font>
+ **<font style="color:rgb(2, 8, 23);">解决方案</font>**<font style="color:rgb(2, 8, 23);">：</font>
    1. <font style="color:rgb(2, 8, 23);">镜像名称需严格遵循</font><font style="color:#2F8EF4;"> </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">仓库域名/命名空间/仓库名:标签</font><font style="color:#2F8EF4;"> </font><font style="color:rgb(2, 8, 23);">格式（如</font><font style="color:#2F8EF4;"> </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">harbor.com/dev-team/myapp:v1</font><font style="color:rgb(2, 8, 23);">）。</font>
    2. <font style="color:rgb(2, 8, 23);">通过仓库管理界面手动创建同名仓库目录（部分私有仓库要求先创建空仓库）。</font>
    3. <font style="color:rgb(2, 8, 23);">确认账户对目标仓库有</font><font style="color:#2F8EF4;"> </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">push</font><font style="color:rgb(2, 8, 23);">权限（权限不足时会伪装为“仓库不存在”错误）。</font>

**<font style="color:rgb(2, 8, 23);">问题现象：镜像拉取超时</font>**

+ **<font style="color:rgb(2, 8, 23);">原因</font>**<font style="color:rgb(2, 8, 23);">：网络连接不稳定或跨国访问公共仓库速度受限。</font>
+ **<font style="color:rgb(2, 8, 23);">解决方案</font>**<font style="color:rgb(2, 8, 23);">：</font>
    1. **<font style="color:rgb(2, 8, 23);">配置镜像加速器</font>**<font style="color:rgb(2, 8, 23);">：</font>
        * <font style="color:rgb(2, 8, 23);">修改 Docker 配置文件 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">/etc/docker/daemon.json</font><font style="color:rgb(2, 8, 23);">，添加国内镜像源（如阿里云、腾讯云镜像加速地址）。</font>
        * <font style="color:rgb(2, 8, 23);">执行 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">systemctl reload docker</font><font style="color:rgb(2, 8, 23);"> 重启服务生效。</font>
    2. **<font style="color:rgb(2, 8, 23);">启用企业版加速功能</font>**<font style="color:rgb(2, 8, 23);">：</font>
        * <font style="color:rgb(2, 8, 23);">私有化部署仓库（如 Harbor）可开启 P2P 分发或全球节点同步功能。</font>
    3. <font style="color:rgb(2, 8, 23);">临时切换网络环境测试（如从公司内网切换至公网）。</font>

**<font style="color:rgb(2, 8, 23);">问题现象：安全扫描报错</font>**

+ **<font style="color:rgb(2, 8, 23);">原因</font>**<font style="color:rgb(2, 8, 23);">：镜像包含高危漏洞或依赖项版本过低。</font>
+ **<font style="color:rgb(2, 8, 23);">解决方案</font>**<font style="color:rgb(2, 8, 23);">：</font>
    1. <font style="color:rgb(2, 8, 23);">使用</font><font style="color:#2F8EF4;"> </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">docker scan 镜像名</font><font style="color:rgb(2, 8, 23);"> 运行安全扫描，查看具体漏洞详情。</font>
    2. <font style="color:rgb(2, 8, 23);">升级 Dockerfile 中 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">FROM</font><font style="color:rgb(2, 8, 23);">指定的基础镜像版本（如 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">FROM alpine:3.19</font><font style="color:rgb(2, 8, 23);">替代 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">alpine:3.10</font><font style="color:rgb(2, 8, 23);">）。</font>
    3. <font style="color:rgb(2, 8, 23);">使用阿里云容器镜像服务提供的「安全镜像」，自动集成漏洞修复补丁。</font>
    4. <font style="color:rgb(2, 8, 23);">在 CI/CD 流程中集成 Trivy 或 Clair 漏洞扫描工具，阻断不安全镜像流入生产环境。</font>

---

### <font style="color:rgb(2, 8, 23);">3.6 最佳实践建议</font>
1. **<font style="color:rgb(2, 8, 23);">生产环境规范</font>**<font style="color:rgb(2, 8, 23);">：</font>
+ <font style="color:rgb(2, 8, 23);">使用企业版并启用安全扫描、加签与访问控制。</font>
+ <font style="color:rgb(2, 8, 23);">避免使用</font><font style="color:#2F8EF4;"> </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">latest</font><font style="color:rgb(2, 8, 23);"> 标签，采用语义化版本管理。</font>
2. **<font style="color:rgb(2, 8, 23);">性能优化</font>**<font style="color:rgb(2, 8, 23);">：</font>
+ <font style="color:rgb(2, 8, 23);">内网推送使用 VPC 域名（如 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">registry-vpc.cn-hangzhou.aliyuncs.com</font><font style="color:rgb(2, 8, 23);">）以提升速度。</font>
+ <font style="color:rgb(2, 8, 23);">大镜像（如 AI 模型）启用按需加载功能，减少启动时间 60%。</font>

<font style="color:rgb(2, 8, 23);">通过以上步骤，可高效利用 ACR 实现镜像全生命周期管理，兼顾安全性与运维效率。如需进一步探索 API 集成或自定义认证，可参考</font>[<font style="color:#2F8EF4;">ACR 官方文档</font>](https://help.aliyun.com/zh/acr)<font style="color:rgb(2, 8, 23);">。</font>

<br><br>
<font style="color:rgba(178,178,178,1);">最后更新于: 2025/6/9 17:00</font>




