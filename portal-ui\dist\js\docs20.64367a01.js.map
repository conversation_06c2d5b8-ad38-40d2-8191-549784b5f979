{"version": 3, "file": "js/docs20.64367a01.js", "mappings": "yHACA,IAAIA,EAA6B,IAAIC,IAAI,YACrCC,EAA6B,IAAID,IAAI,aACrCE,EAA6B,IAAIF,IAAI,aACrCG,EAA6B,IAAIH,IAAI,aACrCI,EAA6B,IAAIJ,IAAI,aACrCK,EAA6B,IAAIL,IAAI,aACrCM,EAA6B,IAAIN,IAAI,aACrCO,EAA6B,IAAIP,IAAI,aACrCQ,EAA6B,IAAIR,IAAI,YACrCS,EAA6B,IAAIT,IAAI,aACrCU,EAA8B,IAAIV,IAAI,aAEtCW,EAAO,o4BAAm6BZ,EAA6B,qLAAiME,EAA6B,mTAAqUC,EAA6B,kHAA4HC,EAA6B,6WAA6XC,EAA6B,4bAAgdC,EAA6B,i6BAAq8BC,EAA6B,yRAAySC,EAA6B,uKAAmLC,EAA6B,qYAAyZC,EAA6B,mSAAqTC,EAA8B,i7GAE5yJ,c", "sources": ["webpack://portal-ui/./src/docs/ollama-qwen-webui.md"], "sourcesContent": ["// Imports\nvar ___HTML_LOADER_IMPORT_0___ = new URL(\"./imgs/Ollama+Qwen1.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_1___ = new URL(\"./imgs/Ollama+Qwen2.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_2___ = new URL(\"./imgs/ollama-qwen-webui3.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_3___ = new URL(\"./imgs/ollama-qwen-webui4.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_4___ = new URL(\"./imgs/ollama-qwen-webui5.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_5___ = new URL(\"./imgs/ollama-qwen-webui6.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_6___ = new URL(\"./imgs/ollama-qwen-webui7.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_7___ = new URL(\"./imgs/ollama-qwen-webui8.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_8___ = new URL(\"./imgs/ollama-qwen-webui9.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_9___ = new URL(\"./imgs/ollama-qwen-webui10.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_10___ = new URL(\"./imgs/ollama-qwen-webui11.png\", import.meta.url);\n// Module\nvar code = \"<h1 id=\\\"容器化部署-ollamaqwen3open-webui\\\"><font style=\\\"color:#020817\\\">容器化部署 Ollama+Qwen3+Open WebUI</font></h1> <p><font style=\\\"color:#020817\\\">本指南全面介绍了在天工开物平台上部署 Ollama 与 Qwen3 大语言模型，通过 WebUI 访问的完整解决方案。该方案不仅提供了详实的部署流程，还提供了如何制作镜像的方案。</font></p> <blockquote> <p><font style=\\\"color:#1f2937\\\">此镜像提供了标准化的API 接口，同时提供了 WebUI 的方式与大模型进行交互。</font></p> </blockquote> <h2 id=\\\"1、部署服务\\\"><strong><font style=\\\"color:#020817\\\">1、部署服务</font></strong></h2> <p><font style=\\\"color:#020817\\\">点击这里【</font><a href=\\\"https://tiangongkaiwu.top/#/console\\\"><font style=\\\"color:#2f8ef4\\\">新增部署任务</font></a><font style=\\\"color:#020817\\\">】，登录后根据页面提示进行部署。【选择 GPU 型号】--&gt;在【服务配置】中选择【预制镜像】--&gt;勾选【服务协议】--&gt;点击【部署服务】--&gt;部署完成，等待镜像拉取！</font></p> <h3 id=\\\"11-访问天工开物控制台，点击【新增部署任务】。\\\"><font style=\\\"color:#020817\\\">1.1 访问</font><a href=\\\"https://tiangongkaiwu.top/#/console\\\">天工开物控制台</a><font style=\\\"color:#020817\\\">，点击【新增部署任务】。</font></h3> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_0___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"12-选择设备\\\"><font style=\\\"color:#020817\\\">1.2 选择设备</font></h3> <p><font style=\\\"color:#020817\\\">基于自身需要进行配置，参考配置为单卡 4090 和 1 个节点（初次使用进行调试）。</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_1___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"13-选择相应预制镜像\\\"><font style=\\\"color:#020817\\\">1.3 选择相应预制镜像</font></h3> <p><font style=\\\"color:#020817\\\">这里选择</font><font style=\\\"color:#020817\\\"> </font><strong><font style=\\\"color:#020817\\\">Open Web UI + Qwen3 30B，</font></strong><font style=\\\"color:#020817\\\">点击【部署服务】。</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_2___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"14-耐心等待节点拉取镜像并启动\\\"><font style=\\\"color:#020817\\\">1.4 耐心等待节点拉取镜像并启动</font></h3> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_3___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"15-部署完成\\\"><font style=\\\"color:#020817\\\">1.5 部署完成</font></h3> <p><font style=\\\"color:#020817\\\">在部署完成页面，能看到两个访问链接，</font></p> <ul> <li><font style=\\\"color:#020817\\\">11434 对应的链接就是 Ollama 服务的 API 访问地址。将这个 API 地址【复制】下来，就可以在任何支持 Ollama 协议的应用程序中使用。</font></li> <li><font style=\\\"color:#020817\\\">8080 对应的链接就是 Open WebUI 的访问地址</font></li> </ul> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_4___ + \"\\\" alt=\\\"\\\"></p> <p><font style=\\\"color:#67676c\\\">请耐心一点哦～ 模型镜像会比较大，</font><strong><font style=\\\"color:#67676c\\\">qwen3:30b-a3b 镜像本身 20G+，打包之后大约 30G+，</font></strong><font style=\\\"color:#67676c\\\"> 拉取镜像会需要一段时间</font></p> <h3 id=\\\"16-如何切换模型\\\"><font style=\\\"color:#020817\\\">1.6 如何切换模型</font></h3> <p><font style=\\\"color:#020817\\\">通过修改 Docker 镜像的环境变量，您可以轻松切换不同的 AI 模型。</font></p> <p><font style=\\\"color:#020817\\\">找到任务列表，选择刚刚创建的任务，点击跳转到详情页面</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_5___ + \"\\\" alt=\\\"\\\"></p> <ol> <li><font style=\\\"color:#020817\\\">进入环境变量设置页面</font></li> <li><font style=\\\"color:#020817\\\">找到输入框，输入以下格式的环境变量</font></li> </ol> <p><font style=\\\"color:#020817\\\">DEFAULT_MODEL=模型名称</font></p> <pre><code class=\\\"language-plain\\\">例如，要切换到Qwen 3.0 32B模型，输入：\\nDEFAULT_MODEL=qwen3:32b\\n</code></pre> <ol> <li><font style=\\\"color:#020817\\\">点击【保存】按钮</font></li> <li><font style=\\\"color:#020817\\\">点击【</font><strong><font style=\\\"color:#020817\\\">应用修改】</font></strong><font style=\\\"color:#020817\\\">按钮使新设置</font><strong><font style=\\\"color:#020817\\\">生效</font></strong></li> </ol> <p><font style=\\\"color:#020817\\\">重要提示：</font></p> <ul> <li><font style=\\\"color:#020817\\\">环境变量修改后必须点击【应用修改】按钮才能生效</font></li> <li><font style=\\\"color:#020817\\\">可用的模型名称请参考 </font><a href=\\\"https://ollama.com/library\\\"><font style=\\\"color:#2f8ef4\\\">Ollama官方模型库</font></a></li> <li><font style=\\\"color:#020817\\\">确保输入的模型名称格式正确，通常为 模型系列:参数规模 的形式</font></li> </ul> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_6___ + \"\\\" alt=\\\"\\\"></p> <h2 id=\\\"2、开始使用-open-webui\\\"><font style=\\\"color:#020817\\\">2、开始使用 Open WebUI</font></h2> <h3 id=\\\"21-访问开始页面\\\"><font style=\\\"color:#020817\\\">2.1 访问开始页面</font></h3> <p><font style=\\\"color:#020817\\\">访问 8080 端口对应的链接，会出现以下界面，说明 Open WebUI 已经成功部署。点击【开始使用】</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_7___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"22-设置管理员账号\\\"><font style=\\\"color:#020817\\\">2.2 设置管理员账号</font></h3> <p><font style=\\\"color:#020817\\\">设置管理员账号和密码，点击【创建管理员账号】</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_8___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"23-开始-ai-对话\\\"><font style=\\\"color:#020817\\\">2.3 开始 AI 对话</font></h3> <p><font style=\\\"color:#020817\\\">创建管理员账号之后会自动登录，默认会选中镜像中的模型</font><font style=\\\"color:#020817\\\"> </font><strong><font style=\\\"color:#020817\\\">qwen3:30b-a3b</font></strong><font style=\\\"color:#020817\\\">，就是制作镜像时设置的镜像名称。</font></p> <p><font style=\\\"color:#020817\\\">现在一起就绪，开始与 AI 的对话之旅吧~</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_9___ + \"\\\" alt=\\\"\\\"></p> <h2 id=\\\"3、-模型速度测试\\\"><font style=\\\"color:#020817\\\">3、 模型速度测试</font></h2> <p><font style=\\\"color:#020817\\\">qwen3 部署完成了，速度怎么样呢？点击 </font><a href=\\\"https://lmspeed.net/zh-CN\\\"><font style=\\\"color:#2f8ef4\\\">LM Speed</font></a><font style=\\\"color:#020817\\\"> 测试一下速度吧～</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_10___ + \"\\\" alt=\\\"\\\"></p> <h2 id=\\\"4、本地打包-ollama-和-open-webui-镜像\\\"><strong><font style=\\\"color:#020817\\\">4、</font><strong><strong><font style=\\\"color:#020817\\\">本地</font></strong></strong><font style=\\\"color:#020817\\\">打包 Ollama 和 Open WebUI 镜像</font></strong></h2> <p><font style=\\\"color:#67676c\\\">温馨提示，如果你只希望使用我们默认的镜像，那么下面的内容您无需关注。</font></p> <h3 id=\\\"41-clone-项目\\\"><font style=\\\"color:#020817\\\">4.1 clone 项目</font></h3> <pre><code class=\\\"language-bash\\\">git clone https://github.com/slmnb-lab/llm-deployment.git\\n</code></pre> <h3 id=\\\"42-修改镜像仓库地址名\\\"><font style=\\\"color:#020817\\\">4.2 修改镜像仓库地址名</font></h3> <p><font style=\\\"color:#67676c\\\">开始之前需要在天工开物中创建一个镜像仓库，镜像仓库名称为 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">{yourusername}</font><font style=\\\"color:#67676c\\\">，镜像标签为 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">30b-a3b</font><font style=\\\"color:#67676c\\\">。访问这里 </font><a href=\\\"https://tiangongkaiwu.top/#/console\\\"><font style=\\\"color:#2f8ef4\\\">初始化镜像仓库</font></a>。<font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">harbor.suanleme.cn/{yourusername}/ollama-webui-qwen3:30b-a3b</font><font style=\\\"color:#67676c\\\">是您创建的镜像仓库地址，这个参数在部署服务的时候会用到，记得替换成你的镜像仓库地址。</font></p> <pre><code class=\\\"language-yaml\\\">services:\\n  ollama-webui-qwen3:\\n    image: harbor.suanleme.cn/{yourusername}/ollama-webui-qwen3:30b-a3b  \\n    build: .\\n    labels:\\n      - suanleme_0.http.port=11434                   # 这里是 ollama 运行的端口，不要修改\\n      - suanleme_0.http.prefix=ollama-webui-qwen3     # 这里是发布的回传域名\\n    restart: unless-stopped\\n    deploy:\\n      resources:\\n        reservations:\\n          devices:\\n            - driver: nvidia\\n              count: all\\n              capabilities: [gpu]\\n    ports:\\n      - &quot;11434:11434&quot;                        # 这里是ollama运行的端口，不要修改\\n      - &quot;8080:8080&quot;                          # 这里是open webui运行的端口，不需要修改\\n</code></pre> <h3 id=\\\"43-运行打包脚本\\\"><font style=\\\"color:#020817\\\">4.3 运行打包脚本</font></h3> <p><font style=\\\"color:#020817\\\">执行成功之后，会在本地生成镜像</font></p> <pre><code class=\\\"language-bash\\\">cd llm-deployment/ollama-webui   # 进入 ollama 目录\\ndocker compose build       # 打包镜像\\n</code></pre> <h2 id=\\\"5、镜像上传\\\"><strong><font style=\\\"color:#020817\\\">5、镜像上传</font></strong></h2> <p><font style=\\\"color:#020817\\\">将打包的镜像上传到天工开物</font><font style=\\\"color:#020817\\\">的镜像仓库</font></p> <h3 id=\\\"51-登录镜像仓库\\\"><font style=\\\"color:#020817\\\">5.1 登录镜像仓库</font></h3> <p><font style=\\\"color:#020817\\\">username 需要替换为自己的天工开物【</font><strong><font style=\\\"color:#020817\\\">镜像仓库】</font></strong><font style=\\\"color:#020817\\\">的【</font><strong><font style=\\\"color:#020817\\\">仓库账号】</font></strong><font style=\\\"color:#020817\\\">！</font></p> <p><font style=\\\"color:#020817\\\">输入密码需要输入初始化镜像仓库时设置的密码</font></p> <pre><code class=\\\"language-bash\\\">### harbor.suanleme.cn 是固定值，{yourusername}需要替换为自己的镜像仓库的用户名！\\ndocker login harbor.suanleme.cn --username={yourusername}\\n\\n## 输入密码  镜像仓库的密码！\\n*******\\n</code></pre> <h3 id=\\\"52-上传镜像\\\"><font style=\\\"color:#020817\\\">5.2 上传镜像</font></h3> <p><font style=\\\"color:#020817\\\">执行以下代码，进行镜像上传</font></p> <pre><code class=\\\"language-bash\\\">## 为新生成的镜像打上标签\\ndocker tag harbor.suanleme.cn/{yourusername}/ollama-webui-qwen3:30b-a3b harbor.suanleme.cn/{yourusername}/ollama-webui-qwen3:30b-a3b\\n\\n## 上传镜像\\ndocker push harbor.suanleme.cn/{yourusername}/ollama-webui-qwen3:30b-a3b\\n</code></pre> <p><font style=\\\"color:#67676c\\\">备注：镜像比较大，如果推送失败了，多试几次就好了。</font><font style=\\\"color:#67676c\\\">😊</font></p> <p><br><br> <font style=\\\"color:#b2b2b2\\\">最后更新于: 2025/6/12 15:10</font></p> \";\n// Exports\nexport default code;"], "names": ["___HTML_LOADER_IMPORT_0___", "URL", "___HTML_LOADER_IMPORT_1___", "___HTML_LOADER_IMPORT_2___", "___HTML_LOADER_IMPORT_3___", "___HTML_LOADER_IMPORT_4___", "___HTML_LOADER_IMPORT_5___", "___HTML_LOADER_IMPORT_6___", "___HTML_LOADER_IMPORT_7___", "___HTML_LOADER_IMPORT_8___", "___HTML_LOADER_IMPORT_9___", "___HTML_LOADER_IMPORT_10___", "code"], "sourceRoot": ""}