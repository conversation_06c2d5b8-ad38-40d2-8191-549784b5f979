{"version": 3, "file": "js/docs12.0b72583f.js", "mappings": "yHACA,IAAIA,EAA6B,IAAIC,IAAI,aACrCC,EAA6B,IAAID,IAAI,YACrCE,EAA6B,IAAIF,IAAI,aACrCG,EAA6B,IAAIH,IAAI,aACrCI,EAA6B,IAAIJ,IAAI,aACrCK,EAA6B,IAAIL,IAAI,aACrCM,EAA6B,IAAIN,IAAI,aACrCO,EAA6B,IAAIP,IAAI,aACrCQ,EAA6B,IAAIR,IAAI,aACrCS,EAA6B,IAAIT,IAAI,aACrCU,EAA8B,IAAIV,IAAI,aACtCW,EAA8B,IAAIX,IAAI,aAEtCY,EAAO,m5CAAo8Cb,EAA6B,yQAAuRE,EAA6B,iCAAuCC,EAA6B,soBAA4pBC,EAA6B,yPAAqQC,EAA6B,0uBAAswBC,EAA6B,uMAAmNC,EAA6B,gYAAkZC,EAA6B,gKAAwKC,EAA6B,8KAA0LF,EAA6B,iYAAmZG,EAA6B,qGAA2GC,EAA8B,oHAA8HC,EAA8B,42CAEl3K,c", "sources": ["webpack://portal-ui/./src/docs/health-check.md"], "sourcesContent": ["// Imports\nvar ___HTML_LOADER_IMPORT_0___ = new URL(\"./imgs/health1.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_1___ = new URL(\"./imgs/health2.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_2___ = new URL(\"./imgs/health3.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_3___ = new URL(\"./imgs/health4.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_4___ = new URL(\"./imgs/health5.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_5___ = new URL(\"./imgs/health6.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_6___ = new URL(\"./imgs/health7.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_7___ = new URL(\"./imgs/health8.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_8___ = new URL(\"./imgs/health9.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_9___ = new URL(\"./imgs/health10.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_10___ = new URL(\"./imgs/health11.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_11___ = new URL(\"./imgs/health12.png\", import.meta.url);\n// Module\nvar code = \"<h1 id=\\\"健康检测\\\"><font style=\\\"color:#020817\\\">健康检测</font></h1> <h2 id=\\\"1概念定位\\\"><font style=\\\"color:#020817\\\">1.概念定位</font></h2> <h3 id=\\\"11-存活探针\\\"><font style=\\\"color:#020817\\\">1.1 存活探针</font></h3> <p><font style=\\\"color:#020817\\\">存活探针用于检测容器运行状态。当应用出现死锁或无响应时（如崩溃、死循环），天工开物平台会自动触发容器重启，无需人工干预。 存活探针不会等待就绪探针成功。如果你想在执行存活探针前等待，你可以使用启动探针。</font></p> <h3 id=\\\"12-就绪探针\\\"><font style=\\\"color:#020817\\\">1.2 就绪探针</font></h3> <p><font style=\\\"color:#020817\\\">就绪探针验证节点是否具备服务能力。在天工开物平台中，未通过检测的节点将被自动移出服务流量池，确保业务连续性。 如果就绪探针返回的状态为失败，会将该节点从所有对应服务中移出，同时启动一个新节点进行替换，保证服务的正常运行。 就绪探针在容器的整个生命期内持续运行。</font></p> <h3 id=\\\"13-启动探针\\\"><font style=\\\"color:#020817\\\">1.3 启动探针</font></h3> <p><font style=\\\"color:#020817\\\">启动探针保护慢启动容器不被误杀。天工开物平台会暂停存活/就绪检测，直到该探针确认应用已完成初始化。 如果配置了这类探针，它会禁用存活检测和就绪检测，直到启动探针成功为止。</font></p> <p><font style=\\\"color:#020817\\\">这类探针仅在启动时执行，不像存活探针和就绪探针那样周期性地运行。</font></p> <h2 id=\\\"2平台视角下的探针实际案例\\\"><font style=\\\"color:#020817\\\">2.平台视角下的探针实际案例</font></h2> <h3 id=\\\"21-启动探针检测容器中的应用是否已经启动\\\"><font style=\\\"color:#020817\\\">2.1 启动探针检测容器中的应用是否已经启动</font></h3> <p><font style=\\\"color:#020817\\\">启动探针保护慢启动容器不被误杀。天工开物平台会暂停存活/就绪检测，直到该探针确认应用已完成初始化。 如果配置了这类探针，它会</font><strong><font style=\\\"color:#020817\\\">禁用存活检测和就绪检测</font></strong><font style=\\\"color:#020817\\\">，直到启动探针成功为止。</font></p> <p><font style=\\\"color:#020817\\\">这类探针仅在启动时执行，不像存活探针和就绪探针那样周期性地运行。</font></p> <p><font style=\\\"color:#67676c\\\">示例镜像地址：harbor.suanleme.cn/xiditgkw/jupyter/base-notebook:latest 端口号配置：8888</font> <img src=\\\"\" + ___HTML_LOADER_IMPORT_0___ + \"\\\" alt=\\\"\\\"> <font style=\\\"color:#020817\\\">启动初始探针设置（下一部分会详细讲解参数及其意义）：</font></p> <p><font style=\\\"color:#020817\\\">修改端口号为 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">8888</font><font style=\\\"color:#020817\\\">（JupyterLab 默认端口）</font> <img src=\\\"\" + ___HTML_LOADER_IMPORT_1___ + \"\\\" alt=\\\"\\\"> 启动探针发现异常： <img src=\\\"\" + ___HTML_LOADER_IMPORT_2___ + \"\\\" alt=\\\"\\\"> <strong><font style=\\\"color:#020817\\\">异常原因：</font></strong></p> <ul> <li><font style=\\\"color:#020817\\\">启动探针配置不当：</font><ul> <li><font style=\\\"color:#020817\\\">路径 /live 返回 404：容器启动后，存活探针立即开始检查 /live 接口（初始延迟 0 秒），但该接口不存在（返回 404），导致探针失败。</font></li> <li><font style=\\\"color:#020817\\\">失败阈值触发：存活探针每 10 秒检查一次，超时时间仅 1 秒，连续 3 次失败后，判定容器不健康，触发重启。</font></li> </ul> </li> <li><font style=\\\"color:#020817\\\">服务初始化时间较长：</font><ul> <li><font style=\\\"color:#020817\\\">JupyterLab 及其扩展（如 jupyter_lsp、jupyter_server_terminals）加载需要时间（约 10 秒），但</font><strong><font style=\\\"color:#020817\\\">启动探针未等待服务完全就绪就开始检查</font></strong><font style=\\\"color:#020817\\\">。</font> <img src=\\\"\" + ___HTML_LOADER_IMPORT_3___ + \"\\\" alt=\\\"\\\"> <strong><font style=\\\"color:#020817\\\">更新启动探针配置：</font></strong><font style=\\\"color:#020817\\\"> 修改检查周期长一些 让 JupyterLab 先启动起来 避免启动时间过短 路径指向容器默认启动路径</font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">/lab</font> <img src=\\\"\" + ___HTML_LOADER_IMPORT_4___ + \"\\\" alt=\\\"\\\"> <strong><font style=\\\"color:#020817\\\">当前服务状态：</font></strong> <font style=\\\"color:#020817\\\">JupyterLab 服务正常运行 服务持续监听 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\"><a href=\\\"http://127.0.0.1:8888/lab\\\">http://127.0.0.1:8888/lab</a></font><font style=\\\"color:#020817\\\">。 用户访问 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">/</font><font style=\\\"color:#52b788;background-color:rgba(142,150,170,.14)\\\"> </font><font style=\\\"color:#020817\\\">路径触发 302 重定向，表明前端路由正常。</font> <font style=\\\"color:#020817\\\">无关键错误： 日志中未出现内核崩溃、探针失败或服务中断的报错。 内核消失问题（</font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">404 Kernel does not exist</font><font style=\\\"color:#020817\\\">）未出现。</font> <img src=\\\"\" + ___HTML_LOADER_IMPORT_5___ + \"\\\" alt=\\\"\\\"></li> </ul> </li> </ul> <h3 id=\\\"22-就绪探针容器检测是否准备好接收流量\\\"><font style=\\\"color:#020817\\\">2.2 就绪探针容器检测是否准备好接收流量</font></h3> <p><font style=\\\"color:#020817\\\">这里以我们预制好的镜像 Whisper 举例</font> <img src=\\\"\" + ___HTML_LOADER_IMPORT_6___ + \"\\\" alt=\\\"\\\"> <font style=\\\"color:#020817\\\">启动成功后进入健康检查设置中：</font> <font style=\\\"color:#020817\\\">修改路径为</font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">/docs#/Endpoints/asr_asr_post</font><font style=\\\"color:#020817\\\">（语音转文字默认接口）</font> <font style=\\\"color:#020817\\\">端口号改为：</font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">9000</font> <img src=\\\"\" + ___HTML_LOADER_IMPORT_7___ + \"\\\" alt=\\\"\\\"> <font style=\\\"color:#020817\\\">保存后可以通过容器日志查看到实时返回的接口信息</font> 服务运行正常：容器状态显示 “运行中”，日志中接口（ /docs）返回 200 状态码，表明服务已成功启动，准备好接收流量（检查周期每 20 秒返回一次） <img src=\\\"\" + ___HTML_LOADER_IMPORT_8___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"23-存活探针检测容器是否正常运行\\\"><font style=\\\"color:#020817\\\">2.3 存活探针检测容器是否正常运行</font></h3> <p><font style=\\\"color:#020817\\\">这里以我们预制好的镜像 Whisper 举例</font> <img src=\\\"\" + ___HTML_LOADER_IMPORT_6___ + \"\\\" alt=\\\"\\\"> <font style=\\\"color:#020817\\\">启动成功后进入健康检查设置中：</font> <font style=\\\"color:#020817\\\">修改路径为</font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">/docs#/Endpoints/asr_asr_post</font><font style=\\\"color:#020817\\\"> （语音转文字默认接口）</font> <font style=\\\"color:#020817\\\">端口号改为：</font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">9000</font> <img src=\\\"\" + ___HTML_LOADER_IMPORT_9___ + \"\\\" alt=\\\"\\\"> 服务运行正常：容器状态显示 “运行中”，日志中接口（ /docs）返回 200 状态码，表明服务已成功启动，准备好接收流量（检查周期每 10 秒返回一次） <img src=\\\"\" + ___HTML_LOADER_IMPORT_10___ + \"\\\" alt=\\\"\\\"></p> <h2 id=\\\"3平台视角下的探针参数作用详细解析\\\"><font style=\\\"color:#020817\\\">3.平台视角下的探针参数作用详细解析</font></h2> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_11___ + \"\\\" alt=\\\"\\\"></p> <p><strong><font style=\\\"color:#020817\\\">HTTPGet：</font></strong><font style=\\\"color:#020817\\\">通过发送 HTTP 请求并检查响应状态码来判断服务状态</font></p> <p><strong><font style=\\\"color:#020817\\\">TCPSocket：</font></strong><font style=\\\"color:#020817\\\">仅检查指定端口是否能建立 TCP 连接，不涉及具体业务逻辑</font></p> <p><strong><font style=\\\"color:#020817\\\">路径：</font></strong><font style=\\\"color:#020817\\\">仅 HTTP Get 类型需要，TCP Socket 类型不需要路径参数</font></p> <p><strong><font style=\\\"color:#020817\\\">端口：</font></strong><font style=\\\"color:#020817\\\">健康检查的目标端口号</font></p> <p><strong><font style=\\\"color:#020817\\\">初始延迟：</font></strong><font style=\\\"color:#020817\\\">0 秒（立即开始检查）</font></p> <p><strong><font style=\\\"color:#020817\\\">检查周期：</font></strong><font style=\\\"color:#020817\\\">X 秒（每 X 秒检测一次）</font></p> <p><strong><font style=\\\"color:#020817\\\">超时时间：</font></strong><font style=\\\"color:#020817\\\">X 秒（每次请求最长等待 X 秒）</font></p> <p><strong><font style=\\\"color:#020817\\\">失败阈值：</font></strong><font style=\\\"color:#020817\\\">X 次（连续失败 X 次判定启动失败）</font></p> <h2 id=\\\"4更多\\\"><font style=\\\"color:#020817\\\">4.更多</font></h2> <p><a href=\\\"https://kubernetes.io/zh-cn/docs/tasks/configure-pod-container/configure-liveness-readiness-startup-probes/\\\"><font style=\\\"color:#2f8ef4\\\">https://kubernetes.io/zh-cn/docs/tasks/configure-pod-container/configure-liveness-readiness-startup-probes/</font></a></p> <p><br><br> <font style=\\\"color:#b2b2b2\\\">最后更新于: 2025/6/19 17:07</font></p> \";\n// Exports\nexport default code;"], "names": ["___HTML_LOADER_IMPORT_0___", "URL", "___HTML_LOADER_IMPORT_1___", "___HTML_LOADER_IMPORT_2___", "___HTML_LOADER_IMPORT_3___", "___HTML_LOADER_IMPORT_4___", "___HTML_LOADER_IMPORT_5___", "___HTML_LOADER_IMPORT_6___", "___HTML_LOADER_IMPORT_7___", "___HTML_LOADER_IMPORT_8___", "___HTML_LOADER_IMPORT_9___", "___HTML_LOADER_IMPORT_10___", "___HTML_LOADER_IMPORT_11___", "code"], "sourceRoot": ""}