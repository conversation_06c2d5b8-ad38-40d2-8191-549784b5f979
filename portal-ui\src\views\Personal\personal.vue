<template>

  <div class="personal-center">
    <SlideNotification
        v-if="showNotification"
        :message="notificationMessage"
        :type="notificationType"
        @close="showNotification = false"
    />
    <div class="content-wrapper">
      <!-- Left Navigation Menu -->
      <div class="left-navigation">
        <div class="center-title">个人中心</div>
        <div class="nav-menu">
          <div class="nav-item1"
               :class="{ active: activeTab === 'basic' }"
               @click="switchTab('basic')">
            基本信息
          </div>
          <div class="nav-item1"
               :class="{ active: activeTab === 'verification' }"
               @click="switchTab('verification')">
            实名认证
          </div>
        </div>
      </div>

      <!-- Main Content Container -->
      <div class="main-container">
        <!-- Basic Information Tab -->
        <div v-if="activeTab === 'basic'" class="tab-content">
          <div class="section-header">
            <h2>基本信息</h2>
          </div>

          <div class="user-info-container">
            <!-- User Profile Card -->
            <div class="profile-card">
              <h3 class="card-title">用户信息</h3>
              <div class="user-avatar-section">
                <div class="avatar">
                  <img v-if="user.avatarUrl" :src="user.avatarUrl" class="avatar-img">
                  <span v-else class="avatar-text">{{userInitial()}}</span>
                </div>
                <div class="username-section">
                  <div class="username">
                    {{user.nickName || '未设置'}}
                    <span class="edit-icon" @click="showUsernameModal = true">🖊</span>
                  </div>
                  <div class="user-info-item">
                    <span class="info-label">手机号</span>
                    <span>{{user.phone || '未绑定'}}</span>
                  </div>
                  <div class="user-info-item">
                    <span class="info-label">性别</span>
                    <span>{{user.sex || '未设置'}}</span>
                    <span class="edit-icon" @click="showGenderModal = true">🖊</span>
                  </div>
                  <div class="user-info-item">
                    <span class="info-label">余额</span>
                    <span>¥{{user.balance?.toFixed(2) || '0.00'}}</span>
                  </div>
                  <div class="verification-badge" @click="openIdVerification">
                    <span class="badge">
                      <span class="check-icon"></span> 个人认证
                      <span v-if="verificationStatus" class="status-text">({{verificationStatus}})</span>
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Login Information Card -->
            <div class="login-card">
              <h3 class="card-title">登录信息</h3>

              <!-- Account & Password Section -->
              <div class="login-section">
                <h4 class="section-subtitle">账号密码</h4>

                <div class="login-item">
                  <div class="login-label">账号</div>
                  <div class="login-value">
                    {{user.username}}
                  </div>
                </div>

                <div class="login-item">
                  <div class="login-label">密码</div>
                  <div class="login-content">
                    <div class="login-description">设置密码后可通过账号登录</div>
                    <div class="login-value">
                      ••••••••
                      <button class="edit-btn" @click="showPasswordModal = true">修改</button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Security Phone Section -->
              <div class="login-section">
                <h4 class="section-subtitle">安全手机</h4>

                <div class="login-item">
                  <div class="login-label">手机号</div>
                  <div class="login-value">
                    {{user.phone}}
                  </div>
                </div>
              </div>

              <!-- Email Section -->
              <div class="login-section" v-if="user.email">
                <h4 class="section-subtitle">电子邮箱</h4>
                <div class="login-item">
                  <div class="login-label">邮箱</div>
                  <div class="login-value">
                    {{user.email || '未绑定'}}
                    <button class="edit-btn" @click="showEmailModal = true">修改</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- ID Verification Tab -->
        <div v-if="activeTab === 'verification'" class="tab-content">
          <div class="section-header">
            <h2>个人认证</h2>
            <div v-if="verificationError" class="verification-error">
              {{verificationError}}
            </div>
          </div>

          <div class="verification-container">
            <div v-if="user.isReal === 1" class="verified-info">
              <div class="verified-status">
                <i class="el-icon-success"></i> 已认证
              </div>
              <div class="verified-item">
                <span class="verified-label">真实姓名：</span>
                <span>{{ desensitizeName(user.realName) }}</span>
              </div>
              <div class="verified-item">
                <span class="verified-label">身份证号：</span>
                <span>{{ desensitizeIdCard(user.realId) }}</span>
              </div>
            </div>

            <div v-else class="verification-form">
              <div class="form-group">
                <label>真实姓名</label>
                <input
                    type="text"
                    v-model="realName"
                    placeholder="请输入真实姓名"
                    :class="{'error-input': realNameError}"
                >
                <div v-if="realNameError" class="error-text">
                  <i class="error-icon"></i> {{realNameError}}
                </div>
              </div>

              <div class="form-group">
                <label>身份证号码</label>
                <input
                    type="text"
                    v-model="idCardNumber"
                    placeholder="请输入身份证号码"
                    :class="{'error-input': idCardError}"
                >
                <div v-if="idCardError" class="error-text">
                  <i class="error-icon"></i> {{idCardError}}
                </div>
              </div>

              <div class="agreement-checkbox">
                <input type="checkbox" id="agreement" v-model="agreementChecked">
                <label for="agreement">
                  我已阅读并同意 天工开物的
                  <!-- <a href="#" class="link">服务条款</a> 和
                  <a href="#" class="link">隐私政策</a> -->
                  <router-link to="/help/user-agreement" class="link">服务条款</router-link> 和
                  <router-link to="/help/privacy-policy" class="link">隐私政策</router-link>

                </label>
              </div>

              <button class="submit-btn" :disabled="!canSubmitVerification" @click="submitIdVerification">提交</button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Password Modal -->
    <div v-if="showPasswordModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>修改密码</h3>
          <span class="close-btn" @click="showPasswordModal = false">&times;</span>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label>当前密码</label>
            <input
                type="password"
                v-model="currentPassword"
                placeholder="请输入当前密码"
            >
          </div>
          <div class="form-group">
            <label>新密码</label>
            <input
                type="password"
                v-model="newPassword"
                placeholder="请输入新密码"
                @blur="validateNewPassword"
            >
            <div v-if="passwordError" class="error-text">
              <i class="error-icon"></i> {{passwordError}}
            </div>
          </div>
          <div class="form-group">
            <label>确认新密码</label>
            <input
                type="password"
                v-model="confirmPassword"
                placeholder="请再次输入新密码"
                @blur="validateConfirmPassword"
            >
            <div v-if="confirmPasswordError" class="error-text">
              <i class="error-icon"></i> {{confirmPasswordError}}
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="cancel-btn" @click="showPasswordModal = false">取消</button>
          <button
              class="confirm-btn"
              @click="changePassword"
          >
            确认
          </button>
        </div>
      </div>
    </div>

    <!-- Username Modal -->
    <div v-if="showUsernameModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>修改昵称</h3>
          <span class="close-btn" @click="showUsernameModal = false">&times;</span>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label>新昵称</label>
            <input type="text" v-model="newUsername" placeholder="请输入新昵称">
          </div>
        </div>
        <div class="modal-footer">
          <button class="cancel-btn" @click="showUsernameModal = false">取消</button>
          <button class="confirm-btn" @click="changeUsername">确认</button>
        </div>
      </div>
    </div>

    <!-- Gender Modal -->
    <div v-if="showGenderModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>设置性别</h3>
          <span class="close-btn" @click="showGenderModal = false">&times;</span>
        </div>
        <div class="modal-body">
          <div class="gender-options">
            <div class="gender-option">
              <input type="radio" id="male" name="gender" value="男" v-model="selectedGender">
              <label for="male">男</label>
            </div>
            <div class="gender-option">
              <input type="radio" id="female" name="gender" value="女" v-model="selectedGender">
              <label for="female">女</label>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="cancel-btn" @click="showGenderModal = false">取消</button>
          <button class="confirm-btn" @click="changeGender">确认</button>
        </div>
      </div>
    </div>

    <!-- Phone Modal -->
    <div v-if="showPhoneModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>修改手机号</h3>
          <span class="close-btn" @click="showPhoneModal = false">&times;</span>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label>新手机号</label>
            <input type="text" v-model="newPhone" placeholder="请输入新手机号">
          </div>
          <div class="form-group">
            <label>验证码</label>
            <div class="verify-code-input">
              <input type="text" v-model="verifyCode" placeholder="请输入验证码">
              <button class="get-code-btn" @click="getVerifyCode" :disabled="isCountingDown">
                {{ countdown > 0 ? `${countdown}秒后重试` : '获取验证码' }}
              </button>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="cancel-btn" @click="showPhoneModal = false">取消</button>
          <button class="confirm-btn" @click="changePhone">确认</button>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
import Layout from "@/components/common/Layout-header";
import {getAnyData, postAnyData} from "@/api/login";
import Cookies from 'js-cookie';
import SlideNotification from '@/components/common/header/SlideNotification.vue';


export default {
  name: 'PersonalCenter',
  components: { Layout,SlideNotification },
  data() {
    return {
      activeTab: 'basic',
      user: {
        id: null,
        username: '',
        nickName: '',
        password: '',
        avatarUrl: null,
        isChangingPassword: false,

        sex: '',
        phone: '',
        email: null,
        balance: 0,
        isReal: 0,
        realName: '',
        realId: '',
        realNumber: 0,
        tenantId: '',
        isLogin: 0,
        notificationMessage: '',
        notificationType: 'info',
      },
      showNotification: false,
      // Password related
      showPasswordModal: false,
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
      passwordError: '',
      confirmPasswordError: '',

      // Username related
      showUsernameModal: false,
      newUsername: '',

      // Gender related
      showGenderModal: false,
      selectedGender: '',

      // Phone related
      showPhoneModal: false,
      newPhone: '',
      verifyCode: '',
      countdown: 0,
      isCountingDown: false,

      // ID Verification related
      realName: '',
      idCardNumber: '',
      realNameError: '',
      idCardError: '',
      verificationError: '',
      agreementChecked: false,
      verificationStatus: '',
      isVerified: false,

      // Cookie related
      userInfoCookieName: 'user_info',
      userInfoExpiryDays: 7
    }
  },
  computed: {
    canSubmitVerification() {
      return this.realName &&
          this.idCardNumber &&
          !this.realNameError &&
          !this.idCardError &&
          this.agreementChecked;
    }
  },
  methods: {

    switchTab(tab) {
      this.activeTab = tab;
      this.$router.push({ query: { ...this.$route.query, activeTab: tab } });
    },
    userInitial() {
      return this.user.nickName && this.user.nickName.length > 0
          ? this.user.nickName.charAt(0).toUpperCase()
          : 'N';
    },

    openIdVerification() {
      if (this.user.isReal === 1) {
        this.$message.info('您已通过实名认证');
        return;
      }
      this.activeTab = 'verification';
    },

    // 获取完整用户信息
    async getUserInfo() {
      try {
        const res = await postAnyData("/logout/cilent/getInfo");
        if (res.data.code === 200) {
          const userData = res.data.data;
          this.user = {
            ...this.user,
            ...userData
          };
          // 根据isReal更新认证状态
          this.verificationStatus = userData.isReal === 1 ? '已认证' : '未认证';
          this.saveUserInfoToCookie(userData);
          return userData;
        } else {
          throw new Error(res.data.msg || "获取用户信息失败");
        }
      } catch (error) {
        throw error;
      }
    },

    validateNewPassword() {
      const newPassword = this.newPassword?.trim();
      this.passwordError = '';

      // 1. 检查是否为空
      if (!newPassword) {
        this.passwordError = '请输入新密码';
        return;
      }

      // 2. 检查密码长度
      const hasMinLength = newPassword.length >= 8;
      if (!hasMinLength) {
        this.passwordError = '密码长度至少为8位';
        return;
      }

      // 3. 检查密码复杂度
      const hasLetter = /[a-zA-Z]/.test(newPassword);
      const hasNumber = /[0-9]/.test(newPassword);
      const hasSymbol = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(newPassword);
      const hasUpperLower = /[A-Z]/.test(newPassword) && /[a-z]/.test(newPassword);

      let strengthCount = 0;
      if (hasLetter && hasNumber) strengthCount++; // 字母+数字组合
      if (hasSymbol) strengthCount++;              // 包含特殊符号
      if (hasUpperLower) strengthCount++;          // 包含大小写字母

      // 4. 最终判断：长度必须 ≥ 8，复杂度至少满足 2 项
      if (strengthCount < 2) {
        const missing = [];
        if (!(hasLetter && hasNumber)) missing.push('包含数字和字母');
        if (!hasSymbol) missing.push('包含特殊符号');
        if (!hasUpperLower) missing.push('包含大小写字母');
        this.passwordError = `密码需至少满足以下两项要求：${missing.join('、')}`;
        return;
      }

      // 5. 校验确认密码是否一致
      if (this.confirmPassword) {
        this.validateConfirmPassword();
      }
    },


    validateConfirmPassword() {
      if (!this.confirmPassword) {
        this.confirmPasswordError = '请确认新密码';
      } else if (this.confirmPassword !== this.newPassword) {
        this.confirmPasswordError = '两次输入的密码不一致';
      } else {
        this.confirmPasswordError = '';
      }
    },

    async changePassword() {
      if (!this.currentPassword) {
        this.showNotificationMessage('请输入当前密码', 'warning');
        return;
      }

      this.validateNewPassword();
      this.validateConfirmPassword();

      if (this.passwordError || this.confirmPasswordError) {
        return;
      }

      this.isChangingPassword = true;
      try {
        const currentUserInfo = await this.getUserInfo();
        const params = {
          ...currentUserInfo,
          password: this.currentPassword,
          newPassword: this.newPassword,
        };


        const res = await postAnyData("/logout/cilent/changePwd", params);
        if (res.data.code === 200) {
          this.showNotificationMessage('密码修改成功', 'success');
          this.showPasswordModal = false;
          this.currentPassword = '';
          this.newPassword = '';
          this.confirmPassword = '';
        } else {
          this.$nextTick(()=>{
            this.showNotificationMessage('原密码输入错误，请检查重试', 'error');

          })
        }
      } catch (error) {
        this.showNotificationMessage('密码修改过程中出错，请稍后重试', 'error');
      }
      finally {
        this.isChangingPassword = false;
      }
    },
    // 显示通知的方法
    showNotificationMessage(message, type = 'info') {
      this.notificationMessage = message;
      this.notificationType = type;
      this.showNotification = true;

      // 3秒后自动关闭通知
      setTimeout(() => {
        this.showNotification = false;
      }, 3000);
    },
    // Username related methods
    async changeUsername() {
      if (!this.newUsername.trim()) {
        this.$message.warning('昵称不能为空');
        return;
      }

      try {
        const currentUserInfo = await this.getUserInfo();

        const params = {
          ...currentUserInfo,
          nickName: this.newUsername
        };

        const res = await postAnyData("/logout/cilent/updateInfo", params);
        if (res.data.code === 200) {
          this.user.nickName = this.newUsername;
          this.saveUserInfoToCookie({
            ...currentUserInfo,
            nickName: this.newUsername
          });

          this.showUsernameModal = false;
          this.newUsername = '';
          this.$message.success('昵称修改成功');
        } else {
          this.$message.error(res.data.message || '修改失败');
        }
      } catch (error) {
        this.$message.error('网络错误');
      }
    },

    // Gender related methods
    async changeGender() {
      if (!this.selectedGender) {
        this.$message.warning('请选择性别');
        return;
      }

      try {
        const currentUserInfo = await this.getUserInfo();

        const params = {
          ...currentUserInfo,
          sex: this.selectedGender
        };

        const res = await postAnyData("/logout/cilent/updateInfo", params);
        if (res.data.code === 200) {
          this.user.sex = this.selectedGender;
          this.saveUserInfoToCookie({
            ...currentUserInfo,
            sex: this.selectedGender,
            // isReal : 0
          });

          this.showGenderModal = false;
          this.$message.success('性别设置成功');
        } else {
          this.$message.error(res.data.message || '修改失败');
        }
      } catch (error) {
        this.$message.error('网络错误');
      }
    },

    // 姓名脱敏处理（保留姓氏，名字用*代替）
    desensitizeName(name) {
      if (!name) return '';
      if (name.length <= 1) return name;
      return name.charAt(0) + '*'.repeat(name.length - 1);
    },

    // 身份证号脱敏处理（显示前4位和后4位，中间用*代替）
    desensitizeIdCard(idCard) {
      if (!idCard) return '';
      if (idCard.length <= 8) return idCard;
      return idCard.substring(0, 4) + '********' + idCard.substring(idCard.length - 4);
    },

    // Phone related methods
    getVerifyCode() {
      if (!this.newPhone) {
        this.$message.warning('请输入手机号');
        return;
      }

      // 模拟发送验证码
      this.isCountingDown = true;
      this.countdown = 60;
      const timer = setInterval(() => {
        this.countdown--;
        if (this.countdown <= 0) {
          clearInterval(timer);
          this.isCountingDown = false;
        }
      }, 1000);

      this.$message.success('验证码已发送');
    },

    async changePhone() {
      if (!this.newPhone) {
        this.$message.warning('请输入新手机号');
        return;
      }

      if (!this.verifyCode) {
        this.$message.warning('请输入验证码');
        return;
      }

      try {
        const currentUserInfo = await this.getUserInfo();

        const params = {
          ...currentUserInfo,
          phone: this.newPhone
        };

        const res = await postAnyData("/logout/cilent/updateInfo", params);
        if (res.data.code === 200) {
          this.user.phone = this.newPhone;
          this.saveUserInfoToCookie({
            ...currentUserInfo,
            phone: this.newPhone
          });

          this.showPhoneModal = false;
          this.newPhone = '';
          this.verifyCode = '';
          this.$message.success('手机号修改成功');
        } else {
          this.$message.error(res.data.message || '修改失败');
        }
      } catch (error) {
        this.$message.error('网络错误');
      }
    },

    // ID Verification methods
    validateRealName() {
      if (!this.realName) {
        this.realNameError = '请输入正确的姓名';
        return false;
      }

      const nameRegex = /^[\u4e00-\u9fa5]{2,10}$/;
      if (!nameRegex.test(this.realName)) {
        this.realNameError = '请输入正确的姓名';
        return false;
      }

      this.realNameError = '';
      return true;
    },

    validateIdCard() {
      if (!this.idCardNumber) {
        this.idCardError = '请输入正确的身份证号码';
        return false;
      }

      const idCardRegex = /(^\d{15$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
      if (!idCardRegex.test(this.idCardNumber)) {
        this.idCardError = '请输入正确的身份证号码';
        return false;
      }

      this.idCardError = '';
      return true;
    },

    async submitIdVerification() {
      const isNameValid = this.validateRealName();
      const isIdCardValid = this.validateIdCard();

      if (!isNameValid || !isIdCardValid || !this.agreementChecked) {
        return;
      }

        const currentUserInfo = await this.getUserInfo();

        const verificationData = {
          name: this.realName,
          id: this.idCardNumber,
          username: currentUserInfo.username,
          userId: currentUserInfo.id
        };

        const res = await postAnyData("/idVerification/verify", verificationData);
        if (res.data.code === 200) {
          this.user.realName = this.realName;
          this.user.realId = this.idCardNumber;
          this.user.isReal = 1;

          this.saveUserInfoToCookie({
            ...currentUserInfo,
            realName: this.realName,
            realId: this.idCardNumber,
            isReal: 1
          });

          // this.verificationStatus = res.data.msg;
          this.showNotificationMessage(res.data.msg, 'success');
          // setTimeout(() => {
          //   location.reload(true);
          // }, 3000);
        } else {
          // this.verificationError = res.data.msg || '实名认证失败，请检查信息是否正确';
          this.showNotificationMessage(res.data.msg, 'error');
        }

    },

    // Cookie related methods
    getUserInfoFromCookie() {
      const userInfoStr = Cookies.get(this.userInfoCookieName);
      if (userInfoStr) {
        try {
          return JSON.parse(userInfoStr);
        } catch (e) {
          return null;
        }
      }
      return null;
    },

    saveUserInfoToCookie(userInfo) {
      const infoToSave = {
        ...userInfo,
        timestamp: new Date().getTime()
      };
      Cookies.set(this.userInfoCookieName, JSON.stringify(infoToSave), {
        expires: this.userInfoExpiryDays,
        secure: true,
        sameSite: 'strict'
      });
    },

    isCacheValid(cachedData) {
      if (!cachedData || !cachedData.timestamp) return false;
      const oneDay = 24 * 60 * 60 * 1000; // 1 day in milliseconds
      return (new Date().getTime() - cachedData.timestamp) < oneDay;
    },

    async fetchUserInfo() {
      const cachedUserInfo = this.getUserInfoFromCookie();

      if (cachedUserInfo && this.isCacheValid(cachedUserInfo)) {
        this.updateLocalUserData(cachedUserInfo);
        return;
      }

      await this.getUserInfo();
    },

    updateLocalUserData(userData) {
      this.user = {
        id: userData.id || this.user.id,
        username: userData.username || this.user.username,
        nickName: userData.nickName || this.user.nickName,
        password: this.currentPassword || this.user.password,
        avatarUrl: userData.avatarUrl || this.user.avatarUrl,
        sex: userData.sex || this.user.sex,
        phone: userData.phone || this.user.phone,
        email: userData.email || this.user.email,
        balance: userData.balance || this.user.balance,
        isReal: userData.isReal !== undefined ? userData.isReal : this.user.isReal,
        realName: userData.realName || this.user.realName,
        realId: userData.realId || this.user.realId,
        realNumber: userData.realNumber || this.user.realNumber,
        tenantId: userData.tenantId || this.user.tenantId,
        isLogin: userData.isLogin || this.user.isLogin
      };
      // 根据isReal更新认证状态
      this.verificationStatus = this.user.isReal === 1 ? '已认证' : '未认证';
    }
  },
  watch: {
    realName() {
      this.validateRealName();
    },
    idCardNumber() {
      this.validateIdCard();
    },
    newPassword() {
      this.validateNewPassword();
    },
    confirmPassword() {
      this.validateConfirmPassword();
    }
  },
  async created() {
    this.user = {
      username: '',
      phone: ''
    };

    try {
      await this.fetchUserInfo();
      this.selectedGender = this.user.sex;

      // 添加这部分代码，从URL参数中读取activeTab
      if (this.$route.query.activeTab === 'verification') {
        this.activeTab = 'verification';
      }
    } catch (error) {
      // console.error(error);
    }
  }
}
</script>

<style scoped>
/* Main Layout */
.personal-center {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: #f5f5f5;
  min-height: 100vh;
  width: 100%;
}

.content-wrapper {
  display: flex;
  width: 100%;
  min-height: calc(100vh - 64px);
}

/* Left Navigation Styles */
.left-navigation {
  min-width: 200px;
  background-color: #fff;
  border-right: 1px solid #eee;
}

.center-title {
  font-weight: 700;
  font-size: 18px;
  color: #303133;
  padding: 20px 16px;
  margin: 0;
  border-bottom: 1px solid #ebeef5;
}

.nav-menu {
  width: 100%;
}

.nav-item1 {
  padding: 14px 16px;
  cursor: pointer;
  transition: all 0.3s;
}

.nav-item1 a {
  display: flex;
  align-items: center;
  color: #103680;
  text-decoration: none;
}

.nav-item1 i {
  margin-right: 8px;
  font-size: 16px;
}

.nav-item1.active {
  background-color: #ecf5ff;
  color: #409eff;
}

.nav-item1.active a {
  color: #409eff !important;
}

.nav-item1:hover {
  background-color: #f5f7fa !important;
}

/* Main Container Styles */
.main-container {
  flex: 1;
  padding: 20px;
  background-color: #f5f5f5;
}

.tab-content {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  padding: 20px;
  margin-bottom: 20px;
}

.section-header {
  margin-bottom: 20px;
  position: relative;
}

.section-header h2 {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin: 0;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.verification-error {
  color: #f56c6c;
  font-size: 14px;
  margin-top: 10px;
}

/* User Info Container */
.user-info-container {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 20px;
}

/* Card Styles */
.profile-card, .login-card {
  background-color: #fff;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-top: 0;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

/* User Avatar Section */
.user-avatar-section {
  display: flex;
  align-items: center;
}

.avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: #f2f0ff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  overflow: hidden;
}

.avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-text {
  font-size: 36px;
  color: #1890ff;
}

.username-section {
  display: flex;
  flex-direction: column;
}

.username {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.user-info-item {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.info-label {
  background-color: #f0f0f0;
  padding: 2px 5px;
  border-radius: 4px;
  margin-right: 5px;
  font-size: 12px;
}

.edit-icon {
  margin-left: 5px;
  color: #666;
  cursor: pointer;
}

/* Badge Styles */
.verification-badge {
  margin-top: 10px;
}

.badge {
  display: inline-flex;
  align-items: center;
  background-color: #e6f7ff;
  color: #1890ff;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
}

.check-icon {
  display: inline-block;
  width: 12px;
  height: 12px;
  background-color: #1890ff;
  margin-right: 4px;
  border-radius: 50%;
}

/* Verified Info */
.verified-info {
  padding: 20px;
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 4px;
}

.verified-item {
  margin-bottom: 10px;
  font-size: 14px;
}

.verified-label {
  font-weight: 500;
  color: #333;
}

.verified-status {
  color: #52c41a;
  font-weight: 500;
  margin-top: 15px;
  display: flex;
  align-items: center;
}

.verified-status i {
  margin-right: 5px;
  font-size: 16px;
}

/* Login Info Styles */
.login-section {
  margin-bottom: 20px;
}

.section-subtitle {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin: 15px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.login-item {
  margin-bottom: 16px;
}

.login-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.login-content {
  margin-top: 5px;
}

.login-description {
  font-size: 12px;
  color: #999;
  margin-bottom: 5px;
}

.login-value {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  color: #333;
}

/* Verification Form */
.verification-container {
  max-width: 300px;
  margin: 0 auto;
}

.verification-form {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: #333;
}

.form-group input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s;
}

.form-group input:focus {
  border-color: #1890ff;
  outline: none;
}

.verified-info {
  padding: 20px;
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 4px;
}

.verified-item {
  margin-bottom: 10px;
  font-size: 14px;
}

.verified-label {
  font-weight: 500;
  color: #333;
}

.verified-status {
  color: #52c41a;
  font-weight: 500;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.verified-status i {
  margin-right: 5px;
  font-size: 16px;
}

.error-input {
  border-color: #f5222d !important;
}

.error-text {
  color: #f5222d;
  font-size: 12px;
  margin-top: 5px;
  display: flex;
  align-items: center;
}

.error-icon {
  display: inline-block;
  width: 14px;
  height: 14px;
  background-color: #f5222d;
  border-radius: 50%;
  margin-right: 5px;
}

.agreement-checkbox {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
}

.agreement-checkbox input {
  margin-right: 8px;
  margin-top: 3px;
}

.agreement-checkbox label {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.link {
  color: #1890ff;
  text-decoration: none;
}

.submit-btn {
  width: 100%;
  padding: 12px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.submit-btn:hover {
  background-color: #40a9ff;
}

.submit-btn:disabled {
  background-color: #d9d9d9;
  cursor: not-allowed;
}

/* Button Styles */
.edit-btn {
  color: #1890ff;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;
}

/* Modal Styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: #fff;
  border-radius: 4px;
  width: 400px;
  max-width: 90%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.modal-header {
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  font-size: 16px;
  font-weight: 500;
  margin: 0;
}

.close-btn {
  font-size: 24px;
  color: #999;
  cursor: pointer;
}

.modal-body {
  padding: 20px;
}

.modal-footer {
  padding: 15px 20px;
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid #eee;
}

.cancel-btn, .confirm-btn {
  padding: 8px 15px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
}

.cancel-btn {
  background-color: #f7f7f7;
  border: 1px solid #ddd;
  color: #666;
  margin-right: 10px;
}

.confirm-btn {
  background-color: #1890ff;
  border: none;
  color: white;
}

/* Gender Options */
.gender-options {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.gender-option {
  display: flex;
  align-items: center;
}

.gender-option input {
  margin-right: 10px;
}

/* Phone Verification Code Input */
.verify-code-input {
  display: flex;
  gap: 10px;
}

.verify-code-input input {
  flex: 1;
}

.get-code-btn {
  padding: 0 15px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  white-space: nowrap;
}

.get-code-btn:disabled {
  background-color: #d9d9d9;
  cursor: not-allowed;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .content-wrapper {
    flex-direction: column;
  }

  .left-navigation {
    width: 100%;
    min-height: auto;
  }

  .user-info-container {
    grid-template-columns: 1fr;
  }

  .modal-content {
    width: 90%;
  }
}

@media (max-width: 480px) {
  .user-avatar-section {
    flex-direction: column;
    align-items: flex-start;
  }

  .avatar {
    margin-bottom: 15px;
  }

  .verify-code-input {
    flex-direction: column;
  }

  .get-code-btn {
    padding: 10px;
    width: 100%;
  }
}
</style>
