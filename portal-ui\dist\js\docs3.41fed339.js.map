{"version": 3, "file": "js/docs3.41fed339.js", "mappings": "wHACA,IAAIA,EAA6B,IAAIC,IAAI,aACrCC,EAA6B,IAAID,IAAI,aACrCE,EAA6B,IAAIF,IAAI,aACrCG,EAA6B,IAAIH,IAAI,aACrCI,EAA6B,IAAIJ,IAAI,aACrCK,EAA6B,IAAIL,IAAI,aACrCM,EAA6B,IAAIN,IAAI,aACrCO,EAA6B,IAAIP,IAAI,aACrCQ,EAA6B,IAAIR,IAAI,YAErCS,EAAO,umBAA0nBV,EAA6B,iNAA6NE,EAA6B,qJAA+JC,EAA6B,sIAAgJC,EAA6B,uHAA+HC,EAA6B,8FAAwGC,EAA6B,6DAAqEC,EAA6B,qHAA+HC,EAA6B,sBAA4BC,EAA6B,wwHAE71D,c", "sources": ["webpack://portal-ui/./src/docs/code-former.md"], "sourcesContent": ["// Imports\nvar ___HTML_LOADER_IMPORT_0___ = new URL(\"./imgs/universal1.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_1___ = new URL(\"./imgs/universal2.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_2___ = new URL(\"./imgs/codeformer3.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_3___ = new URL(\"./imgs/codeformer4.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_4___ = new URL(\"./imgs/codeformer5.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_5___ = new URL(\"./imgs/codeformer6.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_6___ = new URL(\"./imgs/codeformer7.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_7___ = new URL(\"./imgs/codeformer8.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_8___ = new URL(\"./imgs/codeformer9.png\", import.meta.url);\n// Module\nvar code = \"<h1 id=\\\"容器化部署-codeformer\\\">容器化部署 CodeFormer</h1> <p><font style=\\\"color:#020817\\\">本指南详细阐述了在天工开物平台上，高效部署与使用 CodeFormer 项目的技术方案。CodeFormer 通过先进的算法优化图像细节，提升清晰度，保持自然和真实的视觉效果。支持图片和视频的高清修复，操作简单，开源免费，适用于家庭相册修复、社交媒体照片优化以及专业图像处理等多种场景。</font></p> <h2 id=\\\"1在天工开物上运行-codeformer\\\">1.在天工开物上运行 CodeFormer</h2> <p><font style=\\\"color:#020817\\\">天工开物平台提供预构建的 CodeFormer 容器镜像，用户无需本地复杂环境配置，可快速完成部署并启用服务。以下是详细部署步骤：</font></p> <h3 id=\\\"11-创建部署服务\\\">1.1 创建部署服务</h3> <p><font style=\\\"color:#020817\\\">登录<a href=\\\"https://tiangongkaiwu.top/portal/#/console\\\"><font style=\\\"color:#06c\\\">天工开物控制台</font></a>，在控制台首页点击“弹性部署服务”进入管理页面。</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_0___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"12-选择-gpu-型号\\\">1.2 选择 GPU 型号</h3> <p><font style=\\\"color:#020817\\\">根据实际需求选择 GPU 型号：</font></p> <p><font style=\\\"color:#020817\\\">初次使用或调试阶段，推荐配置单张 NVIDIA RTX 4090 GPU</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_1___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"13-选择预制镜像\\\">1.3 选择预制镜像</h3> <p><font style=\\\"color:#020817\\\">在“服务配置”模块切换至“预制服务”选项卡，选择 CodeFormer 官方镜像。</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_2___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"14-部署并访问服务\\\">1.4 部署并访问服务</h3> <p><font style=\\\"color:#020817\\\">点击“部署服务”，平台将自动拉取镜像并启动容器。</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_3___ + \"\\\" alt=\\\"\\\"></p> <p><font style=\\\"color:#020817\\\">部署完成后，在“快捷访问”中复制端口为 7860 的公网访问链接，后续是通过该地址调用服务。</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_4___ + \"\\\" alt=\\\"\\\"></p> <h2 id=\\\"2-快速上手\\\">2. 快速上手</h2> <h3 id=\\\"21-系统使用说明\\\">2.1 系统使用说明</h3> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_5___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"22-参考效果\\\">2.2 参考效果</h3> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_6___ + \"\\\" alt=\\\"\\\"></p> <h2 id=\\\"3api-使用示例\\\">3.API 使用示例</h2> <p><font style=\\\"color:#020817\\\">拖动到页面最底部：</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_7___ + \"\\\" alt=\\\"\\\"><img src=\\\"\" + ___HTML_LOADER_IMPORT_8___ + \"\\\" alt=\\\"\\\"></p> <pre><code class=\\\"language-python\\\">import requests\\nimport base64\\nimport os\\nimport mimetypes\\nAPI_URL = &quot;https://d07071835-codeformer070718-318-pry1oguz-7860.550c.cloud/run/predict&quot;\\ndef get_mime_type(filepath):\\n    &quot;&quot;&quot;\\n    根据文件扩展名确定文件的 MIME 类型。\\n    &quot;&quot;&quot;\\n    mime_type, _ = mimetypes.guess_type(filepath)\\n    return mime_type if mime_type else &#39;application/octet-stream&#39;\\ndef encode_image_to_base64(filepath):\\n    &quot;&quot;&quot;\\n    将图像文件编码为带有数据 URI 前缀的 base64 字符串。\\n    &quot;&quot;&quot;\\n    if not os.path.exists(filepath):\\n        raise FileNotFoundError(f&quot;图片文件未找到：{filepath}&quot;)\\n    mime_type = get_mime_type(filepath)\\n    with open(filepath, &quot;rb&quot;) as image_file:\\n        encoded_string = base64.b64encode(image_file.read()).decode(&#39;utf-8&#39;)\\n    return f&quot;data:{mime_type};base64,{encoded_string}&quot;\\ndef save_base64_to_image(base64_string, output_path):\\n    &quot;&quot;&quot;\\n    解码 base64 字符串 (无论是否带有数据 URI) 并将其保存为图像文件。\\n    &quot;&quot;&quot;\\n    # 如果存在数据 URI 前缀，则移除它\\n    if &#39;,&#39; in base64_string:\\n        _header, encoded_data = base64_string.split(&#39;,&#39;, 1)\\n    else:\\n        encoded_data = base64_string\\n    image_data = base64.b64decode(encoded_data)\\n    with open(output_path, &quot;wb&quot;) as image_file:\\n        image_file.write(image_data)\\n    print(f&quot;图像已成功保存至：{output_path}&quot;)\\ndef main():\\n    &quot;&quot;&quot;\\n    运行 CodeFormer API 请求的主函数。\\n    &quot;&quot;&quot;\\n    # --- 配置区域 ---\\n    # 1. 【请修改】将 &#39;path/to/your/input_image.jpg&#39; 替换为您的输入图片路径\\n    input_image_path = &quot;path/to/your/input_image.jpg&quot;\\n    # 2. 【可修改】设置输出图片的文件名\\n    output_image_path = &quot;restored_image.png&quot;\\n    # 3. 【可修改】根据需要调整 API 参数\\n    background_enhance = True       # 是否增强背景\\n    face_upsample = True            # 是否对人脸进行超分辨率\\n    rescaling_factor = 2            # 缩放因子 (最高为 4)\\n    codeformer_fidelity = 0.5       # Codeformer 保真度 (0 表示更好的质量，1 表示更好的人脸身份保留)\\n    # --- 配置结束 ---\\n    print(&quot;开始图像修复流程...&quot;)\\n    try:\\n        # 步骤 1: 将输入图像编码为 base64\\n        base64_image = encode_image_to_base64(input_image_path)\\n        print(f&quot;成功编码图像：{input_image_path}&quot;)\\n        # 步骤 2: 准备 API 请求的 payload\\n        payload = {\\n            &quot;data&quot;: [\\n                base64_image,\\n                background_enhance,\\n                face_upsample,\\n                rescaling_factor,\\n                codeformer_fidelity,\\n            ]\\n        }\\n        # 步骤 3: 发送 POST 请求\\n        print(f&quot;正在向 CodeFormer API ({API_URL}) 发送请求...&quot;)\\n        response = requests.post(API_URL, json=payload, timeout=300) # 设置 300 秒超时\\n        response.raise_for_status()  # 如果请求失败 (例如 4xx 或 5xx 错误), 则会抛出异常\\n        print(&quot;请求成功，正在处理返回结果...&quot;)\\n        response_data = response.json()\\n        # 步骤 4: 提取并保存输出图像\\n        output_base64 = response_data[&quot;data&quot;][0]\\n        save_base64_to_image(output_base64, output_image_path)\\n        # 可选：打印 API 处理时长\\n        duration = response_data.get(&quot;duration&quot;)\\n        if duration:\\n            print(f&quot;API 处理耗时 {duration:.2f} 秒。&quot;)\\n    except FileNotFoundError as e:\\n        print(f&quot;错误：{e}&quot;)\\n        print(&quot;请确保 &#39;input_image_path&#39; 设置了正确的图片文件路径。&quot;)\\n    except requests.exceptions.RequestException as e:\\n        print(f&quot;API 请求时发生错误：{e}&quot;)\\n    except (KeyError, IndexError) as e:\\n        print(f&quot;处理 API 响应时出错，格式可能不正确：{e}&quot;)\\n        print(&quot;API 返回内容：&quot;, response.text if &#39;response&#39; in locals() else &#39;N/A&#39;)\\n    except Exception as e:\\n        print(f&quot;发生未知错误：{e}&quot;)\\nif __name__ == &quot;__main__&quot;:\\n    main()\\n</code></pre> <p><br><br> <font style=\\\"color:#b2b2b2\\\">最后更新于: 2025/7/14 15:36</font></p> \";\n// Exports\nexport default code;"], "names": ["___HTML_LOADER_IMPORT_0___", "URL", "___HTML_LOADER_IMPORT_1___", "___HTML_LOADER_IMPORT_2___", "___HTML_LOADER_IMPORT_3___", "___HTML_LOADER_IMPORT_4___", "___HTML_LOADER_IMPORT_5___", "___HTML_LOADER_IMPORT_6___", "___HTML_LOADER_IMPORT_7___", "___HTML_LOADER_IMPORT_8___", "code"], "sourceRoot": ""}