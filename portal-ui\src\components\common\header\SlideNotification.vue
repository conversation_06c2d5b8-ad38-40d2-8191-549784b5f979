<template>
  <transition name="slide">
    <div v-if="visible"
         :class="['notification', `notification-${type}`]"
         role="alert"
         :style="{ minHeight: minHeight }">
      <div class="notification-content">
        <div class="icon-wrapper" v-if="type === 'error'">
          <span class="error-icon">×</span>
        </div>
        <span class="message">{{ message }}</span>
        <button v-if="closable" class="close-btn" @click="close">×</button>
      </div>
    </div>
  </transition>
</template>

<script>
export default {
  name: "SlideNotification",
  props: {
    message: {
      type: String,
      required: true
    },
    type: {
      type: String,
      default: 'info',
      validator: value => ['success', 'warning', 'error', 'info'].includes(value)
    },
    duration: {
      type: Number,
      default: 4000
    },
    closable: {
      type: Boolean,
      default: true
    },
    minHeight: {
      type: String,
      default: 'auto'
    }
  },
  data() {
    return {
      visible: false,
      timer: null
    }
  },
  methods: {
    show() {
      this.$nextTick(() => {
        this.visible = true;
        this.startTimer();
      });
    },
    close() {
      this.visible = false;
      this.$emit('close');
    },
    startTimer() {
      if (this.duration > 0) {
        clearTimeout(this.timer);
        this.timer = setTimeout(() => {
          this.close();
        }, this.duration);
      }
    }
  },
  mounted() {
    setTimeout(() => {
      this.show();
    }, 100);
  },
  beforeUnmount() {
    clearTimeout(this.timer);
  }
}
</script>

<style scoped>
.notification {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  width: 90%;
  max-width: 450px;
  border-radius: 6px;
  z-index: 9999;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  padding: 0;
}

.notification-content {
  display: flex;
  align-items: center;
  padding: 12px 16px;
}

.notification-error {
  background-color: #f3f5f6;
  /*border: 1px solid #ffecb3;*/
}

.notification-success {
  background-color: #f3f5f6;
  /*border: 1px solid #ffecb3;*/
}

.notification-warning {
  background-color: #f3f5f6;
  /*border: 1px solid #ffecb3;*/
}

.notification-info {
  background-color: #f3f5f6;
  /*border: 1px solid #ffecb3;*/
}

.icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #f44336;
  margin-right: 12px;
}

.error-icon {
  color: white;
  font-size: 16px;
  font-weight: bold;
}

.message {
  flex: 1;
  color: #444444;
  font-size: 14px;
  padding: 2px 0;
}

.notification-error .message {
  color: #d32f2f;
}

.notification-success .message {
  color: #388e3c;
}

.notification-warning .message {
  color: #f57c00;
}

.notification-info .message {
  color: #1976d2;
}

.close-btn {
  background: transparent;
  border: none;
  color: #9e9e9e;
  cursor: pointer;
  font-size: 18px;
  font-weight: bold;
  padding: 0;
  margin-left: 10px;
  opacity: 0.8;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  opacity: 1;
  color: #616161;
}

/* 改进后的滑动动画 */
.slide-enter-active {
  animation: slideDown 0.5s ease-out forwards;
}

.slide-leave-active {
  animation: slideUp 0.5s ease-in forwards;
}

@keyframes slideDown {
  0% {
    transform: translateX(-50%) translateY(-100%);
    opacity: 0;
  }
  70% {
    transform: translateX(-50%) translateY(10%);
    opacity: 0.9;
  }
  100% {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
  }
}

@keyframes slideUp {
  0% {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
  }
  30% {
    transform: translateX(-50%) translateY(10%);
    opacity: 0.9;
  }
  100% {
    transform: translateX(-50%) translateY(-100%);
    opacity: 0;
  }
}
</style>