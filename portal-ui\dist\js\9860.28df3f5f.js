"use strict";(self["webpackChunkportal_ui"]=self["webpackChunkportal_ui"]||[]).push([[9860],{9860:function(e,t,s){s.r(t),s.d(t,{default:function(){return l}});var i=function(){var e=this,t=e._self._c;return t("div",{staticClass:"iframe-page"},[t("div",{staticClass:"iframe-container"},[t("iframe",{key:e.iframeKey,ref:"iframe",attrs:{src:e.iframeSrc}})])])},r=[],a=s(1955),o={name:"Console",mounted(){const e=this.getCookie("Admin-Token"),t=this.getCookie("isReal");e&&"1"===t||(console.warn("[ConsolePage] 鉴权失败：未登录 / 未实名 跳转首页"),window.location.hash="#/index"),this.$nextTick((()=>{const e=this.$refs.iframe;e&&e.addEventListener("load",(()=>{setTimeout((()=>{this.handleIframeLoad()}),500)}),{once:!0})}))},data(){return{error:null,keyPair:null,iframeSrc:null,config:{},iframeKey:0,hasPostedMessage:!1}},created(){this.refreshIframe()},methods:{refreshIframe(){Object.keys(localStorage).filter((e=>e.startsWith("Hm_"))).forEach((e=>localStorage.removeItem(e))),this.iframeKey+=1,this.hasPostedMessage=!1,this.iframeSrc="https://console.suanli.cn/serverless/idc?type=other&isHiddenPrice=true",this.config.token&&(this.config={token:a.Z.get("suanlemeToken"),rsa_pubk:a.Z.get("publicKey-C"),rsa_prik:a.Z.get("privateKey-B")}),this.config={token:a.Z.get("suanlemeToken"),rsa_pubk:a.Z.get("publicKey-C"),rsa_prik:a.Z.get("privateKey-B")}},handleIframeLoad(){const e=this.$refs.iframe;if(e&&e.contentWindow){if(this.hasPostedMessage)return;const t=this.config.token,s=this.config.rsa_pubk,i=this.config.rsa_prik;if(t&&s&&i){const r={token:t,rsa_pubk:s,rsa_prik:i};e.contentWindow.postMessage(r,"*"),this.hasPostedMessage=!0,console.log(" postMessage已发送"),this.$parent&&this.$parent.$refs&&this.$parent.$refs.header&&(this.$parent.$refs.header.isConsoleReady=!0,this.$parent.$refs.header.isConsoleLoading=!1)}else setTimeout((()=>{this.config={token:a.Z.get("suanlemeToken"),rsa_pubk:a.Z.get("publicKey-C"),rsa_prik:a.Z.get("privateKey-B")},this.handleIframeLoad()}),1e3)}},getCookie(e){const t=document.cookie.match(new RegExp("(^| )"+e+"=([^;]+)"));return t?t[2]:null}},beforeDestroy(){}},n=o,c=s(1001),h=(0,c.Z)(n,i,r,!1,null,"db80cd06",null),l=h.exports}}]);
//# sourceMappingURL=9860.28df3f5f.js.map