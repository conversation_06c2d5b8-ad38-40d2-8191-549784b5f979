# <font style="color:rgb(2, 8, 23);">容器化部署 Ollama+Qwen3</font>
<font style="color:rgb(2, 8, 23);">本指南全面介绍了在天工开物平台上部署 Ollama 与 Qwen3 大语言模型 API 服务的完整解决方案。该方案不仅提供了详实的部署流程，还提供了如何制作镜像的方案。</font>

> <font style="color:rgb(31, 41, 55);">此镜像提供了标准化的</font>**<font style="color:rgb(31, 41, 55);"> API 接口</font>**<font style="color:rgb(31, 41, 55);">，让您能够便捷地通过 </font>**<font style="color:rgb(31, 41, 55);">API 调用方式</font>**<font style="color:rgb(31, 41, 55);">访问和使用所有功能。</font>
>
> <font style="color:rgb(31, 41, 55);">如果您希望通过 Web UI 的方式使用大模型，可以参考另外的最佳实践，参考：容器化部署 Ollama + Qwen3 + Open WebUI</font>
>

## **<font style="color:rgb(2, 8, 23);">1、部署服务</font>**
### <font style="color:rgb(2, 8, 23);">1.1 访问</font>[天工开物控制台](https://tiangongkaiwu.top/portal/#/console)<font style="color:rgb(2, 8, 23);">，点击新增部署。</font>
![](./imgs/Ollama+Qwen1.png)

### <font style="color:rgb(2, 8, 23);">1.2 选择设备</font>
<font style="color:rgb(2, 8, 23);">基于自身需要进行配置，参考配置为单卡 4090 和 1 个节点（初次使用进行调试）。</font>

![](./imgs/Ollama+Qwen2.png)

### <font style="color:rgb(2, 8, 23);">1.3 选择相应预制镜像</font>
<font style="color:rgb(2, 8, 23);">这里选择</font><font style="color:rgb(2, 8, 23);"> </font>**<font style="color:rgb(2, 8, 23);">Qwen3 30B A3B，</font>**<font style="color:rgb(2, 8, 23);">点击部署服务。</font>

![](./imgs/Ollama+Qwen3.png)

### <font style="color:rgb(2, 8, 23);">1.4 耐心等待节点拉取镜像并启动</font>
![](./imgs/Ollama+Qwen4.png)

### <font style="color:rgb(2, 8, 23);">1.5 部署完成</font>
<font style="color:rgb(2, 8, 23);">在部署完成页面，能看到一个公开访问链接。这个链接就是 Ollama 服务的 API 访问地址。</font>

<font style="color:rgb(2, 8, 23);">将这个 API 地址复制下来，就可以在任何支持 Ollama 协议的应用程序中使用。</font>

![](./imgs/Ollama+Qwen5.png)

<font style="color:rgb(2, 8, 23);">在“常规”面板里可以看到公开访问的地址，此地址即为 Ollama 服务的 API 地址。 请耐心一点哦~ 模型镜像会比较大，</font>**<font style="color:rgb(2, 8, 23);">qwen3:30b-a3b 镜像本身 20G+，打包之后大约 40G+，</font>**<font style="color:rgb(2, 8, 23);"> 拉取镜像会需要一段时间。</font>

### <font style="color:rgb(2, 8, 23);">1.6 验证一下</font>
<font style="color:rgb(2, 8, 23);">访问复制的链接，{快捷访问的地址} /api/tags，将链接复制到浏览器，就可以看到以下内容，说明模型已经部署并运行了。</font>

![](./imgs/Ollama+Qwen6.png)

<font style="color:rgb(2, 8, 23);">如果需要在其他兼容 Ollama 的客户端使用时，需要提供的参数如下：</font>

+ <font style="color:rgb(2, 8, 23);">访问地址</font>

<font style="color:rgb(2, 8, 23);">常规 -> 快捷访问中 11434 对应的链接。有的会需要在链接后面加上 /api</font>

+ <font style="color:rgb(2, 8, 23);">ModelId</font>

**<font style="color:rgb(2, 8, 23);">qwen3:30b-a3b</font>**

+ <font style="color:rgb(2, 8, 23);">上下文长度</font>

<font style="color:rgb(2, 8, 23);">32k</font>

+ <font style="color:rgb(2, 8, 23);">模型建议的其他参数（非必须，可以根据需要自行修改）</font>

```plain
{
    "repeat_penalty": 1,
    "temperature": 0.6,
    "top_k": 20,
    "top_p": 0.95
}
```

<font style="color:rgb(2, 8, 23);">使用第三方客户端时，可以按照下图填写内容</font>

![](./imgs/Ollama+Qwen7.png)

## <font style="color:rgb(2, 8, 23);">2、模型速度测试</font>
<font style="color:rgb(2, 8, 23);">qwen3 部署完成了，速度怎么样呢？点击</font><font style="color:rgb(2, 8, 23);"> </font>[<font style="color:#2F8EF4;">LM Speed</font>](https://lmspeed.net/zh-CN)<font style="color:rgb(2, 8, 23);"> </font><font style="color:rgb(2, 8, 23);">测试一下速度吧~</font>

> <font style="color:rgb(103, 103, 108);">如果 LM Speed 无法访问，多刷新几次就可以了 </font><font style="color:rgb(103, 103, 108);">😊</font>
>

![](./imgs/ollama-qwen-webui11.png)

## **<font style="color:rgb(2, 8, 23);">3、构建 Ollama + Qwen3 模型镜像</font>**
> <font style="color:rgb(103, 103, 108);">温馨提示，如果你只希望使用我们默认的镜像，那么下面的内容您无需关注。</font>
>

### <font style="color:rgb(2, 8, 23);">3.1 clone 项目</font>
```plain
git clone https://github.com/slmnb-lab/llm-deployment.git
```

### <font style="color:rgb(2, 8, 23);">3.2 修改模型名称</font>
+ <font style="color:rgb(2, 8, 23);">修改 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">ollama</font><font style="color:rgb(2, 8, 23);"> 目录下的 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">ollama_pull.sh</font><font style="color:rgb(2, 8, 23);"> 文件中的模型名称。当前使用的模型是</font>**<font style="color:rgb(2, 8, 23);"> qwen3:30b-a3b</font>**

<font style="color:rgb(103, 103, 108);">模型列表参考 </font>[<font style="color:#2F8EF4;">Ollama 官网</font>](https://ollama.com/library)

```plain
#!/bin/bash
ollama serve &
sleep 15
ollama pull qwen3:30b-a3b  # 替换成你需要的模型
```

+ <font style="color:rgb(2, 8, 23);">修改 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">ollama</font><font style="color:rgb(2, 8, 23);"> 目录下的 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">compose.yml</font><font style="color:rgb(2, 8, 23);"> 文件中的模型名称。</font>

<font style="color:rgb(103, 103, 108);">开始之前需要在天工开物中创建一个镜像仓库，使用你自己的镜像仓库</font>**<font style="color:rgb(103, 103, 108);">账号名称</font>**<font style="color:rgb(103, 103, 108);">替换{yourusername}，镜像仓库名称为 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">qwen</font><font style="color:rgb(103, 103, 108);">，镜像标签为 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">30b-a3b</font><font style="color:rgb(103, 103, 108);">。访问这里 </font>[<font style="color:#2F8EF4;">初始化镜像仓库</font>](https://tiangongkaiwu.top/#/console)

```plain
services:
  qwen:
    ## {yourusername}是天工开物的镜像仓库账号名称 
    ## qwen3 是镜像名称 30b-a3b 是镜像标签
    image: harbor.suanleme.cn/{yourusername}/qwen3:30b-a3b  
    build: .
    labels:
      - suanleme_0.http.port=11434          # 这里是 ollama 运行的端口，不要修改
      - suanleme_0.http.prefix=qwen332b     # 这里是发布的回传域名前缀
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    ports:
      - "11434:11434"                        # 这里是 ollama 运行的端口，不要修改
```

### <font style="color:rgb(2, 8, 23);">3.3 运行打包脚本</font>
<font style="color:rgb(2, 8, 23);">执行成功之后，会在本地生成镜像</font>

```plain
docker compose build
```

####  问题1:<font style="color:rgb(51, 51, 51);">‘failed to solve: ollama/ollama: failed to resolve source metadata for docker.io/ollama/ollama:latest’</font>
<font style="color:rgb(2, 8, 23);">解决办法：先拉取下来哈</font>

```plain
docker pull ollama/ollama:latest
```

####  问题2:<font style="color:rgb(51, 51, 51);">‘failed to solve: process "/bin/sh -c chmod +x ollama_pull.sh && ./ollama_pull.sh" did not complete successfully: exit code: 127’</font>
<font style="color:rgb(2, 8, 23);">解决办法：将dockerfile中的‘RUN chmod +x ollama_pull.sh && ./ollama_pull.sh’改为‘RUN chmod +x /ollama_pull.sh && /bin/bash /ollama_pull.sh’，再运行打包脚本就好了。</font>

## **<font style="color:rgb(2, 8, 23);">4、镜像上传</font>**
<font style="color:rgb(2, 8, 23);">将打包的镜像上传到天工开物的镜像仓库</font>

### <font style="color:rgb(2, 8, 23);">4.1 登录镜像仓库</font>
<font style="color:rgb(2, 8, 23);">username 需要替换为自己的天工开物</font>**<font style="color:rgb(2, 8, 23);">镜像仓库</font>**<font style="color:rgb(2, 8, 23);">的</font>**<font style="color:rgb(2, 8, 23);">用户名</font>**<font style="color:rgb(2, 8, 23);">！</font>

<font style="color:rgb(2, 8, 23);">输入密码需要输入初始化镜像仓库时设置的密码</font>

```plain
### harbor.suanleme.cn 是固定值，{yourusername}需要替换为自己的镜像仓库的用户名！
docker login harbor.suanleme.cn --username={yourusername}

## 输入密码  镜像仓库的密码!
*******
```

### <font style="color:rgb(2, 8, 23);">4.2 上传镜像</font>
<font style="color:rgb(2, 8, 23);">执行以下代码，进行镜像上传</font>

```plain
## 为新生成的镜像打上标签
docker tag harbor.suanleme.cn/{yourusername}/qwen3:30b-a3b harbor.suanleme.cn/{yourusername}/qwen3:30b-a3b

## 上传镜像
docker push harbor.suanleme.cn/{yourusername}/qwen3:30b-a3b
```

<font style="color:rgb(103, 103, 108);">备注：镜像比较大，如果推送失败了，多试几次就好啦。</font><font style="color:rgb(103, 103, 108);">😊</font>

<br><br>
<font style="color:rgba(178,178,178,1);">最后更新于: 2025/6/12 13:59</font>
