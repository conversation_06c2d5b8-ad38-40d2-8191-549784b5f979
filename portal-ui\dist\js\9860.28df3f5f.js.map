{"version": 3, "file": "js/9860.28df3f5f.js", "mappings": "+JAAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,SAAS,CAACG,IAAIL,EAAIM,UAAUC,IAAI,SAASC,MAAM,CAAC,IAAMR,EAAIS,gBACjN,EACIC,EAAkB,G,UCYtB,GACAC,KAAA,UAEAC,UACA,MAAAC,EAAA,KAAAC,UAAA,eACAC,EAAA,KAAAD,UAAA,UAEAD,GAAA,MAAAE,IACAC,QAAAC,KAAA,qCACAC,OAAAC,SAAAC,KAAA,WAcA,KAAAC,WAAA,KACA,MAAAC,EAAA,KAAAC,MAAAD,OACAA,GACAA,EAAAE,iBAAA,aACAC,YAAA,KACA,KAAAC,kBAAA,GACA,OACA,CAAAC,MAAA,GACA,GAEA,EACAC,OACA,OACAC,MAAA,KACAC,QAAA,KAEArB,UAAA,KACAsB,OAAA,CACA,EACAzB,UAAA,EACA0B,kBAAA,EAEA,EACAC,UACA,KAAAC,eACA,EACAC,QAAA,CAEAD,gBACAE,OAAAC,KAAAC,cACAC,QAAAlC,GAAAA,EAAAmC,WAAA,SACAC,SAAApC,GAAAiC,aAAAI,WAAArC,KAEA,KAAAC,WAAA,EACA,KAAA0B,kBAAA,EACA,KAAAvB,UAAA,yEACA,KAAAsB,OAAAlB,QACA,KAAAkB,OAAA,CACA,MAAAY,EAAAA,EAAAA,IAAA,iBACA,SAAAA,EAAAA,EAAAA,IAAA,eACA,SAAAA,EAAAA,EAAAA,IAAA,kBAKA,KAAAZ,OAAA,CACAlB,MAAA8B,EAAAA,EAAAA,IAAA,iBACAC,SAAAD,EAAAA,EAAAA,IAAA,eACAE,SAAAF,EAAAA,EAAAA,IAAA,gBAGA,EACAjB,mBACA,MAAAJ,EAAA,KAAAC,MAAAD,OACA,GAAAA,GAAAA,EAAAwB,cAAA,CAEA,QAAAd,iBAEA,OAIA,MAAAnB,EAAA,KAAAkB,OAAAlB,MACAkC,EAAA,KAAAhB,OAAAa,SACAI,EAAA,KAAAjB,OAAAc,SAQA,GAAAhC,GAAAkC,GAAAC,EAAA,CAOA,MAAAC,EAAA,CACApC,MAAAA,EACA+B,SAAAG,EACAF,SAAAG,GASA1B,EAAAwB,cAAAI,YAAAD,EAAA,KACA,KAAAjB,kBAAA,EAEAhB,QAAAmC,IAAA,mBAGA,KAAAC,SAAA,KAAAA,QAAA7B,OAAA,KAAA6B,QAAA7B,MAAA8B,SACA,KAAAD,QAAA7B,MAAA8B,OAAAC,gBAAA,EACA,KAAAF,QAAA7B,MAAA8B,OAAAE,kBAAA,EAEA,MAGA9B,YAAA,KAGA,KAAAM,OAAA,CACAlB,MAAA8B,EAAAA,EAAAA,IAAA,iBACAC,SAAAD,EAAAA,EAAAA,IAAA,eACAE,SAAAF,EAAAA,EAAAA,IAAA,iBAGA,KAAAjB,kBAAA,GACA,IAGA,CACA,EACAZ,UAAAH,GACA,MAAA6C,EAAAC,SAAAC,OAAAF,MAAA,IAAAG,OAAA,QAAAhD,EAAA,aACA,OAAA6C,EAAAA,EAAA,OACA,GAEAI,gBAEA,GCnKuP,I,UCQnPC,GAAY,OACd,EACA9D,EACAW,GACA,EACA,KACA,WACA,MAIF,EAAemD,EAAiB,O", "sources": ["webpack://portal-ui/./src/views/Console.vue", "webpack://portal-ui/src/views/Console.vue", "webpack://portal-ui/./src/views/Console.vue?31f7", "webpack://portal-ui/./src/views/Console.vue?b0ff"], "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"iframe-page\"},[_c('div',{staticClass:\"iframe-container\"},[_c('iframe',{key:_vm.iframe<PERSON>ey,ref:\"iframe\",attrs:{\"src\":_vm.iframeSrc}})])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"iframe-page\">\r\n    <div class=\"iframe-container\">\r\n      <iframe\r\n          :key=\"iframeKey\"\r\n          ref=\"iframe\"\r\n          :src=\"iframeSrc\"\r\n      ></iframe>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Cookies from 'js-cookie'\r\nexport default {\r\n  name: \"Console\",\r\n\r\n  mounted() {\r\n    const token = this.getCookie('Admin-Token');\r\n    const isReal = this.getCookie('isReal');\r\n\r\n    if (!token || isReal !== '1' ) {\r\n      console.warn('[ConsolePage] 鉴权失败：未登录 / 未实名 跳转首页');\r\n      window.location.hash = '#/index';\r\n    }\r\n\r\n    // 监听iframe回传的消息\r\n    // window.addEventListener('message', (event) => {\r\n    //     console.log('=== 收到iframe回传消息 ===');\r\n    //     console.log('消息来源:', event.origin);\r\n    //     console.log('消息数据:', event.data);\r\n    //     console.log('消息类型:', typeof event.data);\r\n    //     console.log('时间戳:', new Date().toLocaleTimeString());\r\n    //     console.log('========================');\r\n    // }\r\n    // );\r\n\r\n    this.$nextTick(() => {\r\n      const iframe = this.$refs.iframe;\r\n      if (iframe) {\r\n        iframe.addEventListener('load', () => {\r\n          setTimeout(() => {\r\n            this.handleIframeLoad();\r\n          }, 500);\r\n        }, { once: true });\r\n      }\r\n    });\r\n  },\r\n  data(){\r\n    return{\r\n      error:null,\r\n      keyPair:null,\r\n      // 生产环境替换为 https://suanli.cn?type=other\r\n      iframeSrc: null,\r\n      config: {\r\n      },\r\n      iframeKey: 0,\r\n      hasPostedMessage: false,\r\n    }\r\n  },\r\n  created() {\r\n    this.refreshIframe()\r\n  },\r\n  methods:{\r\n    // 强制禁用浏览器缓存\r\n    refreshIframe() {\r\n      Object.keys(localStorage)\r\n          .filter(key => key.startsWith('Hm_'))\r\n          .forEach(key => localStorage.removeItem(key));\r\n      // 同时更新 key 和 src\r\n      this.iframeKey += 1;\r\n      this.hasPostedMessage = false; // 重置发送标志\r\n      this.iframeSrc = `https://console.suanli.cn/serverless/idc?type=other&isHiddenPrice=true`;\r\n      if (this.config.token){\r\n        this.config={\r\n          \"token\":Cookies.get('suanlemeToken'),\r\n          \"rsa_pubk\":Cookies.get('publicKey-C'),\r\n          \"rsa_prik\":Cookies.get('privateKey-B'),\r\n        }\r\n\r\n      }\r\n      // 确保 config 同步更新\r\n      this.config = {\r\n        token: Cookies.get('suanlemeToken'),\r\n        rsa_pubk: Cookies.get('publicKey-C'),\r\n        rsa_prik: Cookies.get('privateKey-B'),\r\n      };\r\n\r\n    },\r\n    handleIframeLoad() {\r\n      const iframe = this.$refs.iframe\r\n      if (iframe && iframe.contentWindow) {\r\n        // 防止重复发送\r\n        if (this.hasPostedMessage) {\r\n          // console.log('已发送过认证数据，跳过重复发送');\r\n          return;\r\n        }\r\n\r\n        // 校验三个关键参数\r\n        const token = this.config.token;\r\n        const publicKey = this.config.rsa_pubk;\r\n        const privateKey = this.config.rsa_prik;\r\n\r\n        // console.log('=== iframe数据校验 ===');\r\n        // console.log('Token:', token ? '存在' : '缺失',token);\r\n        // console.log('公钥C:', publicKey ? '存在' : '缺失',publicKey);\r\n        // console.log('私钥B:', privateKey ? '存在' : '缺失',privateKey);\r\n\r\n        // 三个参数都存在才发送\r\n        if (token && publicKey && privateKey) {\r\n          // console.log('数据完整，发送给iframe');\r\n          // console.log('=== 发送数据详情 ===');\r\n          // console.log('发送时间:', new Date().toLocaleTimeString());\r\n          // console.log('iframe URL:', iframe.src);\r\n\r\n          // 转换为纯JavaScript对象，避免Vue响应式包装\r\n          const pureData = {\r\n            token: token,\r\n            rsa_pubk: publicKey,\r\n            rsa_prik: privateKey\r\n          };\r\n\r\n          // console.log('发送的完整数据:', pureData);\r\n          // console.log('JSON序列化测试:', JSON.stringify(pureData));\r\n          // console.log('数据类型检查:', typeof pureData);\r\n          // console.log('是否为纯对象:', pureData.constructor === Object);\r\n          // console.log('==================');\r\n\r\n          iframe.contentWindow.postMessage(pureData, '*');\r\n          this.hasPostedMessage = true; // 标记已发送\r\n\r\n          console.log(' postMessage已发送');\r\n\r\n          // 手动触发按钮状态更新\r\n          if (this.$parent && this.$parent.$refs && this.$parent.$refs.header) {\r\n            this.$parent.$refs.header.isConsoleReady = true;\r\n            this.$parent.$refs.header.isConsoleLoading = false;\r\n          }\r\n        } else {\r\n          // console.log('认证数据不完整，延迟重试...');\r\n          // 数据不完整时，延迟重试\r\n          setTimeout(() => {\r\n            // console.log('重试获取认证数据...');\r\n            // 重新获取Cookie数据\r\n            this.config = {\r\n              token: Cookies.get('suanlemeToken'),\r\n              rsa_pubk: Cookies.get('publicKey-C'),\r\n              rsa_prik: Cookies.get('privateKey-B'),\r\n            };\r\n            // 递归重试\r\n            this.handleIframeLoad();\r\n          }, 1000);\r\n        }\r\n\r\n      }\r\n    },\r\n    getCookie(name) {\r\n      const match = document.cookie.match(new RegExp('(^| )' + name + '=([^;]+)'));\r\n      return match ? match[2] : null;\r\n    },\r\n  },\r\n  beforeDestroy() {\r\n\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.iframe-page {\r\n  height: 100vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n}\r\n\r\n.header {\r\n  width: 100%;\r\n  height: 60px;\r\n  background-color: #1a73e8;\r\n  color: #ffffff;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.content {\r\n  flex: 1;\r\n  display: flex;\r\n}\r\n\r\n.sidebar {\r\n  width: 200px;\r\n  background-color: #000000;\r\n  color: #ffffff;\r\n  padding: 20px;\r\n}\r\n\r\n.iframe-container {\r\n  flex: 1;\r\n}\r\n\r\niframe {\r\n  width: 100%;\r\n  height: 100%;\r\n  border: none;\r\n}\r\n</style>\r\n", "import mod from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Console.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Console.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Console.vue?vue&type=template&id=db80cd06&scoped=true&\"\nimport script from \"./Console.vue?vue&type=script&lang=js&\"\nexport * from \"./Console.vue?vue&type=script&lang=js&\"\nimport style0 from \"./Console.vue?vue&type=style&index=0&id=db80cd06&prod&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"db80cd06\",\n  null\n  \n)\n\nexport default component.exports"], "names": ["render", "_vm", "this", "_c", "_self", "staticClass", "key", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ref", "attrs", "iframeSrc", "staticRenderFns", "name", "mounted", "token", "<PERSON><PERSON><PERSON><PERSON>", "isReal", "console", "warn", "window", "location", "hash", "$nextTick", "iframe", "$refs", "addEventListener", "setTimeout", "handleIframeLoad", "once", "data", "error", "keyPair", "config", "hasPostedMessage", "created", "refreshIframe", "methods", "Object", "keys", "localStorage", "filter", "startsWith", "for<PERSON>ach", "removeItem", "Cookies", "rsa_pubk", "rsa_prik", "contentWindow", "public<PERSON>ey", "privateKey", "pureData", "postMessage", "log", "$parent", "header", "isConsoleReady", "isConsoleLoading", "match", "document", "cookie", "RegExp", "<PERSON><PERSON><PERSON><PERSON>", "component"], "sourceRoot": ""}