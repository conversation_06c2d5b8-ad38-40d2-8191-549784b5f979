"use strict";(self["webpackChunkportal_ui"]=self["webpackChunkportal_ui"]||[]).push([[9802],{2478:function(o,t,n){n.r(t);var e=new URL(n(1404),n.b),s=new URL(n(4117),n.b),r=new URL(n(5650),n.b),i=new URL(n(2288),n.b),u=new URL(n(2637),n.b),p=new URL(n(6851),n.b),l=new URL(n(1210),n.b),a=new URL(n(3282),n.b),f=new URL(n(5544),n.b),d=new URL(n(5767),n.b),c='<h1 id="容器化部署-hivisionidphotos">容器化部署 HivisionIDPhotos</h1> <p><font style="color:#020817">本指南详细阐述了在天工开物平台上，高效部署与使用 HivisionIDPhotos 项目的技术方案。HivisionIDPhotos 是一款开源的图片处理工具，可以利用 AI 模型对照片进行轻量级智能抠图、调整尺寸生成不同的标准证件照、替换背景、美颜、智能换正装等操作。有了它，自己在家也能轻松搞定证件照和各种艺术照。</font></p> <h2 id="1在天工开物上运行-hivisionidphotos"><strong>1.在天工开物上运行 HivisionIDPhotos</strong></h2> <p><font style="color:#020817">天工开物平台提供预构建的 HivisionIDPhotos 容器镜像，用户无需本地复杂环境配置，可快速完成部署并启用服务。以下是详细部署步骤：</font></p> <h3 id="11-创建部署服务"><strong>1.1 创建部署服务</strong></h3> <p><font style="color:#020817">登录<a href="https://www.tiangongkaiwu.top/portal/#/console"><font style="color:#06c">天工开物控制台</font></a>，在控制台首页点击“弹性部署服务”进入管理页面。</font></p> <p><img src="'+e+'" alt=""></p> <h3 id="12-选择-gpu-型号"><strong>1.2 选择 GPU 型号</strong></h3> <p><font style="color:#020817">根据实际需求选择 GPU 型号：</font></p> <p><font style="color:#020817">初次使用或调试阶段，推荐配置单张 NVIDIA RTX 4090 GPU</font></p> <p><img src="'+s+'" alt=""></p> <h3 id="13-选择预制镜像"><strong>1.3 选择预制镜像</strong></h3> <p><font style="color:#020817">在“服务配置”模块切换至“预制服务”选项卡，选择 HivisionIDPhotos 官方镜像。</font></p> <p><img src="'+r+'" alt=""></p> <h3 id="14-部署并访问服务"><strong>1.4 部署并访问服务</strong></h3> <p><font style="color:#020817">点击“部署服务”，平台将自动拉取镜像并启动容器。</font></p> <p><img src="'+i+'" alt=""></p> <p><font style="color:#020817">部署完成后，在“快捷访问”中找到端口为 7860 的公网访问链接，点击即可在浏览器中使用 HivisionIDPhotos 的 Web 界面，或通过 8080 端口调用 API 服务。</font></p> <h2 id="2快速上手快速抠图蓝底证件照"><strong>2.快速上手——</strong>快速抠图蓝底证件照</h2> <p><font style="color:#020817">可以点击或直接把要制作的图片拖入，然后在下方选择相关参数：</font></p> <p><img src="'+u+'" alt=""></p> <p><font style="color:#020817">这里以一寸照片，白色背景为例：</font></p> <p><img src="'+p+'" alt=""></p> <p><font style="color:#020817">选择完毕后，点击开始制作：</font></p> <p><font style="color:#020817">几十秒后即可完成：</font></p> <p><img src="'+l+'" alt=""></p> <p><font style="color:#020817">左侧标准，右侧高清，下方还能生成 10 张排版的格式。</font></p> <p><font style="color:#020817">展开下方栏目，还能看到同时生成了社交媒体模版照和抠图图像，确实挺方便：</font></p> <p><img src="'+a+'" alt=""></p> <h2 id="3api-调用指南">3.API 调用指南</h2> <p><font style="color:#020817">HivisionIDPhotos 提供完整的 API 接口体系，支持通过编程方式实现照片创作全流程自动化。以下为官方核心接口详解与调用示范：</font></p> <p><img src="'+f+'" alt=""></p> <p><font style="color:#020817">我们预制好的镜像中 8080 端口为 API 调用接口地址，可以在生产环境中直接使用</font></p> <p><img src="'+d+'" alt=""></p> <h3 id="31-环境准备">3.1 环境准备</h3> <pre><code class="language-powershell">pip install requests\n\nimport requests\nAPI_URL = &quot;http://&lt;您的部署 ID&gt;.550c.cloud:8080/&quot;\n</code></pre> <h3 id="32-核心功能接口">3.2 核心功能接口</h3> <ol> <li><font style="color:#020817">生成透明底证件照（idphoto）</font></li> </ol> <pre><code class="language-python">result = requests.post(\n    f&quot;{API_URL}idphoto&quot;,\n    files={&quot;input_image&quot;: open(&quot;test.jpg&quot;, &quot;rb&quot;)},\n    data={\n        &quot;height&quot;: 413,  # 标准高度（默认 295×413）\n        &quot;width&quot;: 295,   # 标准宽度\n        &quot;human_matting_model&quot;: &quot;modnet_photographic_portrait_matting&quot;,  # 人像分割模型\n        &quot;hd&quot;: True,     # 是否生成高清版\n        &quot;head_measure_ratio&quot;: 0.2,  # 面部占比\n        &quot;head_height_ratio&quot;: 0.45   # 面部位置比例\n    }\n).json()\nstandard_photo = result[&quot;image_base64_standard&quot;]  # 标准证件照（Base64）\nhd_photo = result[&quot;image_base64_hd&quot;]              # 高清证件照（Base64）\n</code></pre> <ol start="2"> <li><font style="color:#020817">添加背景色（add_background）</font></li> </ol> <pre><code class="language-python">result = requests.post(\n    f&quot;{API_URL}add_background&quot;,\n    files={&quot;input_image&quot;: open(&quot;transparent.png&quot;, &quot;rb&quot;)},\n    data={\n        &quot;color&quot;: &quot;638cce&quot;,  # 蓝底 HEX 色值\n        &quot;render&quot;: 1,         # 渐变模式（0=纯色/1=上下渐变/2=中心渐变）\n        &quot;kb&quot;: 200            # 输出文件大小控制（KB）\n    }\n).json()\ncolored_photo = result[&quot;image_base64&quot;]  # 带背景色的证件照\n</code></pre> <ol start="3"> <li><font style="color:#020817">生成六寸排版照（generate_layout_photos）</font></li> </ol> <pre><code class="language-python">result = requests.post(\n    f&quot;{API_URL}generate_layout_photos&quot;,\n    files={&quot;input_image&quot;: open(&quot;idphoto.jpg&quot;, &quot;rb&quot;)},\n    data={&quot;kb&quot;: 500}  # 排版照文件大小控制\n).json()\nlayout_photo = result[&quot;image_base64&quot;]  # 6 寸排版照（含多张证件照）\n</code></pre> <h3 id="33-高级控制参数">3.3 高级控制参数</h3> <table> <thead> <tr> <th>参数</th> <th>作用</th> <th>推荐值</th> </tr> </thead> <tbody><tr> <td><font style="color:#2f8ef4;background-color:#eff0f0">human_matting_model</font></td> <td>人像分割模型选择</td> <td><font style="color:#2f8ef4;background-color:#eff0f0">modnet_photographic_portrait_matting</font>（通用场景）</td> </tr> <tr> <td><font style="color:#2f8ef4;background-color:#eff0f0">face_detect_model</font></td> <td>人脸检测模型</td> <td><font style="color:#2f8ef4;background-color:#eff0f0">mtcnn</font>（快速）/<font style="color:#2f8ef4;background-color:#eff0f0">retinaface-resnet50</font>（高精度）</td> </tr> <tr> <td><font style="color:#2f8ef4;background-color:#eff0f0">head_measure_ratio</font></td> <td>面部占照片面积比例</td> <td>0.15-0.25（标准 0.2）</td> </tr> <tr> <td><font style="color:#2f8ef4;background-color:#eff0f0">head_height_ratio</font></td> <td>面部中心到照片顶部的比例</td> <td>0.4-0.5（标准 0.45）</td> </tr> <tr> <td><font style="color:#2f8ef4;background-color:#eff0f0">render</font></td> <td>背景渲染模式</td> <td>0=纯色/1=上下渐变/2=中心渐变</td> </tr> <tr> <td><font style="color:#2f8ef4;background-color:#eff0f0">kb</font></td> <td>输出文件大小控制（KB）</td> <td>50-300（根据用途调整）</td> </tr> </tbody></table> <h3 id="34-全流程自动化示例">3.4 全流程自动化示例</h3> <pre><code class="language-python">transparent = requests.post(f&quot;{API_URL}idphoto&quot;, ...).json()\nblue_bg = requests.post(\n    f&quot;{API_URL}add_background&quot;,\n    files={&quot;input_image_base64&quot;: transparent[&quot;image_base64_standard&quot;]},\n    data={&quot;color&quot;: &quot;638cce&quot;}\n).json()\nlayout = requests.post(\n    f&quot;{API_URL}generate_layout_photos&quot;,\n    files={&quot;input_image_base64&quot;: blue_bg[&quot;image_base64&quot;]}\n).json()\nwith open(&quot;blue_idphoto.jpg&quot;, &quot;wb&quot;) as f:\n    f.write(base64.b64decode(blue_bg[&quot;image_base64&quot;]))\nwith open(&quot;6inch_layout.jpg&quot;, &quot;wb&quot;) as f:\n    f.write(base64.b64decode(layout[&quot;image_base64&quot;]))\n</code></pre> <p><font style="color:#020817">通过 API 集成，开发者可构建自动化生产线，结合透明底生成、动态换色、智能排版等功能，实现证件照制作全流程智能化。</font></p> <p><br><br> <font style="color:#b2b2b2">最后更新于: 2025/7/3 10:46</font></p> ';t["default"]=c},5767:function(o,t,n){o.exports=n.p+"img/hivision10.707069c6.png"},5650:function(o,t,n){o.exports=n.p+"img/hivision3.4b087e40.png"},2288:function(o,t,n){o.exports=n.p+"img/hivision4.899ba25a.png"},2637:function(o,t,n){o.exports=n.p+"img/hivision5.6f027ffa.png"},6851:function(o,t,n){o.exports=n.p+"img/hivision6.0f129664.png"},1210:function(o,t,n){o.exports=n.p+"img/hivision7.108d2730.png"},3282:function(o,t,n){o.exports=n.p+"img/hivision8.2f063bde.png"},5544:function(o,t,n){o.exports=n.p+"img/hivision9.acdb0f82.png"},1404:function(o,t,n){o.exports=n.p+"img/universal1.9a1b3f4b.png"},4117:function(o,t,n){o.exports=n.p+"img/universal2.4306636e.png"}}]);
//# sourceMappingURL=docs13.70993c3b.js.map