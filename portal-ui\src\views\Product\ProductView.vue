<template>
  <div style="margin-left: 7%">
    <SlideNotification
        v-if="showComingSoon"
        :message="notificationMessage"
        type="warning"
        :duration="2000"
        @close="showComingSoon = false"
    />
    <div style="width: 95%">
      <!-- 导航占位符 -->
      <div class="compute-market">
        <!-- 筛选条件区域 -->
        <div class="filter-section">
          <div class="filter-header" @click="toggleFilterVisibility">
            <span class="filter-title">筛选</span>
            <i class="filter-icon" :class="{ 'collapsed': !isFilterVisible }">{{ isFilterVisible ? '▼' : '►' }}</i>
          </div>

          <!-- 改进的过渡动画 -->
          <transition
              name="slide"
              @before-enter="beforeEnter"
              @enter="enter"
              @after-enter="afterEnter"
              @before-leave="beforeLeave"
              @leave="leave"
              @after-leave="afterLeave">
            <div v-if="isFilterVisible" class="filter-content" ref="filterContent">
              <!-- 内容保持不变 -->
              <!-- 计费模式 - 改为单选按钮 -->
              <div class="filter-row">
                <div class="filter-label">计费模式</div>
                <div class="filter-options checkbox-group">
                  <label class="radio-item">
                    <input type="radio" v-model="showindex" value="priceHour" name="billing">
                    <span class="radio-item"></span>
                    <span class="radio-text">按量</span>
                  </label>
                  <label class="radio-item">
                    <input type="radio" v-model="showindex" value="priceDay" name="billing">
                    <span class="radio-item"></span>
                    <span class="radio-text">包日</span>
                  </label>
                  <label class="radio-item">
                    <input type="radio" v-model="showindex" value="priceMouth" name="billing">
                    <span class="radio-item"></span>
                    <span class="radio-text">包月</span>
                  </label>
                  <label class="radio-item">
                    <input type="radio" v-model="showindex" value="priceYear" name="billing">
                    <span class="radio-item"></span>
                    <span class="radio-text">包年</span>
                  </label>
                </div>
              </div>

              <!-- 可用区筛选 -->
              <div class="filter-row">
                <div class="filter-label">选择可用区</div>
                <div class="filter-options checkbox-group">
                  <label class="checkbox-item">
                    <input type="checkbox" v-model="filters.allRegions" @change="toggleAllRegions">
                    <span class="checkbox-text">全选</span>
                  </label>
                  <label class="checkbox-item" v-for="region in regions" :key="region.id">
                    <input type="checkbox" v-model="filters.selectedRegions" :value="region.id" @change="updateFilters">
                    <span class="checkbox-text">{{ region.name }}</span>
                  </label>
                </div>
              </div>

              <!-- GPU型号 -->
              <div class="filter-row">
                <div class="filter-label">GPU型号</div>
                <div class="filter-options">
                  <div class="gpu-brand">NVIDIA</div>
                  <div v-if="availableGpuModels.length > 0" class="checkbox-group gpu-list">
                    <label class="checkbox-item">
                      <input type="checkbox" v-model="filters.allGpuModels" @change="toggleAllGpuModels">
                      <span class="checkbox-text">全选</span>
                    </label>
                    <label class="checkbox-item" v-for="gpu in availableGpuModels" :key="gpu.id">
                      <input type="checkbox" v-model="filters.selectedGpuModels" :value="gpu.id" @change="updateFilters">
                      <span class="checkbox-text">{{ gpu.name }}</span>
                    </label>
                  </div>
                  <div v-else class="no-options-message">
                    当前筛选条件下无可用GPU型号
                  </div>
                </div>
              </div>

              <!-- 使用场景 -->
              <div class="filter-row">
                <div class="filter-label">使用场景</div>
                <div class="filter-options checkbox-group">
                  <label class="checkbox-item">
                    <input type="checkbox" v-model="filters.usageScenarios.development">
                    <span class="checkbox-text">开发机</span>
                  </label>
                  <!-- 更多场景可以在这里添加 -->
                </div>
              </div>
            </div>
          </transition>
        </div>

        <!-- 服务器列表 -->
        <div v-if="filteredServers.length > 0" class="servers-grid">
          <div
              class="server-card"
              v-for="server in filteredServers"
              :key="server.id"
              @mouseenter="hoveredServer = server.id"
              @mouseleave="hoveredServer = null"
              :class="{ 'server-card-hovered': hoveredServer === server.id }"
          >
            <!-- 区域标识放在右上角 -->
            <div class="region-tag">{{ getRegionName(server.region) }}</div>

            <!-- 服务器标题 -->
            <div class="server-title">
              {{ server.name }}
            </div>

            <!-- 价格和状态 -->
            <div class="server-price-section" v-show="showindex === 'priceHour'">
              <div class="price">
                <span class="currency">¥</span>
                <span class="amount">{{ server.priceHour }}</span>
                <span class="unit">/小时</span>
              </div>
              <!-- 使用动态生成的状态文本和CSS类 -->
              <div class="server-status" :class="getServerStatusClass(server.inventoryNumber)">
                {{ getServerStatusText(server.inventoryNumber) }}
              </div>
            </div>
            <div class="server-price-section" v-show="showindex === 'priceDay'">
              <div class="price">
                <span class="currency">¥</span>
                <span class="amount">{{ server.priceDay }}</span>
                <span class="unit">/天</span>
              </div>
              <!-- 使用动态生成的状态文本和CSS类 -->
              <div class="server-status" :class="getServerStatusClass(server.inventoryNumber)">
                {{ getServerStatusText(server.inventoryNumber) }}
              </div>
            </div>
            <div class="server-price-section" v-show="showindex === 'priceMouth'">
              <div class="price">
                <span class="currency">¥</span>
                <span class="amount">{{ server.priceMouth }}</span>
                <span class="unit">/月</span>
              </div>
              <!-- 使用动态生成的状态文本和CSS类 -->
              <div class="server-status" :class="getServerStatusClass(server.inventoryNumber)">
                {{ getServerStatusText(server.inventoryNumber) }}
              </div>
            </div>
            <div class="server-price-section" v-show="showindex === 'priceYear'">
              <div class="price">
                <span class="currency">¥</span>
                <span class="amount">{{ server.priceYear }}</span>
                <span class="unit">/年</span>
              </div>
              <!-- 使用动态生成的状态文本和CSS类 -->
              <div class="server-status" :class="getServerStatusClass(server.inventoryNumber)">
                {{ getServerStatusText(server.inventoryNumber) }}
              </div>
            </div>

            <!-- 服务器规格 - 修改为垂直对齐的布局 -->
            <div class="server-specs">
              <div class="specs-grid">
                <div class="spec-item">
                  <div class="spec-label">显卡数量</div>
                  <div class="spec-value">{{ server.graphicsCardNumber }}</div>
                </div>

                <div class="spec-item">
                  <div class="spec-label">显存(GB)</div>
                  <div class="spec-value">{{ server.videoMemory }}</div>
                </div>

                <div class="spec-item">
                  <div class="spec-label">VCPU核数</div>
                  <div class="spec-value">{{ server.gpuNuclearNumber }}</div>
                </div>

                <div class="spec-item">
                  <div class="spec-label">系统盘(GB)</div>
                  <div class="spec-value">{{ server.systemDisk }}</div>
                </div>

                <div class="spec-item">
                  <div class="spec-label">云盘(GB)</div>
                  <div class="spec-value">{{ server.cloudDisk || '-' }}</div>
                </div>

                <div class="spec-item">
                  <div class="spec-label">内存(GB)</div>
                  <div class="spec-value">{{ server.internalMemory }}</div>
                </div>
              </div>
            </div>

            <!-- 购买按钮状态根据服务器状态动态调整 -->
            <!--            <button-->
            <!--                class="buy-button"-->
            <!--                :class="{-->
            <!--      'disabled': server.inventoryNumber === 0,-->
            <!--      'buy-button-hovered': hoveredServer === server.id-->
            <!--    }"-->
            <!--                @click="server.inventoryNumber > 0 ? orderOK(server) : null"-->
            <!--            >-->
            <!--              立即租赁-->
            <!--            </button>-->

            <button
                class="buy-button"
                :class="{
      'disabled': server.inventoryNumber === 0,
      'buy-button-hovered': hoveredServer === server.id
    }"
                @click="server.inventoryNumber > 0 ? directToConsole() : null"
            >
              立即租赁
            </button>

          </div>
        </div>
        <!-- 空状态提示 -->
        <div v-else class="empty-state">
          <div class="empty-state-icon">
            <!--            <img src="/path/to/empty-state-icon.png" alt="暂无数据" />-->
          </div>
          <div class="empty-state-text">暂无数据</div>
        </div>
      </div>
    </div>
    <order-detail :visible="showDetail" :server="serverss" :selectedBillingMethod="selectedBillingMethod" @orderSubmitted="buyGpu(serverss)" @price-updated="orderPirce" @time-updated="orderTimes" @close="closeOrderDetail"></order-detail>
    <chatAi></chatAi>
  </div>
</template>

<script>
import Layout from "@/components/common/Layout-header";
import chatAi from "@/components/common/mider/chatAi";
import {postAnyData, getNotAuth, postNotAuth,getAnyData,postJsonData} from "@/api/login";
import {formatDateTime} from "@/utils/component"
import {getToken} from '@/utils/auth'
import orderDetail from "@/views/Product/OrderDetail";
import SlideNotification from '@/components/common/header/SlideNotification.vue';


export default {
  name: "ProductView",
  components: { Layout,orderDetail,chatAi,SlideNotification },
  data() {
    return {
      orderTime:null,
      price:null,
      //记录当前用户选择计费方式
      selectedBillingMethod:'priceHour',
      serverss: {},
      showComingSoon: false,
      notificationMessage: "",
      showDetail:false,
      showindex:'priceHour',
      // 筛选区域可见性
      isFilterVisible: true,

      // 当前悬浮的服务器ID
      hoveredServer: null,

      // 区域数据
      regions: [
        { id: '宁夏-B', name: '宁夏-B' },
        { id: '四川-A', name: '四川-A' },
        { id: '广东-B', name: '广东-B' }
      ],

      // GPU型号数据 - 作为所有可能的GPU型号库
      allGpuModels: [
        { id: 'a100-80g-nvlink', name: 'A100-80G NVLink' },
        { id: 'a800-80g-pcie', name: 'A800-80G PCIe' },
        { id: 'rtx4090-24g-pcie', name: 'RTX4090-24G PCIe' },
        { id: 'a100-80g-nvlink-2', name: 'A100-80G NVLink' },
        { id: 'h100-80g-nvlink', name: 'H100-80G NVLink' },
        { id: 'a800-80g-nvlink', name: 'A800-80G NVLink' },
        { id: 'h800-80g-nvlink', name: 'H800-80G NVLink' }
      ],

      // 服务器状态类型定义
      serverStatuses: {
        AVAILABLE: 'available',     // 资源充足
        SHORTAGE: 'shortage',       // 资源紧张
        UNAVAILABLE: 'unavailable'  // 已售罄
      },

      // 筛选条件 - 改为单选的计费模式
      filters: {
        allRegions: true,
        selectedRegions: [],
        billingMethod: 'hourly', // 改为单个字符串值
        allGpuModels: true,
        selectedGpuModels: [],
        usageScenarios: {
          development: true
        }
      },

      // 服务器数据库 - 模拟从API获取的数据
      allServers: [
        {
          id: 1,
          gpuModel: 'NVIDIA A100-80G NVLink',
          gpuModelId: 'a100-80g-nvlink',
          gpuCount: 8,
          gpuMemory: 80,
          vcpu: 112,
          systemDisk: 50,
          cloudDisk: null,
          memory: 912,
          pricePerHour: 69.76,
          status: 'unavailable',  // 这里不再硬编码，而是从后端获取
          regionId: 'ningxia-b'
        },
        {
          id: 2,
          gpuModel: 'NVIDIA A100-80G NVLink',
          gpuModelId: 'a100-80g-nvlink',
          gpuCount: 4,
          gpuMemory: 80,
          vcpu: 56,
          systemDisk: 50,
          cloudDisk: null,
          memory: 456,
          pricePerHour: 34.88,
          status: 'shortage',  // 示例：资源紧张状态
          regionId: 'ningxia-b'
        },
        {
          id: 3,
          gpuModel: 'NVIDIA A100-80G NVLink',
          gpuModelId: 'a100-80g-nvlink',
          gpuCount: 2,
          gpuMemory: 80,
          vcpu: 28,
          systemDisk: 50,
          cloudDisk: null,
          memory: 228,
          pricePerHour: 17.44,
          status: 'available',
          regionId: 'ningxia-b'
        },
        {
          id: 4,
          gpuModel: 'NVIDIA H100-80G NVLink',
          gpuModelId: 'h100-80g-nvlink',
          gpuCount: 8,
          gpuMemory: 80,
          vcpu: 128,
          systemDisk: 100,
          cloudDisk: 500,
          memory: 1024,
          pricePerHour: 99.88,
          status: 'available',
          regionId: 'guangdong-b'
        },
        {
          id: 5,
          gpuModel: 'NVIDIA RTX4090-24G PCIe',
          gpuModelId: 'rtx4090-24g-pcie',
          gpuCount: 4,
          gpuMemory: 24,
          vcpu: 48,
          systemDisk: 50,
          cloudDisk: null,
          memory: 256,
          pricePerHour: 18.56,
          status: 'shortage',
          regionId: 'sichuan-a'
        },
        {
          id: 6,
          gpuModel: 'NVIDIA A800-80G PCIe',
          gpuModelId: 'a800-80g-pcie',
          gpuCount: 4,
          gpuMemory: 80,
          vcpu: 56,
          systemDisk: 50,
          cloudDisk: null,
          memory: 456,
          pricePerHour: 32.64,
          status: 'unavailable',
          regionId: 'sichuan-a'
        }
      ]
    };
  },
  computed: {
    // 根据所选区域动态获取可用的GPU型号
    availableGpuModels() {
      // 确保有选择的区域
      if (this.filters.selectedRegions.length === 0) {
        return [];
      }
      // 根据所选区域筛选出可用的GPU型号ID
      const availableGpuIds = this.allServers
          .filter(server => this.filters.selectedRegions.includes(server.region))
          .map(server => server.name);

      // 使用Set去重
      const uniqueGpuIds = [...new Set(availableGpuIds)];

      // 返回可用的GPU型号完整信息
      return this.allGpuModels.filter(gpu => uniqueGpuIds.includes(gpu.name));
    },

    // 根据筛选条件过滤服务器
    filteredServers() {
      // 如果首次加载，设置默认选中所有可用的GPU型号
      if (this.filters.allGpuModels && this.filters.selectedGpuModels.length === 0 && this.availableGpuModels.length > 0) {
        this.filters.selectedGpuModels = this.availableGpuModels.map(gpu => gpu.id);
      }

      // 如果没有选择可用区或GPU型号，则不显示服务器
      if (this.filters.selectedRegions.length === 0 || this.filters.selectedGpuModels.length === 0) {
        return [];
      }

      return this.allServers.filter(server => {
        // 区域筛选
        if (!this.filters.selectedRegions.includes(server.region)) {
          return false;
        }

        // GPU型号筛选
        if (!this.filters.selectedGpuModels.includes(server.name)) {
          return false;
        }

        // 使用场景筛选
        if (!this.filters.usageScenarios.development) {
          return false;
        }

        // 所有服务器都支持所有计费方式，此处无需进行过滤
        // 保留此注释作为业务逻辑说明

        return true;
      });
    }
  },
  methods: {
    closeOrderDetail(){
      this.showDetail = false
    },
    //接收订单时间
    orderTimes(newval){
      this.orderTime = newval
    },
    //接收订单价格
    orderPirce(newval){
      this.price = newval
    },
    async directToConsole() {
      // 检查登录状态
      if (!getToken()) {
        this.$router.push('/login');
        this.$toast.success("请先登录后再进行购买");
        return;
      }

      // 获取用户实名信息
      try {
        const res = await postAnyData("/logout/cilent/getInfo");
        if (res.data.code === 200) {
          const isReal = res.data.data.isReal;

          // 未实名认证处理
          if (isReal !== 1) {
            this.notificationMessage = "请先完成实名认证，正在为您跳转到对应页面";
            this.showComingSoon = true;

            setTimeout(() => {
              this.$router.push({
                path: '/personal',
                query: { activeTab: 'verification' }
              });
            }, 2000);
            return;
          }

          // 已实名直接跳转
          this.$router.push('/console');
        }
      } catch (error) {
        this.$toast.error("获取用户信息失败");
        // console.error(error);
      }
    },
    //购买Gpu
    orderOK(serverinfo) {
      // 再次检查库存，防止通过其他方式触发
      if (serverinfo.inventoryNumber <= 0) {
        this.$toast.error("该资源已售罄，无法购买");
        return;
      }

      if(!getToken()) {
        this.$router.push('/login');
        this.$toast.success("请先登录后再进行购买");
        return;
      }

      this.serverss = serverinfo;
      this.selectedBillingMethod = this.showindex;
      this.showDetail = true;
    },

    buyGpu(server){
      let params = {
        id:server.id,
        price:'',
        name:server.name,
        time:this.orderTime
      }
      params.price = this.price
      params.time = this.orderTime
      postJsonData("/system/parameter/decrease",params).then(res =>{
        if (res.data.msg === '余额不足'){
          this.$toast.error("余额不足,请前往充值")
          return
        }
        if (res.data.msg === '库存不足'){
          this.$toast.error("库存不足，请稍后再试")
          return;
        }
        this.$toast.success("租赁成功，请前往控制台使用")
      })
    },
    // 切换筛选区域的可见性
    toggleFilterVisibility() {
      this.isFilterVisible = !this.isFilterVisible;
    },

    // 动画钩子函数 - 进入动画开始前
    beforeEnter(el) {
      el.style.height = '0';
      el.style.opacity = '0';
      el.style.overflow = 'hidden';
    },

    // 进入动画中
    enter(el) {
      const height = el.scrollHeight;
      // 使用 requestAnimationFrame 确保过渡平滑
      requestAnimationFrame(() => {
        el.style.height = height + 'px';
        el.style.opacity = '1';
      });
    },

    // 进入动画结束
    afterEnter(el) {
      // 移除高度限制，允许内容自然流动
      el.style.height = '';
      el.style.overflow = '';
    },

    // 离开动画开始前
    beforeLeave(el) {
      // 设置具体高度，为动画做准备
      el.style.height = el.scrollHeight + 'px';
      el.style.overflow = 'hidden';
    },

    // 离开动画中
    leave(el) {
      // 强制浏览器重排以确保动画正确开始
      void el.offsetHeight;

      // 使用 requestAnimationFrame 确保过渡平滑
      requestAnimationFrame(() => {
        el.style.height = '0';
        el.style.opacity = '0';
      });
    },

    // 离开动画结束
    afterLeave(el) {
      // 清理样式
      el.style.height = '';
      el.style.overflow = '';
    },

    // 全选/取消全选区域
    toggleAllRegions() {
      if (this.filters.allRegions) {
        this.filters.selectedRegions = this.regions.map(region => region.name);
      } else {
        this.filters.selectedRegions = [];
      }
      this.updateFilters();
    },

    // 全选/取消全选GPU型号
    toggleAllGpuModels() {
      if (this.filters.allGpuModels) {
        this.filters.selectedGpuModels = this.availableGpuModels.map(gpu => gpu.id);
      } else {
        this.filters.selectedGpuModels = [];
      }
      this.updateFilters();
    },

    // 更新筛选条件，重新加载GPU型号和服务器数据
    updateFilters() {
      // 同步全选状态
      this.filters.allRegions = this.filters.selectedRegions.length === this.regions.length;

      // 确保availableGpuModels更新后再检查全选状态
      this.$nextTick(() => {
        this.filters.allGpuModels = this.availableGpuModels.length > 0 &&
            this.filters.selectedGpuModels.length === this.availableGpuModels.length;
      });

      // 在实际应用中，这里应该调用API重新获取数据
      // this.fetchServers();
    },

    // 根据区域ID获取区域名称
    getRegionName(regionId) {
      const region = this.regions.find(r => r.id === regionId);
      return region ? region.name : '未知区域';
    },

    // 获取服务器状态显示文本
    getServerStatusText(status) {
      if (status>0 && status<=3){
        status = 'shortage'
      }else if(status>3){
        status = 'available'
      }else if (status ==0){
        status = 'unavailable'
      }
      switch(status) {
        case this.serverStatuses.AVAILABLE:
          return '资源充足';
        case this.serverStatuses.SHORTAGE:
          return '资源紧张';
        case this.serverStatuses.UNAVAILABLE:
          return '已售罄';
        default:
          return '未知状态';
      }
    },

    // 获取服务器状态样式类
    getServerStatusClass(status) {
      if (status>0 && status<=3){
        status = 'shortage'
      }else if(status>3){
        status = 'available'
      }else if (status ==0){
        status = 'unavailable'
      }
      switch(status) {
        case this.serverStatuses.AVAILABLE:
          return 'server-available';
        case this.serverStatuses.SHORTAGE:
          return 'server-shortage';
        case this.serverStatuses.UNAVAILABLE:
          return 'server-unavailable';
        default:
          return '';
      }
    },

    // 从API获取服务器数据
    fetchServers() {
      //加载数据
      let params;
      getNotAuth("/auth/getGpuList").then(respose =>{
        this.allServers = respose.data.data;
        postNotAuth("/system/parameter/getGpu").then(res =>{
          this.allGpuModels = res.data.data;
          for (let i =0;i<this.allGpuModels.length;i++){
            this.allGpuModels[i].id = this.allGpuModels[i].name
          }
          postNotAuth("/system/parameter/getRegion").then(region =>{
            this.regions = region.data.data
            for (let i =0;i<this.regions.length;i++){
              this.regions[i].id = this.regions[i].region
              this.regions[i].name = this.regions[i].region
            }
            this.toggleAllRegions()
            this.toggleAllGpuModels()
          })
        })
        params = {
          regions: this.filters.selectedRegions,
          gpuModels: this.filters.selectedGpuModels,
          billingMethod: this.filters.billingMethod
        };
      })
    }
  },
  created() {
    // 在组件创建时获取服务器数据
    this.fetchServers();
    // 初始化选中所有可用的GPU型号
    this.$nextTick(() => {
      this.filters.selectedGpuModels = this.availableGpuModels.map(gpu => gpu.id);
    });
  },
  watch: {
    // 监听区域选择变化，同步更新GPU型号选择
    'filters.selectedRegions': {
      handler() {
        // 当选择的区域变化时，使用新的可用GPU型号列表更新选中的GPU型号
        if (this.filters.allGpuModels) {
          this.filters.selectedGpuModels = this.availableGpuModels.map(gpu => gpu.id);
        } else {
          // 仅保留在新的可用列表中的GPU型号
          const availableIds = this.availableGpuModels.map(gpu => gpu.id);
          this.filters.selectedGpuModels = this.filters.selectedGpuModels.filter(id => availableIds.includes(id));
        }
      },
      immediate: true
    }
  }
};
</script>

<style scoped>
/* 基础样式 */
.compute-market {
  font-family: Arial, sans-serif;
  max-width: 2560px;
  margin: 0 auto;
  padding: 20px;
  color: #333;
}

/* 筛选区域样式 */
.filter-section {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  padding: 8px 16px;
  border: 1px solid #f0f0f0;
}

.filter-header {
  display: flex;
  align-items: center;
  padding: 6px 0;
  cursor: pointer;
  user-select: none;
  min-height: 32px;
}

.filter-title {
  font-size: 15px;
  font-weight: 400;
  color: #333;
}

.filter-icon {
  color: #409eff;
  margin-left: 8px;
  font-size: 12px;
  transition: transform 0.3s ease;
}

.filter-icon.collapsed {
  transform: rotate(-90deg);
}

/* 改进过渡动画效果 */
.slide-enter-active,
.slide-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  max-height: 800px;
  overflow: hidden;
}

.slide-enter-from,
.slide-leave-to {
  max-height: 0;
  opacity: 0;
  overflow: hidden;
}

.filter-content {
  overflow: hidden;
}

.filter-row {
  display: flex;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
  flex-direction: column;
}

.filter-row:last-child {
  border-bottom: none;
}

.filter-label {
  width: 100%;
  color: #333;
  font-size: 13px;
  font-weight: 400;
  padding-top: 2px;
  margin-bottom: 8px;
}

.filter-options {
  flex: 1;
}

.checkbox-group, .radio-group {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.checkbox-item, .radio-item {
  display: flex;
  align-items: center;
  cursor: pointer;
  min-width: 2px;
}

.checkbox-item input[type="checkbox"], .radio-item input[type="radio"] {
  margin-right: 5px;
  width: 14px;
  height: 14px;
  accent-color: #2196f3;
  cursor: pointer;
}

.checkbox-text, .radio-text {
  font-size: 12px;
  font-weight: 400;
  margin-bottom: -5px;
}

.gpu-brand {
  font-size: 13px;
  font-weight: 400;
  margin-bottom: 8px;
}

.gpu-list {
  margin-top: 6px;
}

/* 服务器卡片网格 - 响应式布局 */
.servers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 15px;
}

/* 服务器卡片样式 */
.server-card {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 16px;
  border: 1px solid #f0f0f0;
  transition: all 0.2s ease;
  position: relative;
}

.server-card-hovered {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(128, 65, 255, 0.2);
}

/* 区域标签 */
.region-tag {
  position: absolute;
  top: 16px;
  right: 16px;
  font-size: 14px;
  font-weight: 500;
  color: #666;
}

.server-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 12px;
  height: 3em;
  color: #333;
  padding-right: 60px;
}

.server-price-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.price {
  display: flex;
  align-items: baseline;
}

.currency {
  font-size: 16px;
  color: #2196f3;
  font-weight: 500;
}

.amount {
  font-size: 26px;
  font-weight: bold;
  color: #2196f3;
  margin: 0 2px;
}

.unit {
  font-size: 14px;
  color: #2196f3;
}

.server-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

/* 资源充足状态 */
.server-available {
  color: #52c41a;
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
}

/* 资源紧张状态 */
.server-shortage {
  color: #fa8c16;
  background-color: #fff7e6;
  border: 1px solid #ffd591;
}

/* 已售罄状态 */
.server-unavailable {
  color: #333;
  background-color: #f5f5f5;
  border: 1px solid #d9d9d9;
}

/* 服务器规格 - 响应式网格布局 */
.server-specs {
  background-color: #f9f9f9;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 16px;
}

.specs-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.spec-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.spec-label {
  font-size: 12px;
  color: #999;
  margin-bottom: 4px;
}

.spec-value {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

/* 购买按钮 */
.buy-button {
  width: 100%;
  padding: 10px 0;
  background-color: white;
  color: #333;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  text-align: center;
  transition: all 0.2s;
}

.buy-button.disabled {
  background-color: #f5f5f5;
  color: #999;
  cursor: not-allowed;
}

.buy-button-hovered:not(.disabled) {
  background-color: #2196f3;
  color: white;
  border-color: #2196f3;
}

/* 空状态提示 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
  color: #999;
}

.empty-state-icon {
  margin-bottom: 16px;
}

.empty-state-icon img {
  width: 80px;
  height: 80px;
  opacity: 0.6;
}

.empty-state-text {
  font-size: 16px;
  color: #999;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .compute-market {
    padding: 10px;
  }

  .filter-section {
    padding: 6px 12px;
  }

  .filter-row {
    padding: 8px 0;
  }

  .checkbox-group, .radio-group {
    gap: 8px;
  }

  .radio-item {
    margin-right: -15px; /* 移动端减小间距 */
  }

  .radio-item input[type="radio"] {
    margin-right: -5px; /* 移动端进一步减小间距 */
  }

  .radio-text {
    font-size: 11px; /* 移动端减小字体 */
    margin-left: -30px; /* 移动端减小间距 */
  }
  .checkbox-item {
    min-width: 70px;
  }

  .servers-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .server-card {
    padding: 12px;
  }

  .specs-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .price .amount {
    font-size: 22px;
  }

  .price .unit {
    font-size: 12px;
  }

  .server-title {
    font-size: 15px;
  }

  .buy-button {
    padding: 8px 0;
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .filter-row {
    flex-direction: column;
  }

  .filter-label {
    margin-bottom: 3px;
  }

  .checkbox-group, .radio-group {
    gap: 6px;
  }

  .checkbox-item, .radio-item {
    min-width: 60px;
  }

  .specs-grid {
    grid-template-columns: 1fr 1fr;
  }

  .price .amount {
    font-size: 20px;
  }

  .server-status {
    font-size: 11px;
    padding: 3px 6px;
  }

  .empty-state-icon img {
    width: 60px;
    height: 60px;
  }

  .empty-state-text {
    font-size: 14px;
  }
}

/* 调整左侧边距在移动端 */
@media (max-width: 768px) {
  div[style^="margin-left"] {
    margin-left: 0 !important;
  }

  div[style^="width"] {
    width: 100% !important;
  }
}
/* 调整左侧边距在安卓显示器端 */
@media (min-width: 1440px) {
  div[style^="margin-left"] {
    margin-left: 3.5% !important;
  }

  div[style^="width"] {
    width: 96% !important;
  }
}

</style>