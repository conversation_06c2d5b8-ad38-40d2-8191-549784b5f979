# <font style="color:rgb(2, 8, 23);">健康检测</font>
## <font style="color:rgb(2, 8, 23);">1.概念定位</font>
### <font style="color:rgb(2, 8, 23);">1.1 存活探针</font>
<font style="color:rgb(2, 8, 23);">存活探针用于检测容器运行状态。当应用出现死锁或无响应时（如崩溃、死循环），天工开物平台会自动触发容器重启，无需人工干预。 存活探针不会等待就绪探针成功。如果你想在执行存活探针前等待，你可以使用启动探针。</font>

### <font style="color:rgb(2, 8, 23);">1.2 就绪探针</font>
<font style="color:rgb(2, 8, 23);">就绪探针验证节点是否具备服务能力。在天工开物平台中，未通过检测的节点将被自动移出服务流量池，确保业务连续性。 如果就绪探针返回的状态为失败，会将该节点从所有对应服务中移出，同时启动一个新节点进行替换，保证服务的正常运行。 就绪探针在容器的整个生命期内持续运行。</font>

### <font style="color:rgb(2, 8, 23);">1.3 启动探针</font>
<font style="color:rgb(2, 8, 23);">启动探针保护慢启动容器不被误杀。天工开物平台会暂停存活/就绪检测，直到该探针确认应用已完成初始化。 如果配置了这类探针，它会禁用存活检测和就绪检测，直到启动探针成功为止。</font>

<font style="color:rgb(2, 8, 23);">这类探针仅在启动时执行，不像存活探针和就绪探针那样周期性地运行。</font>

## <font style="color:rgb(2, 8, 23);">2.平台视角下的探针实际案例</font>
### <font style="color:rgb(2, 8, 23);">2.1 启动探针检测容器中的应用是否已经启动</font>
<font style="color:rgb(2, 8, 23);">启动探针保护慢启动容器不被误杀。天工开物平台会暂停存活/就绪检测，直到该探针确认应用已完成初始化。 如果配置了这类探针，它会</font>**<font style="color:rgb(2, 8, 23);">禁用存活检测和就绪检测</font>**<font style="color:rgb(2, 8, 23);">，直到启动探针成功为止。</font>

<font style="color:rgb(2, 8, 23);">这类探针仅在启动时执行，不像存活探针和就绪探针那样周期性地运行。</font>

<font style="color:rgb(103, 103, 108);">示例镜像地址：harbor.suanleme.cn/xiditgkw/jupyter/base-notebook:latest 端口号配置：8888</font>
![](./imgs/health1.png)
<font style="color:rgb(2, 8, 23);">启动初始探针设置（下一部分会详细讲解参数及其意义）：</font>

<font style="color:rgb(2, 8, 23);">修改端口号为 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">8888</font><font style="color:rgb(2, 8, 23);">（JupyterLab 默认端口）</font>
![](./imgs/health2.png)
启动探针发现异常：
![](./imgs/health3.png)
**<font style="color:rgb(2, 8, 23);">异常原因：</font>**
+ <font style="color:rgb(2, 8, 23);">启动探针配置不当：</font>
    - <font style="color:rgb(2, 8, 23);">路径 /live 返回 404：容器启动后，存活探针立即开始检查 /live 接口（初始延迟 0 秒），但该接口不存在（返回 404），导致探针失败。</font>
    - <font style="color:rgb(2, 8, 23);">失败阈值触发：存活探针每 10 秒检查一次，超时时间仅 1 秒，连续 3 次失败后，判定容器不健康，触发重启。</font>
+ <font style="color:rgb(2, 8, 23);">服务初始化时间较长：</font>
    - <font style="color:rgb(2, 8, 23);">JupyterLab 及其扩展（如 jupyter_lsp、jupyter_server_terminals）加载需要时间（约 10 秒），但</font>**<font style="color:rgb(2, 8, 23);">启动探针未等待服务完全就绪就开始检查</font>**<font style="color:rgb(2, 8, 23);">。</font>
![](./imgs/health4.png)
**<font style="color:rgb(2, 8, 23);">更新启动探针配置：</font>**<font style="color:rgb(2, 8, 23);"> 修改检查周期长一些 让 JupyterLab 先启动起来 避免启动时间过短 路径指向容器默认启动路径</font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">/lab</font>
![](./imgs/health5.png)
**<font style="color:rgb(2, 8, 23);">当前服务状态：</font>**
<font style="color:rgb(2, 8, 23);">JupyterLab 服务正常运行 服务持续监听 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">http://127.0.0.1:8888/lab</font><font style="color:rgb(2, 8, 23);">。 用户访问 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">/</font><font style="color:rgb(82, 183, 136);background-color:rgba(142, 150, 170, 0.14);"> </font><font style="color:rgb(2, 8, 23);">路径触发 302 重定向，表明前端路由正常。</font>
<font style="color:rgb(2, 8, 23);">无关键错误： 日志中未出现内核崩溃、探针失败或服务中断的报错。 内核消失问题（</font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">404 Kernel does not exist</font><font style="color:rgb(2, 8, 23);">）未出现。</font>
![](./imgs/health6.png)
### <font style="color:rgb(2, 8, 23);">2.2 就绪探针容器检测是否准备好接收流量</font>
<font style="color:rgb(2, 8, 23);">这里以我们预制好的镜像 Whisper 举例</font>
![](./imgs/health7.png)
<font style="color:rgb(2, 8, 23);">启动成功后进入健康检查设置中：</font>
<font style="color:rgb(2, 8, 23);">修改路径为</font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">/docs#/Endpoints/asr_asr_post</font><font style="color:rgb(2, 8, 23);">（语音转文字默认接口）</font>
<font style="color:rgb(2, 8, 23);">端口号改为：</font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">9000</font>
![](./imgs/health8.png)
<font style="color:rgb(2, 8, 23);">保存后可以通过容器日志查看到实时返回的接口信息</font>
服务运行正常：容器状态显示 “运行中”，日志中接口（ /docs）返回 200 状态码，表明服务已成功启动，准备好接收流量（检查周期每 20 秒返回一次）
![](./imgs/health9.png)
### <font style="color:rgb(2, 8, 23);">2.3 存活探针检测容器是否正常运行</font>
<font style="color:rgb(2, 8, 23);">这里以我们预制好的镜像 Whisper 举例</font>
![](./imgs/health7.png)
<font style="color:rgb(2, 8, 23);">启动成功后进入健康检查设置中：</font>
<font style="color:rgb(2, 8, 23);">修改路径为</font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">/docs#/Endpoints/asr_asr_post</font><font style="color:rgb(2, 8, 23);"> （语音转文字默认接口）</font>
<font style="color:rgb(2, 8, 23);">端口号改为：</font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">9000</font>
![](./imgs/health10.png)
服务运行正常：容器状态显示 “运行中”，日志中接口（ /docs）返回 200 状态码，表明服务已成功启动，准备好接收流量（检查周期每 10 秒返回一次）
![](./imgs/health11.png)
## <font style="color:rgb(2, 8, 23);">3.平台视角下的探针参数作用详细解析</font>
![](./imgs/health12.png)

**<font style="color:rgb(2, 8, 23);">HTTPGet：</font>**<font style="color:rgb(2, 8, 23);">通过发送 HTTP 请求并检查响应状态码来判断服务状态</font>

**<font style="color:rgb(2, 8, 23);">TCPSocket：</font>**<font style="color:rgb(2, 8, 23);">仅检查指定端口是否能建立 TCP 连接，不涉及具体业务逻辑</font>

**<font style="color:rgb(2, 8, 23);">路径：</font>**<font style="color:rgb(2, 8, 23);">仅 HTTP Get 类型需要，TCP Socket 类型不需要路径参数</font>

**<font style="color:rgb(2, 8, 23);">端口：</font>**<font style="color:rgb(2, 8, 23);">健康检查的目标端口号</font>

**<font style="color:rgb(2, 8, 23);">初始延迟：</font>**<font style="color:rgb(2, 8, 23);">0 秒（立即开始检查）</font>

**<font style="color:rgb(2, 8, 23);">检查周期：</font>**<font style="color:rgb(2, 8, 23);">X 秒（每 X 秒检测一次）</font>

**<font style="color:rgb(2, 8, 23);">超时时间：</font>**<font style="color:rgb(2, 8, 23);">X 秒（每次请求最长等待 X 秒）</font>

**<font style="color:rgb(2, 8, 23);">失败阈值：</font>**<font style="color:rgb(2, 8, 23);">X 次（连续失败 X 次判定启动失败）</font>

## <font style="color:rgb(2, 8, 23);">4.更多</font>
[<font style="color:#2F8EF4;">https://kubernetes.io/zh-cn/docs/tasks/configure-pod-container/configure-liveness-readiness-startup-probes/</font>](https://kubernetes.io/zh-cn/docs/tasks/configure-pod-container/configure-liveness-readiness-startup-probes/)

<br><br>
<font style="color:rgba(178,178,178,1);">最后更新于: 2025/6/19 17:07</font>
