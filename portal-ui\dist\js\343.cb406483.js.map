{"version": 3, "file": "js/343.cb406483.js", "mappings": "6JAAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,SAAS,CAACA,EAAG,MAAM,CAACE,YAAY,yBAAyB,CAACF,EAAG,KAAK,CAACE,YAAY,SAAS,CAACJ,EAAIK,GAAG,UAAUH,EAAG,IAAI,CAACE,YAAY,YAAY,CAACJ,EAAIK,GAAG,8BACxN,EACIC,EAAkB,G,UCUtB,GACAC,KAAA,qBACAC,WAAA,CAAAC,OAAAA,EAAAA,ICdkQ,I,UCQ9PC,GAAY,OACd,EACAX,EACAO,GACA,EACA,KACA,WACA,MAIF,EAAeI,EAAiB,O", "sources": ["webpack://portal-ui/./src/views/AlgorithmCommunity.vue", "webpack://portal-ui/src/views/AlgorithmCommunity.vue", "webpack://portal-ui/./src/views/AlgorithmCommunity.vue?920c", "webpack://portal-ui/./src/views/AlgorithmCommunity.vue?fe25"], "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('Layout',[_c('div',{staticClass:\"coming-soon-container\"},[_c('h1',{staticClass:\"title\"},[_vm._v(\"敬请期待\")]),_c('p',{staticClass:\"subtitle\"},[_vm._v(\"我们正在努力建设中，敬请期待更多精彩内容！\")])])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <Layout>\r\n    <div class=\"coming-soon-container\">\r\n      <h1 class=\"title\">敬请期待</h1>\r\n      <p class=\"subtitle\">我们正在努力建设中，敬请期待更多精彩内容！</p>\r\n    </div>\r\n  </Layout>\r\n</template>\r\n\r\n<script>\r\nimport Layout from \"@/components/common/Layout-header\";\r\n\r\nexport default {\r\n  name: \"AlgorithmCommunity\",\r\n  components: { Layout }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.coming-soon-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 70vh;\r\n  text-align: center;\r\n  background-color: #f9fafb;\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\r\n  padding: 40px;\r\n}\r\n\r\n.title {\r\n  font-size: 3rem;\r\n  color: #1f2937;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.subtitle {\r\n  font-size: 1.2rem;\r\n  color: #4b5563;\r\n}\r\n</style>\r\n", "import mod from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./AlgorithmCommunity.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./AlgorithmCommunity.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./AlgorithmCommunity.vue?vue&type=template&id=3c254012&scoped=true&\"\nimport script from \"./AlgorithmCommunity.vue?vue&type=script&lang=js&\"\nexport * from \"./AlgorithmCommunity.vue?vue&type=script&lang=js&\"\nimport style0 from \"./AlgorithmCommunity.vue?vue&type=style&index=0&id=3c254012&prod&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3c254012\",\n  null\n  \n)\n\nexport default component.exports"], "names": ["render", "_vm", "this", "_c", "_self", "staticClass", "_v", "staticRenderFns", "name", "components", "Layout", "component"], "sourceRoot": ""}