<template>
  <div class="login-left-side">
    <!-- 公司Logo -->
    <div class="logo-container">
      <a @click="navigateTo('/index')" class="logo-link">
        <img class="logo" src="../../../assets/logo_tiangong.png" alt="算力租赁" />
      </a>
      <!--      <h1 class="company-name">天工云</h1>-->
    </div>

    <div class="bottom-text">
      <h2 class="slogan">高效算力 · 智慧未来</h2>
      <p class="sub-slogan">专业算力租赁服务，为您的业务提供强大支持</p>
    </div>

    <!-- 主要视觉元素 -->
    <div class="visual-element">
      <div class="server-illustration">
        <div v-for="(server, index) in servers" :key="index"
             class="server-unit"
             :style="{
               animationDelay: `${index * 0.2}s`,
               transform: `translateY(${index * 4}px)`
             }">
          <div class="server-light"></div>
        </div>
      </div>

      <div class="connections">
<!--        <div v-for="i in 10" :key="i" class="connection-line"></div>-->
      </div>
    </div>

    <!-- 特点介绍 -->
    <div class="features">
      <div v-for="(feature, index) in features" :key="index" class="feature-item">
<!--        <div class="feature-icon">-->
<!--          <component :is="feature.icon" />-->
<!--        </div>-->
        <div class="feature-text">
          <h3>{{ feature.title }}</h3>
          <p>{{ feature.description }}</p>
        </div>
      </div>
    </div>

    <!-- 背景动画元素 -->
    <div class="background-elements">
      <div v-for="i in 20" :key="i"
           class="floating-particle"
           :style="{
             left: `${Math.random() * 100}%`,
             top: `${Math.random() * 100}%`,
             animationDuration: `${3 + Math.random() * 10}s`,
             animationDelay: `${Math.random() * 5}s`
           }">
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref } from 'vue';

// 图标组件
const PerformanceIcon = {
  template: `
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path d="M12 2v4"></path>
      <path d="m16.24 7.76 2.83-2.83"></path>
      <path d="M18 12h4"></path>
      <path d="m16.24 16.24 2.83 2.83"></path>
      <path d="M12 18v4"></path>
      <path d="m7.76 16.24-2.83 2.83"></path>
      <path d="M6 12H2"></path>
      <path d="m7.76 7.76-2.83-2.83"></path>
      <circle cx="12" cy="12" r="4"></circle>
    </svg>
  `
}

const ServerIcon = {
  template: `
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <rect width="20" height="8" x="2" y="2" rx="2" ry="2"></rect>
      <rect width="20" height="8" x="2" y="14" rx="2" ry="2"></rect>
      <line x1="6" x2="6" y1="6" y2="6"></line>
      <line x1="6" x2="6" y1="18" y2="18"></line>
    </svg>
  `
}

const ShieldIcon = {
  template: `
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
    </svg>
  `
}

export default defineComponent({
  name: 'backgroundlogin',
  components: {
    PerformanceIcon,
    ServerIcon,
    ShieldIcon
  },
  methods:{
    navigateTo(path) {
      // 记录当前活动路径作为上一个活动路径
      if (this.currentPath && this.currentPath !== path) {
        this.previousActivePath = this.currentPath;

        // 为当前活动链接和登录按钮添加 active-exit 类
        this.$nextTick(() => {
          const navLinks = document.querySelectorAll('.nav-link, .btn-login');
          navLinks.forEach(link => {
            if ((link.classList.contains('active') ||
                    (path === '/login' && link.classList.contains('btn-login'))) &&
                !link.classList.contains('active-exit')) {
              link.classList.add('active-exit');

              // 等待动画完成后移除 active-exit 类
              setTimeout(() => {
                link.classList.remove('active-exit');
              }, 300); // 匹配你的 CSS transition 持续时间 (0.3s)
            }
          });

          // 更新当前路径
          this.currentPath = path;
        });
      } else {
        this.currentPath = path;
      }

      // 如果当前路径与目标路径相同，则重新加载页面
      if (this.$route.path === path) {
        this.$nextTick(() => {
          window.scrollTo({
            top: 0,
            behavior: 'instant' // 使用即时滚动而不是平滑滚动
          });
          this.$router.go(0); // 刷新当前页面
        });
      } else {
        // 不同路径，正常导航并滚动到顶部
        this.$router.push(path);
        window.scrollTo({
          top: 0,
          behavior: 'instant'
        });
      }
    },
  },
  setup() {
    const logoSrc = ref('/api/placeholder/100/100');
    const servers = ref(Array(5).fill(null));

    const features = ref([
      {
        icon: 'PerformanceIcon',
        title: '高性能算力',
        description: '提供GPU/CPU灵活配置，满足AI训练、渲染等高算力需求'
      },
      {
        icon: 'am-icon-shield',
        title: '安全可靠',
        description: '数据加密传输，多重备份，确保您的业务安全稳定运行'
      }
    ]);


    return {
      logoSrc,
      servers,
      features
    };
  }
});
</script>

<style scoped>
.login-left-side {
  position: relative;
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #025af7 0%, #2196f3 100%);
  color: white;
  padding: 40px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  overflow: hidden;
}

/* Logo样式 */
.logo-container {
  margin-top: -60px;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  margin-left: -40px;
  z-index: 10;
}

.logo {
  width: 180px;
  height: 140px;
  border-radius: 12px;
  margin-right: 15px;
}

/* 主视觉元素 */
.visual-element {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  perspective: 1000px;
  z-index: 5;
}

.server-illustration {
  display: flex;
  flex-direction: column;
  gap: 15px;
  transform: rotateY(25deg) rotateX(10deg);
  transform-style: preserve-3d;
}

.server-unit {
  width: 200px;
  height: 30px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  position: relative;
  backdrop-filter: blur(5px);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.2);
  animation: pulse 2s infinite;
}

.server-light {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: rgba(231, 12, 12, 0);
  box-shadow: 0 0 10px #ffffff;
  animation: blink 1.5s infinite;
}

.connections {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.connection-line {
  position: absolute;
  height: 2px;
  width: 100px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.7), transparent);
  top: calc(30% + (40% * Math.random()));
  left: calc(10% + (50% * Math.random()));
  animation: move 4s infinite linear;
  transform: rotate(calc(-30deg + (60deg * Math.random())));
  opacity: 0.6;
}

/* 特点介绍 */
.features {
  margin-top: 40px;
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  z-index: 10;
}

.feature-item {
  flex: 1;
  min-width: 250px;
  display: flex;
  align-items: flex-start;
  padding: 15px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: transform 0.3s, box-shadow 0.3s;
}

.feature-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

.feature-icon {
  margin-right: 15px;
  color: white;
  background: rgba(255, 255, 255, 0.2);
  padding: 10px;
  border-radius: 10px;
  height: 44px;
  width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-icon svg {
  width: 24px;
  height: 24px;
}

.feature-text h3 {
  margin: 0 0 5px 0;
  font-size: 18px;
}

.feature-text p {
  margin: 0;
  font-size: 14px;
  opacity: 0.8;
  line-height: 1.4;
}

/* 底部文案 */
.bottom-text {
  margin-top: -20px;
  text-align: center;
  z-index: 10;
}

.slogan {
  font-size: 28px;
  margin: 0 0 10px 0;
  background: whitesmoke;
  /*background: linear-gradient(to right, #ffffff, #2196f3);*/
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: bold;
}

.sub-slogan {
  font-size: 16px;
  opacity: 0.9;
  color: whitesmoke;
  margin: 0;
}

/* 背景元素 */
.background-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.floating-particle {
  position: absolute;
  width: 6px;
  height: 6px;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  animation: float 10s infinite linear;
}

/* 动画 */
@keyframes pulse {
  0% { box-shadow: 0 0 5px rgba(255, 255, 255, 0.3); }
  50% { box-shadow: 0 0 15px rgba(255, 255, 255, 0.5); }
  100% { box-shadow: 0 0 5px rgba(255, 255, 255, 0.3); }
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes move {
  0% { transform: translateX(-50px) rotate(var(--rotation, -20deg)); opacity: 0; }
  50% { opacity: 0.8; }
  100% { transform: translateX(150px) rotate(var(--rotation, -20deg)); opacity: 0; }
}

@keyframes float {
  0% { transform: translate(0, 0); opacity: 0; }
  25% { opacity: 0.8; }
  50% { transform: translate(10px, 10px); }
  75% { opacity: 0.4; }
  100% { transform: translate(0, 0); opacity: 0; }
}
</style>