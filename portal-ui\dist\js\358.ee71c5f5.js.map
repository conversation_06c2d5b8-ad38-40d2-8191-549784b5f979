{"version": 3, "file": "js/358.ee71c5f5.js", "mappings": "iJAAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAUF,EAAIG,MAAMC,YAAY,OAAOF,EAAG,MAAM,CAACG,YAAY,mBAAmB,CAACH,EAAG,MAAM,CAACG,YAAY,kBAAkB,CAACH,EAAG,IAAI,CAACG,YAAY,YAAYC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOP,EAAIQ,WAAW,SAAS,IAAI,CAACN,EAAG,MAAM,CAACG,YAAY,OAAOI,MAAM,CAAC,IAAMC,EAAQ,MAAkC,IAAM,cAAcV,EAAIW,GAAG,GAAGT,EAAG,MAAM,CAACG,YAAY,kBAAkB,CAACH,EAAG,MAAM,CAACG,YAAY,uBAAuBL,EAAIY,GAAIZ,EAAIa,SAAS,SAASC,EAAOC,GAAO,OAAOb,EAAG,MAAM,CAACc,IAAID,EAAMV,YAAY,cAAcY,MAAO,CACrjBC,eAA2B,GAARH,EAAF,IACjBI,UAAY,cAAqB,EAARJ,SACvB,CAACb,EAAG,MAAM,CAACG,YAAY,kBAAkB,IAAG,GAAGH,EAAG,MAAM,CAACG,YAAY,kBAAkBH,EAAG,MAAM,CAACG,YAAY,YAAYL,EAAIY,GAAIZ,EAAIoB,UAAU,SAASC,EAAQN,GAAO,OAAOb,EAAG,MAAM,CAACc,IAAID,EAAMV,YAAY,gBAAgB,CAACH,EAAG,MAAM,CAACG,YAAY,gBAAgB,CAACH,EAAG,KAAK,CAACF,EAAIsB,GAAGtB,EAAIuB,GAAGF,EAAQG,UAAUtB,EAAG,IAAI,CAACF,EAAIsB,GAAGtB,EAAIuB,GAAGF,EAAQI,mBAAmB,IAAG,GAAGvB,EAAG,MAAM,CAACG,YAAY,uBAAuBL,EAAIY,GAAI,IAAI,SAASc,GAAG,OAAOxB,EAAG,MAAM,CAACc,IAAIU,EAAErB,YAAY,oBAAoBY,MAAO,CAC7eU,KAAyB,IAAhBC,KAAKC,SAAP,IACPC,IAAwB,IAAhBF,KAAKC,SAAP,IACNE,kBAAsB,EAAoB,GAAhBH,KAAKC,SAAX,IACpBX,eAAmC,EAAhBU,KAAKC,SAAP,MACf,IAAG,IAClB,EACIG,EAAkB,CAAC,WAAY,IAAIhC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAUF,EAAIG,MAAMC,YAAY,OAAOF,EAAG,MAAM,CAACG,YAAY,eAAe,CAACH,EAAG,KAAK,CAACG,YAAY,UAAU,CAACL,EAAIsB,GAAG,iBAAiBpB,EAAG,IAAI,CAACG,YAAY,cAAc,CAACL,EAAIsB,GAAG,2BAC5O,G,mBCsDA,MAAAW,EAAA,CACAC,SAAA,gkBAeAC,EAAA,CACAD,SAAA,8bAUAE,EAAA,CACAF,SAAA,oRAOA,OAAAG,EAAAA,EAAAA,IAAA,CACAC,KAAA,kBACAC,WAAA,CACAN,kBACAE,aACAC,cAEAI,QAAA,CACAhC,WAAAiC,GAEA,KAAAC,aAAA,KAAAA,cAAAD,GACA,KAAAE,mBAAA,KAAAD,YAGA,KAAAE,WAAA,KACA,MAAAC,EAAAC,SAAAC,iBAAA,yBACAF,EAAAG,SAAAC,KACAA,EAAAC,UAAAC,SAAA,WACA,WAAAV,GAAAQ,EAAAC,UAAAC,SAAA,gBACAF,EAAAC,UAAAC,SAAA,iBACAF,EAAAC,UAAAE,IAAA,eAGAC,YAAA,KACAJ,EAAAC,UAAAI,OAAA,iBACA,KACA,IAIA,KAAAZ,YAAAD,CAAA,KAGA,KAAAC,YAAAD,EAIA,KAAAc,OAAAd,OAAAA,EACA,KAAAG,WAAA,KACAY,OAAAC,SAAA,CACA3B,IAAA,EACA4B,SAAA,YAEA,KAAAC,QAAAC,GAAA,OAIA,KAAAD,QAAAE,KAAApB,GACAe,OAAAC,SAAA,CACA3B,IAAA,EACA4B,SAAA,YAGA,GAEAI,QACA,MAAAC,GAAAC,EAAAA,EAAAA,IAAA,4BACAnD,GAAAmD,EAAAA,EAAAA,IAAAC,MAAA,GAAAC,KAAA,OAEA9C,GAAA4C,EAAAA,EAAAA,IAAA,CACA,CACAG,KAAA,kBACA3C,MAAA,QACAC,YAAA,iCAEA,CACA0C,KAAA,iBACA3C,MAAA,OACAC,YAAA,8BAKA,OACAsC,UACAlD,UACAO,WAEA,IClLwQ,I,UCQpQgD,GAAY,OACd,EACArE,EACAiC,GACA,EACA,KACA,WACA,MAIF,EAAeoC,EAAiB,O,gFCnBhC,IAAIrE,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACG,YAAY,cAAc,CAAEL,EAAIqE,iBAAkBnE,EAAG,oBAAoB,CAACO,MAAM,CAAC,QAAUT,EAAIsE,oBAAoB,KAAOtE,EAAIuE,iBAAiB,SAAW,IAAK,UAAYvE,EAAIwE,WAAWlE,GAAG,CAAC,MAAQ,SAASC,GAAQP,EAAIqE,kBAAmB,CAAK,KAAKrE,EAAIyE,KAAKvE,EAAG,MAAM,CAACG,YAAY,aAAa,CAACH,EAAG,oBAAoB,GAAGA,EAAG,MAAM,CAACG,YAAY,cAAc,CAACH,EAAG,MAAM,CAACG,YAAY,wBAAwB,CAACH,EAAG,KAAK,CAACF,EAAIsB,GAAG,eAAepB,EAAG,MAAM,CAACG,YAAY,cAAc,CAACH,EAAG,MAAM,CAACwE,MAAM,CAAC,WAA8B,UAAlB1E,EAAI2E,UAAwB,SAAW,IAAIrE,GAAG,CAAC,MAAQ,SAASC,GAAQP,EAAI2E,UAAY,OAAO,IAAI,CAAC3E,EAAIsB,GAAG,aAAapB,EAAG,MAAM,CAACwE,MAAM,CAAC,WAA8B,YAAlB1E,EAAI2E,UAA0B,SAAW,IAAIrE,GAAG,CAAC,MAAQ,SAASC,GAAQP,EAAI2E,UAAY,SAAS,IAAI,CAAC3E,EAAIsB,GAAG,cAAcpB,EAAG,MAAM,CAACG,YAAY,kBAAkB,CAAoB,UAAlBL,EAAI2E,UAAuBzE,EAAG,MAAM,CAACG,YAAY,cAAc,CAACH,EAAG,IAAI,CAACG,YAAY,cAAcH,EAAG,MAAM,CAACG,YAAY,cAAcqE,MAAM,CAAE,MAAS1E,EAAI4E,OAAOC,QAAS,CAAC3E,EAAG,QAAQ,CAAC4E,WAAW,CAAC,CAACxC,KAAK,QAAQyC,QAAQ,UAAUC,MAAOhF,EAAIiF,UAAUJ,MAAOK,WAAW,oBAAoBzE,MAAM,CAAC,KAAO,OAAO,YAAc,UAAU0E,SAAS,CAAC,MAASnF,EAAIiF,UAAUJ,OAAQvE,GAAG,CAAC,KAAON,EAAIoF,cAAc,MAAQ,SAAS7E,GAAWA,EAAO8E,OAAOC,WAAiBtF,EAAIuF,KAAKvF,EAAIiF,UAAW,QAAS1E,EAAO8E,OAAOL,MAAM,KAAK9E,EAAG,MAAM,CAACG,YAAY,mBAAmB,CAAEL,EAAI4E,OAAOC,MAAO3E,EAAG,MAAM,CAACG,YAAY,iBAAiB,CAACL,EAAIsB,GAAGtB,EAAIuB,GAAGvB,EAAI4E,OAAOC,UAAU7E,EAAIyE,SAASvE,EAAG,MAAM,CAACG,YAAY,gCAAgCqE,MAAM,CAAE,MAAS1E,EAAI4E,OAAOY,OAAQ,CAACtF,EAAG,MAAM,CAACG,YAAY,wBAAwB,CAACH,EAAG,QAAQ,CAAC4E,WAAW,CAAC,CAACxC,KAAK,QAAQyC,QAAQ,UAAUC,MAAOhF,EAAIiF,UAAUO,KAAMN,WAAW,mBAAmBzE,MAAM,CAAC,KAAO,OAAO,YAAc,UAAU0E,SAAS,CAAC,MAASnF,EAAIiF,UAAUO,MAAOlF,GAAG,CAAC,KAAON,EAAIyF,kBAAkB,MAAQ,SAASlF,GAAWA,EAAO8E,OAAOC,WAAiBtF,EAAIuF,KAAKvF,EAAIiF,UAAW,OAAQ1E,EAAO8E,OAAOL,MAAM,KAAK9E,EAAG,SAAS,CAACG,YAAY,sBAAsBI,MAAM,CAAC,UAAYT,EAAIiF,UAAUJ,OAAS7E,EAAI4E,OAAOC,OAAS7E,EAAI0F,UAAUpF,GAAG,CAAC,MAAQN,EAAI2F,sBAAsB,CAAC3F,EAAIsB,GAAG,IAAItB,EAAIuB,GAAGvB,EAAI0F,SAAY,GAAE1F,EAAI4F,gBAAkB,SAAS,SAAS1F,EAAG,MAAM,CAACG,YAAY,mBAAmB,CAAEL,EAAI4E,OAAOY,KAAMtF,EAAG,MAAM,CAACG,YAAY,iBAAiB,CAACL,EAAIsB,GAAGtB,EAAIuB,GAAGvB,EAAI4E,OAAOY,SAASxF,EAAIyE,SAASvE,EAAG,MAAM,CAACG,YAAY,kBAAkB,CAACL,EAAIsB,GAAG,qBAAqBpB,EAAG,cAAc,CAACO,MAAM,CAAC,GAAK,yBAAyB,CAACT,EAAIsB,GAAG,UAAUtB,EAAIsB,GAAG,MAAMpB,EAAG,cAAc,CAACO,MAAM,CAAC,GAAK,yBAAyB,CAACT,EAAIsB,GAAG,WAAW,GAAGpB,EAAG,SAAS,CAACG,YAAY,YAAYI,MAAM,CAAC,UAAYT,EAAIiF,UAAUJ,QAAU7E,EAAIiF,UAAUO,MAAMlF,GAAG,CAAC,MAAQN,EAAI6F,aAAa,CAAC7F,EAAIsB,GAAG,UAAUpB,EAAG,MAAM,CAACG,YAAY,cAAc,CAACH,EAAG,IAAI,CAACO,MAAM,CAAC,KAAO,KAAKH,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOP,EAAIQ,WAAW,YAAY,IAAI,CAACR,EAAIsB,GAAG,UAAUpB,EAAG,OAAO,CAACG,YAAY,WAAW,CAACL,EAAIsB,GAAG,OAAOpB,EAAG,IAAI,CAACO,MAAM,CAAC,KAAO,KAAKH,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOP,EAAIQ,WAAW,cAAc,IAAI,CAACR,EAAIsB,GAAG,cAActB,EAAIyE,KAAwB,YAAlBzE,EAAI2E,UAAyBzE,EAAG,MAAM,CAACG,YAAY,cAAc,CAACH,EAAG,IAAI,CAACG,YAAY,aAAa,CAACL,EAAIsB,GAAG,eAAepB,EAAG,MAAM,CAACG,YAAY,cAAcqE,MAAM,CAAE,MAAS1E,EAAI4E,OAAOkB,WAAY,CAAC5F,EAAG,QAAQ,CAAC4E,WAAW,CAAC,CAACxC,KAAK,QAAQyC,QAAQ,UAAUC,MAAOhF,EAAI+F,YAAYD,SAAUZ,WAAW,yBAAyBzE,MAAM,CAAC,KAAO,OAAO,YAAc,WAAW0E,SAAS,CAAC,MAASnF,EAAI+F,YAAYD,UAAWxF,GAAG,CAAC,KAAON,EAAIgG,iBAAiB,MAAQ,SAASzF,GAAWA,EAAO8E,OAAOC,WAAiBtF,EAAIuF,KAAKvF,EAAI+F,YAAa,WAAYxF,EAAO8E,OAAOL,MAAM,KAAK9E,EAAG,MAAM,CAACG,YAAY,mBAAmB,CAAEL,EAAI4E,OAAOkB,SAAU5F,EAAG,MAAM,CAACG,YAAY,iBAAiB,CAACL,EAAIsB,GAAGtB,EAAIuB,GAAGvB,EAAI4E,OAAOkB,aAAa9F,EAAIyE,SAASvE,EAAG,MAAM,CAACG,YAAY,cAAcqE,MAAM,CAAE,MAAS1E,EAAI4E,OAAOqB,WAAY,CAAC/F,EAAG,MAAM,CAACG,YAAY,4BAA4B,CAAgD,cAA7CL,EAAIkG,gBAAkB,OAAS,YAA0BhG,EAAG,QAAQ,CAAC4E,WAAW,CAAC,CAACxC,KAAK,QAAQyC,QAAQ,UAAUC,MAAOhF,EAAI+F,YAAYE,SAAUf,WAAW,yBAAyBzE,MAAM,CAAC,YAAc,UAAU,KAAO,YAAY0E,SAAS,CAAC,QAAUlB,MAAMkC,QAAQnG,EAAI+F,YAAYE,UAAUjG,EAAIoG,GAAGpG,EAAI+F,YAAYE,SAAS,OAAO,EAAGjG,EAAI+F,YAAYE,UAAW3F,GAAG,CAAC,KAAON,EAAIqG,iBAAiB,OAAS,SAAS9F,GAAQ,IAAI+F,EAAItG,EAAI+F,YAAYE,SAASM,EAAKhG,EAAO8E,OAAOmB,IAAID,EAAKE,QAAuB,GAAGxC,MAAMkC,QAAQG,GAAK,CAAC,IAAII,EAAI,KAAKC,EAAI3G,EAAIoG,GAAGE,EAAII,GAAQH,EAAKE,QAASE,EAAI,GAAI3G,EAAIuF,KAAKvF,EAAI+F,YAAa,WAAYO,EAAIM,OAAO,CAACF,KAAaC,GAAK,GAAI3G,EAAIuF,KAAKvF,EAAI+F,YAAa,WAAYO,EAAIO,MAAM,EAAEF,GAAKC,OAAON,EAAIO,MAAMF,EAAI,IAAM,MAAM3G,EAAIuF,KAAKvF,EAAI+F,YAAa,WAAYS,EAAK,KAAoD,WAA7CxG,EAAIkG,gBAAkB,OAAS,YAAuBhG,EAAG,QAAQ,CAAC4E,WAAW,CAAC,CAACxC,KAAK,QAAQyC,QAAQ,UAAUC,MAAOhF,EAAI+F,YAAYE,SAAUf,WAAW,yBAAyBzE,MAAM,CAAC,YAAc,UAAU,KAAO,SAAS0E,SAAS,CAAC,QAAUnF,EAAI8G,GAAG9G,EAAI+F,YAAYE,SAAS,OAAO3F,GAAG,CAAC,KAAON,EAAIqG,iBAAiB,OAAS,SAAS9F,GAAQ,OAAOP,EAAIuF,KAAKvF,EAAI+F,YAAa,WAAY,KAAK,KAAK7F,EAAG,QAAQ,CAAC4E,WAAW,CAAC,CAACxC,KAAK,QAAQyC,QAAQ,UAAUC,MAAOhF,EAAI+F,YAAYE,SAAUf,WAAW,yBAAyBzE,MAAM,CAAC,YAAc,UAAU,KAAOT,EAAIkG,gBAAkB,OAAS,YAAYf,SAAS,CAAC,MAASnF,EAAI+F,YAAYE,UAAW3F,GAAG,CAAC,KAAON,EAAIqG,iBAAiB,MAAQ,SAAS9F,GAAWA,EAAO8E,OAAOC,WAAiBtF,EAAIuF,KAAKvF,EAAI+F,YAAa,WAAYxF,EAAO8E,OAAOL,MAAM,KAAK9E,EAAG,OAAO,CAACG,YAAY,kBAAkBC,GAAG,CAAC,MAAQN,EAAI+G,2BAA2B,CAAC7G,EAAG,IAAI,CAACwE,MAAM,CAAC,WAAY1E,EAAIkG,gBAAkB,UAAY,UAAUhG,EAAG,MAAM,CAACG,YAAY,mBAAmB,CAAEL,EAAI4E,OAAOqB,SAAU/F,EAAG,MAAM,CAACG,YAAY,iBAAiB,CAACL,EAAIsB,GAAGtB,EAAIuB,GAAGvB,EAAI4E,OAAOqB,aAAajG,EAAIyE,SAASvE,EAAG,MAAM,CAACG,YAAY,kBAAkB,CAACL,EAAIsB,GAAG,qBAAqBpB,EAAG,cAAc,CAACO,MAAM,CAAC,GAAK,yBAAyB,CAACT,EAAIsB,GAAG,UAAUtB,EAAIsB,GAAG,MAAMpB,EAAG,cAAc,CAACO,MAAM,CAAC,GAAK,yBAAyB,CAACT,EAAIsB,GAAG,WAAW,GAAGpB,EAAG,SAAS,CAACG,YAAY,YAAYI,MAAM,CAAC,UAAYT,EAAI+F,YAAYD,WAAa9F,EAAI+F,YAAYE,UAAU3F,GAAG,CAAC,MAAQN,EAAIgH,eAAe,CAAChH,EAAIsB,GAAG,UAAUpB,EAAG,MAAM,CAACG,YAAY,cAAc,CAACH,EAAG,IAAI,CAACO,MAAM,CAAC,KAAO,KAAKH,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOP,EAAIQ,WAAW,YAAY,IAAI,CAACR,EAAIsB,GAAG,UAAUpB,EAAG,OAAO,CAACG,YAAY,WAAW,CAACL,EAAIsB,GAAG,OAAOpB,EAAG,IAAI,CAACO,MAAM,CAAC,KAAO,KAAKH,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOP,EAAIQ,WAAW,cAAc,IAAI,CAACR,EAAIsB,GAAG,cAActB,EAAIyE,YAAY,EACh+M,EACIzC,EAAkB,G,4FCuKtB,GACAM,KAAA,QACAC,WAAA,CACA0E,kBAAA,IACAC,gBAAAA,EAAAA,GAEAC,OACA,OACAxC,UAAA,QACAM,UAAA,CACAJ,MAAA,GACAW,KAAA,IAEAO,YAAA,CACAD,SAAA,GACAG,SAAA,IAEAC,iBAAA,EACAtB,OAAA,CACAC,MAAA,GACAW,KAAA,GACAM,SAAA,GACAG,SAAA,IAEAP,UAAA,EACAE,UAAA,GACAwB,MAAA,KACA/C,kBAAA,EACAC,oBAAA,GACAC,iBAAA,UACAC,UAAA,OAEA,EACA6C,UAEA,KAAAC,sBACA,KAAAC,MAAA,eACA,EACAC,YACA,KAAAD,MAAA,kBACA,EACA/E,QAAA,CACAiF,wBAAAC,EAAAC,EAAA,QACA,KAAArD,oBAAAoD,EACA,KAAAnD,iBAAAoD,EACA,KAAAtD,kBAAA,CACA,EAEA,wBACA,IACA,KAAAuD,MAAA,KAEA,MAAAC,QAAAC,OAAAC,OAAAC,YACA,CACA1F,KAAA,WACA2F,cAAA,KACAC,eAAA,IAAAC,WAAA,SACAC,KAAA,CAAA9F,KAAA,aAEA,EACA,uBAIA+F,QAAAP,OAAAC,OAAAO,UAAA,OAAAT,EAAAU,WACAC,EAAAC,KAAAC,OAAAC,gBAAA,IAAAR,WAAAE,KAGAO,QAAAd,OAAAC,OAAAO,UAAA,QAAAT,EAAAgB,YACAC,EAAAL,KAAAC,OAAAC,gBAAA,IAAAR,WAAAS,KAGAG,EAAAA,EAAAA,IAAA,cAAAP,GACAO,EAAAA,EAAAA,IAAA,eAAAD,EACA,OAAAE,GACA,KAAApB,MAAA,6BACA,SACA,KAAAqB,WAAA,CACA,CACA,EAEA3B,sBAEA,MAAA4B,EAAAC,aAAAC,QAAA,qBACAC,EAAAF,aAAAC,QAAA,0BAEA,GAAAF,GAAAG,EAAA,CACA,MAAAC,GAAA,IAAAC,MAAAC,UACAC,EAAA7H,KAAA8H,MAAAC,SAAAN,GAAAC,GAAA,KAGAG,EAAA,GACA,KAAA/D,UAAA,EACA,KAAAE,UAAA6D,EACA,KAAAG,iBAGA,KAAA3E,UAAAJ,QAAAqE,IACA,KAAAxD,UAAA,IAIA,KAAAmE,uBAEA,CACA,EAEAA,wBACAV,aAAAW,WAAA,qBACAX,aAAAW,WAAA,0BACA,KAAApE,UAAA,EACA,KAAAE,UAAA,GACA,KAAAwB,QACA2C,cAAA,KAAA3C,OACA,KAAAA,MAAA,KAEA,EAEAhC,gBACA,MAAA4E,EAAA,gBACA,QAAA/E,UAAAJ,MAEA,GAAAmF,EAAAC,KAAA,KAAAhF,UAAAJ,OAEA,CACA,KAAAD,OAAAC,MAAA,GAGA,MAAAqE,EAAAC,aAAAC,QAAA,qBACAC,EAAAF,aAAAC,QAAA,0BAEA,GAAAF,IAAA,KAAAjE,UAAAJ,OAAAwE,EAAA,CACA,MAAAC,GAAA,IAAAC,MAAAC,UACAC,EAAA7H,KAAA8H,MAAAC,SAAAN,GAAAC,GAAA,KAEAG,EAAA,IACA,KAAA/D,UAAA,EACA,KAAAE,UAAA6D,EAEA,KAAArC,OACA,KAAAwC,iBAGA,CACA,MArBA,KAAAhF,OAAAC,MAAA,iBAFA,KAAAD,OAAAC,MAAA,QAwBA,EAEA,0BAKA,OAHA,KAAAD,OAAAY,KAAA,GAGA,KAAAP,UAAAO,KAIA,SAAAP,UAAAO,KAAA0E,QAAA,QAAAD,KAAA,KAAAhF,UAAAO,WAAA,GACA,KAAAZ,OAAAY,KAAA,cACA,IALA,KAAAZ,OAAAY,KAAA,UACA,EAMA,EACAoE,iBAEA,KAAAxC,OACA2C,cAAA,KAAA3C,OAIA,KAAAA,MAAA+C,aAAA,KACA,QAAAvE,WAAA,EACAmE,cAAA,KAAA3C,OACA,KAAAA,MAAA,KACA,KAAA1B,UAAA,EACA,KAAAE,UAAA,GAEA,KAAAiE,4BACA,CACA,KAAAjE,YAEA,MAAAyD,GAAA,IAAAE,MAAAC,UAAA,SAAA5D,UACAuD,aAAAiB,QAAA,yBAAAf,EAAAgB,WACA,IACA,IACA,EACA7J,WAAAiC,GAEA,KAAAc,OAAAd,OAAAA,EAEA,KAAAG,WAAA,KACAY,OAAAC,SAAA,CACA3B,IAAA,EACA4B,SAAA,YAEA,KAAAC,QAAAC,GAAA,OAIA,KAAAD,QAAAE,KAAApB,GACAe,OAAAC,SAAA,CACA3B,IAAA,EACA4B,SAAA,aAGA,KAAAhB,YAAAD,CACA,EAEAuD,mBACA,MAAAgE,EAAA,gBACA,KAAAjE,YAAAD,SAEAkE,EAAAC,KAAA,KAAAlE,YAAAD,UAGA,KAAAlB,OAAAkB,SAAA,GAFA,KAAAlB,OAAAkB,SAAA,aAFA,KAAAlB,OAAAkB,SAAA,SAMA,EAEAO,mBACA,KAAAN,YAAAE,SAEA,KAAAF,YAAAE,SAAAiE,OAAA,EACA,KAAAtF,OAAAqB,SAAA,YAEA,KAAArB,OAAAqB,SAAA,GAJA,KAAArB,OAAAqB,SAAA,SAMA,EAGAN,sBAEA,KAAAP,gBACA,KAAAR,OAAAC,QAEA,KAAAa,UAAA,EACA,KAAA4E,eAAA,GAEAC,EAAAA,EAAAA,IAAA,kBAAA1F,MAAA,KAAAI,UAAAJ,QAAA2F,MAAAC,IACA,SAAAA,EAAAtD,KAAA3B,KAAA,CAEA,MAAA8D,GAAA,IAAAC,MAAAC,UACAH,EAAAC,EAAA,IAEAH,aAAAiB,QAAA,yBAAAnF,UAAAJ,OACAsE,aAAAiB,QAAA,yBAAAf,EAAAgB,YAEA,KAAA5C,wBAAA,kCACA,KAAAmC,gBACA,MACA,KAAAhF,OAAAY,KAAAiF,EAAAtD,KAAAuD,KAAA,UACA,KAAAhF,UAAA,CAEA,IAEA,EACAiF,eAKA,GAHA,KAAA/F,OAAAY,KAAA,IAGA,KAAAP,UAAAO,KAEA,OADA,KAAAZ,OAAAY,KAAA,UACA,EAEA,YAAAP,UAAAO,KAAA0E,SAAA,QAAAD,KAAA,KAAAhF,UAAAO,MAEA,OADA,KAAAZ,OAAAY,KAAA,cACA,EAGA,IA4BA,OA1BAoF,EAAAA,EAAAA,IAAA,oBACA/F,MAAA,KAAAI,UAAAJ,MACAW,KAAA,KAAAP,UAAAO,OACAgF,MAAAC,GACA,KAAAA,EAAAtD,KAAA3B,OACA+E,EAAAA,EAAAA,IAAA,mBACA1F,MAAA,KAAAI,UAAAJ,MACAW,KAAA,KAAAP,UAAAO,OACAgF,MAAAC,IACA,QAAAA,EAAAtD,KAAA3B,MACAqF,EAAAA,EAAAA,IAAAJ,EAAAtD,KAAA2D,OACA,KAAAvD,MAAA,kBACA,KAAA5D,QAAAE,KAAA,eACA,SAAA4G,EAAAtD,KAAA3B,KAGA,OAFA,KAAAZ,OAAAY,KAAAiF,EAAAtD,KAAAuD,KAAA,SAEA,CACA,KAEA,GACA,MAAAD,EAAAtD,KAAA3B,MACA,KAAAZ,OAAAY,KAAAiF,EAAAtD,KAAAuD,KAAA,SAEA,QAHA,KAMA,CACA,OAAA9C,GAGA,OAFA,KAAAhD,OAAAY,KAAA,cAEA,CACA,CACA,EAEAK,aAGA,GAFA,KAAAT,gBACA,KAAAuF,eACA,KAAA/F,OAAAC,OAAA,KAAAD,OAAAY,KAAA,CAEA,KAAAZ,OAAAC,OAAA,KAAAD,OAAAY,IAGA,MACA,KAAAuF,kBACA,KAAAxD,MAAA,iBACA,EACAP,eAKA,GAJA,KAAAhB,mBACA,KAAAK,mBAGA,KAAAzB,OAAAkB,UAAA,KAAAlB,OAAAqB,SAAA,CACA,KAAArB,OAAAkB,UAAA,KAAAlB,OAAAqB,SAEA,MACA,CAGA,MAAA+E,EAAA,KAAAC,SAAA,KAAAA,SAAA,CACAC,MAAA,EACAC,KAAA,SACAC,QAAA,oBACA,MAGAb,EAAAA,EAAAA,IAAA,mBAAAxE,aACAyE,MAAAC,IAEAO,GAAAA,EAAAK,QAEAZ,EAAAtD,MAAA,KAAAsD,EAAAtD,KAAA3B,OAEAqF,EAAAA,EAAAA,IAAAJ,EAAAtD,KAAA2D,OACA,KAAArD,wBAAA,kBACA,KAAAF,MAAA,kBACA,KAAA5D,QAAAE,KAAA,UACA,KAAAkH,mBAGA,KAAAnG,OAAAqB,SAAAwE,EAAAtD,KAAAuD,GAEA,IAEAY,OAAA1D,IAEAoD,GAAAA,EAAAK,QAGA,KAAAzG,OAAAqB,SAAA,eAGA,EAEAc,2BACA,KAAAb,iBAAA,KAAAA,eACA,GAEAqF,gBACA,KAAAnE,OACA2C,cAAA,KAAA3C,OAEA,KAAAG,MAAA,eACA,GC7hB8P,I,UCQ1PnD,GAAY,OACd,EACArE,EACAiC,GACA,EACA,KACA,WACA,MAIF,EAAeoC,EAAiB,O,uBCnBhC,IAAIoH,EAAa,EAAQ,KAErBC,EAAU/C,OACVgD,EAAaC,UAEjBC,EAAOC,QAAU,SAAUC,GACzB,GAAuB,iBAAZA,GAAwBN,EAAWM,GAAW,OAAOA,EAChE,MAAMJ,EAAW,aAAeD,EAAQK,GAAY,kBACtD,C,uBCRA,IAAIC,EAAgB,EAAQ,MAExBL,EAAaC,UAEjBC,EAAOC,QAAU,SAAUG,EAAIC,GAC7B,GAAIF,EAAcE,EAAWD,GAAK,OAAOA,EACzC,MAAMN,EAAW,uBACnB,C,mBCNAE,EAAOC,QAAgC,oBAAfK,aAAiD,oBAAZC,Q,mCCA7D,IAmCIC,EAAMC,EAAaJ,EAnCnBK,EAAsB,EAAQ,MAC9BC,EAAc,EAAQ,MACtBC,EAAS,EAAQ,MACjBhB,EAAa,EAAQ,KACrBiB,EAAW,EAAQ,KACnBC,EAAS,EAAQ,MACjBC,EAAU,EAAQ,KAClBC,EAAc,EAAQ,MACtBC,EAA8B,EAAQ,MACtCC,EAAgB,EAAQ,MACxBC,EAAwB,EAAQ,MAChChB,EAAgB,EAAQ,MACxBiB,EAAiB,EAAQ,MACzBC,EAAiB,EAAQ,MACzBC,EAAkB,EAAQ,MAC1BC,EAAM,EAAQ,MACdC,EAAsB,EAAQ,MAE9BC,EAAuBD,EAAoBE,QAC3CC,EAAmBH,EAAoBI,IACvCC,EAAYjB,EAAOiB,UACnBC,EAAqBD,GAAaA,EAAUE,UAC5CC,EAAoBpB,EAAOoB,kBAC3BC,EAA6BD,GAAqBA,EAAkBD,UACpEG,EAAaL,GAAaT,EAAeS,GACzCM,EAAsBL,GAAsBV,EAAeU,GAC3DM,EAAkBC,OAAON,UACzBhC,EAAYa,EAAOb,UAEnBuC,EAAgBhB,EAAgB,eAChCiB,EAAkBhB,EAAI,mBACtBiB,EAA0B,wBAE1BC,EAA4B/B,KAAyBW,GAA4C,UAA1BN,EAAQH,EAAO8B,OACtFC,GAA2B,EAG3BC,EAA6B,CAC/Bf,UAAW,EACXtF,WAAY,EACZyF,kBAAmB,EACnBa,WAAY,EACZC,YAAa,EACbC,WAAY,EACZC,YAAa,EACbC,aAAc,EACdC,aAAc,GAGZC,EAA8B,CAChCC,cAAe,EACfC,eAAgB,GAGdC,EAAS,SAAgBlD,GAC3B,IAAKS,EAAST,GAAK,OAAO,EAC1B,IAAImD,EAAQxC,EAAQX,GACpB,MAAiB,aAAVmD,GACFzC,EAAO8B,EAA4BW,IACnCzC,EAAOqC,EAA6BI,EAC3C,EAEIC,EAA2B,SAAUpD,GACvC,IAAIqD,EAAQrC,EAAehB,GAC3B,GAAKS,EAAS4C,GAAd,CACA,IAAIC,EAAQ/B,EAAiB8B,GAC7B,OAAQC,GAAS5C,EAAO4C,EAAOlB,GAA4BkB,EAAMlB,GAA2BgB,EAAyBC,EAFzF,CAG9B,EAEIE,EAAe,SAAUvD,GAC3B,IAAKS,EAAST,GAAK,OAAO,EAC1B,IAAImD,EAAQxC,EAAQX,GACpB,OAAOU,EAAO8B,EAA4BW,IACrCzC,EAAOqC,EAA6BI,EAC3C,EAEIK,EAAc,SAAUxD,GAC1B,GAAIuD,EAAavD,GAAK,OAAOA,EAC7B,MAAML,EAAU,8BAClB,EAEI8D,EAAyB,SAAUC,GACrC,GAAIlE,EAAWkE,MAAQzC,GAAkBlB,EAAc+B,EAAY4B,IAAK,OAAOA,EAC/E,MAAM/D,EAAUiB,EAAY8C,GAAK,oCACnC,EAEIC,EAAyB,SAAUC,EAAKC,EAAUC,EAAQC,GAC5D,GAAKxD,EAAL,CACA,GAAIuD,EAAQ,IAAK,IAAIE,KAASxB,EAA4B,CACxD,IAAIyB,EAAwBzD,EAAOwD,GACnC,GAAIC,GAAyBvD,EAAOuD,EAAsBtC,UAAWiC,GAAM,WAClEK,EAAsBtC,UAAUiC,EACzC,CAAE,MAAOhI,GAEP,IACEqI,EAAsBtC,UAAUiC,GAAOC,CACzC,CAAE,MAAOK,GAAsB,CACjC,CACF,CACKnC,EAAoB6B,KAAQE,GAC/BhD,EAAciB,EAAqB6B,EAAKE,EAASD,EAC7CxB,GAA6BX,EAAmBkC,IAAQC,EAAUE,EAdhD,CAgB1B,EAEII,EAA+B,SAAUP,EAAKC,EAAUC,GAC1D,IAAIE,EAAOC,EACX,GAAK1D,EAAL,CACA,GAAIU,EAAgB,CAClB,GAAI6C,EAAQ,IAAKE,KAASxB,EAExB,GADAyB,EAAwBzD,EAAOwD,GAC3BC,GAAyBvD,EAAOuD,EAAuBL,GAAM,WACxDK,EAAsBL,EAC/B,CAAE,MAAOhI,GAAqB,CAEhC,GAAKkG,EAAW8B,KAAQE,EAKjB,OAHL,IACE,OAAOhD,EAAcgB,EAAY8B,EAAKE,EAASD,EAAWxB,GAA6BP,EAAW8B,IAAQC,EAC5G,CAAE,MAAOjI,GAAqB,CAElC,CACA,IAAKoI,KAASxB,EACZyB,EAAwBzD,EAAOwD,IAC3BC,GAA2BA,EAAsBL,KAAQE,GAC3DhD,EAAcmD,EAAuBL,EAAKC,EAlBtB,CAqB1B,EAEA,IAAKzD,KAAQoC,EACXnC,EAAcG,EAAOJ,GACrBH,EAAYI,GAAeA,EAAYsB,UACnC1B,EAAWoB,EAAqBpB,GAAWmC,GAA2B/B,EACrEgC,GAA4B,EAGnC,IAAKjC,KAAQ2C,EACX1C,EAAcG,EAAOJ,GACrBH,EAAYI,GAAeA,EAAYsB,UACnC1B,IAAWoB,EAAqBpB,GAAWmC,GAA2B/B,GAI5E,KAAKgC,IAA8B7C,EAAWsC,IAAeA,IAAesC,SAASzC,aAEnFG,EAAa,WACX,MAAMnC,EAAU,uBAClB,EACI0C,GAA2B,IAAKjC,KAAQoC,EACtChC,EAAOJ,IAAOa,EAAeT,EAAOJ,GAAO0B,GAInD,KAAKO,IAA8BN,GAAuBA,IAAwBC,KAChFD,EAAsBD,EAAWH,UAC7BU,GAA2B,IAAKjC,KAAQoC,EACtChC,EAAOJ,IAAOa,EAAeT,EAAOJ,GAAMuB,UAAWI,GAS7D,GAJIM,GAA6BrB,EAAea,KAAgCE,GAC9Ed,EAAeY,EAA4BE,GAGzCxB,IAAgBG,EAAOqB,EAAqBG,GAQ9C,IAAK9B,KAPLmC,GAA2B,EAC3BxB,EAAsBgB,EAAqBG,EAAe,CACxDmC,cAAc,EACd7C,IAAK,WACH,OAAOf,EAASxM,MAAQA,KAAKkO,QAAmBmC,CAClD,IAEW9B,EAAgChC,EAAOJ,IAClDS,EAA4BL,EAAOJ,GAAO+B,EAAiB/B,GAI/DR,EAAOC,QAAU,CACfwC,0BAA2BA,EAC3BF,gBAAiBI,GAA4BJ,EAC7CqB,YAAaA,EACbC,uBAAwBA,EACxBE,uBAAwBA,EACxBQ,6BAA8BA,EAC9Bf,yBAA0BA,EAC1BF,OAAQA,EACRK,aAAcA,EACdzB,WAAYA,EACZC,oBAAqBA,E,uBC/LvB,IAAIwC,EAAoB,EAAQ,MAEhC3E,EAAOC,QAAU,SAAUQ,EAAamE,GACtC,IAAIzP,EAAQ,EACRmJ,EAASqG,EAAkBC,GAC3BC,EAAS,IAAIpE,EAAYnC,GAC7B,MAAOA,EAASnJ,EAAO0P,EAAO1P,GAASyP,EAAKzP,KAC5C,OAAO0P,CACT,C,uBCRA,IAAIF,EAAoB,EAAQ,MAIhC3E,EAAOC,QAAU,SAAU6E,EAAGhB,GAI5B,IAHA,IAAIiB,EAAMJ,EAAkBG,GACxBE,EAAI,IAAIlB,EAAEiB,GACVE,EAAI,EACDA,EAAIF,EAAKE,IAAKD,EAAEC,GAAKH,EAAEC,EAAME,EAAI,GACxC,OAAOD,CACT,C,uBCVA,IAAIL,EAAoB,EAAQ,MAC5BO,EAAsB,EAAQ,MAE9BC,EAAcC,WAIlBpF,EAAOC,QAAU,SAAU6E,EAAGhB,EAAG3O,EAAOiE,GACtC,IAAI2L,EAAMJ,EAAkBG,GACxBO,EAAgBH,EAAoB/P,GACpCmQ,EAAcD,EAAgB,EAAIN,EAAMM,EAAgBA,EAC5D,GAAIC,GAAeP,GAAOO,EAAc,EAAG,MAAMH,EAAY,mBAG7D,IAFA,IAAIH,EAAI,IAAIlB,EAAEiB,GACVE,EAAI,EACDA,EAAIF,EAAKE,IAAKD,EAAEC,GAAKA,IAAMK,EAAclM,EAAQ0L,EAAEG,GAC1D,OAAOD,CACT,C,sBChBA,IAAIO,EAAwB,EAAQ,MAChC3F,EAAa,EAAQ,KACrB4F,EAAa,EAAQ,MACrBlE,EAAkB,EAAQ,MAE1BgB,EAAgBhB,EAAgB,eAChCmE,EAAUpD,OAGVqD,EAAuE,aAAnDF,EAAW,WAAc,OAAOG,SAAW,CAAhC,IAG/BC,EAAS,SAAUxF,EAAIhL,GACzB,IACE,OAAOgL,EAAGhL,EACZ,CAAE,MAAO4G,GAAqB,CAChC,EAGAgE,EAAOC,QAAUsF,EAAwBC,EAAa,SAAUpF,GAC9D,IAAI0E,EAAGe,EAAKhB,EACZ,YAAcH,IAAPtE,EAAmB,YAAqB,OAAPA,EAAc,OAEO,iBAAjDyF,EAAMD,EAAOd,EAAIW,EAAQrF,GAAKkC,IAA8BuD,EAEpEH,EAAoBF,EAAWV,GAEH,WAA3BD,EAASW,EAAWV,KAAmBlF,EAAWkF,EAAEgB,QAAU,YAAcjB,CACnF,C,uBC5BA,IAAIkB,EAAQ,EAAQ,MAEpB/F,EAAOC,SAAW8F,GAAM,WACtB,SAASC,IAAkB,CAG3B,OAFAA,EAAEjE,UAAUkE,YAAc,KAEnB5D,OAAOjB,eAAe,IAAI4E,KAASA,EAAEjE,SAC9C,G,uBCPA,IAAImE,EAAc,EAAQ,MACtBC,EAAiB,EAAQ,MAE7BnG,EAAOC,QAAU,SAAUxG,EAAQ/C,EAAM0P,GAGvC,OAFIA,EAAWxE,KAAKsE,EAAYE,EAAWxE,IAAKlL,EAAM,CAAE2P,QAAQ,IAC5DD,EAAWE,KAAKJ,EAAYE,EAAWE,IAAK5P,EAAM,CAAE6P,QAAQ,IACzDJ,EAAeK,EAAE/M,EAAQ/C,EAAM0P,EACxC,C,mBCPApG,EAAOC,QAAU,CACfwG,eAAgB,CAAEC,EAAG,iBAAkBC,EAAG,EAAGC,EAAG,GAChDC,mBAAoB,CAAEH,EAAG,qBAAsBC,EAAG,EAAGC,EAAG,GACxDE,sBAAuB,CAAEJ,EAAG,wBAAyBC,EAAG,EAAGC,EAAG,GAC9DG,mBAAoB,CAAEL,EAAG,qBAAsBC,EAAG,EAAGC,EAAG,GACxDI,sBAAuB,CAAEN,EAAG,wBAAyBC,EAAG,EAAGC,EAAG,GAC9DK,mBAAoB,CAAEP,EAAG,sBAAuBC,EAAG,EAAGC,EAAG,GACzDM,2BAA4B,CAAER,EAAG,8BAA+BC,EAAG,EAAGC,EAAG,GACzEO,cAAe,CAAET,EAAG,gBAAiBC,EAAG,EAAGC,EAAG,GAC9CQ,kBAAmB,CAAEV,EAAG,oBAAqBC,EAAG,EAAGC,EAAG,GACtDS,oBAAqB,CAAEX,EAAG,sBAAuBC,EAAG,GAAIC,EAAG,GAC3DU,kBAAmB,CAAEZ,EAAG,oBAAqBC,EAAG,GAAIC,EAAG,GACvDW,YAAa,CAAEb,EAAG,aAAcC,EAAG,GAAIC,EAAG,GAC1CY,yBAA0B,CAAEd,EAAG,2BAA4BC,EAAG,GAAIC,EAAG,GACrEa,eAAgB,CAAEf,EAAG,gBAAiBC,EAAG,GAAIC,EAAG,GAChDc,mBAAoB,CAAEhB,EAAG,qBAAsBC,EAAG,GAAIC,EAAG,GACzDe,gBAAiB,CAAEjB,EAAG,iBAAkBC,EAAG,GAAIC,EAAG,GAClDgB,kBAAmB,CAAElB,EAAG,oBAAqBC,EAAG,GAAIC,EAAG,GACvDiB,cAAe,CAAEnB,EAAG,eAAgBC,EAAG,GAAIC,EAAG,GAC9CkB,aAAc,CAAEpB,EAAG,cAAeC,EAAG,GAAIC,EAAG,GAC5CmB,WAAY,CAAErB,EAAG,YAAaC,EAAG,GAAIC,EAAG,GACxCoB,iBAAkB,CAAEtB,EAAG,mBAAoBC,EAAG,GAAIC,EAAG,GACrDqB,mBAAoB,CAAEvB,EAAG,qBAAsBC,EAAG,GAAIC,EAAG,GACzDsB,aAAc,CAAExB,EAAG,cAAeC,EAAG,GAAIC,EAAG,GAC5CuB,qBAAsB,CAAEzB,EAAG,wBAAyBC,EAAG,GAAIC,EAAG,GAC9DwB,eAAgB,CAAE1B,EAAG,iBAAkBC,EAAG,GAAIC,EAAG,G,uBCzBnD,IAAIyB,EAAc,EAAQ,MAEtBC,EAASC,MACTC,EAAUH,EAAY,GAAGG,SAEzBC,EAAO,SAAWC,GAAO,OAAO5L,OAAOwL,EAAOI,GAAKC,MAAS,CAArD,CAAuD,UAE9DC,EAA2B,uBAC3BC,EAAwBD,EAAyBvK,KAAKoK,GAE1DzI,EAAOC,QAAU,SAAU0I,EAAOG,GAChC,GAAID,GAAyC,iBAATF,IAAsBL,EAAOS,kBAC/D,MAAOD,IAAeH,EAAQH,EAAQG,EAAOC,EAA0B,IACvE,OAAOD,CACX,C,uBCdA,IAAIN,EAAc,EAAQ,MACtBW,EAAY,EAAQ,MAExBhJ,EAAOC,QAAU,SAAUgJ,EAAQ7T,EAAK8T,GACtC,IAEE,OAAOb,EAAYW,EAAU3G,OAAO8G,yBAAyBF,EAAQ7T,GAAK8T,IAC5E,CAAE,MAAOlN,GAAqB,CAChC,C,uBCRA,IAAI4D,EAAa,EAAQ,KACrBiB,EAAW,EAAQ,KACnBQ,EAAiB,EAAQ,MAG7BrB,EAAOC,QAAU,SAAUmJ,EAAOC,EAAOC,GACvC,IAAIC,EAAWC,EAUf,OAPEnI,GAEAzB,EAAW2J,EAAYF,EAAMpD,cAC7BsD,IAAcD,GACdzI,EAAS2I,EAAqBD,EAAUxH,YACxCyH,IAAuBF,EAAQvH,WAC/BV,EAAe+H,EAAOI,GACjBJ,CACT,C,uBCjBA,IAAIrI,EAAU,EAAQ,KAEtBf,EAAOC,QAAU,SAAUG,GACzB,IAAImD,EAAQxC,EAAQX,GACpB,MAAgB,iBAATmD,GAAqC,kBAATA,CACrC,C,uBCLA,IAAI9E,EAAW,EAAQ,MAEvBuB,EAAOC,QAAU,SAAUC,EAAUuJ,GACnC,YAAoB/E,IAAbxE,EAAyByF,UAAUrH,OAAS,EAAI,GAAKmL,EAAWhL,EAASyB,EAClF,C,uBCJA,IAAIY,EAAS,EAAQ,MACjBlB,EAAa,EAAQ,KACrB8J,EAAW,EAAQ,MACnBC,EAAY,EAAQ,MACpBC,EAA2B,EAAQ,MAEnCC,EAAWF,EAAU,YACrBlE,EAAUpD,OACVD,EAAkBqD,EAAQ1D,UAK9B/B,EAAOC,QAAU2J,EAA2BnE,EAAQrE,eAAiB,SAAU0D,GAC7E,IAAImE,EAASS,EAAS5E,GACtB,GAAIhE,EAAOmI,EAAQY,GAAW,OAAOZ,EAAOY,GAC5C,IAAI5D,EAAcgD,EAAOhD,YACzB,OAAIrG,EAAWqG,IAAgBgD,aAAkBhD,EACxCA,EAAYlE,UACZkH,aAAkBxD,EAAUrD,EAAkB,IACzD,C,uBCnBA,IAAI0H,EAAsB,EAAQ,MAC9BC,EAAW,EAAQ,MACnBC,EAAqB,EAAQ,MAMjChK,EAAOC,QAAUoC,OAAOhB,iBAAmB,aAAe,CAAC,EAAI,WAC7D,IAEIkF,EAFA0D,GAAiB,EACjB5L,EAAO,CAAC,EAEZ,IACEkI,EAASuD,EAAoBzH,OAAON,UAAW,YAAa,OAC5DwE,EAAOlI,EAAM,IACb4L,EAAiB5L,aAAgBhG,KACnC,CAAE,MAAO2D,GAAqB,CAC9B,OAAO,SAAwB8I,EAAGrB,GAKhC,OAJAsG,EAASjF,GACTkF,EAAmBvG,GACfwG,EAAgB1D,EAAOzB,EAAGrB,GACzBqB,EAAEoF,UAAYzG,EACZqB,CACT,CACF,CAhB+D,QAgBzDJ,E,uBCzBN,IAAIyF,EAAc,EAAQ,MAEtBrK,EAAaC,UAIjBC,EAAOC,QAAU,SAAUC,GACzB,IAAIkK,EAAOD,EAAYjK,EAAU,UACjC,GAAmB,iBAARkK,EAAkB,MAAMtK,EAAW,kCAE9C,OAAOuK,OAAOD,EAChB,C,uBCXA,IAAI9I,EAAkB,EAAQ,MAE1BgB,EAAgBhB,EAAgB,eAChCjD,EAAO,CAAC,EAEZA,EAAKiE,GAAiB,IAEtBtC,EAAOC,QAA2B,eAAjBnD,OAAOuB,E,uBCPxB,IAAI0C,EAAU,EAAQ,KAElBlB,EAAU/C,OAEdkD,EAAOC,QAAU,SAAUC,GACzB,GAA0B,WAAtBa,EAAQb,GAAwB,MAAMH,UAAU,6CACpD,OAAOF,EAAQK,EACjB,C,oCCNA,IAAIoK,EAAkB,EAAQ,MAC1BC,EAAsB,EAAQ,KAE9B3G,EAAc2G,EAAoB3G,YAClCG,EAAyBwG,EAAoBxG,uBAC7CP,EAA2B+G,EAAoB/G,yBAInDO,EAAuB,cAAc,WACnC,OAAOuG,EAAgB1G,EAAYvP,MAAOmP,EAAyBnP,MACrE,G,oCCXA,IAAIkW,EAAsB,EAAQ,KAC9BlC,EAAc,EAAQ,MACtBW,EAAY,EAAQ,MACpBwB,EAA8B,EAAQ,MAEtC5G,EAAc2G,EAAoB3G,YAClCJ,EAA2B+G,EAAoB/G,yBAC/CO,EAAyBwG,EAAoBxG,uBAC7C0G,EAAOpC,EAAYkC,EAAoBpI,oBAAoBsI,MAI/D1G,EAAuB,YAAY,SAAkB2G,QACjChG,IAAdgG,GAAyB1B,EAAU0B,GACvC,IAAI5F,EAAIlB,EAAYvP,MAChB2Q,EAAIwF,EAA4BhH,EAAyBsB,GAAIA,GACjE,OAAO2F,EAAKzF,EAAG0F,EACjB,G,oCCjBA,IAAIC,EAAY,EAAQ,MACpBJ,EAAsB,EAAQ,KAC9BK,EAAgB,EAAQ,MACxB1F,EAAsB,EAAQ,MAC9B2F,EAAW,EAAQ,MAEnBjH,EAAc2G,EAAoB3G,YAClCJ,EAA2B+G,EAAoB/G,yBAC/CO,EAAyBwG,EAAoBxG,uBAE7C+G,IAAiB,WACnB,IAEE,IAAIjJ,UAAU,GAAG,QAAQ,EAAG,CAAEkJ,QAAS,WAAc,MAAM,CAAG,GAChE,CAAE,MAAO/O,GAGP,OAAiB,IAAVA,CACT,CACF,CATqB,GAarB+H,EAAuB,OAAQ,CAAE,KAAQ,SAAU5O,EAAOiE,GACxD,IAAI0L,EAAIlB,EAAYvP,MAChBgR,EAAgBH,EAAoB/P,GACpC6V,EAAcJ,EAAc9F,GAAK+F,EAASzR,IAAUA,EACxD,OAAOuR,EAAU7F,EAAGtB,EAAyBsB,GAAIO,EAAe2F,EAClE,GAAI,SAAUF,E,uBC5Bd,EAAQ,K,uBCAR,EAAQ,K,uBCAR,EAAQ,K,oCCAR,IAAIG,EAAI,EAAQ,MACZrK,EAAS,EAAQ,MACjBsK,EAAa,EAAQ,MACrBC,EAA2B,EAAQ,MACnChF,EAAiB,UACjBrF,EAAS,EAAQ,MACjBsK,EAAa,EAAQ,MACrBC,EAAoB,EAAQ,MAC5BC,EAA0B,EAAQ,MAClCC,EAAwB,EAAQ,MAChCC,EAAkB,EAAQ,MAC1B7K,EAAc,EAAQ,MACtB8K,EAAU,EAAQ,MAElBC,EAAgB,eAChBnD,EAAQ2C,EAAW,SACnBS,EAAqBT,EAAWQ,GAEhCE,EAAgB,WAClBR,EAAW/W,KAAMwX,GACjB,IAAIC,EAAkBnG,UAAUrH,OAC5BxC,EAAUwP,EAAwBQ,EAAkB,OAAIpH,EAAYiB,UAAU,IAC9EjP,EAAO4U,EAAwBQ,EAAkB,OAAIpH,EAAYiB,UAAU,GAAI,SAC/EoG,EAAO,IAAIJ,EAAmB7P,EAASpF,GACvCsF,EAAQuM,EAAMzM,GAIlB,OAHAE,EAAMtF,KAAOgV,EACbvF,EAAe4F,EAAM,QAASZ,EAAyB,EAAGK,EAAgBxP,EAAM2M,MAAO,KACvF0C,EAAkBU,EAAM1X,KAAMuX,GACvBG,CACT,EAEIF,EAAwBD,EAAc7J,UAAY4J,EAAmB5J,UAErEiK,EAAkB,UAAWzD,EAAMmD,GACnCO,EAA0B,UAAW,IAAIN,EAAmB,EAAG,GAG/DvF,EAAauF,GAAsBhL,GAAe0B,OAAO8G,yBAAyBvI,EAAQ8K,GAI1FQ,IAAqB9F,KAAgBA,EAAW+F,UAAY/F,EAAW3B,cAEvE2H,EAAqBJ,IAAoBE,IAAqBD,EAIlEhB,EAAE,CAAErK,QAAQ,EAAMqF,aAAa,EAAM/B,OAAQuH,GAAWW,GAAsB,CAC5EC,aAAcD,EAAqBR,EAAgBD,IAGrD,IAAIW,EAAyBpB,EAAWQ,GACpCa,EAAkCD,EAAuBvK,UAE7D,GAAIwK,EAAgCtG,cAAgBqG,EAKlD,IAAK,IAAIlX,KAJJqW,GACHtF,EAAeoG,EAAiC,cAAepB,EAAyB,EAAGmB,IAG7Ef,EAAuB,GAAIzK,EAAOyK,EAAuBnW,GAAM,CAC7E,IAAIoX,EAAWjB,EAAsBnW,GACjCqX,EAAeD,EAAS9F,EACvB5F,EAAOwL,EAAwBG,IAClCtG,EAAemG,EAAwBG,EAActB,EAAyB,EAAGqB,EAAS7F,GAE9F,C", "sources": ["webpack://portal-ui/./src/views/Login/backgroundlogin.vue", "webpack://portal-ui/src/views/Login/backgroundlogin.vue", "webpack://portal-ui/./src/views/Login/backgroundlogin.vue?d758", "webpack://portal-ui/./src/views/Login/backgroundlogin.vue?eb08", "webpack://portal-ui/./src/views/Login/login.vue", "webpack://portal-ui/src/views/Login/login.vue", "webpack://portal-ui/./src/views/Login/login.vue?87b3", "webpack://portal-ui/./src/views/Login/login.vue?5cda", "webpack://portal-ui/./node_modules/core-js/internals/a-possible-prototype.js", "webpack://portal-ui/./node_modules/core-js/internals/an-instance.js", "webpack://portal-ui/./node_modules/core-js/internals/array-buffer-basic-detection.js", "webpack://portal-ui/./node_modules/core-js/internals/array-buffer-view-core.js", "webpack://portal-ui/./node_modules/core-js/internals/array-from-constructor-and-list.js", "webpack://portal-ui/./node_modules/core-js/internals/array-to-reversed.js", "webpack://portal-ui/./node_modules/core-js/internals/array-with.js", "webpack://portal-ui/./node_modules/core-js/internals/classof.js", "webpack://portal-ui/./node_modules/core-js/internals/correct-prototype-getter.js", "webpack://portal-ui/./node_modules/core-js/internals/define-built-in-accessor.js", "webpack://portal-ui/./node_modules/core-js/internals/dom-exception-constants.js", "webpack://portal-ui/./node_modules/core-js/internals/error-stack-clear.js", "webpack://portal-ui/./node_modules/core-js/internals/function-uncurry-this-accessor.js", "webpack://portal-ui/./node_modules/core-js/internals/inherit-if-required.js", "webpack://portal-ui/./node_modules/core-js/internals/is-big-int-array.js", "webpack://portal-ui/./node_modules/core-js/internals/normalize-string-argument.js", "webpack://portal-ui/./node_modules/core-js/internals/object-get-prototype-of.js", "webpack://portal-ui/./node_modules/core-js/internals/object-set-prototype-of.js", "webpack://portal-ui/./node_modules/core-js/internals/to-big-int.js", "webpack://portal-ui/./node_modules/core-js/internals/to-string-tag-support.js", "webpack://portal-ui/./node_modules/core-js/internals/to-string.js", "webpack://portal-ui/./node_modules/core-js/modules/es.typed-array.to-reversed.js", "webpack://portal-ui/./node_modules/core-js/modules/es.typed-array.to-sorted.js", "webpack://portal-ui/./node_modules/core-js/modules/es.typed-array.with.js", "webpack://portal-ui/./node_modules/core-js/modules/esnext.typed-array.to-reversed.js", "webpack://portal-ui/./node_modules/core-js/modules/esnext.typed-array.to-sorted.js", "webpack://portal-ui/./node_modules/core-js/modules/esnext.typed-array.with.js", "webpack://portal-ui/./node_modules/core-js/modules/web.dom-exception.stack.js"], "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c,_setup=_vm._self._setupProxy;return _c('div',{staticClass:\"login-left-side\"},[_c('div',{staticClass:\"logo-container\"},[_c('a',{staticClass:\"logo-link\",on:{\"click\":function($event){return _vm.navigateTo('/index')}}},[_c('img',{staticClass:\"logo\",attrs:{\"src\":require(\"../../assets/logo_tiangong.png\"),\"alt\":\"算力租赁\"}})])]),_vm._m(0),_c('div',{staticClass:\"visual-element\"},[_c('div',{staticClass:\"server-illustration\"},_vm._l((_vm.servers),function(server,index){return _c('div',{key:index,staticClass:\"server-unit\",style:({\n               animationDelay: `${index * 0.2}s`,\n               transform: `translateY(${index * 4}px)`\n             })},[_c('div',{staticClass:\"server-light\"})])}),0),_c('div',{staticClass:\"connections\"})]),_c('div',{staticClass:\"features\"},_vm._l((_vm.features),function(feature,index){return _c('div',{key:index,staticClass:\"feature-item\"},[_c('div',{staticClass:\"feature-text\"},[_c('h3',[_vm._v(_vm._s(feature.title))]),_c('p',[_vm._v(_vm._s(feature.description))])])])}),0),_c('div',{staticClass:\"background-elements\"},_vm._l((20),function(i){return _c('div',{key:i,staticClass:\"floating-particle\",style:({\n             left: `${Math.random() * 100}%`,\n             top: `${Math.random() * 100}%`,\n             animationDuration: `${3 + Math.random() * 10}s`,\n             animationDelay: `${Math.random() * 5}s`\n           })})}),0)])\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c,_setup=_vm._self._setupProxy;return _c('div',{staticClass:\"bottom-text\"},[_c('h2',{staticClass:\"slogan\"},[_vm._v(\"高效算力 · 智慧未来\")]),_c('p',{staticClass:\"sub-slogan\"},[_vm._v(\"专业算力租赁服务，为您的业务提供强大支持\")])])\n}]\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"login-left-side\">\r\n    <!-- 公司Logo -->\r\n    <div class=\"logo-container\">\r\n      <a @click=\"navigateTo('/index')\" class=\"logo-link\">\r\n        <img class=\"logo\" src=\"../../assets/logo_tiangong.png\" alt=\"算力租赁\" />\r\n      </a>\r\n      <!--      <h1 class=\"company-name\">天工云</h1>-->\r\n    </div>\r\n\r\n    <div class=\"bottom-text\">\r\n      <h2 class=\"slogan\">高效算力 · 智慧未来</h2>\r\n      <p class=\"sub-slogan\">专业算力租赁服务，为您的业务提供强大支持</p>\r\n    </div>\r\n\r\n    <!-- 主要视觉元素 -->\r\n    <div class=\"visual-element\">\r\n      <div class=\"server-illustration\">\r\n        <div v-for=\"(server, index) in servers\" :key=\"index\"\r\n             class=\"server-unit\"\r\n             :style=\"{\r\n               animationDelay: `${index * 0.2}s`,\r\n               transform: `translateY(${index * 4}px)`\r\n             }\">\r\n          <div class=\"server-light\"></div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"connections\">\r\n<!--        <div v-for=\"i in 10\" :key=\"i\" class=\"connection-line\"></div>-->\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 特点介绍 -->\r\n    <div class=\"features\">\r\n      <div v-for=\"(feature, index) in features\" :key=\"index\" class=\"feature-item\">\r\n<!--        <div class=\"feature-icon\">-->\r\n<!--          <component :is=\"feature.icon\" />-->\r\n<!--        </div>-->\r\n        <div class=\"feature-text\">\r\n          <h3>{{ feature.title }}</h3>\r\n          <p>{{ feature.description }}</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 背景动画元素 -->\r\n    <div class=\"background-elements\">\r\n      <div v-for=\"i in 20\" :key=\"i\"\r\n           class=\"floating-particle\"\r\n           :style=\"{\r\n             left: `${Math.random() * 100}%`,\r\n             top: `${Math.random() * 100}%`,\r\n             animationDuration: `${3 + Math.random() * 10}s`,\r\n             animationDelay: `${Math.random() * 5}s`\r\n           }\">\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { defineComponent, ref } from 'vue';\r\n\r\n// 图标组件\r\nconst PerformanceIcon = {\r\n  template: `\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\r\n      <path d=\"M12 2v4\"></path>\r\n      <path d=\"m16.24 7.76 2.83-2.83\"></path>\r\n      <path d=\"M18 12h4\"></path>\r\n      <path d=\"m16.24 16.24 2.83 2.83\"></path>\r\n      <path d=\"M12 18v4\"></path>\r\n      <path d=\"m7.76 16.24-2.83 2.83\"></path>\r\n      <path d=\"M6 12H2\"></path>\r\n      <path d=\"m7.76 7.76-2.83-2.83\"></path>\r\n      <circle cx=\"12\" cy=\"12\" r=\"4\"></circle>\r\n    </svg>\r\n  `\r\n}\r\n\r\nconst ServerIcon = {\r\n  template: `\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\r\n      <rect width=\"20\" height=\"8\" x=\"2\" y=\"2\" rx=\"2\" ry=\"2\"></rect>\r\n      <rect width=\"20\" height=\"8\" x=\"2\" y=\"14\" rx=\"2\" ry=\"2\"></rect>\r\n      <line x1=\"6\" x2=\"6\" y1=\"6\" y2=\"6\"></line>\r\n      <line x1=\"6\" x2=\"6\" y1=\"18\" y2=\"18\"></line>\r\n    </svg>\r\n  `\r\n}\r\n\r\nconst ShieldIcon = {\r\n  template: `\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\r\n      <path d=\"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z\"></path>\r\n    </svg>\r\n  `\r\n}\r\n\r\nexport default defineComponent({\r\n  name: 'backgroundlogin',\r\n  components: {\r\n    PerformanceIcon,\r\n    ServerIcon,\r\n    ShieldIcon\r\n  },\r\n  methods:{\r\n    navigateTo(path) {\r\n      // 记录当前活动路径作为上一个活动路径\r\n      if (this.currentPath && this.currentPath !== path) {\r\n        this.previousActivePath = this.currentPath;\r\n\r\n        // 为当前活动链接和登录按钮添加 active-exit 类\r\n        this.$nextTick(() => {\r\n          const navLinks = document.querySelectorAll('.nav-link, .btn-login');\r\n          navLinks.forEach(link => {\r\n            if ((link.classList.contains('active') ||\r\n                    (path === '/login' && link.classList.contains('btn-login'))) &&\r\n                !link.classList.contains('active-exit')) {\r\n              link.classList.add('active-exit');\r\n\r\n              // 等待动画完成后移除 active-exit 类\r\n              setTimeout(() => {\r\n                link.classList.remove('active-exit');\r\n              }, 300); // 匹配你的 CSS transition 持续时间 (0.3s)\r\n            }\r\n          });\r\n\r\n          // 更新当前路径\r\n          this.currentPath = path;\r\n        });\r\n      } else {\r\n        this.currentPath = path;\r\n      }\r\n\r\n      // 如果当前路径与目标路径相同，则重新加载页面\r\n      if (this.$route.path === path) {\r\n        this.$nextTick(() => {\r\n          window.scrollTo({\r\n            top: 0,\r\n            behavior: 'instant' // 使用即时滚动而不是平滑滚动\r\n          });\r\n          this.$router.go(0); // 刷新当前页面\r\n        });\r\n      } else {\r\n        // 不同路径，正常导航并滚动到顶部\r\n        this.$router.push(path);\r\n        window.scrollTo({\r\n          top: 0,\r\n          behavior: 'instant'\r\n        });\r\n      }\r\n    },\r\n  },\r\n  setup() {\r\n    const logoSrc = ref('/api/placeholder/100/100');\r\n    const servers = ref(Array(5).fill(null));\r\n\r\n    const features = ref([\r\n      {\r\n        icon: 'PerformanceIcon',\r\n        title: '高性能算力',\r\n        description: '提供GPU/CPU灵活配置，满足AI训练、渲染等高算力需求'\r\n      },\r\n      {\r\n        icon: 'am-icon-shield',\r\n        title: '安全可靠',\r\n        description: '数据加密传输，多重备份，确保您的业务安全稳定运行'\r\n      }\r\n    ]);\r\n\r\n\r\n    return {\r\n      logoSrc,\r\n      servers,\r\n      features\r\n    };\r\n  }\r\n});\r\n</script>\r\n\r\n<style scoped>\r\n.login-left-side {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100vh;\r\n  background: linear-gradient(135deg, #025af7 0%, #2196f3 100%);\r\n  color: white;\r\n  padding: 40px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: space-between;\r\n  overflow: hidden;\r\n}\r\n\r\n/* Logo样式 */\r\n.logo-container {\r\n  margin-top: -60px;\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n  margin-left: -40px;\r\n  z-index: 10;\r\n}\r\n\r\n.logo {\r\n  width: 180px;\r\n  height: 140px;\r\n  border-radius: 12px;\r\n  margin-right: 15px;\r\n}\r\n\r\n/* 主视觉元素 */\r\n.visual-element {\r\n  flex: 1;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  perspective: 1000px;\r\n  z-index: 5;\r\n}\r\n\r\n.server-illustration {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 15px;\r\n  transform: rotateY(25deg) rotateX(10deg);\r\n  transform-style: preserve-3d;\r\n}\r\n\r\n.server-unit {\r\n  width: 200px;\r\n  height: 30px;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  border-radius: 6px;\r\n  position: relative;\r\n  backdrop-filter: blur(5px);\r\n  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.2);\r\n  animation: pulse 2s infinite;\r\n}\r\n\r\n.server-light {\r\n  position: absolute;\r\n  right: 10px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  width: 8px;\r\n  height: 8px;\r\n  border-radius: 50%;\r\n  background-color: rgba(231, 12, 12, 0);\r\n  box-shadow: 0 0 10px #ffffff;\r\n  animation: blink 1.5s infinite;\r\n}\r\n\r\n.connections {\r\n  position: absolute;\r\n  width: 100%;\r\n  height: 100%;\r\n  pointer-events: none;\r\n}\r\n\r\n.connection-line {\r\n  position: absolute;\r\n  height: 2px;\r\n  width: 100px;\r\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.7), transparent);\r\n  top: calc(30% + (40% * Math.random()));\r\n  left: calc(10% + (50% * Math.random()));\r\n  animation: move 4s infinite linear;\r\n  transform: rotate(calc(-30deg + (60deg * Math.random())));\r\n  opacity: 0.6;\r\n}\r\n\r\n/* 特点介绍 */\r\n.features {\r\n  margin-top: 40px;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 20px;\r\n  z-index: 10;\r\n}\r\n\r\n.feature-item {\r\n  flex: 1;\r\n  min-width: 250px;\r\n  display: flex;\r\n  align-items: flex-start;\r\n  padding: 15px;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: 12px;\r\n  backdrop-filter: blur(10px);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  transition: transform 0.3s, box-shadow 0.3s;\r\n}\r\n\r\n.feature-item:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);\r\n  border-color: rgba(255, 255, 255, 0.4);\r\n}\r\n\r\n.feature-icon {\r\n  margin-right: 15px;\r\n  color: white;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  padding: 10px;\r\n  border-radius: 10px;\r\n  height: 44px;\r\n  width: 44px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.feature-icon svg {\r\n  width: 24px;\r\n  height: 24px;\r\n}\r\n\r\n.feature-text h3 {\r\n  margin: 0 0 5px 0;\r\n  font-size: 18px;\r\n}\r\n\r\n.feature-text p {\r\n  margin: 0;\r\n  font-size: 14px;\r\n  opacity: 0.8;\r\n  line-height: 1.4;\r\n}\r\n\r\n/* 底部文案 */\r\n.bottom-text {\r\n  margin-top: -20px;\r\n  text-align: center;\r\n  z-index: 10;\r\n}\r\n\r\n.slogan {\r\n  font-size: 28px;\r\n  margin: 0 0 10px 0;\r\n  background: whitesmoke;\r\n  /*background: linear-gradient(to right, #ffffff, #2196f3);*/\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  font-weight: bold;\r\n}\r\n\r\n.sub-slogan {\r\n  font-size: 16px;\r\n  opacity: 0.9;\r\n  color: whitesmoke;\r\n  margin: 0;\r\n}\r\n\r\n/* 背景元素 */\r\n.background-elements {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  pointer-events: none;\r\n}\r\n\r\n.floating-particle {\r\n  position: absolute;\r\n  width: 6px;\r\n  height: 6px;\r\n  background-color: rgba(255, 255, 255, 0.5);\r\n  border-radius: 50%;\r\n  animation: float 10s infinite linear;\r\n}\r\n\r\n/* 动画 */\r\n@keyframes pulse {\r\n  0% { box-shadow: 0 0 5px rgba(255, 255, 255, 0.3); }\r\n  50% { box-shadow: 0 0 15px rgba(255, 255, 255, 0.5); }\r\n  100% { box-shadow: 0 0 5px rgba(255, 255, 255, 0.3); }\r\n}\r\n\r\n@keyframes blink {\r\n  0%, 100% { opacity: 1; }\r\n  50% { opacity: 0.5; }\r\n}\r\n\r\n@keyframes move {\r\n  0% { transform: translateX(-50px) rotate(var(--rotation, -20deg)); opacity: 0; }\r\n  50% { opacity: 0.8; }\r\n  100% { transform: translateX(150px) rotate(var(--rotation, -20deg)); opacity: 0; }\r\n}\r\n\r\n@keyframes float {\r\n  0% { transform: translate(0, 0); opacity: 0; }\r\n  25% { opacity: 0.8; }\r\n  50% { transform: translate(10px, 10px); }\r\n  75% { opacity: 0.4; }\r\n  100% { transform: translate(0, 0); opacity: 0; }\r\n}\r\n</style>", "import mod from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./backgroundlogin.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./backgroundlogin.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./backgroundlogin.vue?vue&type=template&id=771899f4&scoped=true&\"\nimport script from \"./backgroundlogin.vue?vue&type=script&lang=js&\"\nexport * from \"./backgroundlogin.vue?vue&type=script&lang=js&\"\nimport style0 from \"./backgroundlogin.vue?vue&type=style&index=0&id=771899f4&prod&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"771899f4\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"login-page\"},[(_vm.showNotification)?_c('SlideNotification',{attrs:{\"message\":_vm.notificationMessage,\"type\":_vm.notificationType,\"duration\":3000,\"minHeight\":_vm.minHeight},on:{\"close\":function($event){_vm.showNotification = false}}}):_vm._e(),_c('div',{staticClass:\"left-side\"},[_c('backgroundlogin')],1),_c('div',{staticClass:\"right-side\"},[_c('div',{staticClass:\"login-form-container\"},[_c('h3',[_vm._v(\"欢迎来到 天工开物\")]),_c('div',{staticClass:\"login-tabs\"},[_c('div',{class:['tab-item', _vm.activeTab === 'phone' ? 'active' : ''],on:{\"click\":function($event){_vm.activeTab = 'phone'}}},[_vm._v(\" 手机号登录 \")]),_c('div',{class:['tab-item', _vm.activeTab === 'account' ? 'active' : ''],on:{\"click\":function($event){_vm.activeTab = 'account'}}},[_vm._v(\" 账号登录 \")])]),_c('div',{staticClass:\"form-container\"},[(_vm.activeTab === 'phone')?_c('div',{staticClass:\"login-form\"},[_c('p',{staticClass:\"form-note\"}),_c('div',{staticClass:\"input-group\",class:{ 'error': _vm.errors.phone }},[_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.phoneForm.phone),expression:\"phoneForm.phone\"}],attrs:{\"type\":\"text\",\"placeholder\":\"请输入手机号\"},domProps:{\"value\":(_vm.phoneForm.phone)},on:{\"blur\":_vm.validatePhone,\"input\":function($event){if($event.target.composing)return;_vm.$set(_vm.phoneForm, \"phone\", $event.target.value)}}}),_c('div',{staticClass:\"error-container\"},[(_vm.errors.phone)?_c('div',{staticClass:\"error-message\"},[_vm._v(_vm._s(_vm.errors.phone))]):_vm._e()])]),_c('div',{staticClass:\"input-group verification-code\",class:{ 'error': _vm.errors.code }},[_c('div',{staticClass:\"code-input-container\"},[_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.phoneForm.code),expression:\"phoneForm.code\"}],attrs:{\"type\":\"text\",\"placeholder\":\"请输入验证码\"},domProps:{\"value\":(_vm.phoneForm.code)},on:{\"blur\":_vm.validateCodegeshi,\"input\":function($event){if($event.target.composing)return;_vm.$set(_vm.phoneForm, \"code\", $event.target.value)}}}),_c('button',{staticClass:\"get-code-btn-inline\",attrs:{\"disabled\":!_vm.phoneForm.phone || _vm.errors.phone || _vm.codeSent},on:{\"click\":_vm.getVerificationCode}},[_vm._v(\" \"+_vm._s(_vm.codeSent ? `${_vm.countdown}秒后重试` : '获取验证码')+\" \")])]),_c('div',{staticClass:\"error-container\"},[(_vm.errors.code)?_c('div',{staticClass:\"error-message\"},[_vm._v(_vm._s(_vm.errors.code))]):_vm._e()])]),_c('div',{staticClass:\"agreement-text\"},[_vm._v(\" 登录视为您已阅读并同意天工开物 \"),_c('router-link',{attrs:{\"to\":\"/help/user-agreement\"}},[_vm._v(\"服务条款\")]),_vm._v(\" 和\"),_c('router-link',{attrs:{\"to\":\"/help/privacy-policy\"}},[_vm._v(\"隐私政策\")])],1),_c('button',{staticClass:\"login-btn\",attrs:{\"disabled\":!_vm.phoneForm.phone || !_vm.phoneForm.code},on:{\"click\":_vm.phoneLogin}},[_vm._v(\" 登录 \")]),_c('div',{staticClass:\"login-link\"},[_c('a',{attrs:{\"href\":\"#\"},on:{\"click\":function($event){return _vm.navigateTo('/register')}}},[_vm._v(\"立即注册\")]),_c('span',{staticClass:\"divider\"},[_vm._v(\"|\")]),_c('a',{attrs:{\"href\":\"#\"},on:{\"click\":function($event){return _vm.navigateTo('/forgetpass')}}},[_vm._v(\"忘记密码\")])])]):_vm._e(),(_vm.activeTab === 'account')?_c('div',{staticClass:\"login-form\"},[_c('p',{staticClass:\"form-note\"},[_vm._v(\"手机号即为登录账号\")]),_c('div',{staticClass:\"input-group\",class:{ 'error': _vm.errors.username }},[_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.accountForm.username),expression:\"accountForm.username\"}],attrs:{\"type\":\"text\",\"placeholder\":\"请输入登录账号\"},domProps:{\"value\":(_vm.accountForm.username)},on:{\"blur\":_vm.validateUsername,\"input\":function($event){if($event.target.composing)return;_vm.$set(_vm.accountForm, \"username\", $event.target.value)}}}),_c('div',{staticClass:\"error-container\"},[(_vm.errors.username)?_c('div',{staticClass:\"error-message\"},[_vm._v(_vm._s(_vm.errors.username))]):_vm._e()])]),_c('div',{staticClass:\"input-group\",class:{ 'error': _vm.errors.password }},[_c('div',{staticClass:\"password-input-container\"},[((_vm.passwordVisible ? 'text' : 'password')==='checkbox')?_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.accountForm.password),expression:\"accountForm.password\"}],attrs:{\"placeholder\":\"请输入登录密码\",\"type\":\"checkbox\"},domProps:{\"checked\":Array.isArray(_vm.accountForm.password)?_vm._i(_vm.accountForm.password,null)>-1:(_vm.accountForm.password)},on:{\"blur\":_vm.validatePassword,\"change\":function($event){var $$a=_vm.accountForm.password,$$el=$event.target,$$c=$$el.checked?(true):(false);if(Array.isArray($$a)){var $$v=null,$$i=_vm._i($$a,$$v);if($$el.checked){$$i<0&&(_vm.$set(_vm.accountForm, \"password\", $$a.concat([$$v])))}else{$$i>-1&&(_vm.$set(_vm.accountForm, \"password\", $$a.slice(0,$$i).concat($$a.slice($$i+1))))}}else{_vm.$set(_vm.accountForm, \"password\", $$c)}}}}):((_vm.passwordVisible ? 'text' : 'password')==='radio')?_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.accountForm.password),expression:\"accountForm.password\"}],attrs:{\"placeholder\":\"请输入登录密码\",\"type\":\"radio\"},domProps:{\"checked\":_vm._q(_vm.accountForm.password,null)},on:{\"blur\":_vm.validatePassword,\"change\":function($event){return _vm.$set(_vm.accountForm, \"password\", null)}}}):_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.accountForm.password),expression:\"accountForm.password\"}],attrs:{\"placeholder\":\"请输入登录密码\",\"type\":_vm.passwordVisible ? 'text' : 'password'},domProps:{\"value\":(_vm.accountForm.password)},on:{\"blur\":_vm.validatePassword,\"input\":function($event){if($event.target.composing)return;_vm.$set(_vm.accountForm, \"password\", $event.target.value)}}}),_c('span',{staticClass:\"password-toggle\",on:{\"click\":_vm.togglePasswordVisibility}},[_c('i',{class:['eye-icon', _vm.passwordVisible ? 'visible' : '']})])]),_c('div',{staticClass:\"error-container\"},[(_vm.errors.password)?_c('div',{staticClass:\"error-message\"},[_vm._v(_vm._s(_vm.errors.password))]):_vm._e()])]),_c('div',{staticClass:\"agreement-text\"},[_vm._v(\" 登录视为您已阅读并同意天工开物 \"),_c('router-link',{attrs:{\"to\":\"/help/user-agreement\"}},[_vm._v(\"服务条款\")]),_vm._v(\" 和\"),_c('router-link',{attrs:{\"to\":\"/help/privacy-policy\"}},[_vm._v(\"隐私政策\")])],1),_c('button',{staticClass:\"login-btn\",attrs:{\"disabled\":!_vm.accountForm.username || !_vm.accountForm.password},on:{\"click\":_vm.accountLogin}},[_vm._v(\" 登录 \")]),_c('div',{staticClass:\"login-link\"},[_c('a',{attrs:{\"href\":\"#\"},on:{\"click\":function($event){return _vm.navigateTo('/register')}}},[_vm._v(\"立即注册\")]),_c('span',{staticClass:\"divider\"},[_vm._v(\"|\")]),_c('a',{attrs:{\"href\":\"#\"},on:{\"click\":function($event){return _vm.navigateTo('/forgetpass')}}},[_vm._v(\"忘记密码\")])])]):_vm._e()])])])],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"login-page\">\r\n    <SlideNotification\r\n        v-if=\"showNotification\"\r\n        :message=\"notificationMessage\"\r\n        :type=\"notificationType\"\r\n        :duration=\"3000\"\r\n        :minHeight= minHeight\r\n        @close=\"showNotification = false\"\r\n    />\r\n\r\n    <div class=\"left-side\">\r\n      <backgroundlogin />\r\n\r\n<!--      <div class=\"logo-container\">-->\r\n<!--        <div class=\"logo-area\">-->\r\n<!--          <img src=\"../assets/images/logo2.png\" alt=\"算力租赁\">-->\r\n<!--        </div>-->\r\n<!--      </div>-->\r\n\r\n<!--      <div class=\"headline-container\">-->\r\n<!--        <h2 class=\"headline\">天工开物 AI算力云</h2>-->\r\n<!--        <p class=\"subheadline\">-->\r\n<!--          专门面向AI 2.0 时代的创新平台。利用顶尖的技术驱动，为用户的大模型开发、训练、运行、应用提供完整的工具链。-->\r\n<!--        </p>-->\r\n<!--      </div>-->\r\n    </div>\r\n\r\n    <div class=\"right-side\">\r\n      <div class=\"login-form-container\">\r\n        <h3>欢迎来到 天工开物</h3>\r\n\r\n        <!-- Login tabs -->\r\n        <div class=\"login-tabs\">\r\n          <div\r\n              :class=\"['tab-item', activeTab === 'phone' ? 'active' : '']\"\r\n              @click=\"activeTab = 'phone'\"\r\n          >\r\n            手机号登录\r\n          </div>\r\n          <div\r\n              :class=\"['tab-item', activeTab === 'account' ? 'active' : '']\"\r\n              @click=\"activeTab = 'account'\"\r\n          >\r\n            账号登录\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Login form container with fixed height -->\r\n        <div class=\"form-container\">\r\n          <!-- Phone login form -->\r\n          <div v-if=\"activeTab === 'phone'\" class=\"login-form\">\r\n            <p class=\"form-note\">   </p>\r\n\r\n            <div class=\"input-group\" :class=\"{ 'error': errors.phone }\">\r\n              <input\r\n                  type=\"text\"\r\n                  v-model=\"phoneForm.phone\"\r\n                  placeholder=\"请输入手机号\"\r\n                  @blur=\"validatePhone\"\r\n              />\r\n              <div class=\"error-container\">\r\n                <div v-if=\"errors.phone\" class=\"error-message\">{{ errors.phone }}</div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"input-group verification-code\" :class=\"{ 'error': errors.code }\">\r\n              <div class=\"code-input-container\">\r\n                <input\r\n                    type=\"text\"\r\n                    v-model=\"phoneForm.code\"\r\n                    placeholder=\"请输入验证码\"\r\n                    @blur=\"validateCodegeshi\"\r\n                />\r\n                <button\r\n                    class=\"get-code-btn-inline\"\r\n                    @click=\"getVerificationCode\"\r\n                    :disabled=\"!phoneForm.phone || errors.phone || codeSent\"\r\n                >\r\n                  {{ codeSent ? `${countdown}秒后重试` : '获取验证码' }}\r\n                </button>\r\n              </div>\r\n              <div class=\"error-container\">\r\n                <div v-if=\"errors.code\" class=\"error-message\">{{ errors.code }}</div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"agreement-text\">\r\n              登录视为您已阅读并同意天工开物\r\n              <router-link to=\"/help/user-agreement\" >服务条款</router-link> 和<router-link to=\"/help/privacy-policy\" >隐私政策</router-link>\r\n            </div>\r\n\r\n            <button\r\n                class=\"login-btn\"\r\n                @click=\"phoneLogin\"\r\n                :disabled=\"!phoneForm.phone || !phoneForm.code\"\r\n            >\r\n              登录\r\n            </button>\r\n            <div class=\"login-link\">\r\n              <a href=\"#\" @click=\"navigateTo('/register')\">立即注册</a>\r\n              <span class=\"divider\">|</span>\r\n              <a href=\"#\" @click=\"navigateTo('/forgetpass')\">忘记密码</a>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Account login form -->\r\n          <div v-if=\"activeTab === 'account'\" class=\"login-form\">\r\n            <p class=\"form-note\">手机号即为登录账号</p>\r\n            <div class=\"input-group\" :class=\"{ 'error': errors.username }\">\r\n              <input\r\n                  type=\"text\"\r\n                  v-model=\"accountForm.username\"\r\n                  placeholder=\"请输入登录账号\"\r\n                  @blur=\"validateUsername\"\r\n              />\r\n              <div class=\"error-container\">\r\n                <div v-if=\"errors.username\" class=\"error-message\">{{ errors.username }}</div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"input-group\" :class=\"{ 'error': errors.password }\">\r\n              <div class=\"password-input-container\">\r\n                <input\r\n                    :type=\"passwordVisible ? 'text' : 'password'\"\r\n                    v-model=\"accountForm.password\"\r\n                    placeholder=\"请输入登录密码\"\r\n                    @blur=\"validatePassword\"\r\n                />\r\n                <span class=\"password-toggle\" @click=\"togglePasswordVisibility\">\r\n                  <i :class=\"['eye-icon', passwordVisible ? 'visible' : '']\"></i>\r\n                </span>\r\n              </div>\r\n              <div class=\"error-container\">\r\n                <div v-if=\"errors.password\" class=\"error-message\">{{ errors.password }}</div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"agreement-text\">\r\n              登录视为您已阅读并同意天工开物 \r\n              <router-link to=\"/help/user-agreement\" >服务条款</router-link> 和<router-link to=\"/help/privacy-policy\" >隐私政策</router-link>\r\n            </div>\r\n\r\n            <button\r\n                class=\"login-btn\"\r\n                @click=\"accountLogin\"\r\n                :disabled=\"!accountForm.username || !accountForm.password\"\r\n            >\r\n              登录\r\n            </button>\r\n            <div class=\"login-link\">\r\n              <a href=\"#\" @click=\"navigateTo('/register')\">立即注册</a>\r\n              <span class=\"divider\">|</span>\r\n              <a href=\"#\" @click=\"navigateTo('/forgetpass')\">忘记密码</a>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import {postAnyData, getAnyData, postLogin, postJsonData} from \"@/api/login\";\r\nimport { getToken, setToken, removeToken } from '@/utils/auth'\r\nimport SlideNotification from '@/components/common/header/SlideNotification.vue';\r\nimport Cookies from 'js-cookie'\r\nimport backgroundlogin from '@/views/Login/backgroundlogin.vue';\r\n\r\nexport default {\r\n  name: \"login\",\r\n  components: {\r\n    SlideNotification,\r\n    backgroundlogin\r\n  },\r\n  data() {\r\n    return {\r\n      activeTab: 'phone', // 默认选中手机号登录\r\n      phoneForm: {\r\n        phone: '',\r\n        code: ''\r\n      },\r\n      accountForm: {\r\n        username: '',\r\n        password: ''\r\n      },\r\n      passwordVisible: false,\r\n      errors: {\r\n        phone: '',\r\n        code: '',\r\n        username: '',\r\n        password: ''\r\n      },\r\n      codeSent: false,\r\n      countdown: 60,\r\n      timer: null,\r\n      showNotification: false,\r\n      notificationMessage: '',\r\n      notificationType: 'success',\r\n      minHeight: '50px'\r\n    }\r\n  },\r\n  created() {\r\n    // 页面创建时检查本地存储中的计时器状态\r\n    this.checkCountdownState();\r\n    this.$emit('hiden-layout')\r\n  },\r\n  activated() {\r\n    this.$emit('hiden-layout', true); // 从缓存返回时再次隐藏\r\n  },\r\n  methods: {\r\n    showNotificationMessage(message, type = 'info') {\r\n      this.notificationMessage = message;\r\n      this.notificationType = type;\r\n      this.showNotification = true;\r\n    },\r\n    //生成公私钥对\r\n    async generateKeyPair() {\r\n      try {\r\n        this.error = null;\r\n        // 生成密钥对\r\n        const keyPair = await crypto.subtle.generateKey(\r\n                {\r\n                  name: 'RSA-OAEP',\r\n                  modulusLength: 2048,\r\n                  publicExponent: new Uint8Array([1, 0, 1]),\r\n                  hash: { name: 'SHA-256' }\r\n                },\r\n                true,\r\n                ['encrypt', 'decrypt']\r\n        );\r\n\r\n        // 导出公钥\r\n        const exportedPublicKey = await crypto.subtle.exportKey('spki', keyPair.publicKey);\r\n        const publicKeyPem = btoa(String.fromCharCode(...new Uint8Array(exportedPublicKey)));\r\n\r\n        // 导出私钥\r\n        const exportedPrivateKey = await crypto.subtle.exportKey('pkcs8', keyPair.privateKey);\r\n        const privateKeyPem = btoa(String.fromCharCode(...new Uint8Array(exportedPrivateKey)));\r\n\r\n        const key = { publicKeyPem, privateKeyPem };\r\n        Cookies.set('publicKey-B',publicKeyPem)\r\n        Cookies.set('privateKey-B',privateKeyPem)\r\n      } catch (err) {\r\n        this.error = '密钥对生成失败，请确保在安全上下文（HTTPS）中运行';\r\n      } finally {\r\n        this.isLoading = false;\r\n      }\r\n    },\r\n\r\n    checkCountdownState() {\r\n      // 从localStorage获取倒计时信息\r\n      const storedPhone = localStorage.getItem('verificationPhone');\r\n      const expireTime = localStorage.getItem('verificationExpireTime');\r\n\r\n      if (storedPhone && expireTime) {\r\n        const now = new Date().getTime();\r\n        const timeLeft = Math.ceil((parseInt(expireTime) - now) / 1000);\r\n\r\n        // 如果倒计时还没结束\r\n        if (timeLeft > 0) {\r\n          this.codeSent = true;\r\n          this.countdown = timeLeft;\r\n          this.startCountdown();\r\n\r\n          // 如果当前输入的手机号与存储的手机号一致，应用倒计时限制\r\n          if (this.phoneForm.phone === storedPhone) {\r\n            this.codeSent = true;\r\n          }\r\n        } else {\r\n          // 倒计时已结束，清除存储\r\n          this.clearCountdownStorage();\r\n        }\r\n      }\r\n    },\r\n\r\n    clearCountdownStorage() {\r\n      localStorage.removeItem('verificationPhone');\r\n      localStorage.removeItem('verificationExpireTime');\r\n      this.codeSent = false;\r\n      this.countdown = 60;\r\n      if (this.timer) {\r\n        clearInterval(this.timer);\r\n        this.timer = null;\r\n      }\r\n    },\r\n\r\n    validatePhone() {\r\n      const phoneRegex = /^1[3-9]\\d{9}$/;\r\n      if (!this.phoneForm.phone) {\r\n        this.errors.phone = '请输入手机号';\r\n      } else if (!phoneRegex.test(this.phoneForm.phone)) {\r\n        this.errors.phone = '请输入有效的手机号';\r\n      } else {\r\n        this.errors.phone = '';\r\n\r\n        // 检查该手机号是否处于冷却期\r\n        const storedPhone = localStorage.getItem('verificationPhone');\r\n        const expireTime = localStorage.getItem('verificationExpireTime');\r\n\r\n        if (storedPhone === this.phoneForm.phone && expireTime) {\r\n          const now = new Date().getTime();\r\n          const timeLeft = Math.ceil((parseInt(expireTime) - now) / 1000);\r\n\r\n          if (timeLeft > 0) {\r\n            this.codeSent = true;\r\n            this.countdown = timeLeft;\r\n            // 如果没有定时器，则重新启动\r\n            if (!this.timer) {\r\n              this.startCountdown();\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n\r\n    async validateCodegeshi() {\r\n      // 清空错误\r\n      this.errors.code = '';\r\n\r\n      // 前端基础验证（保持原有长度判断）\r\n      if (!this.phoneForm.code) {\r\n        this.errors.code = '请输入验证码';\r\n        return false;\r\n      }\r\n      if (this.phoneForm.code.length !== 4 || !/^\\d+$/.test(this.phoneForm.code)) {\r\n        this.errors.code = '验证码必须为4位数字';\r\n        return false;\r\n      }\r\n    },\r\n    startCountdown() {\r\n      // 清除可能存在的旧定时器\r\n      if (this.timer) {\r\n        clearInterval(this.timer);\r\n      }\r\n\r\n      // 使用固定的时间间隔\r\n      this.timer = setInterval(() => {\r\n        if (this.countdown <= 1) {\r\n          clearInterval(this.timer);\r\n          this.timer = null;\r\n          this.codeSent = false;\r\n          this.countdown = 60;\r\n          // 清除localStorage中的记录\r\n          this.clearCountdownStorage();\r\n        } else {\r\n          this.countdown--;\r\n          // 更新localStorage中的过期时间（可选，保持同步）\r\n          const expireTime = new Date().getTime() + (this.countdown * 1000);\r\n          localStorage.setItem('verificationExpireTime', expireTime.toString());\r\n        }\r\n      }, 1000);\r\n    },\r\n    navigateTo(path) {\r\n      // 如果当前路径与目标路径相同，则重新加载页面\r\n      if (this.$route.path === path) {\r\n        // 先跳转到一个临时路由（如果有的话）或者重新加载页面\r\n        this.$nextTick(() => {\r\n          window.scrollTo({\r\n            top: 0,\r\n            behavior: 'instant' // 使用即时滚动而不是平滑滚动\r\n          });\r\n          this.$router.go(0); // 刷新当前页面\r\n        });\r\n      } else {\r\n        // 不同路径，正常导航并滚动到顶部\r\n        this.$router.push(path);\r\n        window.scrollTo({\r\n          top: 0,\r\n          behavior: 'instant'\r\n        });\r\n      }\r\n      this.currentPath = path;\r\n    },\r\n\r\n    validateUsername() {\r\n      const phoneRegex = /^1[3-9]\\d{9}$/;\r\n      if (!this.accountForm.username) {\r\n        this.errors.username = '请输入登录账号';\r\n      } else if (!phoneRegex.test(this.accountForm.username)) {\r\n        this.errors.username = '请输入有效的登录账号';\r\n      } else {\r\n        this.errors.username = '';\r\n      }\r\n    },\r\n\r\n    validatePassword() {\r\n      if (!this.accountForm.password) {\r\n        this.errors.password = '请输入登录密码';\r\n      } else if (this.accountForm.password.length < 8) {\r\n        this.errors.password = '密码长度至少为8位';\r\n      } else {\r\n        this.errors.password = '';\r\n      }\r\n    },\r\n\r\n\r\n    getVerificationCode() {\r\n      // 先验证手机号格式\r\n      this.validatePhone();\r\n      if (this.errors.phone) return;\r\n      // 显示发送中状态\r\n      this.codeSent = true;\r\n      this.isSendingCode = true;\r\n      // 调用发送验证码接口\r\n      postLogin(\"/auth/sendCode\", { phone: this.phoneForm.phone}).then(res => {\r\n        if (res.data.code === 200) {\r\n          // 存储验证码发送时间和手机号到localStorage\r\n          const now = new Date().getTime();\r\n          const expireTime = now + (60 * 1000); // 当前时间 + 60秒\r\n\r\n          localStorage.setItem('verificationPhone', this.phoneForm.phone);\r\n          localStorage.setItem('verificationExpireTime', expireTime.toString());\r\n\r\n          this.showNotificationMessage('验证码已发送，可能会有延迟，请耐心等待！', 'success');\r\n          this.startCountdown();\r\n        } else {\r\n          this.errors.code = res.data.msg || '验证码发送失败';\r\n          this.codeSent = false; // 发送失败时可重新发送\r\n          // this.showNotificationMessage(res.data.msg || '验证码发送失败', 'error');\r\n        }\r\n      })\r\n    },\r\n    validateCode() {\r\n      // 清空错误\r\n      this.errors.code = '';\r\n\r\n      // 前端基础验证（保持原有长度判断）\r\n      if (!this.phoneForm.code) {\r\n        this.errors.code = '请输入验证码';\r\n        return false;\r\n      }\r\n      if (this.phoneForm.code.length !== 4 || !/^\\d+$/.test(this.phoneForm.code)) {\r\n        this.errors.code = '验证码必须为4位数字';\r\n        return false;\r\n      }\r\n\r\n      try {\r\n        // 新增接口验证（参数与发送接口一致）\r\n        postAnyData(\"/auth/verifyCode\", {\r\n          phone: this.phoneForm.phone, // 必须携带手机号\r\n          code: this.phoneForm.code\r\n        }).then(res =>{\r\n          if (res.data.code == 200) {\r\n            postLogin(\"/auth/codeLogin\", {\r\n              phone: this.phoneForm.phone, // 必须携带手机号\r\n              code: this.phoneForm.code\r\n            }).then(res =>{\r\n              if (res.data.code == 200) {\r\n                setToken(res.data.token);\r\n                this.$emit(\"refresh-header\")\r\n                this.$router.push('/index');\r\n              } else if (res.data.code !== 200) {\r\n                this.errors.code = res.data.msg || '验证码错误';\r\n                // this.showNotificationMessage(res.data.msg || '验证码错误', 'error');\r\n                return false;\r\n              }\r\n            })\r\n            return true;\r\n          } else if (res.data.code !== 200) {\r\n            this.errors.code = res.data.msg || '验证码错误';\r\n            // this.showNotificationMessage(res.data.msg || '验证码错误', 'error');\r\n            return false;\r\n          }\r\n        })\r\n        return true;\r\n      } catch (error) {\r\n        this.errors.code = '网络异常，请稍后重试';\r\n        // this.showNotificationMessage('网络异常，请稍后重试', 'error');\r\n        return false;\r\n      }\r\n    },\r\n\r\n    phoneLogin() {\r\n      this.validatePhone();\r\n      this.validateCode();\r\n      if (this.errors.phone || this.errors.code) {\r\n        // 如果有错误，显示提示信息\r\n        const errorMessage = this.errors.phone || this.errors.code || '请正确填写手机号和验证码';\r\n        // this.showNotificationMessage(errorMessage, 'error');\r\n        return; // 如果表单无效，直接返回，不发送请求\r\n      }\r\n      this.generateKeyPair()\r\n      this.$emit(\"refresh-header\")\r\n    },\r\n    accountLogin() {\r\n      this.validateUsername();\r\n      this.validatePassword();\r\n\r\n      // 表单验证失败处理\r\n      if (this.errors.username || this.errors.password) {\r\n        const errorMessage = this.errors.username || this.errors.password || '请正确填写用户名和密码';\r\n        // this.showNotificationMessage(errorMessage, 'error');\r\n        return;\r\n      }\r\n\r\n      // 显示加载状态\r\n      const loading = this.$loading ? this.$loading({\r\n        lock: true,\r\n        text: '登录中...',\r\n        spinner: 'el-icon-loading',\r\n      }) : null;\r\n\r\n      // 调用登录API\r\n      postLogin(\"/auth/login\", this.accountForm)\r\n          .then(res => {\r\n            // 关闭加载状态\r\n            if (loading) loading.close();\r\n\r\n            if (res.data && res.data.code == 200) {\r\n              // 登录成功\r\n              setToken(res.data.token);\r\n              this.showNotificationMessage('登录成功', 'success');\r\n              this.$emit(\"refresh-header\")\r\n              this.$router.push('/index');\r\n              this.generateKeyPair()\r\n            } else {\r\n              // 登录失败，服务器返回了错误码\r\n              this.errors.password = res.data.msg;\r\n              // this.showNotificationMessage(res.data.msg || '登录失败，请检查账号和密码', 'error');\r\n            }\r\n          })\r\n          .catch(error => {\r\n            // 关闭加载状态\r\n            if (loading) loading.close();\r\n\r\n            // 处理网络错误或其他异常\r\n            this.errors.password = '网络异常，请稍后重试';\r\n            // this.showNotificationMessage('网络异常，请稍后重试', 'error');\r\n          });\r\n    },\r\n\r\n    togglePasswordVisibility() {\r\n      this.passwordVisible = !this.passwordVisible;\r\n    }\r\n  },\r\n  beforeDestroy() {\r\n    if (this.timer) {\r\n      clearInterval(this.timer);\r\n    }\r\n    this.$emit('hiden-layout')\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* Full page layout */\r\n.login-page {\r\n  display: flex;\r\n  min-height: 100vh;\r\n  overflow: hidden;\r\n}\r\n\r\n/* Left side styling */\r\n.left-side {\r\n  flex: 1;\r\n  position: relative;\r\n  background: linear-gradient(135deg, #cdb3e5, #5127d5);\r\n  display: flex;\r\n  flex-direction: column;\r\n  padding: 0rem;\r\n  color: #030303;\r\n}\r\n\r\n.logo-container {\r\n  z-index: 2;\r\n  padding: 1rem 0;\r\n}\r\n\r\n.logo {\r\n  font-size: 15px;\r\n  font-weight: bold;\r\n  margin: 0;\r\n  color: white;\r\n}\r\n\r\n.logo-subtitle {\r\n  font-size: 14px;\r\n  margin: 0;\r\n}\r\n\r\n.headline-container {\r\n  margin-top: 10px;\r\n  text-align: left;\r\n  z-index: 2;\r\n  max-width: 80%;\r\n  margin-left: 100px;\r\n}\r\n\r\n.headline {\r\n  font-size: 25px;\r\n  font-weight: 500;\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.subheadline {\r\n  font-size: 14px;\r\n  line-height: 1.6;\r\n  opacity: 0.9;\r\n  margin-bottom: 1rem\r\n}\r\n\r\n/* Animated background */\r\n.animated-background {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  overflow: hidden;\r\n  opacity: 0.4;\r\n}\r\n\r\n.hex-container {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.hex {\r\n  position: absolute;\r\n  width: 100px;\r\n  height: 110px;\r\n  background: rgba(255, 255, 255, 0.15);\r\n  clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);\r\n}\r\n\r\n.hex1 {\r\n  top: 20%;\r\n  left: 10%;\r\n  transform: scale(1.5);\r\n  animation: float 8s infinite ease-in-out;\r\n}\r\n\r\n.hex2 {\r\n  top: 60%;\r\n  left: 20%;\r\n  transform: scale(1.2);\r\n  animation: float 7s infinite ease-in-out reverse;\r\n}\r\n\r\n.hex3 {\r\n  top: 30%;\r\n  left: 50%;\r\n  transform: scale(1.3);\r\n  animation: float 10s infinite ease-in-out 1s;\r\n}\r\n\r\n.hex4 {\r\n  top: 70%;\r\n  left: 70%;\r\n  transform: scale(1.1);\r\n  animation: float 6s infinite ease-in-out 2s;\r\n}\r\n\r\n.hex5 {\r\n  top: 40%;\r\n  left: 80%;\r\n  transform: scale(1.4);\r\n  animation: float 9s infinite ease-in-out 3s;\r\n}\r\n\r\n.floating-cube {\r\n  position: absolute;\r\n  width: 50px;\r\n  height: 50px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  top: 25%;\r\n  left: 30%;\r\n  animation: float 8s infinite ease-in-out, rotate 15s infinite linear;\r\n}\r\n\r\n.floating-sphere {\r\n  position: absolute;\r\n  width: 70px;\r\n  height: 70px;\r\n  border-radius: 50%;\r\n  background: rgba(255, 255, 255, 0.15);\r\n  top: 50%;\r\n  left: 40%;\r\n  animation: float 10s infinite ease-in-out reverse;\r\n}\r\n\r\n.floating-diamond {\r\n  position: absolute;\r\n  width: 40px;\r\n  height: 40px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  transform: rotate(45deg);\r\n  top: 65%;\r\n  left: 60%;\r\n  animation: float 7s infinite ease-in-out 2s;\r\n}\r\n\r\n.ripple-effect {\r\n  position: absolute;\r\n  width: 200px;\r\n  height: 200px;\r\n  border-radius: 50%;\r\n  border: 3px solid rgba(255, 255, 255, 0.1);\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  animation: ripple 6s infinite linear;\r\n}\r\n\r\n@keyframes float {\r\n  0%, 100% {\r\n    transform: translateY(0);\r\n  }\r\n  50% {\r\n    transform: translateY(20px);\r\n  }\r\n}\r\n\r\n@keyframes rotate {\r\n  0% {\r\n    transform: rotate(0deg);\r\n  }\r\n  100% {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n@keyframes ripple {\r\n  0% {\r\n    width: 0;\r\n    height: 0;\r\n    opacity: 0.8;\r\n  }\r\n  100% {\r\n    width: 300px;\r\n    height: 300px;\r\n    opacity: 0;\r\n  }\r\n}\r\n\r\n/* Right side styling */\r\n.right-side {\r\n  flex: 1;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  background-color: #f8f9fa;\r\n  padding: 2rem;\r\n}\r\n\r\n.login-form-container {\r\n  background-color: white;\r\n  border-radius: 8px;\r\n  padding: 2rem;\r\n  width: 100%;\r\n  max-width: 400px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.login-form-container h3 {\r\n  font-size: 24px;\r\n  font-weight: 500;\r\n  margin-bottom: 20px;\r\n  text-align: center;\r\n  color: #333;\r\n}\r\n\r\n.login-tabs {\r\n  display: flex;\r\n  border-bottom: 1px solid #e0e0e0;\r\n  margin-bottom: 20px;\r\n  /*height: 70px;*/\r\n}\r\n\r\n.tab-item {\r\n  flex: 1;\r\n  padding: 10px 0;\r\n  text-align: center;\r\n  cursor: pointer;\r\n  position: relative;\r\n  color: #666;\r\n  transition: color 0.3s;\r\n}\r\n\r\n.tab-item.active {\r\n  color: #4169E1;\r\n  font-weight: 500;\r\n}\r\n\r\n.tab-item.active:after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: -1px;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 2px;\r\n  background-color: #4169E1;\r\n}\r\n\r\n/* 表单容器，定义固定高度 */\r\n.form-container {\r\n  min-height: 300px;\r\n  position: relative;\r\n  height: 330px;\r\n}\r\n\r\n.login-form {\r\n  margin-top: 20px;\r\n  width: 100%;\r\n}\r\n\r\n.form-note {\r\n  color: #999;\r\n  font-size: 12px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.input-group {\r\n  margin-bottom: 20px;\r\n  position: relative;\r\n}\r\n\r\n.input-group input {\r\n  width: 100%;\r\n  padding: 12px 15px;\r\n  border: 1px solid #ddd;\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n  transition: border-color 0.3s;\r\n}\r\n\r\n.input-group input:focus {\r\n  outline: none;\r\n  border-color: #6a26cd;\r\n}\r\n/*错误输入框变红*/\r\n.input-group.error input {\r\n  outline: none;\r\n  border: 2px solid #ff4d4f; /* 红色边框 */\r\n  /*background-color: #fff1f0; !* 淡红色背景 *!*/\r\n}\r\n\r\n\r\n/* 错误信息容器，固定高度 */\r\n.error-container {\r\n  min-height: 10px;\r\n  display: block;\r\n}\r\n\r\n/* 验证码输入框与按钮在同一行 */\r\n.verification-code .code-input-container {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.verification-code input {\r\n  flex: 1;\r\n  margin-right: 0; /* 移除原有的右边距 */\r\n}\r\n\r\n.error-container {\r\n  order: 3; /* 将错误容器放在最下方 */\r\n  width: 100%;\r\n  margin-top: 4px;\r\n}\r\n\r\n.get-code-btn-inline {\r\n  flex-shrink: 0;\r\n  width: 130px;\r\n  border: none;\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n  background-color: #2196f3;\r\n  color: #ffffff;\r\n}\r\n\r\n/* 密码输入容器，确保图标垂直居中 */\r\n.password-input-container {\r\n  position: relative;\r\n}\r\n\r\n.password-toggle {\r\n  position: absolute;\r\n  right: 15px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\ninput[type=\"password\"]::-ms-reveal,\r\ninput[type=\"password\"]::-webkit-credentials-auto-fill-button,\r\ninput[type=\"password\"]::-webkit-clear-button {\r\n  display: none !important;\r\n  pointer-events: none;\r\n}\r\n\r\n.eye-icon {\r\n  display: inline-block;\r\n  width: 20px;\r\n  height: 20px;\r\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z\"></path><circle cx=\"12\" cy=\"12\" r=\"3\"></circle></svg>');\r\n  background-repeat: no-repeat;\r\n  background-position: center;\r\n  opacity: 0.5;\r\n}\r\n\r\n.eye-icon.visible {\r\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z\"></path><circle cx=\"12\" cy=\"12\" r=\"3\"></circle><line x1=\"1\" y1=\"1\" x2=\"23\" y2=\"23\"></line></svg>');\r\n}\r\n\r\n.agreement-text {\r\n  font-size: 12px;\r\n  color: #999;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.agreement-text a {\r\n  color: #4169E1;\r\n  text-decoration: none;\r\n}\r\n\r\n.login-btn {\r\n  width: 100%;\r\n  padding: 12px 0;\r\n  background-color: #2196f3;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 4px;\r\n  font-size: 16px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s;\r\n}\r\n\r\n.login-btn:hover:not(:disabled) {\r\n  background-color: #4169E1;\r\n}\r\n\r\n.login-btn:disabled {\r\n  background-color: #2196f3;\r\n  cursor: not-allowed;\r\n}\r\n/*错误提示词提示*/\r\n.error-message {\r\n  color: #f44336;\r\n  font-size: 12px;\r\n  margin-top: 0px;\r\n  /*margin-left: 5px;*/\r\n  max-height: 10px;\r\n}\r\n\r\n/* 人机验证弹窗样式 */\r\n.captcha-modal {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 1000;\r\n}\r\n\r\n.captcha-container {\r\n  background-color: white;\r\n  border-radius: 8px;\r\n  width: 350px;\r\n  overflow: hidden;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.captcha-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 15px;\r\n  border-bottom: 1px solid #eee;\r\n}\r\n\r\n.captcha-header h4 {\r\n  margin: 0;\r\n  font-size: 18px;\r\n  color: #333;\r\n}\r\n\r\n.close-btn {\r\n  font-size: 24px;\r\n  cursor: pointer;\r\n  color: #999;\r\n}\r\n\r\n.captcha-content {\r\n  padding: 20px;\r\n}\r\n\r\n.captcha-content p {\r\n  margin-top: 0;\r\n  margin-bottom: 20px;\r\n  color: #666;\r\n}\r\n\r\n.logo-area {\r\n  flex: 0 0 auto;\r\n  margin-right: 10px;\r\n}\r\n\r\n.logo-link {\r\n  display: flex;\r\n  align-items: center;\r\n  text-decoration: none;\r\n  cursor: pointer;\r\n}\r\n\r\n.logo-area img {\r\n  height: 30px;\r\n  max-width: 100%;\r\n}\r\n\r\n.logo-text {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-left: 10px;\r\n}\r\n\r\n/* 调整提示文字大小 */\r\n.input-group input::placeholder {\r\n  font-size: 12px;\r\n}\r\n\r\n/* 独立验证码按钮样式 */\r\n.get-code-btn-standalone {\r\n  margin-top: 10px;\r\n  padding: 10px 15px;\r\n  background-color: #2196f3;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s;\r\n}\r\n\r\n.get-code-btn-standalone:hover:not(:disabled) {\r\n  background-color: #5a20b0;\r\n}\r\n\r\n.get-code-btn-standalone:disabled {\r\n  background-color: #9254de;\r\n  cursor: not-allowed;\r\n}\r\n\r\n/* Login link */\r\n.login-link {\r\n  margin-top: 15px;\r\n  text-align: center;\r\n  font-size: 14px;\r\n}\r\n\r\n.login-link a {\r\n  color: #4169E1;\r\n  text-decoration: none;\r\n}\r\n.divider {\r\n  margin: 0 10px;\r\n  color: #ddd;\r\n}\r\n@media screen and (max-width: 768px) {\r\n  .left-side {\r\n    display: none; /* 在手机端隐藏左侧背景 */\r\n  }\r\n\r\n  .right-side {\r\n    flex: 1 0 100%; /* 让右侧占据全部宽度 */\r\n    padding: 1rem; /* 减少内边距以适应小屏幕 */\r\n  }\r\n\r\n  .login-form-container {\r\n    max-width: 100%; /* 让登录表单占据全部可用宽度 */\r\n    box-shadow: none; /* 移除阴影以节省空间 */\r\n    padding: 1.5rem; /* 调整内边距 */\r\n  }\r\n\r\n}\r\n</style>\r\n", "import mod from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./login.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./login.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./login.vue?vue&type=template&id=681ef15d&scoped=true&\"\nimport script from \"./login.vue?vue&type=script&lang=js&\"\nexport * from \"./login.vue?vue&type=script&lang=js&\"\nimport style0 from \"./login.vue?vue&type=style&index=0&id=681ef15d&prod&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"681ef15d\",\n  null\n  \n)\n\nexport default component.exports", "var isCallable = require('../internals/is-callable');\n\nvar $String = String;\nvar $TypeError = TypeError;\n\nmodule.exports = function (argument) {\n  if (typeof argument == 'object' || isCallable(argument)) return argument;\n  throw $TypeError(\"Can't set \" + $String(argument) + ' as a prototype');\n};\n", "var isPrototypeOf = require('../internals/object-is-prototype-of');\n\nvar $TypeError = TypeError;\n\nmodule.exports = function (it, Prototype) {\n  if (isPrototypeOf(Prototype, it)) return it;\n  throw $TypeError('Incorrect invocation');\n};\n", "// eslint-disable-next-line es/no-typed-arrays -- safe\nmodule.exports = typeof ArrayBuffer != 'undefined' && typeof DataView != 'undefined';\n", "'use strict';\nvar NATIVE_ARRAY_BUFFER = require('../internals/array-buffer-basic-detection');\nvar DESCRIPTORS = require('../internals/descriptors');\nvar global = require('../internals/global');\nvar isCallable = require('../internals/is-callable');\nvar isObject = require('../internals/is-object');\nvar hasOwn = require('../internals/has-own-property');\nvar classof = require('../internals/classof');\nvar tryToString = require('../internals/try-to-string');\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\nvar defineBuiltIn = require('../internals/define-built-in');\nvar defineBuiltInAccessor = require('../internals/define-built-in-accessor');\nvar isPrototypeOf = require('../internals/object-is-prototype-of');\nvar getPrototypeOf = require('../internals/object-get-prototype-of');\nvar setPrototypeOf = require('../internals/object-set-prototype-of');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\nvar uid = require('../internals/uid');\nvar InternalStateModule = require('../internals/internal-state');\n\nvar enforceInternalState = InternalStateModule.enforce;\nvar getInternalState = InternalStateModule.get;\nvar Int8Array = global.Int8Array;\nvar Int8ArrayPrototype = Int8Array && Int8Array.prototype;\nvar Uint8ClampedArray = global.Uint8ClampedArray;\nvar Uint8ClampedArrayPrototype = Uint8ClampedArray && Uint8ClampedArray.prototype;\nvar TypedArray = Int8Array && getPrototypeOf(Int8Array);\nvar TypedArrayPrototype = Int8ArrayPrototype && getPrototypeOf(Int8ArrayPrototype);\nvar ObjectPrototype = Object.prototype;\nvar TypeError = global.TypeError;\n\nvar TO_STRING_TAG = wellKnownSymbol('toStringTag');\nvar TYPED_ARRAY_TAG = uid('TYPED_ARRAY_TAG');\nvar TYPED_ARRAY_CONSTRUCTOR = 'TypedArrayConstructor';\n// Fixing native typed arrays in Opera Presto crashes the browser, see #595\nvar NATIVE_ARRAY_BUFFER_VIEWS = NATIVE_ARRAY_BUFFER && !!setPrototypeOf && classof(global.opera) !== 'Opera';\nvar TYPED_ARRAY_TAG_REQUIRED = false;\nvar NAME, Constructor, Prototype;\n\nvar TypedArrayConstructorsList = {\n  Int8Array: 1,\n  Uint8Array: 1,\n  Uint8ClampedArray: 1,\n  Int16Array: 2,\n  Uint16Array: 2,\n  Int32Array: 4,\n  Uint32Array: 4,\n  Float32Array: 4,\n  Float64Array: 8\n};\n\nvar BigIntArrayConstructorsList = {\n  BigInt64Array: 8,\n  BigUint64Array: 8\n};\n\nvar isView = function isView(it) {\n  if (!isObject(it)) return false;\n  var klass = classof(it);\n  return klass === 'DataView'\n    || hasOwn(TypedArrayConstructorsList, klass)\n    || hasOwn(BigIntArrayConstructorsList, klass);\n};\n\nvar getTypedArrayConstructor = function (it) {\n  var proto = getPrototypeOf(it);\n  if (!isObject(proto)) return;\n  var state = getInternalState(proto);\n  return (state && hasOwn(state, TYPED_ARRAY_CONSTRUCTOR)) ? state[TYPED_ARRAY_CONSTRUCTOR] : getTypedArrayConstructor(proto);\n};\n\nvar isTypedArray = function (it) {\n  if (!isObject(it)) return false;\n  var klass = classof(it);\n  return hasOwn(TypedArrayConstructorsList, klass)\n    || hasOwn(BigIntArrayConstructorsList, klass);\n};\n\nvar aTypedArray = function (it) {\n  if (isTypedArray(it)) return it;\n  throw TypeError('Target is not a typed array');\n};\n\nvar aTypedArrayConstructor = function (C) {\n  if (isCallable(C) && (!setPrototypeOf || isPrototypeOf(TypedArray, C))) return C;\n  throw TypeError(tryToString(C) + ' is not a typed array constructor');\n};\n\nvar exportTypedArrayMethod = function (KEY, property, forced, options) {\n  if (!DESCRIPTORS) return;\n  if (forced) for (var ARRAY in TypedArrayConstructorsList) {\n    var TypedArrayConstructor = global[ARRAY];\n    if (TypedArrayConstructor && hasOwn(TypedArrayConstructor.prototype, KEY)) try {\n      delete TypedArrayConstructor.prototype[KEY];\n    } catch (error) {\n      // old WebKit bug - some methods are non-configurable\n      try {\n        TypedArrayConstructor.prototype[KEY] = property;\n      } catch (error2) { /* empty */ }\n    }\n  }\n  if (!TypedArrayPrototype[KEY] || forced) {\n    defineBuiltIn(TypedArrayPrototype, KEY, forced ? property\n      : NATIVE_ARRAY_BUFFER_VIEWS && Int8ArrayPrototype[KEY] || property, options);\n  }\n};\n\nvar exportTypedArrayStaticMethod = function (KEY, property, forced) {\n  var ARRAY, TypedArrayConstructor;\n  if (!DESCRIPTORS) return;\n  if (setPrototypeOf) {\n    if (forced) for (ARRAY in TypedArrayConstructorsList) {\n      TypedArrayConstructor = global[ARRAY];\n      if (TypedArrayConstructor && hasOwn(TypedArrayConstructor, KEY)) try {\n        delete TypedArrayConstructor[KEY];\n      } catch (error) { /* empty */ }\n    }\n    if (!TypedArray[KEY] || forced) {\n      // V8 ~ Chrome 49-50 `%TypedArray%` methods are non-writable non-configurable\n      try {\n        return defineBuiltIn(TypedArray, KEY, forced ? property : NATIVE_ARRAY_BUFFER_VIEWS && TypedArray[KEY] || property);\n      } catch (error) { /* empty */ }\n    } else return;\n  }\n  for (ARRAY in TypedArrayConstructorsList) {\n    TypedArrayConstructor = global[ARRAY];\n    if (TypedArrayConstructor && (!TypedArrayConstructor[KEY] || forced)) {\n      defineBuiltIn(TypedArrayConstructor, KEY, property);\n    }\n  }\n};\n\nfor (NAME in TypedArrayConstructorsList) {\n  Constructor = global[NAME];\n  Prototype = Constructor && Constructor.prototype;\n  if (Prototype) enforceInternalState(Prototype)[TYPED_ARRAY_CONSTRUCTOR] = Constructor;\n  else NATIVE_ARRAY_BUFFER_VIEWS = false;\n}\n\nfor (NAME in BigIntArrayConstructorsList) {\n  Constructor = global[NAME];\n  Prototype = Constructor && Constructor.prototype;\n  if (Prototype) enforceInternalState(Prototype)[TYPED_ARRAY_CONSTRUCTOR] = Constructor;\n}\n\n// WebKit bug - typed arrays constructors prototype is Object.prototype\nif (!NATIVE_ARRAY_BUFFER_VIEWS || !isCallable(TypedArray) || TypedArray === Function.prototype) {\n  // eslint-disable-next-line no-shadow -- safe\n  TypedArray = function TypedArray() {\n    throw TypeError('Incorrect invocation');\n  };\n  if (NATIVE_ARRAY_BUFFER_VIEWS) for (NAME in TypedArrayConstructorsList) {\n    if (global[NAME]) setPrototypeOf(global[NAME], TypedArray);\n  }\n}\n\nif (!NATIVE_ARRAY_BUFFER_VIEWS || !TypedArrayPrototype || TypedArrayPrototype === ObjectPrototype) {\n  TypedArrayPrototype = TypedArray.prototype;\n  if (NATIVE_ARRAY_BUFFER_VIEWS) for (NAME in TypedArrayConstructorsList) {\n    if (global[NAME]) setPrototypeOf(global[NAME].prototype, TypedArrayPrototype);\n  }\n}\n\n// WebKit bug - one more object in Uint8ClampedArray prototype chain\nif (NATIVE_ARRAY_BUFFER_VIEWS && getPrototypeOf(Uint8ClampedArrayPrototype) !== TypedArrayPrototype) {\n  setPrototypeOf(Uint8ClampedArrayPrototype, TypedArrayPrototype);\n}\n\nif (DESCRIPTORS && !hasOwn(TypedArrayPrototype, TO_STRING_TAG)) {\n  TYPED_ARRAY_TAG_REQUIRED = true;\n  defineBuiltInAccessor(TypedArrayPrototype, TO_STRING_TAG, {\n    configurable: true,\n    get: function () {\n      return isObject(this) ? this[TYPED_ARRAY_TAG] : undefined;\n    }\n  });\n  for (NAME in TypedArrayConstructorsList) if (global[NAME]) {\n    createNonEnumerableProperty(global[NAME], TYPED_ARRAY_TAG, NAME);\n  }\n}\n\nmodule.exports = {\n  NATIVE_ARRAY_BUFFER_VIEWS: NATIVE_ARRAY_BUFFER_VIEWS,\n  TYPED_ARRAY_TAG: TYPED_ARRAY_TAG_REQUIRED && TYPED_ARRAY_TAG,\n  aTypedArray: aTypedArray,\n  aTypedArrayConstructor: aTypedArrayConstructor,\n  exportTypedArrayMethod: exportTypedArrayMethod,\n  exportTypedArrayStaticMethod: exportTypedArrayStaticMethod,\n  getTypedArrayConstructor: getTypedArrayConstructor,\n  isView: isView,\n  isTypedArray: isTypedArray,\n  TypedArray: TypedArray,\n  TypedArrayPrototype: TypedArrayPrototype\n};\n", "var lengthOfArrayLike = require('../internals/length-of-array-like');\n\nmodule.exports = function (Constructor, list) {\n  var index = 0;\n  var length = lengthOfArrayLike(list);\n  var result = new Constructor(length);\n  while (length > index) result[index] = list[index++];\n  return result;\n};\n", "var lengthOfArrayLike = require('../internals/length-of-array-like');\n\n// https://tc39.es/proposal-change-array-by-copy/#sec-array.prototype.toReversed\n// https://tc39.es/proposal-change-array-by-copy/#sec-%typedarray%.prototype.toReversed\nmodule.exports = function (O, C) {\n  var len = lengthOfArrayLike(O);\n  var A = new C(len);\n  var k = 0;\n  for (; k < len; k++) A[k] = O[len - k - 1];\n  return A;\n};\n", "var lengthOfArrayLike = require('../internals/length-of-array-like');\nvar toIntegerOrInfinity = require('../internals/to-integer-or-infinity');\n\nvar $RangeError = RangeError;\n\n// https://tc39.es/proposal-change-array-by-copy/#sec-array.prototype.with\n// https://tc39.es/proposal-change-array-by-copy/#sec-%typedarray%.prototype.with\nmodule.exports = function (O, C, index, value) {\n  var len = lengthOfArrayLike(O);\n  var relativeIndex = toIntegerOrInfinity(index);\n  var actualIndex = relativeIndex < 0 ? len + relativeIndex : relativeIndex;\n  if (actualIndex >= len || actualIndex < 0) throw $RangeError('Incorrect index');\n  var A = new C(len);\n  var k = 0;\n  for (; k < len; k++) A[k] = k === actualIndex ? value : O[k];\n  return A;\n};\n", "var TO_STRING_TAG_SUPPORT = require('../internals/to-string-tag-support');\nvar isCallable = require('../internals/is-callable');\nvar classofRaw = require('../internals/classof-raw');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar TO_STRING_TAG = wellKnownSymbol('toStringTag');\nvar $Object = Object;\n\n// ES3 wrong here\nvar CORRECT_ARGUMENTS = classofRaw(function () { return arguments; }()) == 'Arguments';\n\n// fallback for IE11 Script Access Denied error\nvar tryGet = function (it, key) {\n  try {\n    return it[key];\n  } catch (error) { /* empty */ }\n};\n\n// getting tag from ES6+ `Object.prototype.toString`\nmodule.exports = TO_STRING_TAG_SUPPORT ? classofRaw : function (it) {\n  var O, tag, result;\n  return it === undefined ? 'Undefined' : it === null ? 'Null'\n    // @@toStringTag case\n    : typeof (tag = tryGet(O = $Object(it), TO_STRING_TAG)) == 'string' ? tag\n    // builtinTag case\n    : CORRECT_ARGUMENTS ? classofRaw(O)\n    // ES3 arguments fallback\n    : (result = classofRaw(O)) == 'Object' && isCallable(O.callee) ? 'Arguments' : result;\n};\n", "var fails = require('../internals/fails');\n\nmodule.exports = !fails(function () {\n  function F() { /* empty */ }\n  F.prototype.constructor = null;\n  // eslint-disable-next-line es/no-object-getprototypeof -- required for testing\n  return Object.getPrototypeOf(new F()) !== F.prototype;\n});\n", "var makeBuiltIn = require('../internals/make-built-in');\nvar defineProperty = require('../internals/object-define-property');\n\nmodule.exports = function (target, name, descriptor) {\n  if (descriptor.get) makeBuiltIn(descriptor.get, name, { getter: true });\n  if (descriptor.set) makeBuiltIn(descriptor.set, name, { setter: true });\n  return defineProperty.f(target, name, descriptor);\n};\n", "module.exports = {\n  IndexSizeError: { s: 'INDEX_SIZE_ERR', c: 1, m: 1 },\n  DOMStringSizeError: { s: 'DOMSTRING_SIZE_ERR', c: 2, m: 0 },\n  HierarchyRequestError: { s: 'HIERARCHY_REQUEST_ERR', c: 3, m: 1 },\n  WrongDocumentError: { s: 'WRONG_DOCUMENT_ERR', c: 4, m: 1 },\n  InvalidCharacterError: { s: 'INVALID_CHARACTER_ERR', c: 5, m: 1 },\n  NoDataAllowedError: { s: 'NO_DATA_ALLOWED_ERR', c: 6, m: 0 },\n  NoModificationAllowedError: { s: 'NO_MODIFICATION_ALLOWED_ERR', c: 7, m: 1 },\n  NotFoundError: { s: 'NOT_FOUND_ERR', c: 8, m: 1 },\n  NotSupportedError: { s: 'NOT_SUPPORTED_ERR', c: 9, m: 1 },\n  InUseAttributeError: { s: 'INUSE_ATTRIBUTE_ERR', c: 10, m: 1 },\n  InvalidStateError: { s: 'INVALID_STATE_ERR', c: 11, m: 1 },\n  SyntaxError: { s: 'SYNTAX_ERR', c: 12, m: 1 },\n  InvalidModificationError: { s: 'INVALID_MODIFICATION_ERR', c: 13, m: 1 },\n  NamespaceError: { s: 'NAMESPACE_ERR', c: 14, m: 1 },\n  InvalidAccessError: { s: 'INVALID_ACCESS_ERR', c: 15, m: 1 },\n  ValidationError: { s: 'VALIDATION_ERR', c: 16, m: 0 },\n  TypeMismatchError: { s: 'TYPE_MISMATCH_ERR', c: 17, m: 1 },\n  SecurityError: { s: 'SECURITY_ERR', c: 18, m: 1 },\n  NetworkError: { s: 'NETWORK_ERR', c: 19, m: 1 },\n  AbortError: { s: 'ABORT_ERR', c: 20, m: 1 },\n  URLMismatchError: { s: 'URL_MISMATCH_ERR', c: 21, m: 1 },\n  QuotaExceededError: { s: 'QUOTA_EXCEEDED_ERR', c: 22, m: 1 },\n  TimeoutError: { s: 'TIMEOUT_ERR', c: 23, m: 1 },\n  InvalidNodeTypeError: { s: 'INVALID_NODE_TYPE_ERR', c: 24, m: 1 },\n  DataCloneError: { s: 'DATA_CLONE_ERR', c: 25, m: 1 }\n};\n", "var uncurryThis = require('../internals/function-uncurry-this');\n\nvar $Error = Error;\nvar replace = uncurryThis(''.replace);\n\nvar TEST = (function (arg) { return String($Error(arg).stack); })('zxcasd');\n// eslint-disable-next-line redos/no-vulnerable -- safe\nvar V8_OR_CHAKRA_STACK_ENTRY = /\\n\\s*at [^:]*:[^\\n]*/;\nvar IS_V8_OR_CHAKRA_STACK = V8_OR_CHAKRA_STACK_ENTRY.test(TEST);\n\nmodule.exports = function (stack, dropEntries) {\n  if (IS_V8_OR_CHAKRA_STACK && typeof stack == 'string' && !$Error.prepareStackTrace) {\n    while (dropEntries--) stack = replace(stack, V8_OR_CHAKRA_STACK_ENTRY, '');\n  } return stack;\n};\n", "var uncurryThis = require('../internals/function-uncurry-this');\nvar aCallable = require('../internals/a-callable');\n\nmodule.exports = function (object, key, method) {\n  try {\n    // eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\n    return uncurryThis(aCallable(Object.getOwnPropertyDescriptor(object, key)[method]));\n  } catch (error) { /* empty */ }\n};\n", "var isCallable = require('../internals/is-callable');\nvar isObject = require('../internals/is-object');\nvar setPrototypeOf = require('../internals/object-set-prototype-of');\n\n// makes subclassing work correct for wrapped built-ins\nmodule.exports = function ($this, dummy, Wrapper) {\n  var NewTarget, NewTargetPrototype;\n  if (\n    // it can work only with native `setPrototypeOf`\n    setPrototypeOf &&\n    // we haven't completely correct pre-ES6 way for getting `new.target`, so use this\n    isCallable(NewTarget = dummy.constructor) &&\n    NewTarget !== Wrapper &&\n    isObject(NewTargetPrototype = NewTarget.prototype) &&\n    NewTargetPrototype !== Wrapper.prototype\n  ) setPrototypeOf($this, NewTargetPrototype);\n  return $this;\n};\n", "var classof = require('../internals/classof');\n\nmodule.exports = function (it) {\n  var klass = classof(it);\n  return klass == 'BigInt64Array' || klass == 'BigUint64Array';\n};\n", "var toString = require('../internals/to-string');\n\nmodule.exports = function (argument, $default) {\n  return argument === undefined ? arguments.length < 2 ? '' : $default : toString(argument);\n};\n", "var hasOwn = require('../internals/has-own-property');\nvar isCallable = require('../internals/is-callable');\nvar toObject = require('../internals/to-object');\nvar sharedKey = require('../internals/shared-key');\nvar CORRECT_PROTOTYPE_GETTER = require('../internals/correct-prototype-getter');\n\nvar IE_PROTO = sharedKey('IE_PROTO');\nvar $Object = Object;\nvar ObjectPrototype = $Object.prototype;\n\n// `Object.getPrototypeOf` method\n// https://tc39.es/ecma262/#sec-object.getprototypeof\n// eslint-disable-next-line es/no-object-getprototypeof -- safe\nmodule.exports = CORRECT_PROTOTYPE_GETTER ? $Object.getPrototypeOf : function (O) {\n  var object = toObject(O);\n  if (hasOwn(object, IE_PROTO)) return object[IE_PROTO];\n  var constructor = object.constructor;\n  if (isCallable(constructor) && object instanceof constructor) {\n    return constructor.prototype;\n  } return object instanceof $Object ? ObjectPrototype : null;\n};\n", "/* eslint-disable no-proto -- safe */\nvar uncurryThisAccessor = require('../internals/function-uncurry-this-accessor');\nvar anObject = require('../internals/an-object');\nvar aPossiblePrototype = require('../internals/a-possible-prototype');\n\n// `Object.setPrototypeOf` method\n// https://tc39.es/ecma262/#sec-object.setprototypeof\n// Works with __proto__ only. Old v8 can't work with null proto objects.\n// eslint-disable-next-line es/no-object-setprototypeof -- safe\nmodule.exports = Object.setPrototypeOf || ('__proto__' in {} ? function () {\n  var CORRECT_SETTER = false;\n  var test = {};\n  var setter;\n  try {\n    setter = uncurryThisAccessor(Object.prototype, '__proto__', 'set');\n    setter(test, []);\n    CORRECT_SETTER = test instanceof Array;\n  } catch (error) { /* empty */ }\n  return function setPrototypeOf(O, proto) {\n    anObject(O);\n    aPossiblePrototype(proto);\n    if (CORRECT_SETTER) setter(O, proto);\n    else O.__proto__ = proto;\n    return O;\n  };\n}() : undefined);\n", "var toPrimitive = require('../internals/to-primitive');\n\nvar $TypeError = TypeError;\n\n// `ToBigInt` abstract operation\n// https://tc39.es/ecma262/#sec-tobigint\nmodule.exports = function (argument) {\n  var prim = toPrimitive(argument, 'number');\n  if (typeof prim == 'number') throw $TypeError(\"Can't convert number to bigint\");\n  // eslint-disable-next-line es/no-bigint -- safe\n  return BigInt(prim);\n};\n", "var wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar TO_STRING_TAG = wellKnownSymbol('toStringTag');\nvar test = {};\n\ntest[TO_STRING_TAG] = 'z';\n\nmodule.exports = String(test) === '[object z]';\n", "var classof = require('../internals/classof');\n\nvar $String = String;\n\nmodule.exports = function (argument) {\n  if (classof(argument) === 'Symbol') throw TypeError('Cannot convert a Symbol value to a string');\n  return $String(argument);\n};\n", "'use strict';\nvar arrayToReversed = require('../internals/array-to-reversed');\nvar ArrayBufferViewCore = require('../internals/array-buffer-view-core');\n\nvar aTypedArray = ArrayBufferViewCore.aTypedArray;\nvar exportTypedArrayMethod = ArrayBufferViewCore.exportTypedArrayMethod;\nvar getTypedArrayConstructor = ArrayBufferViewCore.getTypedArrayConstructor;\n\n// `%TypedArray%.prototype.toReversed` method\n// https://tc39.es/proposal-change-array-by-copy/#sec-%typedarray%.prototype.toReversed\nexportTypedArrayMethod('toReversed', function toReversed() {\n  return arrayToReversed(aTypedArray(this), getTypedArrayConstructor(this));\n});\n", "'use strict';\nvar ArrayBufferViewCore = require('../internals/array-buffer-view-core');\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar aCallable = require('../internals/a-callable');\nvar arrayFromConstructorAndList = require('../internals/array-from-constructor-and-list');\n\nvar aTypedArray = ArrayBufferViewCore.aTypedArray;\nvar getTypedArrayConstructor = ArrayBufferViewCore.getTypedArrayConstructor;\nvar exportTypedArrayMethod = ArrayBufferViewCore.exportTypedArrayMethod;\nvar sort = uncurryThis(ArrayBufferViewCore.TypedArrayPrototype.sort);\n\n// `%TypedArray%.prototype.toSorted` method\n// https://tc39.es/proposal-change-array-by-copy/#sec-%typedarray%.prototype.toSorted\nexportTypedArrayMethod('toSorted', function toSorted(compareFn) {\n  if (compareFn !== undefined) aCallable(compareFn);\n  var O = aTypedArray(this);\n  var A = arrayFromConstructorAndList(getTypedArrayConstructor(O), O);\n  return sort(A, compareFn);\n});\n", "'use strict';\nvar arrayWith = require('../internals/array-with');\nvar ArrayBufferViewCore = require('../internals/array-buffer-view-core');\nvar isBigIntArray = require('../internals/is-big-int-array');\nvar toIntegerOrInfinity = require('../internals/to-integer-or-infinity');\nvar toBigInt = require('../internals/to-big-int');\n\nvar aTypedArray = ArrayBufferViewCore.aTypedArray;\nvar getTypedArrayConstructor = ArrayBufferViewCore.getTypedArrayConstructor;\nvar exportTypedArrayMethod = ArrayBufferViewCore.exportTypedArrayMethod;\n\nvar PROPER_ORDER = !!function () {\n  try {\n    // eslint-disable-next-line no-throw-literal, es/no-typed-arrays, es/no-array-prototype-with -- required for testing\n    new Int8Array(1)['with'](2, { valueOf: function () { throw 8; } });\n  } catch (error) {\n    // some early implementations, like WebKit, does not follow the final semantic\n    // https://github.com/tc39/proposal-change-array-by-copy/pull/86\n    return error === 8;\n  }\n}();\n\n// `%TypedArray%.prototype.with` method\n// https://tc39.es/proposal-change-array-by-copy/#sec-%typedarray%.prototype.with\nexportTypedArrayMethod('with', { 'with': function (index, value) {\n  var O = aTypedArray(this);\n  var relativeIndex = toIntegerOrInfinity(index);\n  var actualValue = isBigIntArray(O) ? toBigInt(value) : +value;\n  return arrayWith(O, getTypedArrayConstructor(O), relativeIndex, actualValue);\n} }['with'], !PROPER_ORDER);\n", "// TODO: Remove from `core-js@4`\nrequire('../modules/es.typed-array.to-reversed');\n", "// TODO: Remove from `core-js@4`\nrequire('../modules/es.typed-array.to-sorted');\n", "// TODO: Remove from `core-js@4`\nrequire('../modules/es.typed-array.with');\n", "'use strict';\nvar $ = require('../internals/export');\nvar global = require('../internals/global');\nvar getBuiltIn = require('../internals/get-built-in');\nvar createPropertyDescriptor = require('../internals/create-property-descriptor');\nvar defineProperty = require('../internals/object-define-property').f;\nvar hasOwn = require('../internals/has-own-property');\nvar anInstance = require('../internals/an-instance');\nvar inheritIfRequired = require('../internals/inherit-if-required');\nvar normalizeStringArgument = require('../internals/normalize-string-argument');\nvar DOMExceptionConstants = require('../internals/dom-exception-constants');\nvar clearErrorStack = require('../internals/error-stack-clear');\nvar DESCRIPTORS = require('../internals/descriptors');\nvar IS_PURE = require('../internals/is-pure');\n\nvar DOM_EXCEPTION = 'DOMException';\nvar Error = getBuiltIn('Error');\nvar NativeDOMException = getBuiltIn(DOM_EXCEPTION);\n\nvar $DOMException = function DOMException() {\n  anInstance(this, DOMExceptionPrototype);\n  var argumentsLength = arguments.length;\n  var message = normalizeStringArgument(argumentsLength < 1 ? undefined : arguments[0]);\n  var name = normalizeStringArgument(argumentsLength < 2 ? undefined : arguments[1], 'Error');\n  var that = new NativeDOMException(message, name);\n  var error = Error(message);\n  error.name = DOM_EXCEPTION;\n  defineProperty(that, 'stack', createPropertyDescriptor(1, clearErrorStack(error.stack, 1)));\n  inheritIfRequired(that, this, $DOMException);\n  return that;\n};\n\nvar DOMExceptionPrototype = $DOMException.prototype = NativeDOMException.prototype;\n\nvar ERROR_HAS_STACK = 'stack' in Error(DOM_EXCEPTION);\nvar DOM_EXCEPTION_HAS_STACK = 'stack' in new NativeDOMException(1, 2);\n\n// eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\nvar descriptor = NativeDOMException && DESCRIPTORS && Object.getOwnPropertyDescriptor(global, DOM_EXCEPTION);\n\n// Bun ~ 0.1.1 DOMException have incorrect descriptor and we can't redefine it\n// https://github.com/Jarred-Sumner/bun/issues/399\nvar BUGGY_DESCRIPTOR = !!descriptor && !(descriptor.writable && descriptor.configurable);\n\nvar FORCED_CONSTRUCTOR = ERROR_HAS_STACK && !BUGGY_DESCRIPTOR && !DOM_EXCEPTION_HAS_STACK;\n\n// `DOMException` constructor patch for `.stack` where it's required\n// https://webidl.spec.whatwg.org/#es-DOMException-specialness\n$({ global: true, constructor: true, forced: IS_PURE || FORCED_CONSTRUCTOR }, { // TODO: fix export logic\n  DOMException: FORCED_CONSTRUCTOR ? $DOMException : NativeDOMException\n});\n\nvar PolyfilledDOMException = getBuiltIn(DOM_EXCEPTION);\nvar PolyfilledDOMExceptionPrototype = PolyfilledDOMException.prototype;\n\nif (PolyfilledDOMExceptionPrototype.constructor !== PolyfilledDOMException) {\n  if (!IS_PURE) {\n    defineProperty(PolyfilledDOMExceptionPrototype, 'constructor', createPropertyDescriptor(1, PolyfilledDOMException));\n  }\n\n  for (var key in DOMExceptionConstants) if (hasOwn(DOMExceptionConstants, key)) {\n    var constant = DOMExceptionConstants[key];\n    var constantName = constant.s;\n    if (!hasOwn(PolyfilledDOMException, constantName)) {\n      defineProperty(PolyfilledDOMException, constantName, createPropertyDescriptor(6, constant.c));\n    }\n  }\n}\n"], "names": ["render", "_vm", "this", "_c", "_self", "_setupProxy", "staticClass", "on", "$event", "navigateTo", "attrs", "require", "_m", "_l", "servers", "server", "index", "key", "style", "animationDelay", "transform", "features", "feature", "_v", "_s", "title", "description", "i", "left", "Math", "random", "top", "animationDuration", "staticRenderFns", "PerformanceIcon", "template", "ServerIcon", "ShieldIcon", "defineComponent", "name", "components", "methods", "path", "currentPath", "previousActivePath", "$nextTick", "navLinks", "document", "querySelectorAll", "for<PERSON>ach", "link", "classList", "contains", "add", "setTimeout", "remove", "$route", "window", "scrollTo", "behavior", "$router", "go", "push", "setup", "logoSrc", "ref", "Array", "fill", "icon", "component", "showNotification", "notificationMessage", "notificationType", "minHeight", "_e", "class", "activeTab", "errors", "phone", "directives", "rawName", "value", "phoneForm", "expression", "domProps", "validatePhone", "target", "composing", "$set", "code", "validate<PERSON><PERSON><PERSON><PERSON>", "codeSent", "getVerificationCode", "countdown", "phoneLogin", "username", "accountForm", "validateUsername", "password", "passwordVisible", "isArray", "_i", "validatePassword", "$$a", "$$el", "$$c", "checked", "$$v", "$$i", "concat", "slice", "_q", "togglePasswordVisibility", "accountLogin", "SlideNotification", "backgroundlogin", "data", "timer", "created", "checkCountdownState", "$emit", "activated", "showNotificationMessage", "message", "type", "error", "keyPair", "crypto", "subtle", "<PERSON><PERSON>ey", "modulus<PERSON>ength", "publicExponent", "Uint8Array", "hash", "exportedPublicKey", "exportKey", "public<PERSON>ey", "publicKeyPem", "btoa", "String", "fromCharCode", "exportedPrivateKey", "privateKey", "privateKeyPem", "Cookies", "err", "isLoading", "storedPhone", "localStorage", "getItem", "expireTime", "now", "Date", "getTime", "timeLeft", "ceil", "parseInt", "startCountdown", "clearCountdownStorage", "removeItem", "clearInterval", "phoneRegex", "test", "length", "setInterval", "setItem", "toString", "isSendingCode", "postLogin", "then", "res", "msg", "validateCode", "postAnyData", "setToken", "token", "generateKeyPair", "loading", "$loading", "lock", "text", "spinner", "close", "catch", "<PERSON><PERSON><PERSON><PERSON>", "isCallable", "$String", "$TypeError", "TypeError", "module", "exports", "argument", "isPrototypeOf", "it", "Prototype", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DataView", "NAME", "<PERSON><PERSON><PERSON><PERSON>", "NATIVE_ARRAY_BUFFER", "DESCRIPTORS", "global", "isObject", "hasOwn", "classof", "tryToString", "createNonEnumerableProperty", "defineBuiltIn", "defineBuiltInAccessor", "getPrototypeOf", "setPrototypeOf", "wellKnownSymbol", "uid", "InternalStateModule", "enforceInternalState", "enforce", "getInternalState", "get", "Int8Array", "Int8ArrayPrototype", "prototype", "Uint8ClampedArray", "Uint8ClampedArrayPrototype", "TypedArray", "TypedArrayPrototype", "ObjectPrototype", "Object", "TO_STRING_TAG", "TYPED_ARRAY_TAG", "TYPED_ARRAY_CONSTRUCTOR", "NATIVE_ARRAY_BUFFER_VIEWS", "opera", "TYPED_ARRAY_TAG_REQUIRED", "TypedArrayConstructorsList", "Int16Array", "Uint16Array", "Int32Array", "Uint32Array", "Float32Array", "Float64Array", "BigIntArrayConstructorsList", "BigInt64Array", "BigUint64Array", "<PERSON><PERSON><PERSON><PERSON>", "klass", "getTypedArrayConstructor", "proto", "state", "isTypedArray", "aTypedArray", "aTypedArrayConstructor", "C", "exportTypedArrayMethod", "KEY", "property", "forced", "options", "ARRAY", "TypedArrayConstructor", "error2", "exportTypedArrayStaticMethod", "Function", "configurable", "undefined", "lengthOfArrayLike", "list", "result", "O", "len", "A", "k", "toIntegerOrInfinity", "$RangeError", "RangeError", "relativeIndex", "actualIndex", "TO_STRING_TAG_SUPPORT", "classofRaw", "$Object", "CORRECT_ARGUMENTS", "arguments", "tryGet", "tag", "callee", "fails", "F", "constructor", "makeBuiltIn", "defineProperty", "descriptor", "getter", "set", "setter", "f", "IndexSizeError", "s", "c", "m", "DOMStringSizeError", "HierarchyRequestError", "WrongDocumentError", "InvalidCharacterError", "NoDataAllowedError", "NoModificationAllowedError", "NotFoundError", "NotSupportedError", "InUseAttributeError", "InvalidStateError", "SyntaxError", "InvalidModificationError", "NamespaceError", "InvalidAccessError", "ValidationError", "TypeMismatchError", "SecurityError", "NetworkError", "AbortError", "URLMismatchError", "QuotaExceededError", "TimeoutError", "InvalidNodeTypeError", "DataCloneError", "uncurryThis", "$Error", "Error", "replace", "TEST", "arg", "stack", "V8_OR_CHAKRA_STACK_ENTRY", "IS_V8_OR_CHAKRA_STACK", "dropEntries", "prepareStackTrace", "aCallable", "object", "method", "getOwnPropertyDescriptor", "$this", "dummy", "Wrapper", "<PERSON><PERSON><PERSON><PERSON>", "NewTargetPrototype", "$default", "toObject", "sharedKey", "CORRECT_PROTOTYPE_GETTER", "IE_PROTO", "uncurry<PERSON><PERSON><PERSON><PERSON><PERSON>or", "anObject", "aPossiblePrototype", "CORRECT_SETTER", "__proto__", "toPrimitive", "prim", "BigInt", "arrayToReversed", "ArrayBufferViewCore", "arrayFromConstructorAndList", "sort", "compareFn", "arrayWith", "isBigIntArray", "toBigInt", "PROPER_ORDER", "valueOf", "actualValue", "$", "getBuiltIn", "createPropertyDescriptor", "anInstance", "inheritIfRequired", "normalizeStringArgument", "DOMExceptionConstants", "clearErrorStack", "IS_PURE", "DOM_EXCEPTION", "NativeDOMException", "$DOMException", "DOMExceptionPrototype", "<PERSON><PERSON><PERSON><PERSON>", "that", "ERROR_HAS_STACK", "DOM_EXCEPTION_HAS_STACK", "BUGGY_DESCRIPTOR", "writable", "FORCED_CONSTRUCTOR", "DOMException", "PolyfilledDOMException", "PolyfilledDOMExceptionPrototype", "constant", "constant<PERSON>ame"], "sourceRoot": ""}