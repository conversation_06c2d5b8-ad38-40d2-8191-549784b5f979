# <font style="color:rgb(2, 8, 23);">容器化部署 FramePack-F1 图生视频框架</font>
## <font style="color:rgb(2, 8, 23);">1 部署步骤</font>
<font style="color:rgb(2, 8, 23);">我们提供了构建完毕的 FramePack-F1 镜像可以直接部署使用。</font>

### <font style="color:rgb(2, 8, 23);">1.1 访问</font>[天工开物控制台](https://tiangongkaiwu.top/#/console)<font style="color:rgb(2, 8, 23);">，点击新增部署。</font>
![](./imgs/universal1.png)

### <font style="color:rgb(2, 8, 23);">1.2 基于自身需要进行配置，参考配置为单卡 4090 和 1 个节点（初次使用进行调试）。</font>
![](./imgs/universal2.png)

### <font style="color:rgb(2, 8, 23);">1.3 选择相应预制镜像</font>
![](./imgs/frame-pack3.png)

### <font style="color:rgb(2, 8, 23);">1.4 点击部署服务，耐心等待节点拉取镜像并启动。</font>
![](./imgs/frame-pack4.png)

### <font style="color:rgb(2, 8, 23);">1.5 节点启动后，你所在“任务详情页”中看到的内容可能如下：</font>
![](./imgs/frame-pack5.png)

### <font style="color:rgb(2, 8, 23);">1.6 我们可以点击快速访问下方“7860”端口的链接，测试 Gradio 运行情况</font>
<font style="color:rgb(2, 8, 23);">我们首先点击该输入框上传一张图片，如下图：</font>

![](./imgs/frame-pack6.png)

<font style="color:rgb(2, 8, 23);">接下来填写 prompt（该模型对英文支持性较好），描述我们希望图片中的人物如何活动。</font>

<font style="color:rgb(2, 8, 23);">最后点击生成按钮，可以看到右侧已经出现了一个预览框，并且下方也出现了进度条，接下来我们耐心稍等片刻：</font>

![](./imgs/frame-pack7.png)

### <font style="color:rgb(2, 8, 23);">1.7 保存视频</font>
<font style="color:rgb(2, 8, 23);">如果我们需要保存该视频到本地，可以在视频生成完毕后，点击视频右上角的下载按钮：</font>

![](./imgs/frame-pack8.png)

<font style="color:rgb(2, 8, 23);">或者我们也可以选择直接右键该视频，选择“视频另存为”，选择想要保存的位置：</font>

![](./imgs/frame-pack9.png)

<br><br>
<font style="color:rgba(178,178,178,1);">最后更新于: 2025/6/17 17:22</font>
