.example-container .am-nav-tabs > li.am-active > a, .example-container .am-nav-tabs > li.am-active > a:focus, .example-container .am-nav-tabs > li.am-active > a:hover {
    color: #59bcdb;
    background-color: #fff;
    border: 0 solid #ddd;
    border-bottom-color: transparent;
    cursor: default
}
.example{
    /*display: flex;*/
    width: 100%
}
.example-container {
    margin-top: 50px
}

.example-container .am-nav-tabs {
    border-bottom: 0;
    border-top: 1px solid #e9e9e9
}

.example-container .am-nav-tabs li.am-active {
    border-top: 3px solid #59bcdb
}

.example-container .am-nav-tabs li a {
    padding: 24px 0 10px;
    text-align: center;
    color: #262626
}

.example-container .am-nav-tabs li a i {
    margin-right: 10px
}

.example-container .am-tabs-bd {
    margin: 33px 0 0;
    color: #262626;
    border: 0
}

.example-container .am-tabs-bd div[class^=am-tab-pane] {
    padding: 0
}

.example-container .am-tabs-bd .example-item-bg {
    background-size: cover;
    position: relative;
    display: block;
    height: 200px;
    margin-top: 30px;
    border-radius: 3px;
    overflow: hidden
}

.example-container .am-tabs-bd img {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: block
}

.example-container .am-tabs-bd span {
    display: none;
    color: #fff;
    font-size: 14px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%)
}

.example-container .am-g .am-u-md-3:nth-of-type(1) {
    padding-left: 0
}

.example-container .am-g .am-u-md-3:nth-of-type(4) {
    padding-right: 0
}

/* ========================================================*/

.tabs{
    width: 100%;
    height: 100%;
    /*background: red;*/
    display: flex;
    flex-wrap: wrap;
}
.tab {
    margin: 5px 6.5px;
    border-radius: 10px;
    margin-top: 10px;
    /*background: #10a0ea;*/
    /*padding: 4px;*/
}
.tab img{
    width: 277px;
    height: 300px;
    background-repeat: no-repeat;
    background-position: top;
    border-radius: 10px;
    background: black;
    opacity: 0.8;
}

.tab img:hover{
    opacity: 1;
}

@media screen and (max-width: 640px) {
    .tabs{
        display: flex;
        justify-content: center;
        /*align-items: center;*/
    }
}

