# 常见问题
## <font style="color:rgb(2, 8, 23);">平台使用</font>
### <font style="color:rgb(2, 8, 23);">如何实现弹性扩缩容？</font>
<font style="color:rgb(2, 8, 23);">支持随时根据服务负载情况修改 GPU 数量。设置办法：</font>

1. <font style="color:rgb(2, 8, 23);">服务部署阶段，选择合适的节点数量（推荐先选一个，后续再根据需要随时添加）</font>
2. <font style="color:rgb(2, 8, 23);">在对应服务页面，点击左侧边栏的【设置】，修改服务运行的节点数量。节点越多，性能越好，但也会增加成本</font>

### <font style="color:rgb(2, 8, 23);">什么是 Serverless 与无状态？</font>
+ <font style="color:rgb(2, 8, 23);">本系统采用无状态部署方式，默认情况下，不会对历史状态及数据进行存储。若您对服务状态存在特定要求，则需自行实现相应的存储功能。</font>
+ <font style="color:rgb(2, 8, 23);">该部署方式更适用于推理场景，能够在应用上线后有效满足生产需求。然而，对于训练及科学计算研发等需求，您可能需要依据实际情况进行判断。</font>

### <font style="color:rgb(2, 8, 23);">弹性部署与其他平台的容器实例（或虚拟机）有什么区别？</font>
1. <font style="color:rgb(2, 8, 23);">我们并未采用直接租赁实例这一常规方式，而是基于系统负载均衡机制，为用户动态的节点分配策略。在此过程中，流量自特定来源汇聚，随后被导向不同的目的地。</font>

![](./imgs/qustion1.png)

2. <font style="color:rgb(2, 8, 23);">容器实例或虚拟机在关机后，存在一段数据保留期。然而，弹性部署的容器在关机后会即刻释放数据，并无数据保留情况。</font>

## <font style="color:rgb(2, 8, 23);">部署阶段</font>
### <font style="color:rgb(2, 8, 23);">拉取镜像时间长，不知道拉取完了没</font>
<font style="color:rgb(2, 8, 23);">根据镜像的规模大小，首次拉取或许会耗费一定的下载时长。您能够查看节点详情中的事件记录，确认是否存在拉取镜像的相关事件。若长时间未能成功拉取，且事件呈现异常状态，建议您与技术人员取得联系，以便他们为您进行排查与处理。</font>

### <font style="color:rgb(2, 8, 23);">快捷访问点开后域名链接报错</font>
<font style="color:rgb(103, 103, 108);">upstream connect error or disconnect/reset before headers. retried and the latest reset reason: remote connection failure, transport failure reason: delayed connect error: Connection refused</font>

<font style="color:rgb(2, 8, 23);">当前域名解析可能尚未生效。请等待数秒后，刷新页面并再次尝试。</font>

![](./imgs/qustion2.png)

### <font style="color:rgb(2, 8, 23);">点击快捷访问端口后出现</font><font style="color:rgb(2, 8, 23);background-color:rgba(142, 150, 170, 0.14);">no healthy upstream</font>
![](./imgs/qustion3.png)

<font style="color:rgb(2, 8, 23);">该错误表明 API 网关或负载均衡器无法找到可用的健康后端服务实例来处理请求。</font>

<font style="color:rgb(2, 8, 23);">解决措施：</font>


1. <font style="color:rgb(2, 8, 23);">检查后端服务状态</font>
+ **<font style="color:rgb(2, 8, 23);">确认服务是否运行</font>**
+ **<font style="color:rgb(2, 8, 23);">查看服务日志</font>**<font style="color:rgb(2, 8, 23);">：通过日志定位崩溃原因</font>
2. <font style="color:rgb(2, 8, 23);">验证健康检查配置</font>
+ **<font style="color:rgb(2, 8, 23);">检查健康检查接口</font>**
+ **<font style="color:rgb(2, 8, 23);">调整健康检查参数：</font>**<font style="color:rgb(2, 8, 23);">在网关配置中增加健康检查的超时时间或重试次数（避免因短暂延迟误判）</font>

### <font style="color:rgb(2, 8, 23);">Huggingface 下载太慢了怎么办？</font>
<font style="color:rgb(2, 8, 23);">鉴于 Hugging Face 服务器位于海外，而我们自身的服务器处于国内环境。建议您参照以下文章内容，对 Hugging Face 加速代理源进行配置。完成配置后，即可实现模型的高速下载。</font>

[<font style="color:#2F8EF4;">https://zhuanlan.zhihu.com/p/663712983</font>](https://zhuanlan.zhihu.com/p/663712983)

<br><br>
<font style="color:rgba(178,178,178,1);">最后更新于: 2025/6/11 15:24</font>