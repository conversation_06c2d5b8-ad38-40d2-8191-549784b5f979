"use strict";(self["webpackChunkportal_ui"]=self["webpackChunkportal_ui"]||[]).push([[1910],{3644:function(t,e,s){s.d(e,{Z:function(){return l}});var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"mider-container"},[e("div",{staticClass:"mider-sidebar"},[e("div",{staticClass:"icon-wrapper"},[e("div",{staticClass:"icon-item",on:{mouseenter:function(e){return t.showPopup("wechat")},mouseleave:function(e){return t.hidePopup("wechat")}}},[e("i",{staticClass:"iconfont icon-wechat"},[e("svg",{attrs:{viewBox:"0 0 1024 1024",width:"24",height:"24"}},[e("path",{attrs:{d:"M690.1 377.4c5.9 0 11.8 0.2 17.6 0.5-24.4-128.7-158.3-227.1-319.9-227.1C209 150.8 64 271.4 64 420.2c0 81.1 43.6 154.2 111.9 203.6 5.5 3.9 9.1 10.3 9.1 17.6 0 2.4-0.5 4.6-1.1 6.9-5.5 20.3-14.2 52.8-14.6 54.3-0.7 2.6-1.7 5.2-1.7 7.9 0 5.9 4.8 10.8 10.8 10.8 2.3 0 4.2-0.9 6.2-2l70.9-40.9c5-2.9 10.5-4.6 16.3-4.6 3 0 5.9 0.5 8.7 1.4 35.9 10.5 74.6 16.2 114.2 16.2 9.8 0 19.5-0.3 29-1C624.9 597 702.1 497.6 702.1 378.1c0-0.2-0.1-0.4-0.1-0.7 0 0-0.1-0.3-0.1-0.5-0.8 0-1.8-0.1-2.8-0.1-3.1 0-6.2 0.2-9 0.6z",fill:"#82c91e"}}),e("path",{attrs:{d:"M380.5 503.3c-27.9 0-50.7-22.8-50.7-50.7s22.8-50.7 50.7-50.7 50.7 22.8 50.7 50.7-22.8 50.7-50.7 50.7zM534.3 503.3c-27.9 0-50.7-22.8-50.7-50.7s22.8-50.7 50.7-50.7 50.7 22.8 50.7 50.7-22.8 50.7-50.7 50.7z",fill:"#82c91e"}}),e("path",{attrs:{d:"M683.5 673.6c82.1 0 151.2-56 171.5-131.8 4.1-15.2-9.3-29.5-24.9-26.6-3.5 0.6-7.1 1-10.7 1-34.4 0-62.4-28-62.4-62.4 0-1.6 0.1-3.3 0.2-4.9 0.8-19.3-24.5-28.7-35.7-13.5-39.1 53-102.7 87.5-174.5 87.5-12.3 0-24.4-1-36.1-3-5.2-0.9-10.5 0.8-14.1 4.5-4.6 4.6-16.7 17.2-16.7 17.2l-0.6 0.6c-5.4 5.4-8.7 12.3-9.5 19.8-0.9 8.5 1.9 17.1 7.7 23.6l1.3 1.3c6.3 7.5 15.6 11.7 25.6 11.7 7.1 0 14.1-2.3 19.7-6.5l10.6-7.8c6.2-4.5 14.8-3.3 19.3 2.8 4.3 5.8 3.4 14-2.1 18.8-14.4 12.6-32.9 19.7-51.9 19.7-4.3 0-8.7-0.3-13-1-21.3-3.5-40.2-14.4-54.3-31.3l-0.3-0.3c-12.9-15.7-19.4-34.9-18.3-54.5 0.8-14.5 6.4-28.5 16.1-39.7 1.2-1.4 2.5-2.6 3.8-3.9l31.5-31.5c1.9-1.9 3.8-3.9 5.4-6.1 5.8-7.7 8.9-17 8.9-26.8 0-11.3-4.2-22.1-11.8-30.4-12.4-13.5-31.5-15.2-48.5-11.7-91.3 18.9-160.7 100.1-160.8 196.9 0 109.5 89.1 198.6 198.6 198.6 36.7 0 71.9-9.9 102.8-28.5 5.7-3.4 12.8-3.3 18.5 0.2 36.7 22.7 79.1 35.3 123.2 35.3z",fill:"#82c91e"}}),e("path",{attrs:{d:"M770.9 576.9m-50.7 0a50.7 50.7 0 1 0 101.4 0 50.7 50.7 0 1 0-101.4 0Z",fill:"#82c91e"}}),e("path",{attrs:{d:"M602.4 576.9m-50.7 0a50.7 50.7 0 1 0 101.4 0 50.7 50.7 0 1 0-101.4 0Z",fill:"#82c91e"}})])]),e("div",{directives:[{name:"show",rawName:"v-show",value:"wechat"===t.activePopup,expression:"activePopup === 'wechat'"}],staticClass:"popup-container wechat-popup"},[e("div",{staticClass:"popup-content"},[e("h3",[t._v("微信扫码咨询客服")]),e("div",{staticClass:"qr-code"},[e("img",{attrs:{src:t.wechatQRCode,alt:"微信客服二维码"}})])])])]),e("div",{staticClass:"icon-item",on:{mouseenter:function(e){return t.showPopup("contact")},mouseleave:function(e){return t.hidePopup("contact")}}},[e("i",{staticClass:"iconfont icon-phone"},[e("svg",{attrs:{viewBox:"0 0 1024 1024",width:"24",height:"24"}},[e("path",{attrs:{d:"M877.1 238.7L770.6 132.3c-13-13-30.4-20.3-48.8-20.3s-35.8 7.2-48.8 20.3L558.3 246.8c-13 13-20.3 30.5-20.3 48.9 0 18.5 7.2 35.8 20.3 48.9l89.6 89.7c-20.6 47.8-49.6 90.6-86.4 127.3-36.7 36.9-79.6 66-127.2 86.6l-89.6-89.7c-13-13-30.4-20.3-48.8-20.3-18.5 0-35.8 7.2-48.8 20.3L132.3 673c-13 13-20.3 30.5-20.3 48.9 0 18.5 7.2 35.8 20.3 48.9l106.4 106.4c22.2 22.2 52.8 34.9 84.2 34.9 6.5 0 12.8-0.5 19.2-1.6 132.4-21.8 263.8-92.3 369.9-198.3C818 606 888.4 474.6 910.4 342.1c6.3-37.6-6.3-76.3-33.3-103.4z",fill:"#1677ff"}})])]),e("div",{directives:[{name:"show",rawName:"v-show",value:"contact"===t.activePopup,expression:"activePopup === 'contact'"}],staticClass:"popup-container contact-popup"},[e("div",{staticClass:"popup-content"},[e("h3",[t._v("商务合作请联系电话")]),e("p",{staticClass:"phone-number"},[t._v("13913283376")]),e("p",[t._v("使用问题请咨询微信客服")]),e("div",{staticClass:"qr-code"},[e("img",{attrs:{src:t.contactQRCode,alt:"联系电话二维码"}})])])])]),e("div",{staticClass:"icon-item",on:{click:t.showFeedbackModal,mouseenter:t.showTooltip,mouseleave:t.hideTooltip}},[e("i",{staticClass:"iconfont icon-feedback"},[e("svg",{attrs:{viewBox:"0 0 1024 1024",width:"24",height:"24"}},[e("path",{attrs:{d:"M573 421c-23.1 0-41 17.9-41 40s17.9 40 41 40c21.1 0 39-17.9 39-40s-17.9-40-39-40zM293 421c-23.1 0-41 17.9-41 40s17.9 40 41 40c21.1 0 39-17.9 39-40s-17.9-40-39-40z",fill:"#fa8c16"}}),e("path",{attrs:{d:"M894 345c-48.1-66-115.3-110.1-189-130v0.1c-17.1-19-36.4-36.5-58-52.1-163.7-119-393.5-82.7-513 81-96.3 133-92.2 311.9 6 439l0.8 132.6c0 3.2 0.5 6.4 1.5 9.4 5.3 16.9 23.3 26.2 40.1 20.9L309 806c33.4 11.9 68.4 18 104.7 18 72.2 0 143.1-21.4 205.3-61.8 57.4-37.3 104-89.9 131.8-150.1 23.9-52 35.2-106.8 33.6-160.9 28.8-29.9 50.4-64.7 63.6-103 17.8-52.7 13.1-108.7-14-155.1z m-68 106.7c-25.8 53.9-71.3 89.7-126.2 99.8-5.7 1-9.7 3.9-13 10.3-8.1 16.1-19.1 30.6-32.7 43.1-11.8 10.9-24.9 20.5-42.9 14.6-9.7-3.2-15.2-9.4-19.1-20.8-6.6-19.2-12.9-18.2-27.2-9.9-81.5 47.5-165.2 37.3-232.5-25.3-30.5-28.6-54.2-65.1-62.7-109-8.4-44 0.4-89.6 26.7-130.5 22.9-35.6 52.4-62 89.9-79.3 24.8-11.5 50.9-18.7 78.5-20.5 101.5-6.2 197.2 41.2 244.1 132.2 12.8 24.8 25.6 60.7 30.4 87.3 5.5 30.3 2.9 53.5-13.3 87z",fill:"#fa8c16"}})])]),e("div",{directives:[{name:"show",rawName:"v-show",value:t.showFeedbackTooltip,expression:"showFeedbackTooltip"}],staticClass:"tooltip"},[t._v(" 反馈与建议 ")])])])]),t.showModal?e("div",{staticClass:"modal-overlay",on:{click:function(e){return e.target!==e.currentTarget?null:t.closeFeedbackModal.apply(null,arguments)}}},[e("div",{staticClass:"feedback-modal"},[e("div",{staticClass:"modal-header"},[e("h3",[t._v("反馈与建议")]),e("span",{staticClass:"close-btn",on:{click:t.closeFeedbackModal}},[t._v("×")])]),e("div",{staticClass:"modal-body"},[e("div",{staticClass:"alert alert-warning"},[e("i",{staticClass:"iconfont icon-warning"},[e("svg",{attrs:{viewBox:"0 0 1024 1024",width:"16",height:"16"}},[e("path",{attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296z m32 440c-26.5 0-48-21.5-48-48s21.5-48 48-48 48 21.5 48 48-21.5 48-48 48z",fill:"#faad14"}})])]),t._v(" 您的反馈我们将认真对待，不断优化产品功能和体验 ")]),e("div",{staticClass:"form-group"},[e("label",{staticClass:"required"},[t._v("问题类型：")]),e("div",{staticClass:"select-wrapper"},[e("select",{directives:[{name:"model",rawName:"v-model",value:t.feedback.type,expression:"feedback.type"}],attrs:{required:""},on:{change:function(e){var s=Array.prototype.filter.call(e.target.options,(function(t){return t.selected})).map((function(t){var e="_value"in t?t._value:t.value;return e}));t.$set(t.feedback,"type",e.target.multiple?s:s[0])}}},[e("option",{attrs:{value:""}},[t._v("请选择")]),e("option",{attrs:{value:"功能建议"}},[t._v("功能建议")]),e("option",{attrs:{value:"产品故障"}},[t._v("产品故障")]),e("option",{attrs:{value:"体验不佳"}},[t._v("体验不佳")]),e("option",{attrs:{value:"账户相关"}},[t._v("账户相关")]),e("option",{attrs:{value:"其他"}},[t._v("其他")])])]),!t.feedback.type&&t.showErrors?e("p",{staticClass:"error-text"},[t._v("请选择问题类型")]):t._e()]),e("div",{staticClass:"form-group"},[e("label",{staticClass:"required"},[t._v("问题描述：")]),e("textarea",{directives:[{name:"model",rawName:"v-model",value:t.feedback.description,expression:"feedback.description"}],attrs:{placeholder:"请输入",required:""},domProps:{value:t.feedback.description},on:{input:function(e){e.target.composing||t.$set(t.feedback,"description",e.target.value)}}}),!t.feedback.description&&t.showErrors?e("p",{staticClass:"error-text"},[t._v("请输入问题描述")]):t._e()]),e("div",{staticClass:"form-group"},[e("label",{staticClass:"required1"},[t._v("问题截图：")]),e("div",{staticClass:"image-uploader",on:{click:t.triggerFileUpload,dragover:function(t){t.preventDefault()},drop:function(e){return e.preventDefault(),t.onFileDrop.apply(null,arguments)}}},[e("input",{ref:"fileInput",staticStyle:{display:"none"},attrs:{type:"file",accept:"image/*"},on:{change:t.onFileChange}}),t.feedback.image?e("div",{staticClass:"preview-container"},[e("img",{staticClass:"image-preview",attrs:{src:t.feedback.imagePreview,alt:"问题截图预览"}}),e("div",{staticClass:"remove-image",on:{click:function(e){return e.stopPropagation(),t.removeImage.apply(null,arguments)}}},[t._v("×")])]):e("div",{staticClass:"upload-placeholder"},[e("i",{staticClass:"iconfont icon-upload"},[e("svg",{attrs:{viewBox:"0 0 1024 1024",width:"28",height:"28"}},[e("path",{attrs:{d:"M518.3 459c-3.2-4.1-9.4-4.1-12.6 0l-112 141.7c-4.1 5.2-0.4 12.9 6.3 12.9h73.9V856c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V613.7H624c6.7 0 10.4-7.7 6.3-12.9L518.3 459z",fill:"#bfbfbf"}}),e("path",{attrs:{d:"M811.4 366.7C765.6 245.9 648.9 160 512.2 160S258.8 245.8 213 366.6C127.3 389.1 64 467.2 64 560c0 110.5 89.5 200 199.9 200H304c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8h-40.1c-33.7 0-65.4-13.4-89-37.7-23.5-24.2-36-56.8-34.9-90.6 0.9-26.4 9.9-51.2 26.2-72.1 16.7-21.3 40.1-36.8 66.1-43.7l37.9-9.9 13.9-36.6c8.6-22.8 20.6-44.1 35.7-63.4 14.9-19.2 32.6-35.9 52.4-49.9 41.1-28.9 89.5-44.2 140-44.2s98.9 15.3 140 44.2c19.9 14 37.5 30.8 52.4 49.9 15.1 19.3 27.1 40.7 35.7 63.4l13.8 36.5 37.8 10c26.1 6.9 49.6 22.5 66.3 43.8 16.4 21 25.4 45.9 26.3 72.3 1.1 33.9-11.4 66.5-34.9 90.7-23.6 24.4-55.3 37.7-89 37.7h-40c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h40.1C758.5 760 848 670.5 848 560c0-92.7-63.1-170.7-148.6-193.3z",fill:"#bfbfbf"}})])]),e("p",[t._v("点击/拖拽至此处添加图片")])])])])]),e("div",{staticClass:"modal-footer"},[e("button",{staticClass:"btn btn-cancel",on:{click:t.closeFeedbackModal}},[t._v("取消")]),e("button",{staticClass:"btn btn-submit",on:{click:t.confirmSubmit}},[t._v("提交")])])])]):t._e(),t.showConfirmation?e("div",{staticClass:"modal-overlay"},[e("div",{staticClass:"confirmation-dialog"},[e("div",{staticClass:"confirmation-icon"},[e("svg",{attrs:{viewBox:"0 0 1024 1024",width:"32",height:"32"}},[e("path",{attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m193.5 301.7l-210.6 292a31.8 31.8 0 0 1-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z",fill:"#52c41a"}})])]),e("div",{staticClass:"confirmation-title"},[t._v("提交成功")]),e("div",{staticClass:"confirmation-message"},[t._v("感谢您的反馈，我们会尽快处理")]),e("div",{staticClass:"confirmation-actions"},[e("button",{staticClass:"btn btn-primary",on:{click:t.closeConfirmation}},[t._v("确定")])])])]):t._e()])},i=[],o={name:"Mider",data(){return{activePopup:null,showModal:!1,showErrors:!1,showFeedbackTooltip:!1,showConfirmation:!1,wechatQRCode:s(46),contactQRCode:s(46),feedback:{type:"",description:"",instanceId:"",image:null,imagePreview:null}}},methods:{showPopup(t){this.activePopup=t},hidePopup(t){this.activePopup===t&&(this.activePopup=null)},showTooltip(){this.showFeedbackTooltip=!0},hideTooltip(){this.showFeedbackTooltip=!1},showFeedbackModal(){this.showModal=!0,this.showErrors=!1},closeFeedbackModal(){this.showModal=!1,this.feedback={type:"",description:"",instanceId:"",image:null,imagePreview:null},this.showErrors=!1},triggerFileUpload(){this.$refs.fileInput.click()},onFileChange(t){const e=t.target.files[0];e&&this.processImage(e)},onFileDrop(t){const e=t.dataTransfer.files[0];e&&e.type.match("image.*")&&this.processImage(e)},processImage(t){if(t&&t.type.match("image.*")){this.feedback.image=t;const e=new FileReader;e.onload=t=>{this.feedback.imagePreview=t.target.result},e.readAsDataURL(t)}},removeImage(){this.feedback.image=null,this.feedback.imagePreview=null},confirmSubmit(){this.showErrors=!0,this.feedback.type&&this.feedback.description&&this.submitFeedback()},submitFeedback(){if(this.feedback.image){const t=new FormData;t.append("type",this.feedback.type),t.append("description",this.feedback.description),t.append("instanceId",this.feedback.instanceId),t.append("image",this.feedback.image)}this.showModal=!1,this.showConfirmation=!0,this.feedback={type:"",description:"",instanceId:"",image:null,imagePreview:null}},closeConfirmation(){this.showConfirmation=!1}}},c=o,n=s(1001),r=(0,n.Z)(c,a,i,!1,null,"37397f14",null),l=r.exports},9484:function(t,e,s){s.d(e,{Z:function(){return l}});var a=function(){var t=this,e=t._self._c;return e("div",[e("div",{staticClass:"chat-container"},[e("div",{staticClass:"question-carousel",on:{mouseenter:t.pauseCarousel,mouseleave:t.resumeCarousel}},[e("transition-group",{staticClass:"carousel-wrapper",attrs:{name:"slide",tag:"div"}},t._l(t.questions,(function(s,a){return e("div",{directives:[{name:"show",rawName:"v-show",value:t.currentQuestionIndex===a,expression:"currentQuestionIndex === index"}],key:s,staticClass:"question-item",on:{click:function(e){return t.sendCarouselQuestion(s)},mouseenter:function(e){return t.witde(a)}}},[t._v(" "+t._s(s)+" ")])})),0)],1),e("div",{staticClass:"chat-icon",class:{"chat-icon-active":t.showChat},on:{click:t.toggleChat}},[e("i",{staticClass:"fas fa-comment"})])]),e("div",{directives:[{name:"show",rawName:"v-show",value:t.showChat,expression:"showChat"}],staticClass:"chat-window"},[e("div",{staticClass:"chat-header"},[t._m(0),e("div",{staticClass:"chat-controls"},[e("i",{staticClass:"fas fa-times",on:{click:t.toggleChat}})])]),e("div",{ref:"messagesContainer",staticClass:"chat-messages"},[t._l(t.messages,(function(s,a){return e("div",{key:a,class:["message",s.type]},["bot"===s.type?e("div",{staticClass:"avatar"},[e("i",{staticClass:"fas fa-robot"})]):t._e(),e("div",{staticClass:"message-content"},[e("div",{staticClass:"message-text",domProps:{innerHTML:t._s(t.formatMessage(s.text))}}),e("div",{staticClass:"message-time"},[t._v(t._s(t.formatTime(s.time)))])])])})),t.loading?e("div",{staticClass:"typing-indicator"},[e("div",{staticClass:"dot"}),e("div",{staticClass:"dot"}),e("div",{staticClass:"dot"})]):t._e()],2),e("div",{staticClass:"chat-input"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.userInput,expression:"userInput"}],attrs:{type:"text",placeholder:"请输入您的问题...",disabled:t.loading},domProps:{value:t.userInput},on:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.sendMessage.apply(null,arguments)},input:function(e){e.target.composing||(t.userInput=e.target.value)}}}),e("button",{attrs:{disabled:t.loading||!t.userInput.trim()},on:{click:t.sendMessage}},[e("i",{staticClass:"fas fa-paper-plane"})])])])])},i=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"chat-title"},[e("i",{staticClass:"fas fa-robot"}),e("span",[t._v("智能客服")])])}],o=(s(7658),{name:"chatAi",data(){return{showChat:!1,userInput:"",messages:[{type:"bot",text:"您好！我是智能客服助手，有什么可以帮您？",time:new Date}],loading:!1,historyMessages:[],questions:["如何租赁GPU算力？","支持哪些支付方式？","如何查看订单状态？"],currentQuestionIndex:0,carouselTimer:null,carouselInterval:3e3,isPaused:!1}},beforeDestroy(){this.clearCarousel()},mounted(){if(this.startCarousel(),!document.getElementById("font-awesome")){const t=document.createElement("link");t.id="font-awesome",t.rel="stylesheet",t.href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css",document.head.appendChild(t)}},methods:{witde(t){this.currentQuestionIndex=t,this.pauseCarousel()},startCarousel(){let t=this;this.clearCarousel(),this.carouselTimer=setInterval((()=>{t.isPaused||(this.currentQuestionIndex=(this.currentQuestionIndex+1)%this.questions.length,console.log("数据",this.currentQuestionIndex),console.log("ispasued",t.isPaused))}),this.carouselInterval)},pauseCarousel(){this.isPaused=!0},resumeCarousel(){this.isPaused=!1},clearCarousel(){this.carouselTimer&&(clearInterval(this.carouselTimer),this.carouselTimer=null)},sendCarouselQuestion(t){this.userInput=t,this.sendMessage()},toggleChat(){this.showChat=!this.showChat,this.showChat&&this.$nextTick((()=>{this.scrollToBottom()}))},async sendMessage(){if(!this.userInput.trim()||this.loading)return;this.messages.push({type:"user",text:this.userInput,time:new Date});const t=this.userInput;this.userInput="",this.loading=!0,this.historyMessages.push({role:"user",content:t});const e={model:"Qwen/QwQ-32B",messages:[{role:"system",content:"你是天工开物算力租赁网站的客服，当我问到算力租赁相关问题的时候再进行回答，如果我的问题与算力无关则回答“小天也不知道怎么办了呢，不如我们来聊聊算力相关的话题吧！”,如果问题涉及到你是谁或者你好的这种礼貌性踊跃，可以进行礼貌性的回复"},...this.historyMessages],stream:!0,options:{presence_penalty:1.2,frequency_penalty:1.5,seed:12345}};this.$nextTick((()=>{this.scrollToBottom()}));try{const t=await fetch("https://api.siliconflow.cn/v1/chat/completions",{method:"POST",headers:{Authorization:"Bearer sk-yxjtnfarjndtcwbjwmscdejrgtlpaaebnfdxcfnhhuanbqty","Content-Type":"application/json"},body:JSON.stringify(e)}),a=t.body.getReader(),i=new TextDecoder,o=this.messages.push({type:"bot",text:"",time:new Date})-1;while(1){const{done:t,value:e}=await a.read();if(t)break;const c=i.decode(e),n=c.split("\n").filter((t=>t.trim()));for(const a of n)try{const t=a.slice(6).trim();if(""===t||"[DONE]"===t)continue;let e=JSON.parse(t);if(e.choices){if(null!=e.choices[0].delta.reasoning_content)continue;if("\n\n"==e.choices[0].delta.content)continue;this.messages[o].text+=e.choices[0].delta.content}}catch(s){}}this.historyMessages.push({role:"assistant",content:this.messages[o].text})}catch(a){this.messages.push({type:"bot",text:"抱歉，系统暂时无法响应，请稍后再试。",time:new Date})}finally{this.loading=!1,this.$nextTick((()=>{this.scrollToBottom()}))}},async callChatAPI(t){return await new Promise((t=>setTimeout(t,1e3))),`感谢您的提问: "${t}"。这是一个模拟回复，请替换为真实API调用。`},scrollToBottom(){const t=this.$refs.messagesContainer;t.scrollTop=t.scrollHeight},formatTime(t){return new Date(t).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})},formatMessage(t){return t.replace(/\n/g,"<br>").replace(/(https?:\/\/[^\s]+)/g,'<a href="$1" target="_blank">$1</a>')}}}),c=o,n=s(1001),r=(0,n.Z)(c,a,i,!1,null,"46c63c47",null),l=r.exports},46:function(t,e,s){t.exports=s.p+"img/wechat.7b433d2e.jpg"}}]);
//# sourceMappingURL=1910.de1689ab.js.map