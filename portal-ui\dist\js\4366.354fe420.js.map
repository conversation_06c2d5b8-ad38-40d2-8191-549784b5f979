{"version": 3, "file": "js/4366.354fe420.js", "mappings": "+JAAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,cAAc,CAAEJ,EAAIK,aAAcH,EAAG,oBAAoB,CAACI,MAAM,CAAC,QAAU,uBAAuB,KAAO,UAAU,aAAaN,EAAIO,uBAAuBC,GAAG,CAAC,MAAQ,SAASC,GAAQT,EAAIK,cAAe,CAAK,KAAKL,EAAIU,KAAKR,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,oBAAoB,GAAGA,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,KAAK,CAACF,EAAIW,GAAG,cAAcT,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,IAAI,CAACE,YAAY,aAAa,CAACJ,EAAIW,GAAG,iBAAiBT,EAAG,MAAM,CAACE,YAAY,cAAcQ,MAAM,CAAE,MAASZ,EAAIa,OAAOC,QAAS,CAACZ,EAAG,QAAQ,CAACa,WAAW,CAAC,CAACC,KAAK,QAAQC,QAAQ,UAAUC,MAAOlB,EAAImB,UAAUL,MAAOM,WAAW,oBAAoBd,MAAM,CAAC,KAAO,OAAO,YAAc,UAAUe,SAAS,CAAC,MAASrB,EAAImB,UAAUL,OAAQN,GAAG,CAAC,KAAOR,EAAIsB,cAAc,MAAQ,SAASb,GAAWA,EAAOc,OAAOC,WAAiBxB,EAAIyB,KAAKzB,EAAImB,UAAW,QAASV,EAAOc,OAAOL,MAAM,KAAKhB,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAAEJ,EAAIa,OAAOC,MAAOZ,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACJ,EAAIW,GAAGX,EAAI0B,GAAG1B,EAAIa,OAAOC,UAAUd,EAAIU,SAASR,EAAG,MAAM,CAACE,YAAY,gCAAgCQ,MAAM,CAAE,MAASZ,EAAIa,OAAOc,OAAQ,CAACzB,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,QAAQ,CAACa,WAAW,CAAC,CAACC,KAAK,QAAQC,QAAQ,UAAUC,MAAOlB,EAAImB,UAAUQ,KAAMP,WAAW,mBAAmBd,MAAM,CAAC,KAAO,OAAO,YAAc,UAAUe,SAAS,CAAC,MAASrB,EAAImB,UAAUQ,MAAOnB,GAAG,CAAC,KAAOR,EAAI4B,aAAa,MAAQ,SAASnB,GAAWA,EAAOc,OAAOC,WAAiBxB,EAAIyB,KAAKzB,EAAImB,UAAW,OAAQV,EAAOc,OAAOL,MAAM,KAAKhB,EAAG,SAAS,CAACE,YAAY,sBAAsBE,MAAM,CAAC,UAAYN,EAAImB,UAAUL,OAASd,EAAIa,OAAOC,OAASd,EAAI6B,UAAUrB,GAAG,CAAC,MAAQR,EAAI8B,sBAAsB,CAAC9B,EAAIW,GAAG,IAAIX,EAAI0B,GAAG1B,EAAI6B,SAAY,GAAE7B,EAAI+B,gBAAkB,SAAS,SAAS7B,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAAEJ,EAAIa,OAAOc,KAAMzB,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACJ,EAAIW,GAAGX,EAAI0B,GAAG1B,EAAIa,OAAOc,SAAS3B,EAAIU,SAASR,EAAG,MAAM,CAACE,YAAY,cAAcQ,MAAM,CAAE,MAASZ,EAAIa,OAAOmB,cAAe,CAAC9B,EAAG,MAAM,CAACE,YAAY,4BAA4B,CAAgD,cAA7CJ,EAAIiC,gBAAkB,OAAS,YAA0B/B,EAAG,QAAQ,CAACa,WAAW,CAAC,CAACC,KAAK,QAAQC,QAAQ,UAAUC,MAAOlB,EAAImB,UAAUa,YAAaZ,WAAW,0BAA0Bd,MAAM,CAAC,YAAc,SAAS,KAAO,YAAYe,SAAS,CAAC,QAAUa,MAAMC,QAAQnC,EAAImB,UAAUa,aAAahC,EAAIoC,GAAGpC,EAAImB,UAAUa,YAAY,OAAO,EAAGhC,EAAImB,UAAUa,aAAcxB,GAAG,CAAC,KAAOR,EAAIqC,oBAAoB,OAAS,SAAS5B,GAAQ,IAAI6B,EAAItC,EAAImB,UAAUa,YAAYO,EAAK9B,EAAOc,OAAOiB,IAAID,EAAKE,QAAuB,GAAGP,MAAMC,QAAQG,GAAK,CAAC,IAAII,EAAI,KAAKC,EAAI3C,EAAIoC,GAAGE,EAAII,GAAQH,EAAKE,QAASE,EAAI,GAAI3C,EAAIyB,KAAKzB,EAAImB,UAAW,cAAemB,EAAIM,OAAO,CAACF,KAAaC,GAAK,GAAI3C,EAAIyB,KAAKzB,EAAImB,UAAW,cAAemB,EAAIO,MAAM,EAAEF,GAAKC,OAAON,EAAIO,MAAMF,EAAI,IAAM,MAAM3C,EAAIyB,KAAKzB,EAAImB,UAAW,cAAeqB,EAAK,KAAoD,WAA7CxC,EAAIiC,gBAAkB,OAAS,YAAuB/B,EAAG,QAAQ,CAACa,WAAW,CAAC,CAACC,KAAK,QAAQC,QAAQ,UAAUC,MAAOlB,EAAImB,UAAUa,YAAaZ,WAAW,0BAA0Bd,MAAM,CAAC,YAAc,SAAS,KAAO,SAASe,SAAS,CAAC,QAAUrB,EAAI8C,GAAG9C,EAAImB,UAAUa,YAAY,OAAOxB,GAAG,CAAC,KAAOR,EAAIqC,oBAAoB,OAAS,SAAS5B,GAAQ,OAAOT,EAAIyB,KAAKzB,EAAImB,UAAW,cAAe,KAAK,KAAKjB,EAAG,QAAQ,CAACa,WAAW,CAAC,CAACC,KAAK,QAAQC,QAAQ,UAAUC,MAAOlB,EAAImB,UAAUa,YAAaZ,WAAW,0BAA0Bd,MAAM,CAAC,YAAc,SAAS,KAAON,EAAIiC,gBAAkB,OAAS,YAAYZ,SAAS,CAAC,MAASrB,EAAImB,UAAUa,aAAcxB,GAAG,CAAC,KAAOR,EAAIqC,oBAAoB,MAAQ,SAAS5B,GAAWA,EAAOc,OAAOC,WAAiBxB,EAAIyB,KAAKzB,EAAImB,UAAW,cAAeV,EAAOc,OAAOL,MAAM,KAAKhB,EAAG,OAAO,CAACE,YAAY,kBAAkBI,GAAG,CAAC,MAAQR,EAAI+C,2BAA2B,CAAC7C,EAAG,IAAI,CAACU,MAAM,CAAC,WAAYZ,EAAIiC,gBAAkB,UAAY,UAAU/B,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAAEJ,EAAIa,OAAOmB,YAAa9B,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACJ,EAAIW,GAAGX,EAAI0B,GAAG1B,EAAIa,OAAOmB,gBAAgBhC,EAAIU,SAASR,EAAG,MAAM,CAACE,YAAY,cAAcQ,MAAM,CAAE,MAASZ,EAAIa,OAAOmC,kBAAmB,CAAC9C,EAAG,MAAM,CAACE,YAAY,4BAA4B,CAAuD,cAApDJ,EAAIiD,uBAAyB,OAAS,YAA0B/C,EAAG,QAAQ,CAACa,WAAW,CAAC,CAACC,KAAK,QAAQC,QAAQ,UAAUC,MAAOlB,EAAImB,UAAU6B,gBAAiB5B,WAAW,8BAA8Bd,MAAM,CAAC,YAAc,WAAW,KAAO,YAAYe,SAAS,CAAC,QAAUa,MAAMC,QAAQnC,EAAImB,UAAU6B,iBAAiBhD,EAAIoC,GAAGpC,EAAImB,UAAU6B,gBAAgB,OAAO,EAAGhD,EAAImB,UAAU6B,iBAAkBxC,GAAG,CAAC,KAAOR,EAAIkD,wBAAwB,OAAS,SAASzC,GAAQ,IAAI6B,EAAItC,EAAImB,UAAU6B,gBAAgBT,EAAK9B,EAAOc,OAAOiB,IAAID,EAAKE,QAAuB,GAAGP,MAAMC,QAAQG,GAAK,CAAC,IAAII,EAAI,KAAKC,EAAI3C,EAAIoC,GAAGE,EAAII,GAAQH,EAAKE,QAASE,EAAI,GAAI3C,EAAIyB,KAAKzB,EAAImB,UAAW,kBAAmBmB,EAAIM,OAAO,CAACF,KAAaC,GAAK,GAAI3C,EAAIyB,KAAKzB,EAAImB,UAAW,kBAAmBmB,EAAIO,MAAM,EAAEF,GAAKC,OAAON,EAAIO,MAAMF,EAAI,IAAM,MAAM3C,EAAIyB,KAAKzB,EAAImB,UAAW,kBAAmBqB,EAAK,KAA2D,WAApDxC,EAAIiD,uBAAyB,OAAS,YAAuB/C,EAAG,QAAQ,CAACa,WAAW,CAAC,CAACC,KAAK,QAAQC,QAAQ,UAAUC,MAAOlB,EAAImB,UAAU6B,gBAAiB5B,WAAW,8BAA8Bd,MAAM,CAAC,YAAc,WAAW,KAAO,SAASe,SAAS,CAAC,QAAUrB,EAAI8C,GAAG9C,EAAImB,UAAU6B,gBAAgB,OAAOxC,GAAG,CAAC,KAAOR,EAAIkD,wBAAwB,OAAS,SAASzC,GAAQ,OAAOT,EAAIyB,KAAKzB,EAAImB,UAAW,kBAAmB,KAAK,KAAKjB,EAAG,QAAQ,CAACa,WAAW,CAAC,CAACC,KAAK,QAAQC,QAAQ,UAAUC,MAAOlB,EAAImB,UAAU6B,gBAAiB5B,WAAW,8BAA8Bd,MAAM,CAAC,YAAc,WAAW,KAAON,EAAIiD,uBAAyB,OAAS,YAAY5B,SAAS,CAAC,MAASrB,EAAImB,UAAU6B,iBAAkBxC,GAAG,CAAC,KAAOR,EAAIkD,wBAAwB,MAAQ,SAASzC,GAAWA,EAAOc,OAAOC,WAAiBxB,EAAIyB,KAAKzB,EAAImB,UAAW,kBAAmBV,EAAOc,OAAOL,MAAM,KAAKhB,EAAG,OAAO,CAACE,YAAY,kBAAkBI,GAAG,CAAC,MAAQR,EAAImD,kCAAkC,CAACjD,EAAG,IAAI,CAACU,MAAM,CAAC,WAAYZ,EAAIiD,uBAAyB,UAAY,UAAU/C,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAAEJ,EAAIa,OAAOmC,gBAAiB9C,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACJ,EAAIW,GAAGX,EAAI0B,GAAG1B,EAAIa,OAAOmC,oBAAoBhD,EAAIU,SAASR,EAAG,SAAS,CAACE,YAAY,YAAYE,MAAM,CAAC,UAAYN,EAAIoD,aAAepD,EAAIqD,aAAa7C,GAAG,CAAC,MAAQR,EAAIsD,gBAAgB,CAACtD,EAAIW,GAAG,IAAIX,EAAI0B,GAAG1B,EAAIqD,YAAc,SAAW,QAAQ,OAAOnD,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,IAAI,CAACI,MAAM,CAAC,KAAO,KAAKE,GAAG,CAAC,MAAQR,EAAIuD,YAAY,CAACvD,EAAIW,GAAG,qBAAqB,EACj/M,EACI6C,EAAkB,G,wCC+GtB,GACAxC,KAAA,aACAyC,WAAA,CACAC,kBAAA,IACAC,gBAAAA,EAAAA,GAEAC,OACA,OACAzC,UAAA,CACAL,MAAA,GACAa,KAAA,GACAK,YAAA,GACAgB,gBAAA,IAEAf,iBAAA,EACAgB,wBAAA,EACApC,OAAA,CACAC,MAAA,GACAa,KAAA,GACAK,YAAA,GACAgB,gBAAA,IAEAnB,UAAA,EACAE,UAAA,GACA8B,MAAA,KACAxD,cAAA,EACAgD,aAAA,EACAS,eAAA,EACAvD,sBAAA,OAEA,EACAwD,SAAA,CACAX,cACA,YAAAjC,UAAAL,OACA,KAAAK,UAAAQ,MACA,KAAAR,UAAAa,aACA,KAAAb,UAAA6B,kBACA,KAAAnC,OAAAC,QACA,KAAAD,OAAAc,OACA,KAAAd,OAAAmB,cACA,KAAAnB,OAAAmC,eACA,GAEAgB,UAEA,KAAAC,MAAA,eACA,EAEAC,QAAA,CACA5C,gBACA,MAAA6C,EAAA,gBACA,KAAAhD,UAAAL,MAEAqD,EAAAC,KAAA,KAAAjD,UAAAL,OAGA,KAAAD,OAAAC,MAAA,GAFA,KAAAD,OAAAC,MAAA,YAFA,KAAAD,OAAAC,MAAA,QAMA,EAEAc,eACA,KAAAT,UAAAQ,KAEA,SAAAR,UAAAQ,KAAA0C,QAAA,QAAAD,KAAA,KAAAjD,UAAAQ,MAGA,KAAAd,OAAAc,KAAA,GAFA,KAAAd,OAAAc,KAAA,WAFA,KAAAd,OAAAc,KAAA,QAMA,EAEAU,sBACA,MAAAL,EAAA,KAAAb,UAAAa,YAGA,GAFA,KAAAnB,OAAAmB,YAAA,IAEAA,EAEA,YADA,KAAAnB,OAAAmB,YAAA,UAIA,MAAAsC,EAAAtC,EAAAqC,QAAA,EACA,IAAAC,EAEA,YADA,KAAAzD,OAAAmB,YAAA,aAKA,MAAAuC,EAAA,WAAAH,KAAApC,GACAwC,EAAA,QAAAJ,KAAApC,GACAyC,EAAA,sCAAAL,KAAApC,GACA0C,EAAA,QAAAN,KAAApC,IAAA,QAAAoC,KAAApC,GAEA,IAAA2C,EAAA,EAKA,GAJAJ,GAAAC,GAAAG,IACAF,GAAAE,IACAD,GAAAC,IAEAA,GAAA,EACA,KAAA9D,OAAAmB,YAAA,OACA,CACA,MAAA4C,EAAA,GACAL,GAAAC,GAAAI,EAAAC,KAAA,WACAJ,GAAAG,EAAAC,KAAA,UACAH,GAAAE,EAAAC,KAAA,WAEA,KAAAhE,OAAAmB,YAAA,sBAAA4C,EAAAE,KAAA,MACA,CAGA,KAAA3D,UAAA6B,iBACA,KAAAE,yBAEA,EAGAA,0BACA,KAAA/B,UAAA6B,gBAEA,KAAA7B,UAAA6B,kBAAA,KAAA7B,UAAAa,YACA,KAAAnB,OAAAmC,gBAAA,aAEA,KAAAnC,OAAAmC,gBAAA,GAJA,KAAAnC,OAAAmC,gBAAA,QAMA,EAEA,4BAEA,GADA,KAAA1B,iBACA,KAAAT,OAAAC,MAGA,IAEA,KAAAe,UAAA,EACA,KAAAkD,eAAA,EAEA,MAAAC,QAAAC,EAAAA,EAAAA,IAAA,kBACAnE,MAAA,KAAAK,UAAAL,QAIA,MAAAkE,EAAApB,KAAAjC,MACA,KAAAtB,cAAA,EACA6E,YAAA,KACA,KAAA7E,cAAA,IACA,KACA,KAAA8E,mBAEA,KAAAtE,OAAAc,KAAAqD,EAAApB,KAAAwB,KAAA,UACA,KAAAvD,UAAA,EAEA,OAAAwD,GACA,KAAAxE,OAAAc,KAAA,gBACA,KAAAE,UAAA,CACA,SACA,KAAAkD,eAAA,CACA,CACA,EAEAI,iBAEA,KAAAtB,OACAyB,cAAA,KAAAzB,OAGA,KAAA9B,UAAA,GAEA,KAAA8B,MAAA0B,aAAA,KACA,KAAAxD,WAAA,GACAuD,cAAA,KAAAzB,OACA,KAAAhC,UAAA,EACA,KAAAE,UAAA,IAEA,KAAAA,WACA,GACA,IACA,EAGAyD,aACA,WAAAC,SAAA,CAAAC,EAAAC,KACA,KAAAtC,aAAA,EACA,KAAAS,eAAA,GAEA8B,EAAAA,EAAAA,IAAA,oBACA9E,MAAA,KAAAK,UAAAL,MACAa,KAAA,KAAAR,UAAAQ,OACAkE,MAAAC,IACA,KAAAhC,eAAA,EACAgC,EAAAlC,MAAA,MAAAkC,EAAAlC,KAAAjC,KACA+D,GAAA,IAEA,KAAA7E,OAAAc,KAAAmE,EAAAlC,KAAAwB,KAAA,UACAO,EAAA,IAAAI,MAAAD,EAAAlC,KAAAwB,KAAA,YACA,IACAY,OAAAC,IACA,KAAAnC,eAAA,EACA,KAAAjD,OAAAc,KAAA,UACAgE,EAAAM,EAAA,IACAC,SAAA,KACA,KAAA7C,aAAA,IACA,GAEA,EAEAC,gBAEA,KAAAhC,gBACA,KAAAM,eACA,KAAAS,sBACA,KAAAa,0BAEA,KAAAE,aAGA,KAAAoC,aAAAK,MAAAM,IACAA,IAEAlB,EAAAA,EAAAA,IAAA,uBACAnE,MAAA,KAAAK,UAAAL,MACAa,KAAA,KAAAR,UAAAQ,KACAyE,SAAA,KAAAjF,UAAAa,cACA6D,MAAAC,IACAA,EAAAlC,MAAA,MAAAkC,EAAAlC,KAAAjC,KACA,KAAA4B,YAEA,KAAA1C,OAAAc,KAAAmE,EAAAlC,KAAAyC,SAAA,QACA,IACAL,OAAAC,IACA,KAAApF,OAAAc,KAAAmE,IAAAlC,KAAAyC,SAAA,gBAEA,IACAL,OAAAC,OAEA,EAEA1C,YACA,KAAA+C,QAAAzB,KAAA,SACA,EAEA9B,2BACA,KAAAd,iBAAA,KAAAA,eACA,EAEAkB,kCACA,KAAAF,wBAAA,KAAAA,sBACA,GAEAsD,gBAEA,KAAAtC,MAAA,eACA,GC1WuQ,I,UCQnQuC,GAAY,OACd,EACAzG,EACAyD,GACA,EACA,KACA,WACA,MAIF,EAAegD,EAAiB,O,uDCnBhC,IAAIzG,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAUF,EAAIG,MAAMsG,YAAY,OAAOvG,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,IAAI,CAACE,YAAY,YAAYI,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOT,EAAI0G,WAAW,SAAS,IAAI,CAACxG,EAAG,MAAM,CAACE,YAAY,OAAOE,MAAM,CAAC,IAAMqG,EAAQ,MAAkC,IAAM,cAAc3G,EAAI4G,GAAG,GAAG1G,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,uBAAuBJ,EAAI6G,GAAI7G,EAAI8G,SAAS,SAASC,EAAOC,GAAO,OAAO9G,EAAG,MAAM,CAAC+G,IAAID,EAAM5G,YAAY,cAAc8G,MAAO,CACrjBC,eAA2B,GAARH,EAAF,IACjBI,UAAY,cAAqB,EAARJ,SACvB,CAAC9G,EAAG,MAAM,CAACE,YAAY,kBAAkB,IAAG,GAAGF,EAAG,MAAM,CAACE,YAAY,kBAAkBF,EAAG,MAAM,CAACE,YAAY,YAAYJ,EAAI6G,GAAI7G,EAAIqH,UAAU,SAASC,EAAQN,GAAO,OAAO9G,EAAG,MAAM,CAAC+G,IAAID,EAAM5G,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,KAAK,CAACF,EAAIW,GAAGX,EAAI0B,GAAG4F,EAAQC,UAAUrH,EAAG,IAAI,CAACF,EAAIW,GAAGX,EAAI0B,GAAG4F,EAAQE,mBAAmB,IAAG,GAAGtH,EAAG,MAAM,CAACE,YAAY,uBAAuBJ,EAAI6G,GAAI,IAAI,SAASY,GAAG,OAAOvH,EAAG,MAAM,CAAC+G,IAAIQ,EAAErH,YAAY,oBAAoB8G,MAAO,CAC7eQ,KAAyB,IAAhBC,KAAKC,SAAP,IACPC,IAAwB,IAAhBF,KAAKC,SAAP,IACNE,kBAAsB,EAAoB,GAAhBH,KAAKC,SAAX,IACpBT,eAAmC,EAAhBQ,KAAKC,SAAP,MACf,IAAG,IAClB,EACIpE,EAAkB,CAAC,WAAY,IAAIxD,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAUF,EAAIG,MAAMsG,YAAY,OAAOvG,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,KAAK,CAACE,YAAY,UAAU,CAACJ,EAAIW,GAAG,iBAAiBT,EAAG,IAAI,CAACE,YAAY,cAAc,CAACJ,EAAIW,GAAG,2BAC5O,G,mBCsDA,MAAAoH,EAAA,CACAC,SAAA,gkBAeAC,EAAA,CACAD,SAAA,8bAUAE,EAAA,CACAF,SAAA,oRAOA,OAAAG,EAAAA,EAAAA,IAAA,CACAnH,KAAA,kBACAyC,WAAA,CACAsE,kBACAE,aACAC,cAEAhE,QAAA,CACAwC,WAAA0B,GAEA,KAAAC,aAAA,KAAAA,cAAAD,GACA,KAAAE,mBAAA,KAAAD,YAGA,KAAAE,WAAA,KACA,MAAAC,EAAAC,SAAAC,iBAAA,yBACAF,EAAAG,SAAAC,KACAA,EAAAC,UAAAC,SAAA,WACA,WAAAV,GAAAQ,EAAAC,UAAAC,SAAA,gBACAF,EAAAC,UAAAC,SAAA,iBACAF,EAAAC,UAAAE,IAAA,eAGA7D,YAAA,KACA0D,EAAAC,UAAAG,OAAA,iBACA,KACA,IAIA,KAAAX,YAAAD,CAAA,KAGA,KAAAC,YAAAD,EAIA,KAAAa,OAAAb,OAAAA,EACA,KAAAG,WAAA,KACAW,OAAAC,SAAA,CACAtB,IAAA,EACAuB,SAAA,YAEA,KAAA9C,QAAA+C,GAAA,OAIA,KAAA/C,QAAAzB,KAAAuD,GACAc,OAAAC,SAAA,CACAtB,IAAA,EACAuB,SAAA,YAGA,GAEAE,QACA,MAAAC,GAAAC,EAAAA,EAAAA,IAAA,4BACA1C,GAAA0C,EAAAA,EAAAA,IAAAtH,MAAA,GAAAuH,KAAA,OAEApC,GAAAmC,EAAAA,EAAAA,IAAA,CACA,CACAE,KAAA,kBACAnC,MAAA,QACAC,YAAA,iCAEA,CACAkC,KAAA,iBACAnC,MAAA,OACAC,YAAA,8BAKA,OACA+B,UACAzC,UACAO,WAEA,IClLwQ,I,UCQpQb,GAAY,OACd,EACAzG,EACAyD,GACA,EACA,KACA,WACA,MAIF,EAAegD,EAAiB,O", "sources": ["webpack://portal-ui/./src/views/Login/ForgetPassView.vue", "webpack://portal-ui/src/views/Login/ForgetPassView.vue", "webpack://portal-ui/./src/views/Login/ForgetPassView.vue?a34f", "webpack://portal-ui/./src/views/Login/ForgetPassView.vue?693d", "webpack://portal-ui/./src/views/Login/backgroundlogin.vue", "webpack://portal-ui/src/views/Login/backgroundlogin.vue", "webpack://portal-ui/./src/views/Login/backgroundlogin.vue?d758", "webpack://portal-ui/./src/views/Login/backgroundlogin.vue?eb08"], "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"login-page\"},[(_vm.showCodeSent)?_c('SlideNotification',{attrs:{\"message\":\"验证码已发送，可能会有延迟，请耐心等待！\",\"type\":\"success\",\"min-height\":_vm.notificationMinHeight},on:{\"close\":function($event){_vm.showCodeSent = false}}}):_vm._e(),_c('div',{staticClass:\"left-side\"},[_c('backgroundlogin')],1),_c('div',{staticClass:\"right-side\"},[_c('div',{staticClass:\"login-form-container\"},[_c('h3',[_vm._v(\"重置统一登录密码\")]),_c('div',{staticClass:\"form-container\"},[_c('div',{staticClass:\"login-form\"},[_c('p',{staticClass:\"form-note\"},[_vm._v(\"请输入手机号接收验证码\")]),_c('div',{staticClass:\"input-group\",class:{ 'error': _vm.errors.phone }},[_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.resetForm.phone),expression:\"resetForm.phone\"}],attrs:{\"type\":\"text\",\"placeholder\":\"请输入手机号\"},domProps:{\"value\":(_vm.resetForm.phone)},on:{\"blur\":_vm.validatePhone,\"input\":function($event){if($event.target.composing)return;_vm.$set(_vm.resetForm, \"phone\", $event.target.value)}}}),_c('div',{staticClass:\"error-container\"},[(_vm.errors.phone)?_c('div',{staticClass:\"error-message\"},[_vm._v(_vm._s(_vm.errors.phone))]):_vm._e()])]),_c('div',{staticClass:\"input-group verification-code\",class:{ 'error': _vm.errors.code }},[_c('div',{staticClass:\"code-input-container\"},[_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.resetForm.code),expression:\"resetForm.code\"}],attrs:{\"type\":\"text\",\"placeholder\":\"请输入验证码\"},domProps:{\"value\":(_vm.resetForm.code)},on:{\"blur\":_vm.validateCode,\"input\":function($event){if($event.target.composing)return;_vm.$set(_vm.resetForm, \"code\", $event.target.value)}}}),_c('button',{staticClass:\"get-code-btn-inline\",attrs:{\"disabled\":!_vm.resetForm.phone || _vm.errors.phone || _vm.codeSent},on:{\"click\":_vm.getVerificationCode}},[_vm._v(\" \"+_vm._s(_vm.codeSent ? `${_vm.countdown}秒后重试` : '获取验证码')+\" \")])]),_c('div',{staticClass:\"error-container\"},[(_vm.errors.code)?_c('div',{staticClass:\"error-message\"},[_vm._v(_vm._s(_vm.errors.code))]):_vm._e()])]),_c('div',{staticClass:\"input-group\",class:{ 'error': _vm.errors.newPassword }},[_c('div',{staticClass:\"password-input-container\"},[((_vm.passwordVisible ? 'text' : 'password')==='checkbox')?_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.resetForm.newPassword),expression:\"resetForm.newPassword\"}],attrs:{\"placeholder\":\"请输入新密码\",\"type\":\"checkbox\"},domProps:{\"checked\":Array.isArray(_vm.resetForm.newPassword)?_vm._i(_vm.resetForm.newPassword,null)>-1:(_vm.resetForm.newPassword)},on:{\"blur\":_vm.validateNewPassword,\"change\":function($event){var $$a=_vm.resetForm.newPassword,$$el=$event.target,$$c=$$el.checked?(true):(false);if(Array.isArray($$a)){var $$v=null,$$i=_vm._i($$a,$$v);if($$el.checked){$$i<0&&(_vm.$set(_vm.resetForm, \"newPassword\", $$a.concat([$$v])))}else{$$i>-1&&(_vm.$set(_vm.resetForm, \"newPassword\", $$a.slice(0,$$i).concat($$a.slice($$i+1))))}}else{_vm.$set(_vm.resetForm, \"newPassword\", $$c)}}}}):((_vm.passwordVisible ? 'text' : 'password')==='radio')?_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.resetForm.newPassword),expression:\"resetForm.newPassword\"}],attrs:{\"placeholder\":\"请输入新密码\",\"type\":\"radio\"},domProps:{\"checked\":_vm._q(_vm.resetForm.newPassword,null)},on:{\"blur\":_vm.validateNewPassword,\"change\":function($event){return _vm.$set(_vm.resetForm, \"newPassword\", null)}}}):_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.resetForm.newPassword),expression:\"resetForm.newPassword\"}],attrs:{\"placeholder\":\"请输入新密码\",\"type\":_vm.passwordVisible ? 'text' : 'password'},domProps:{\"value\":(_vm.resetForm.newPassword)},on:{\"blur\":_vm.validateNewPassword,\"input\":function($event){if($event.target.composing)return;_vm.$set(_vm.resetForm, \"newPassword\", $event.target.value)}}}),_c('span',{staticClass:\"password-toggle\",on:{\"click\":_vm.togglePasswordVisibility}},[_c('i',{class:['eye-icon', _vm.passwordVisible ? 'visible' : '']})])]),_c('div',{staticClass:\"error-container\"},[(_vm.errors.newPassword)?_c('div',{staticClass:\"error-message\"},[_vm._v(_vm._s(_vm.errors.newPassword))]):_vm._e()])]),_c('div',{staticClass:\"input-group\",class:{ 'error': _vm.errors.confirmPassword }},[_c('div',{staticClass:\"password-input-container\"},[((_vm.confirmPasswordVisible ? 'text' : 'password')==='checkbox')?_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.resetForm.confirmPassword),expression:\"resetForm.confirmPassword\"}],attrs:{\"placeholder\":\"请再次输入新密码\",\"type\":\"checkbox\"},domProps:{\"checked\":Array.isArray(_vm.resetForm.confirmPassword)?_vm._i(_vm.resetForm.confirmPassword,null)>-1:(_vm.resetForm.confirmPassword)},on:{\"blur\":_vm.validateConfirmPassword,\"change\":function($event){var $$a=_vm.resetForm.confirmPassword,$$el=$event.target,$$c=$$el.checked?(true):(false);if(Array.isArray($$a)){var $$v=null,$$i=_vm._i($$a,$$v);if($$el.checked){$$i<0&&(_vm.$set(_vm.resetForm, \"confirmPassword\", $$a.concat([$$v])))}else{$$i>-1&&(_vm.$set(_vm.resetForm, \"confirmPassword\", $$a.slice(0,$$i).concat($$a.slice($$i+1))))}}else{_vm.$set(_vm.resetForm, \"confirmPassword\", $$c)}}}}):((_vm.confirmPasswordVisible ? 'text' : 'password')==='radio')?_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.resetForm.confirmPassword),expression:\"resetForm.confirmPassword\"}],attrs:{\"placeholder\":\"请再次输入新密码\",\"type\":\"radio\"},domProps:{\"checked\":_vm._q(_vm.resetForm.confirmPassword,null)},on:{\"blur\":_vm.validateConfirmPassword,\"change\":function($event){return _vm.$set(_vm.resetForm, \"confirmPassword\", null)}}}):_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.resetForm.confirmPassword),expression:\"resetForm.confirmPassword\"}],attrs:{\"placeholder\":\"请再次输入新密码\",\"type\":_vm.confirmPasswordVisible ? 'text' : 'password'},domProps:{\"value\":(_vm.resetForm.confirmPassword)},on:{\"blur\":_vm.validateConfirmPassword,\"input\":function($event){if($event.target.composing)return;_vm.$set(_vm.resetForm, \"confirmPassword\", $event.target.value)}}}),_c('span',{staticClass:\"password-toggle\",on:{\"click\":_vm.toggleConfirmPasswordVisibility}},[_c('i',{class:['eye-icon', _vm.confirmPasswordVisible ? 'visible' : '']})])]),_c('div',{staticClass:\"error-container\"},[(_vm.errors.confirmPassword)?_c('div',{staticClass:\"error-message\"},[_vm._v(_vm._s(_vm.errors.confirmPassword))]):_vm._e()])]),_c('button',{staticClass:\"login-btn\",attrs:{\"disabled\":!_vm.isFormValid || _vm.isVerifying},on:{\"click\":_vm.resetPassword}},[_vm._v(\" \"+_vm._s(_vm.isVerifying ? '验证中...' : '重置密码')+\" \")]),_c('div',{staticClass:\"login-link\"},[_c('a',{attrs:{\"href\":\"#\"},on:{\"click\":_vm.goToLogin}},[_vm._v(\"返回登录\")])])])])])])],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"login-page\">\r\n    <SlideNotification\r\n        v-if=\"showCodeSent\"\r\n        message=\"验证码已发送，可能会有延迟，请耐心等待！\"\r\n        type=\"success\"\r\n        :min-height=\"notificationMinHeight\"\r\n        @close=\"showCodeSent = false\"\r\n    />\r\n\r\n\r\n    <div class=\"left-side\">\r\n      <backgroundlogin />\r\n    </div>\r\n\r\n    <div class=\"right-side\">\r\n      <div class=\"login-form-container\">\r\n        <h3>重置统一登录密码</h3>\r\n\r\n        <div class=\"form-container\">\r\n          <div class=\"login-form\">\r\n            <p class=\"form-note\">请输入手机号接收验证码</p>\r\n\r\n            <div class=\"input-group\" :class=\"{ 'error': errors.phone }\">\r\n              <input\r\n                  type=\"text\"\r\n                  v-model=\"resetForm.phone\"\r\n                  placeholder=\"请输入手机号\"\r\n                  @blur=\"validatePhone\"\r\n              />\r\n              <div class=\"error-container\">\r\n                <div v-if=\"errors.phone\" class=\"error-message\">{{ errors.phone }}</div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"input-group verification-code\" :class=\"{ 'error': errors.code }\">\r\n              <div class=\"code-input-container\">\r\n                <input\r\n                    type=\"text\"\r\n                    v-model=\"resetForm.code\"\r\n                    placeholder=\"请输入验证码\"\r\n                    @blur=\"validateCode\"\r\n                />\r\n                <button\r\n                    class=\"get-code-btn-inline\"\r\n                    @click=\"getVerificationCode\"\r\n                    :disabled=\"!resetForm.phone || errors.phone || codeSent\"\r\n                >\r\n                  {{ codeSent ? `${countdown}秒后重试` : '获取验证码' }}\r\n                </button>\r\n              </div>\r\n              <div class=\"error-container\">\r\n                <div v-if=\"errors.code\" class=\"error-message\">{{ errors.code }}</div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"input-group\" :class=\"{ 'error': errors.newPassword }\">\r\n              <div class=\"password-input-container\">\r\n                <input\r\n                    :type=\"passwordVisible ? 'text' : 'password'\"\r\n                    v-model=\"resetForm.newPassword\"\r\n                    placeholder=\"请输入新密码\"\r\n                    @blur=\"validateNewPassword\"\r\n                />\r\n                <span class=\"password-toggle\" @click=\"togglePasswordVisibility\">\r\n                  <i :class=\"['eye-icon', passwordVisible ? 'visible' : '']\"></i>\r\n                </span>\r\n              </div>\r\n              <div class=\"error-container\">\r\n                <div v-if=\"errors.newPassword\" class=\"error-message\">{{ errors.newPassword }}</div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"input-group\" :class=\"{ 'error': errors.confirmPassword }\">\r\n              <div class=\"password-input-container\">\r\n                <input\r\n                    :type=\"confirmPasswordVisible ? 'text' : 'password'\"\r\n                    v-model=\"resetForm.confirmPassword\"\r\n                    placeholder=\"请再次输入新密码\"\r\n                    @blur=\"validateConfirmPassword\"\r\n                />\r\n                <span class=\"password-toggle\" @click=\"toggleConfirmPasswordVisibility\">\r\n                  <i :class=\"['eye-icon', confirmPasswordVisible ? 'visible' : '']\"></i>\r\n                </span>\r\n              </div>\r\n              <div class=\"error-container\">\r\n                <div v-if=\"errors.confirmPassword\" class=\"error-message\">{{ errors.confirmPassword }}</div>\r\n              </div>\r\n            </div>\r\n\r\n            <button\r\n                class=\"login-btn\"\r\n                @click=\"resetPassword\"\r\n                :disabled=\"!isFormValid || isVerifying\"\r\n            >\r\n              {{ isVerifying ? '验证中...' : '重置密码' }}\r\n            </button>\r\n            <div class=\"login-link\">\r\n              <a href=\"#\" @click=\"goToLogin\">返回登录</a>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {postAnyData, postLogin, postNotAuth} from \"@/api/login\";\r\nimport SlideNotification from '@/components/common/header/SlideNotification.vue';\r\nimport backgroundlogin from '@/views/Login/backgroundlogin.vue';\r\n\r\n\r\nexport default {\r\n  name: \"forgetpass\",\r\n  components: {\r\n    SlideNotification,\r\n    backgroundlogin\r\n  },\r\n  data() {\r\n    return {\r\n      resetForm: {\r\n        phone: '',\r\n        code: '',\r\n        newPassword: '',\r\n        confirmPassword: ''\r\n      },\r\n      passwordVisible: false,\r\n      confirmPasswordVisible: false,\r\n      errors: {\r\n        phone: '',\r\n        code: '',\r\n        newPassword: '',\r\n        confirmPassword: ''\r\n      },\r\n      codeSent: false,\r\n      countdown: 60,\r\n      timer: null,\r\n      showCodeSent: false, // 验证码发送提示状态\r\n      isVerifying: false,  // 验证中状态\r\n      showVerifying: false, // 验证中提示状态\r\n      notificationMinHeight: '50px' // 自定义通知高度\r\n    }\r\n  },\r\n  computed: {\r\n    isFormValid() {\r\n      return this.resetForm.phone &&\r\n          this.resetForm.code &&\r\n          this.resetForm.newPassword &&\r\n          this.resetForm.confirmPassword &&\r\n          !this.errors.phone &&\r\n          !this.errors.code &&\r\n          !this.errors.newPassword &&\r\n          !this.errors.confirmPassword;\r\n    }\r\n  },\r\n  created() {\r\n    // 页面创建时检查本地存储中的计时器状态\r\n    this.$emit('hiden-layout')\r\n  },\r\n\r\n  methods: {\r\n    validatePhone() {\r\n      const phoneRegex = /^1[3-9]\\d{9}$/;\r\n      if (!this.resetForm.phone) {\r\n        this.errors.phone = '请输入手机号';\r\n      } else if (!phoneRegex.test(this.resetForm.phone)) {\r\n        this.errors.phone = '请输入有效的手机号';\r\n      } else {\r\n        this.errors.phone = '';\r\n      }\r\n    },\r\n\r\n    validateCode() {\r\n      if (!this.resetForm.code) {\r\n        this.errors.code = '请输入验证码';\r\n      } else if (this.resetForm.code.length !== 4 || !/^\\d+$/.test(this.resetForm.code)) {\r\n        this.errors.code = '验证码格式不正确';\r\n      } else {\r\n        this.errors.code = '';\r\n      }\r\n    },\r\n\r\n    validateNewPassword() {\r\n      const newPassword = this.resetForm.newPassword;\r\n      this.errors.newPassword = ''; // 重置错误信息\r\n\r\n      if (!newPassword) {\r\n        this.errors.newPassword = '请输入新密码';\r\n        return;\r\n      }\r\n\r\n      const hasMinLength = newPassword.length >= 8;\r\n      if (!hasMinLength) {\r\n        this.errors.newPassword = '密码长度至少为8位';\r\n        return;\r\n      }\r\n\r\n      // 下面是三选二的强度项\r\n      const hasLetter = /[a-zA-Z]/.test(newPassword);\r\n      const hasNumber = /[0-9]/.test(newPassword);\r\n      const hasSymbol = /[!@#$%^&*()_+\\-=$${};':\"\\\\|,.<>\\/?]/.test(newPassword);\r\n      const hasUpperLower = /[A-Z]/.test(newPassword) && /[a-z]/.test(newPassword);\r\n\r\n      let strengthCount = 0;\r\n      if (hasLetter && hasNumber) strengthCount++; // 字母+数字\r\n      if (hasSymbol) strengthCount++;\r\n      if (hasUpperLower) strengthCount++;\r\n\r\n      if (strengthCount >= 2) {\r\n        this.errors.newPassword = ''; // 密码符合要求\r\n      } else {\r\n        const missingRequirements = [];\r\n        if (!(hasLetter && hasNumber)) missingRequirements.push('包含数字和字母');\r\n        if (!hasSymbol) missingRequirements.push('包含特殊符号');\r\n        if (!hasUpperLower) missingRequirements.push('包含大小写字母');\r\n\r\n        this.errors.newPassword = `密码强度不足，请满足以下至少两项要求：${missingRequirements.join('、')}`;\r\n      }\r\n\r\n      // 校验确认密码是否一致\r\n      if (this.resetForm.confirmPassword) {\r\n        this.validateConfirmPassword();\r\n      }\r\n    },\r\n\r\n\r\n    validateConfirmPassword() {\r\n      if (!this.resetForm.confirmPassword) {\r\n        this.errors.confirmPassword = '请确认新密码';\r\n      } else if (this.resetForm.confirmPassword !== this.resetForm.newPassword) {\r\n        this.errors.confirmPassword = '两次输入的密码不一致';\r\n      } else {\r\n        this.errors.confirmPassword = '';\r\n      }\r\n    },\r\n\r\n    async getVerificationCode() {\r\n      this.validatePhone();\r\n      if (this.errors.phone) return;\r\n\r\n      // 调用发送验证码API\r\n      try {\r\n        // 显示发送中状态\r\n        this.codeSent = true;\r\n        this.isSendingCode = true;\r\n        // 调用发送验证码接口\r\n        const response = await postLogin(\"/auth/sendCode\", {\r\n          phone: this.resetForm.phone\r\n        });\r\n\r\n        // 处理响应\r\n        if (response.data.code === 200) {\r\n          this.showCodeSent = true;\r\n          setTimeout(() => {\r\n            this.showCodeSent = false;\r\n          }, 3000);\r\n          this.startCountdown();\r\n        } else {\r\n          this.errors.code = response.data.msg || '验证码发送失败';\r\n          this.codeSent = false; // 发送失败时可重新发送\r\n        }\r\n      } catch (error) {\r\n        this.errors.code = '验证码发送失败，请稍后重试';\r\n        this.codeSent = false;\r\n      } finally {\r\n        this.isSendingCode = false;\r\n      }\r\n    },\r\n\r\n    startCountdown() {\r\n      // 清除可能存在的旧定时器\r\n      if (this.timer) {\r\n        clearInterval(this.timer);\r\n      }\r\n\r\n      this.countdown = 60;\r\n      // 使用固定的时间间隔\r\n      this.timer = setInterval(() => {\r\n        if (this.countdown <= 1) {\r\n          clearInterval(this.timer);\r\n          this.codeSent = false;\r\n          this.countdown = 60;\r\n        } else {\r\n          this.countdown--;\r\n        }\r\n      }, 1000);\r\n    },\r\n\r\n    // 验证验证码是否正确\r\n    verifyCode() {\r\n      return new Promise((resolve, reject) => {\r\n        this.isVerifying = true;\r\n        this.showVerifying = true;\r\n\r\n        postAnyData(\"/auth/verifyCode\", {\r\n          phone: this.resetForm.phone,\r\n          code: this.resetForm.code\r\n        }).then(res => {\r\n          this.showVerifying = false;\r\n          if (res.data && res.data.code === 200) {\r\n            resolve(true);\r\n          } else {\r\n            this.errors.code = res.data.msg || '验证码验证失败';\r\n            reject(new Error(res.data.msg || '验证码验证失败'));\r\n          }\r\n        }).catch(err => {\r\n          this.showVerifying = false;\r\n          this.errors.code = '验证码验证失败';\r\n          reject(err);\r\n        }).finally(() => {\r\n          this.isVerifying = false;\r\n        });\r\n      });\r\n    },\r\n\r\n    resetPassword() {\r\n      // 验证所有字段\r\n      this.validatePhone();\r\n      this.validateCode();\r\n      this.validateNewPassword();\r\n      this.validateConfirmPassword();\r\n\r\n      if (!this.isFormValid) return;\r\n\r\n      // 验证验证码\r\n      this.verifyCode().then(verified => {\r\n        if (verified) {\r\n          // 验证码正确，继续重置密码\r\n          postLogin(\"/auth/resetPassword\", {\r\n            phone: this.resetForm.phone,\r\n            code: this.resetForm.code,\r\n            password: this.resetForm.newPassword,\r\n          }).then(res => {\r\n            if (res.data && res.data.code === 200) {\r\n              this.goToLogin();\r\n            } else {\r\n              this.errors.code= res.data.message || '密码重置失败';\r\n            }\r\n          }).catch(err => {\r\n            this.errors.code= res.data.message || '网络异常，密码重置失败';\r\n          });\r\n        }\r\n      }).catch(err => {\r\n      });\r\n    },\r\n\r\n    goToLogin() {\r\n      this.$router.push('/login');\r\n    },\r\n\r\n    togglePasswordVisibility() {\r\n      this.passwordVisible = !this.passwordVisible;\r\n    },\r\n\r\n    toggleConfirmPasswordVisibility() {\r\n      this.confirmPasswordVisible = !this.confirmPasswordVisible;\r\n    }\r\n  },\r\n  beforeDestroy() {\r\n\r\n    this.$emit('hiden-layout')\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* Full page layout */\r\n.login-page {\r\n  display: flex;\r\n  min-height: 100vh;\r\n  overflow: hidden;\r\n}\r\n\r\n/* Left side styling */\r\n.left-side {\r\n  flex: 1;\r\n  position: relative;\r\n  background: linear-gradient(135deg, #cdb3e5, #5127d5);\r\n  display: flex;\r\n  flex-direction: column;\r\n  /*padding: 2rem;*/\r\n  color: #030303;\r\n}\r\n\r\n.logo-container {\r\n  z-index: 2;\r\n  padding: 1rem 0;\r\n}\r\n\r\n.logo {\r\n  font-size: 15px;\r\n  font-weight: bold;\r\n  margin: 0;\r\n  color: white;\r\n}\r\n\r\n.logo-subtitle {\r\n  font-size: 14px;\r\n  margin: 0;\r\n}\r\n\r\n.headline-container {\r\n  margin-top: 10px;\r\n  text-align: left;\r\n  z-index: 2;\r\n  max-width: 80%;\r\n  margin-left: 100px;\r\n}\r\n\r\n.headline {\r\n  font-size: 25px;\r\n  font-weight: 500;\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.subheadline {\r\n  font-size: 14px;\r\n  line-height: 1.6;\r\n  opacity: 0.9;\r\n  margin-bottom: 1rem\r\n}\r\n\r\n/* Animated background */\r\n.animated-background {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  overflow: hidden;\r\n  opacity: 0.4;\r\n}\r\n\r\n.hex-container {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.hex {\r\n  position: absolute;\r\n  width: 100px;\r\n  height: 110px;\r\n  background: rgba(255, 255, 255, 0.15);\r\n  clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);\r\n}\r\n\r\n.hex1 {\r\n  top: 20%;\r\n  left: 10%;\r\n  transform: scale(1.5);\r\n  animation: float 8s infinite ease-in-out;\r\n}\r\n\r\n.hex2 {\r\n  top: 60%;\r\n  left: 20%;\r\n  transform: scale(1.2);\r\n  animation: float 7s infinite ease-in-out reverse;\r\n}\r\n\r\n.hex3 {\r\n  top: 30%;\r\n  left: 50%;\r\n  transform: scale(1.3);\r\n  animation: float 10s infinite ease-in-out 1s;\r\n}\r\n\r\n.hex4 {\r\n  top: 70%;\r\n  left: 70%;\r\n  transform: scale(1.1);\r\n  animation: float 6s infinite ease-in-out 2s;\r\n}\r\n\r\n.hex5 {\r\n  top: 40%;\r\n  left: 80%;\r\n  transform: scale(1.4);\r\n  animation: float 9s infinite ease-in-out 3s;\r\n}\r\n\r\n.floating-cube {\r\n  position: absolute;\r\n  width: 50px;\r\n  height: 50px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  top: 25%;\r\n  left: 30%;\r\n  animation: float 8s infinite ease-in-out, rotate 15s infinite linear;\r\n}\r\n\r\n.floating-sphere {\r\n  position: absolute;\r\n  width: 70px;\r\n  height: 70px;\r\n  border-radius: 50%;\r\n  background: rgba(255, 255, 255, 0.15);\r\n  top: 50%;\r\n  left: 40%;\r\n  animation: float 10s infinite ease-in-out reverse;\r\n}\r\n\r\n.floating-diamond {\r\n  position: absolute;\r\n  width: 40px;\r\n  height: 40px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  transform: rotate(45deg);\r\n  top: 65%;\r\n  left: 60%;\r\n  animation: float 7s infinite ease-in-out 2s;\r\n}\r\n\r\n.ripple-effect {\r\n  position: absolute;\r\n  width: 200px;\r\n  height: 200px;\r\n  border-radius: 50%;\r\n  border: 3px solid rgba(255, 255, 255, 0.1);\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  animation: ripple 6s infinite linear;\r\n}\r\n\r\n@keyframes float {\r\n  0%, 100% {\r\n    transform: translateY(0);\r\n  }\r\n  50% {\r\n    transform: translateY(20px);\r\n  }\r\n}\r\n\r\n@keyframes rotate {\r\n  0% {\r\n    transform: rotate(0deg);\r\n  }\r\n  100% {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n@keyframes ripple {\r\n  0% {\r\n    width: 0;\r\n    height: 0;\r\n    opacity: 0.8;\r\n  }\r\n  100% {\r\n    width: 300px;\r\n    height: 300px;\r\n    opacity: 0;\r\n  }\r\n}\r\n\r\n/* Right side styling */\r\n.right-side {\r\n  flex: 1;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  background-color: #f8f9fa;\r\n  padding: 2rem;\r\n}\r\n\r\n.login-form-container {\r\n  background-color: white;\r\n  border-radius: 8px;\r\n  padding: 2rem;\r\n  width: 100%;\r\n  max-width: 400px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.login-form-container h3 {\r\n  font-size: 24px;\r\n  font-weight: 500;\r\n  margin-bottom: 20px;\r\n  text-align: center;\r\n  color: #333;\r\n}\r\n\r\n/* 表单容器，定义固定高度 */\r\n.form-container {\r\n  min-height: 300px;\r\n  position: relative;\r\n}\r\n\r\n.login-form {\r\n  margin-top: 20px;\r\n  width: 100%;\r\n}\r\n\r\n.form-note {\r\n  color: #999;\r\n  font-size: 12px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.input-group {\r\n  margin-bottom: 20px;\r\n  position: relative;\r\n}\r\n\r\n.input-group input {\r\n  width: 100%;\r\n  padding: 12px 15px;\r\n  border: 1px solid #ddd;\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n  transition: border-color 0.3s;\r\n}\r\n\r\n.input-group input:focus {\r\n  outline: none;\r\n  border-color: #6a26cd;\r\n}\r\n\r\n/*错误输入框变红*/\r\n.input-group.error input {\r\n  outline: none;\r\n  border: 2px solid #ff4d4f; /* 红色边框 */\r\n}\r\n\r\n/* 错误信息容器，固定高度 */\r\n.error-container {\r\n  min-height: 10px;\r\n  display: block;\r\n}\r\n\r\n/* 验证码输入框与按钮在同一行 */\r\n.verification-code .code-input-container {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.verification-code input {\r\n  flex: 1;\r\n  margin-right: 0; /* 移除原有的右边距 */\r\n}\r\n\r\n.error-container {\r\n  order: 3; /* 将错误容器放在最下方 */\r\n  width: 100%;\r\n  margin-top: 4px;\r\n}\r\n\r\n.get-code-btn-inline {\r\n  flex-shrink: 0;\r\n  width: 130px;\r\n  border: none;\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n  background-color: #2196f3;\r\n  color: #ffffff;\r\n}\r\n\r\n/* 密码输入容器，确保图标垂直居中 */\r\n.password-input-container {\r\n  position: relative;\r\n  margin-top: 30px;\r\n  /*margin-bottom: 20px;*/\r\n}\r\n\r\n.password-toggle {\r\n  position: absolute;\r\n\r\n  right: 15px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\ninput[type=\"password\"]::-ms-reveal,\r\ninput[type=\"password\"]::-webkit-credentials-auto-fill-button,\r\ninput[type=\"password\"]::-webkit-clear-button {\r\n  display: none !important;\r\n  pointer-events: none;\r\n}\r\n\r\n.eye-icon {\r\n  display: inline-block;\r\n  width: 20px;\r\n  height: 20px;\r\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z\"></path><circle cx=\"12\" cy=\"12\" r=\"3\"></circle></svg>');\r\n  background-repeat: no-repeat;\r\n  background-position: center;\r\n  opacity: 0.5;\r\n}\r\n\r\n.eye-icon.visible {\r\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z\"></path><circle cx=\"12\" cy=\"12\" r=\"3\"></circle><line x1=\"1\" y1=\"1\" x2=\"23\" y2=\"23\"></line></svg>');\r\n}\r\n\r\n.login-btn {\r\n  width: 100%;\r\n  padding: 12px 0;\r\n  background-color: #2196f3;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 4px;\r\n  font-size: 16px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s;\r\n}\r\n\r\n.login-btn:hover:not(:disabled) {\r\n  background-color: #043ef1;\r\n}\r\n\r\n.login-btn:disabled {\r\n  background-color: #2196f3;\r\n  cursor: not-allowed;\r\n}\r\n\r\n/*错误提示词提示*/\r\n.error-message {\r\n  color: #f44336;\r\n  font-size: 12px;\r\n  margin-top: 0px;\r\n  max-height: 10px;\r\n}\r\n\r\n/* Login link */\r\n.login-link {\r\n  margin-top: 15px;\r\n  text-align: center;\r\n  font-size: 14px;\r\n}\r\n\r\n.login-link a {\r\n  color: #4169E1;\r\n  text-decoration: none;\r\n}\r\n.logo-area {\r\n  flex: 0 0 auto;\r\n  margin-right: 10px;\r\n}\r\n\r\n.logo-link {\r\n  display: flex;\r\n  align-items: center;\r\n  text-decoration: none;\r\n  cursor: pointer;\r\n}\r\n\r\n.logo-area img {\r\n  height: 30px;\r\n  max-width: 100%;\r\n}\r\n\r\n.logo-text {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-left: 10px;\r\n}\r\n@media screen and (max-width: 768px) {\r\n  .left-side {\r\n    display: none; /* 在手机端隐藏左侧背景 */\r\n  }\r\n\r\n  .right-side {\r\n    flex: 1 0 100%; /* 让右侧占据全部宽度 */\r\n    padding: 1rem; /* 减少内边距以适应小屏幕 */\r\n  }\r\n\r\n  .login-form-container {\r\n    max-width: 100%; /* 让登录表单占据全部可用宽度 */\r\n    box-shadow: none; /* 移除阴影以节省空间 */\r\n    padding: 1.5rem; /* 调整内边距 */\r\n  }\r\n\r\n}\r\n</style>\r\n", "import mod from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./ForgetPassView.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./ForgetPassView.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./ForgetPassView.vue?vue&type=template&id=174895c0&scoped=true&\"\nimport script from \"./ForgetPassView.vue?vue&type=script&lang=js&\"\nexport * from \"./ForgetPassView.vue?vue&type=script&lang=js&\"\nimport style0 from \"./ForgetPassView.vue?vue&type=style&index=0&id=174895c0&prod&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"174895c0\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c,_setup=_vm._self._setupProxy;return _c('div',{staticClass:\"login-left-side\"},[_c('div',{staticClass:\"logo-container\"},[_c('a',{staticClass:\"logo-link\",on:{\"click\":function($event){return _vm.navigateTo('/index')}}},[_c('img',{staticClass:\"logo\",attrs:{\"src\":require(\"../../assets/logo_tiangong.png\"),\"alt\":\"算力租赁\"}})])]),_vm._m(0),_c('div',{staticClass:\"visual-element\"},[_c('div',{staticClass:\"server-illustration\"},_vm._l((_vm.servers),function(server,index){return _c('div',{key:index,staticClass:\"server-unit\",style:({\n               animationDelay: `${index * 0.2}s`,\n               transform: `translateY(${index * 4}px)`\n             })},[_c('div',{staticClass:\"server-light\"})])}),0),_c('div',{staticClass:\"connections\"})]),_c('div',{staticClass:\"features\"},_vm._l((_vm.features),function(feature,index){return _c('div',{key:index,staticClass:\"feature-item\"},[_c('div',{staticClass:\"feature-text\"},[_c('h3',[_vm._v(_vm._s(feature.title))]),_c('p',[_vm._v(_vm._s(feature.description))])])])}),0),_c('div',{staticClass:\"background-elements\"},_vm._l((20),function(i){return _c('div',{key:i,staticClass:\"floating-particle\",style:({\n             left: `${Math.random() * 100}%`,\n             top: `${Math.random() * 100}%`,\n             animationDuration: `${3 + Math.random() * 10}s`,\n             animationDelay: `${Math.random() * 5}s`\n           })})}),0)])\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c,_setup=_vm._self._setupProxy;return _c('div',{staticClass:\"bottom-text\"},[_c('h2',{staticClass:\"slogan\"},[_vm._v(\"高效算力 · 智慧未来\")]),_c('p',{staticClass:\"sub-slogan\"},[_vm._v(\"专业算力租赁服务，为您的业务提供强大支持\")])])\n}]\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"login-left-side\">\r\n    <!-- 公司Logo -->\r\n    <div class=\"logo-container\">\r\n      <a @click=\"navigateTo('/index')\" class=\"logo-link\">\r\n        <img class=\"logo\" src=\"../../assets/logo_tiangong.png\" alt=\"算力租赁\" />\r\n      </a>\r\n      <!--      <h1 class=\"company-name\">天工云</h1>-->\r\n    </div>\r\n\r\n    <div class=\"bottom-text\">\r\n      <h2 class=\"slogan\">高效算力 · 智慧未来</h2>\r\n      <p class=\"sub-slogan\">专业算力租赁服务，为您的业务提供强大支持</p>\r\n    </div>\r\n\r\n    <!-- 主要视觉元素 -->\r\n    <div class=\"visual-element\">\r\n      <div class=\"server-illustration\">\r\n        <div v-for=\"(server, index) in servers\" :key=\"index\"\r\n             class=\"server-unit\"\r\n             :style=\"{\r\n               animationDelay: `${index * 0.2}s`,\r\n               transform: `translateY(${index * 4}px)`\r\n             }\">\r\n          <div class=\"server-light\"></div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"connections\">\r\n<!--        <div v-for=\"i in 10\" :key=\"i\" class=\"connection-line\"></div>-->\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 特点介绍 -->\r\n    <div class=\"features\">\r\n      <div v-for=\"(feature, index) in features\" :key=\"index\" class=\"feature-item\">\r\n<!--        <div class=\"feature-icon\">-->\r\n<!--          <component :is=\"feature.icon\" />-->\r\n<!--        </div>-->\r\n        <div class=\"feature-text\">\r\n          <h3>{{ feature.title }}</h3>\r\n          <p>{{ feature.description }}</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 背景动画元素 -->\r\n    <div class=\"background-elements\">\r\n      <div v-for=\"i in 20\" :key=\"i\"\r\n           class=\"floating-particle\"\r\n           :style=\"{\r\n             left: `${Math.random() * 100}%`,\r\n             top: `${Math.random() * 100}%`,\r\n             animationDuration: `${3 + Math.random() * 10}s`,\r\n             animationDelay: `${Math.random() * 5}s`\r\n           }\">\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { defineComponent, ref } from 'vue';\r\n\r\n// 图标组件\r\nconst PerformanceIcon = {\r\n  template: `\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\r\n      <path d=\"M12 2v4\"></path>\r\n      <path d=\"m16.24 7.76 2.83-2.83\"></path>\r\n      <path d=\"M18 12h4\"></path>\r\n      <path d=\"m16.24 16.24 2.83 2.83\"></path>\r\n      <path d=\"M12 18v4\"></path>\r\n      <path d=\"m7.76 16.24-2.83 2.83\"></path>\r\n      <path d=\"M6 12H2\"></path>\r\n      <path d=\"m7.76 7.76-2.83-2.83\"></path>\r\n      <circle cx=\"12\" cy=\"12\" r=\"4\"></circle>\r\n    </svg>\r\n  `\r\n}\r\n\r\nconst ServerIcon = {\r\n  template: `\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\r\n      <rect width=\"20\" height=\"8\" x=\"2\" y=\"2\" rx=\"2\" ry=\"2\"></rect>\r\n      <rect width=\"20\" height=\"8\" x=\"2\" y=\"14\" rx=\"2\" ry=\"2\"></rect>\r\n      <line x1=\"6\" x2=\"6\" y1=\"6\" y2=\"6\"></line>\r\n      <line x1=\"6\" x2=\"6\" y1=\"18\" y2=\"18\"></line>\r\n    </svg>\r\n  `\r\n}\r\n\r\nconst ShieldIcon = {\r\n  template: `\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\r\n      <path d=\"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z\"></path>\r\n    </svg>\r\n  `\r\n}\r\n\r\nexport default defineComponent({\r\n  name: 'backgroundlogin',\r\n  components: {\r\n    PerformanceIcon,\r\n    ServerIcon,\r\n    ShieldIcon\r\n  },\r\n  methods:{\r\n    navigateTo(path) {\r\n      // 记录当前活动路径作为上一个活动路径\r\n      if (this.currentPath && this.currentPath !== path) {\r\n        this.previousActivePath = this.currentPath;\r\n\r\n        // 为当前活动链接和登录按钮添加 active-exit 类\r\n        this.$nextTick(() => {\r\n          const navLinks = document.querySelectorAll('.nav-link, .btn-login');\r\n          navLinks.forEach(link => {\r\n            if ((link.classList.contains('active') ||\r\n                    (path === '/login' && link.classList.contains('btn-login'))) &&\r\n                !link.classList.contains('active-exit')) {\r\n              link.classList.add('active-exit');\r\n\r\n              // 等待动画完成后移除 active-exit 类\r\n              setTimeout(() => {\r\n                link.classList.remove('active-exit');\r\n              }, 300); // 匹配你的 CSS transition 持续时间 (0.3s)\r\n            }\r\n          });\r\n\r\n          // 更新当前路径\r\n          this.currentPath = path;\r\n        });\r\n      } else {\r\n        this.currentPath = path;\r\n      }\r\n\r\n      // 如果当前路径与目标路径相同，则重新加载页面\r\n      if (this.$route.path === path) {\r\n        this.$nextTick(() => {\r\n          window.scrollTo({\r\n            top: 0,\r\n            behavior: 'instant' // 使用即时滚动而不是平滑滚动\r\n          });\r\n          this.$router.go(0); // 刷新当前页面\r\n        });\r\n      } else {\r\n        // 不同路径，正常导航并滚动到顶部\r\n        this.$router.push(path);\r\n        window.scrollTo({\r\n          top: 0,\r\n          behavior: 'instant'\r\n        });\r\n      }\r\n    },\r\n  },\r\n  setup() {\r\n    const logoSrc = ref('/api/placeholder/100/100');\r\n    const servers = ref(Array(5).fill(null));\r\n\r\n    const features = ref([\r\n      {\r\n        icon: 'PerformanceIcon',\r\n        title: '高性能算力',\r\n        description: '提供GPU/CPU灵活配置，满足AI训练、渲染等高算力需求'\r\n      },\r\n      {\r\n        icon: 'am-icon-shield',\r\n        title: '安全可靠',\r\n        description: '数据加密传输，多重备份，确保您的业务安全稳定运行'\r\n      }\r\n    ]);\r\n\r\n\r\n    return {\r\n      logoSrc,\r\n      servers,\r\n      features\r\n    };\r\n  }\r\n});\r\n</script>\r\n\r\n<style scoped>\r\n.login-left-side {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100vh;\r\n  background: linear-gradient(135deg, #025af7 0%, #2196f3 100%);\r\n  color: white;\r\n  padding: 40px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: space-between;\r\n  overflow: hidden;\r\n}\r\n\r\n/* Logo样式 */\r\n.logo-container {\r\n  margin-top: -60px;\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n  margin-left: -40px;\r\n  z-index: 10;\r\n}\r\n\r\n.logo {\r\n  width: 180px;\r\n  height: 140px;\r\n  border-radius: 12px;\r\n  margin-right: 15px;\r\n}\r\n\r\n/* 主视觉元素 */\r\n.visual-element {\r\n  flex: 1;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  perspective: 1000px;\r\n  z-index: 5;\r\n}\r\n\r\n.server-illustration {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 15px;\r\n  transform: rotateY(25deg) rotateX(10deg);\r\n  transform-style: preserve-3d;\r\n}\r\n\r\n.server-unit {\r\n  width: 200px;\r\n  height: 30px;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  border-radius: 6px;\r\n  position: relative;\r\n  backdrop-filter: blur(5px);\r\n  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.2);\r\n  animation: pulse 2s infinite;\r\n}\r\n\r\n.server-light {\r\n  position: absolute;\r\n  right: 10px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  width: 8px;\r\n  height: 8px;\r\n  border-radius: 50%;\r\n  background-color: rgba(231, 12, 12, 0);\r\n  box-shadow: 0 0 10px #ffffff;\r\n  animation: blink 1.5s infinite;\r\n}\r\n\r\n.connections {\r\n  position: absolute;\r\n  width: 100%;\r\n  height: 100%;\r\n  pointer-events: none;\r\n}\r\n\r\n.connection-line {\r\n  position: absolute;\r\n  height: 2px;\r\n  width: 100px;\r\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.7), transparent);\r\n  top: calc(30% + (40% * Math.random()));\r\n  left: calc(10% + (50% * Math.random()));\r\n  animation: move 4s infinite linear;\r\n  transform: rotate(calc(-30deg + (60deg * Math.random())));\r\n  opacity: 0.6;\r\n}\r\n\r\n/* 特点介绍 */\r\n.features {\r\n  margin-top: 40px;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 20px;\r\n  z-index: 10;\r\n}\r\n\r\n.feature-item {\r\n  flex: 1;\r\n  min-width: 250px;\r\n  display: flex;\r\n  align-items: flex-start;\r\n  padding: 15px;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: 12px;\r\n  backdrop-filter: blur(10px);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  transition: transform 0.3s, box-shadow 0.3s;\r\n}\r\n\r\n.feature-item:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);\r\n  border-color: rgba(255, 255, 255, 0.4);\r\n}\r\n\r\n.feature-icon {\r\n  margin-right: 15px;\r\n  color: white;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  padding: 10px;\r\n  border-radius: 10px;\r\n  height: 44px;\r\n  width: 44px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.feature-icon svg {\r\n  width: 24px;\r\n  height: 24px;\r\n}\r\n\r\n.feature-text h3 {\r\n  margin: 0 0 5px 0;\r\n  font-size: 18px;\r\n}\r\n\r\n.feature-text p {\r\n  margin: 0;\r\n  font-size: 14px;\r\n  opacity: 0.8;\r\n  line-height: 1.4;\r\n}\r\n\r\n/* 底部文案 */\r\n.bottom-text {\r\n  margin-top: -20px;\r\n  text-align: center;\r\n  z-index: 10;\r\n}\r\n\r\n.slogan {\r\n  font-size: 28px;\r\n  margin: 0 0 10px 0;\r\n  background: whitesmoke;\r\n  /*background: linear-gradient(to right, #ffffff, #2196f3);*/\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  font-weight: bold;\r\n}\r\n\r\n.sub-slogan {\r\n  font-size: 16px;\r\n  opacity: 0.9;\r\n  color: whitesmoke;\r\n  margin: 0;\r\n}\r\n\r\n/* 背景元素 */\r\n.background-elements {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  pointer-events: none;\r\n}\r\n\r\n.floating-particle {\r\n  position: absolute;\r\n  width: 6px;\r\n  height: 6px;\r\n  background-color: rgba(255, 255, 255, 0.5);\r\n  border-radius: 50%;\r\n  animation: float 10s infinite linear;\r\n}\r\n\r\n/* 动画 */\r\n@keyframes pulse {\r\n  0% { box-shadow: 0 0 5px rgba(255, 255, 255, 0.3); }\r\n  50% { box-shadow: 0 0 15px rgba(255, 255, 255, 0.5); }\r\n  100% { box-shadow: 0 0 5px rgba(255, 255, 255, 0.3); }\r\n}\r\n\r\n@keyframes blink {\r\n  0%, 100% { opacity: 1; }\r\n  50% { opacity: 0.5; }\r\n}\r\n\r\n@keyframes move {\r\n  0% { transform: translateX(-50px) rotate(var(--rotation, -20deg)); opacity: 0; }\r\n  50% { opacity: 0.8; }\r\n  100% { transform: translateX(150px) rotate(var(--rotation, -20deg)); opacity: 0; }\r\n}\r\n\r\n@keyframes float {\r\n  0% { transform: translate(0, 0); opacity: 0; }\r\n  25% { opacity: 0.8; }\r\n  50% { transform: translate(10px, 10px); }\r\n  75% { opacity: 0.4; }\r\n  100% { transform: translate(0, 0); opacity: 0; }\r\n}\r\n</style>", "import mod from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./backgroundlogin.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./backgroundlogin.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./backgroundlogin.vue?vue&type=template&id=771899f4&scoped=true&\"\nimport script from \"./backgroundlogin.vue?vue&type=script&lang=js&\"\nexport * from \"./backgroundlogin.vue?vue&type=script&lang=js&\"\nimport style0 from \"./backgroundlogin.vue?vue&type=style&index=0&id=771899f4&prod&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"771899f4\",\n  null\n  \n)\n\nexport default component.exports"], "names": ["render", "_vm", "this", "_c", "_self", "staticClass", "showCodeSent", "attrs", "notificationMinHeight", "on", "$event", "_e", "_v", "class", "errors", "phone", "directives", "name", "rawName", "value", "resetForm", "expression", "domProps", "validatePhone", "target", "composing", "$set", "_s", "code", "validateCode", "codeSent", "getVerificationCode", "countdown", "newPassword", "passwordVisible", "Array", "isArray", "_i", "validateNewPassword", "$$a", "$$el", "$$c", "checked", "$$v", "$$i", "concat", "slice", "_q", "togglePasswordVisibility", "confirmPassword", "confirmPasswordVisible", "validateConfirmPassword", "toggleConfirmPasswordVisibility", "isFormValid", "isVerifying", "resetPassword", "goToLogin", "staticRenderFns", "components", "SlideNotification", "backgroundlogin", "data", "timer", "showVerifying", "computed", "created", "$emit", "methods", "phoneRegex", "test", "length", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hasLetter", "hasNumber", "hasSymbol", "has<PERSON><PERSON><PERSON><PERSON><PERSON>", "strengthCount", "missingRequirements", "push", "join", "isSendingCode", "response", "postLogin", "setTimeout", "startCountdown", "msg", "error", "clearInterval", "setInterval", "verifyCode", "Promise", "resolve", "reject", "postAnyData", "then", "res", "Error", "catch", "err", "finally", "verified", "password", "message", "$router", "<PERSON><PERSON><PERSON><PERSON>", "component", "_setupProxy", "navigateTo", "require", "_m", "_l", "servers", "server", "index", "key", "style", "animationDelay", "transform", "features", "feature", "title", "description", "i", "left", "Math", "random", "top", "animationDuration", "PerformanceIcon", "template", "ServerIcon", "ShieldIcon", "defineComponent", "path", "currentPath", "previousActivePath", "$nextTick", "navLinks", "document", "querySelectorAll", "for<PERSON>ach", "link", "classList", "contains", "add", "remove", "$route", "window", "scrollTo", "behavior", "go", "setup", "logoSrc", "ref", "fill", "icon"], "sourceRoot": ""}