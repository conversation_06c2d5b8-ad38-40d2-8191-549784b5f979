{"version": 3, "file": "js/docs25.9fd7b08a.js", "mappings": "yHACA,IAAIA,EAA6B,IAAIC,IAAI,aACrCC,EAA6B,IAAID,IAAI,aACrCE,EAA6B,IAAIF,IAAI,aAErCG,EAAO,i9BAAk/BJ,EAA6B,yzBAAq1BE,EAA6B,uNAAmOC,EAA6B,mnCAE5oE,c", "sources": ["webpack://portal-ui/./src/docs/qustion.md"], "sourcesContent": ["// Imports\nvar ___HTML_LOADER_IMPORT_0___ = new URL(\"./imgs/qustion1.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_1___ = new URL(\"./imgs/qustion2.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_2___ = new URL(\"./imgs/qustion3.png\", import.meta.url);\n// Module\nvar code = \"<h1 id=\\\"常见问题\\\">常见问题</h1> <h2 id=\\\"平台使用\\\"><font style=\\\"color:#020817\\\">平台使用</font></h2> <h3 id=\\\"如何实现弹性扩缩容？\\\"><font style=\\\"color:#020817\\\">如何实现弹性扩缩容？</font></h3> <p><font style=\\\"color:#020817\\\">支持随时根据服务负载情况修改 GPU 数量。设置办法：</font></p> <ol> <li><font style=\\\"color:#020817\\\">服务部署阶段，选择合适的节点数量（推荐先选一个，后续再根据需要随时添加）</font></li> <li><font style=\\\"color:#020817\\\">在对应服务页面，点击左侧边栏的【设置】，修改服务运行的节点数量。节点越多，性能越好，但也会增加成本</font></li> </ol> <h3 id=\\\"什么是-serverless-与无状态？\\\"><font style=\\\"color:#020817\\\">什么是 Serverless 与无状态？</font></h3> <ul> <li><font style=\\\"color:#020817\\\">本系统采用无状态部署方式，默认情况下，不会对历史状态及数据进行存储。若您对服务状态存在特定要求，则需自行实现相应的存储功能。</font></li> <li><font style=\\\"color:#020817\\\">该部署方式更适用于推理场景，能够在应用上线后有效满足生产需求。然而，对于训练及科学计算研发等需求，您可能需要依据实际情况进行判断。</font></li> </ul> <h3 id=\\\"弹性部署与其他平台的容器实例（或虚拟机）有什么区别？\\\"><font style=\\\"color:#020817\\\">弹性部署与其他平台的容器实例（或虚拟机）有什么区别？</font></h3> <ol> <li><font style=\\\"color:#020817\\\">我们并未采用直接租赁实例这一常规方式，而是基于系统负载均衡机制，为用户动态的节点分配策略。在此过程中，流量自特定来源汇聚，随后被导向不同的目的地。</font></li> </ol> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_0___ + \"\\\" alt=\\\"\\\"></p> <ol start=\\\"2\\\"> <li><font style=\\\"color:#020817\\\">容器实例或虚拟机在关机后，存在一段数据保留期。然而，弹性部署的容器在关机后会即刻释放数据，并无数据保留情况。</font></li> </ol> <h2 id=\\\"部署阶段\\\"><font style=\\\"color:#020817\\\">部署阶段</font></h2> <h3 id=\\\"拉取镜像时间长，不知道拉取完了没\\\"><font style=\\\"color:#020817\\\">拉取镜像时间长，不知道拉取完了没</font></h3> <p><font style=\\\"color:#020817\\\">根据镜像的规模大小，首次拉取或许会耗费一定的下载时长。您能够查看节点详情中的事件记录，确认是否存在拉取镜像的相关事件。若长时间未能成功拉取，且事件呈现异常状态，建议您与技术人员取得联系，以便他们为您进行排查与处理。</font></p> <h3 id=\\\"快捷访问点开后域名链接报错\\\"><font style=\\\"color:#020817\\\">快捷访问点开后域名链接报错</font></h3> <p><font style=\\\"color:#67676c\\\">upstream connect error or disconnect/reset before headers. retried and the latest reset reason: remote connection failure, transport failure reason: delayed connect error: Connection refused</font></p> <p><font style=\\\"color:#020817\\\">当前域名解析可能尚未生效。请等待数秒后，刷新页面并再次尝试。</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_1___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"点击快捷访问端口后出现no-healthy-upstream\\\"><font style=\\\"color:#020817\\\">点击快捷访问端口后出现</font><font style=\\\"color:#020817;background-color:rgba(142,150,170,.14)\\\">no healthy upstream</font></h3> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_2___ + \"\\\" alt=\\\"\\\"></p> <p><font style=\\\"color:#020817\\\">该错误表明 API 网关或负载均衡器无法找到可用的健康后端服务实例来处理请求。</font></p> <p><font style=\\\"color:#020817\\\">解决措施：</font></p> <ol> <li><font style=\\\"color:#020817\\\">检查后端服务状态</font></li> </ol> <ul> <li><strong><font style=\\\"color:#020817\\\">确认服务是否运行</font></strong></li> <li><strong><font style=\\\"color:#020817\\\">查看服务日志</font></strong><font style=\\\"color:#020817\\\">：通过日志定位崩溃原因</font></li> </ul> <ol start=\\\"2\\\"> <li><font style=\\\"color:#020817\\\">验证健康检查配置</font></li> </ol> <ul> <li><strong><font style=\\\"color:#020817\\\">检查健康检查接口</font></strong></li> <li><strong><font style=\\\"color:#020817\\\">调整健康检查参数：</font></strong><font style=\\\"color:#020817\\\">在网关配置中增加健康检查的超时时间或重试次数（避免因短暂延迟误判）</font></li> </ul> <h3 id=\\\"huggingface-下载太慢了怎么办？\\\"><font style=\\\"color:#020817\\\">Huggingface 下载太慢了怎么办？</font></h3> <p><font style=\\\"color:#020817\\\">鉴于 Hugging Face 服务器位于海外，而我们自身的服务器处于国内环境。建议您参照以下文章内容，对 Hugging Face 加速代理源进行配置。完成配置后，即可实现模型的高速下载。</font></p> <p><a href=\\\"https://zhuanlan.zhihu.com/p/663712983\\\"><font style=\\\"color:#2f8ef4\\\">https://zhuanlan.zhihu.com/p/663712983</font></a></p> <p><br><br> <font style=\\\"color:#b2b2b2\\\">最后更新于: 2025/6/11 15:24</font></p> \";\n// Exports\nexport default code;"], "names": ["___HTML_LOADER_IMPORT_0___", "URL", "___HTML_LOADER_IMPORT_1___", "___HTML_LOADER_IMPORT_2___", "code"], "sourceRoot": ""}