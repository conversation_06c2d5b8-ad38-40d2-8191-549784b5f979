{"version": 3, "file": "js/docs15.75a21b55.js", "mappings": "wHACA,IAAIA,EAA6B,IAAIC,IAAI,aACrCC,EAA6B,IAAID,IAAI,aACrCE,EAA6B,IAAIF,IAAI,aACrCG,EAA6B,IAAIH,IAAI,aACrCI,EAA6B,IAAIJ,IAAI,YACrCK,EAA6B,IAAIL,IAAI,aACrCM,EAA6B,IAAIN,IAAI,aACrCO,EAA6B,IAAIP,IAAI,aACrCQ,EAA6B,IAAIR,IAAI,YACrCS,EAA6B,IAAIT,IAAI,aACrCU,EAA8B,IAAIV,IAAI,aACtCW,EAA8B,IAAIX,IAAI,aACtCY,EAA8B,IAAIZ,IAAI,aACtCa,EAA8B,IAAIb,IAAI,aACtCc,EAA8B,IAAId,IAAI,aAEtCe,EAAO,y/BAAwhChB,EAA6B,8WAAgYE,EAA6B,skCAA8mCC,EAA6B,uVAAqWC,EAA6B,0JAAsKC,EAA6B,mIAA2IC,EAA6B,6FAAqGC,EAA6B,2xFAAu1FC,EAA6B,2iIAAyqIC,EAA6B,mIAA6IC,EAA6B,sBAA4BC,EAA8B,mgFAAmmFC,EAA8B,2lCAAuoCC,EAA8B,wFAAgGC,EAA8B,qlCAAimCC,EAA8B,+wBAExxf,c", "sources": ["webpack://portal-ui/./src/docs/jupyter-lab.md"], "sourcesContent": ["// Imports\nvar ___HTML_LOADER_IMPORT_0___ = new URL(\"./imgs/universal1.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_1___ = new URL(\"./imgs/universal2.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_2___ = new URL(\"./imgs/jupyter3.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_3___ = new URL(\"./imgs/jupyter4.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_4___ = new URL(\"./imgs/jupyter5.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_5___ = new URL(\"./imgs/jupyter6.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_6___ = new URL(\"./imgs/jupyter7.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_7___ = new URL(\"./imgs/jupyter8.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_8___ = new URL(\"./imgs/jupyter9.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_9___ = new URL(\"./imgs/jupyter10.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_10___ = new URL(\"./imgs/jupyter11.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_11___ = new URL(\"./imgs/jupyter12.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_12___ = new URL(\"./imgs/jupyter13.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_13___ = new URL(\"./imgs/jupyter14.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_14___ = new URL(\"./imgs/jupyter15.png\", import.meta.url);\n// Module\nvar code = \"<h1 id=\\\"容器化部署-jupyterlab\\\"><font style=\\\"color:#020817\\\">容器化部署 JupyterLab</font></h1> <blockquote> <p><font style=\\\"color:#1f2937\\\">在 Serverless 类型的平台上此任务仅可使用单个节点运行，并且“停止任务”将致使数据丢失。若需保留数据，可参照文档中阿里云 OSS 配置部分，通过挂载云存储实现数据持久化。后续我们会推出容器实例产品，会一定程度上解决上面问题，敬请期待。</font></p> </blockquote> <h2 id=\\\"1在天工开物上运行-jupyterlab\\\"><font style=\\\"color:#020817\\\">1.在天工开物</font><font style=\\\"color:#020817\\\">上运行 JupyterLab</font></h2> <p><font style=\\\"color:#020817\\\">我们在</font><strong><font style=\\\"color:#020817\\\">服务配置——预制镜像</font></strong><font style=\\\"color:#020817\\\">中提供了预构建的 JupyterLab 容器映像，旨在满足一般要求。您可以选择直接在天工开物上运行这些容器以执行任务。或者，您还可以通过使用我们资源管理里镜像仓库所提供的免费 Docker 镜像仓库服务，来方便管理您自身的 Docker 镜像。</font></p> <p><font style=\\\"color:#67676c\\\">镜像仓库地址：</font><a href=\\\"https://test.tiangongkaiwu.top/#/console\\\">https://tiangongkaiwu.top/#/console</a></p> <p><font style=\\\"color:#020817\\\">访问天工开物控制台 </font><a href=\\\"https://test.tiangongkaiwu.top/#/console\\\">https://tiangongkaiwu.top/#/console</a></p> <p><font style=\\\"color:#020817\\\">点击新建部署任务</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_0___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"11-选择-gpu-型号\\\"><font style=\\\"color:#020817\\\">1.1 选择 GPU 型号</font></h3> <p><strong><font style=\\\"color:#020817\\\">选择 GPU 型号：</font></strong><font style=\\\"color:#020817\\\">推荐选择不限区域——享受全国闲置算力资源 随取随用</font></p> <p><strong><font style=\\\"color:#020817\\\">GPU 型号推荐配置：</font></strong><font style=\\\"color:#020817\\\">随便选 直接勾选「4090（1 卡）」，快速启动</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_1___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"12-选择服务配置\\\"><font style=\\\"color:#020817\\\">1.2 选择服务配置</font></h3> <p><font style=\\\"color:#020817\\\">直接选择</font><strong><font style=\\\"color:#020817\\\">预制镜像</font></strong><font style=\\\"color:#020817\\\">中我们打好的 JupyterLab 镜像</font></p> <p><font style=\\\"color:#020817\\\">版本信息：</font></p> <ul> <li><strong><font style=\\\"color:#020817\\\">系统包</font></strong><font style=\\\"color:#020817\\\">：sudo、wget、ca-certificates、bzip2、unzip、git 和 vim 等系统包。</font></li> <li><strong><font style=\\\"color:#020817\\\">Miniconda 版本</font></strong><font style=\\\"color:#020817\\\">：Miniconda3-py37_4.10.3。</font></li> <li><strong><font style=\\\"color:#020817\\\">Conda 环境版本</font></strong><font style=\\\"color:#020817\\\">：Python 3.7 的 Conda 环境。</font></li> <li><strong><font style=\\\"color:#020817\\\">Pip 和 Setuptools 版本</font></strong><font style=\\\"color:#020817\\\">：pip 20.3.4 和 setuptools 44.0.0。</font></li> <li><strong><font style=\\\"color:#020817\\\">TensorFlow 版本</font></strong><font style=\\\"color:#020817\\\">：tensorflow-gpu 1.15.5。</font></li> </ul> <p><strong><font style=\\\"color:#020817\\\">目前该镜像版本还不具备 OSS 的能力</font></strong></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_2___ + \"\\\" alt=\\\"\\\"></p> <p><font style=\\\"color:#020817\\\">（选配）云数据服务环境变量：访问密钥 ID/访问密钥，以及存储桶和文件夹名称传递给容器</font></p> <p><strong><font style=\\\"color:#020817\\\">高级设置：</font></strong><font style=\\\"color:#020817\\\">建议先部署 1 个节点进行服务验证，确认稳定性后再进行横向扩展</font></p> <p><font style=\\\"color:#020817\\\">每个节点对应一台独立服务器，增加节点数量可提升服务性能。系统通过负载均衡器自动分配流量，确保服务高可用性。</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_3___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"13-部署\\\"><font style=\\\"color:#020817\\\">1.3 部署</font></h3> <p><font style=\\\"color:#020817\\\">开始拉取 JupyterLab 镜像：</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_4___ + \"\\\" alt=\\\"\\\"></p> <p><font style=\\\"color:#020817\\\">由于镜像体积较大，我们需要耐心等待一段时间，部署完成后页面显示如下图。节点分配完成后，可以通过点击回传链接访问服务：</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_5___ + \"\\\" alt=\\\"\\\"></p> <p><font style=\\\"color:#020817\\\">然后开始使用 JupyterLab 服务</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_6___ + \"\\\" alt=\\\"\\\"></p> <p><font style=\\\"color:#020817\\\">现在，您可以编写 Python 代码来学习、测试、微调或训练 Hugging Face 中流行的 AI 模型。如有缺少库或依赖项，您可以在 notebook 或 Terminal 中在线安装它们。您还可以构建您的容器镜像，以包含基于提供的 Dockerfile 模板的特定库和依赖项。</font></p> <p><font style=\\\"color:#020817\\\">通过共享对 JupyterLab 实例的访问权限，团队成员可以协同编辑同一笔记本或使用相同的终端。在 JupyterLab 终端中进行的操作会实时同步到其他团队成员的浏览器界面中，反之亦然。这种协作体验类似于 WebEx 或 Zoom 中的屏幕共享功能，实现了高效的远程协作。</font></p> <p><u><font style=\\\"color:#020817\\\">JupyterLab</font></u><font style=\\\"color:#020817\\\"> </font><font style=\\\"color:#020817\\\">是一款开源应用，围绕计算型 Notebook 文档的概念构建。它支持代码共享和执行、数据处理、可视化，并提供一系列交互功能来创建图表。</font></p> <p><font style=\\\"color:#020817\\\">天工开物为 AI 与数据科学学习者及从业者提供创新解决方案：通过云端搭建 JupyterLab 环境直接调用高性价比弹性 GPU 算力，用户可无缝开展 CUDA 编程实践，进行 PyTorch/TensorFlow 框架开发，并高效完成模型训练调优、迁移学习及推理部署。突破传统模式三大瓶颈——无需采购昂贵硬件实现成本优化，免去本地环境搭建节省运维精力，更通过即时算力扩容和标准化开发环境显著提升研究效率，使开发者能够专注于核心算法创新与工程实践。</font></p> <h2 id=\\\"2自定义与扩展：利用-docker-hub-资源优化您的-jupyterlab-镜像\\\"><font style=\\\"color:#020817\\\">2.自定义与扩展：利用 Docker Hub 资源优化您的 JupyterLab 镜像</font></h2> <p><font style=\\\"color:#020817\\\">当您当前的任务需求无法由我们提供的默认镜像满足时，您可以选择使用 Docker Hub 上提供的多种预构建 JupyterLab 容器镜像。这些镜像针对不同场景（如人工智能/机器学习、数据科学、高性能计算等）进行了优化，支持开箱即用。此外，您还可以基于 GitHub 上提供的 Dockerfile 模板，灵活定制和扩展符合自身需求的镜像。</font></p> <table> <thead> <tr> <th><strong><font style=\\\"color:#020817\\\">镜像名称</font></strong></th> <th><strong><font style=\\\"color:#020817\\\">下载命令</font></strong></th> <th><strong><font style=\\\"color:#020817\\\">特点</font></strong></th> </tr> </thead> <tbody><tr> <td><font style=\\\"color:#020817\\\">jupyter/base-notebook</font></td> <td><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">docker pull jupyter/base-notebook</font></td> <td><font style=\\\"color:#020817\\\">官方基础镜像，适合初学者和轻量级需求。</font></td> </tr> <tr> <td><font style=\\\"color:#020817\\\">jupyter/minimal-notebook</font></td> <td><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">docker pull jupyter/minimal-notebook</font></td> <td><font style=\\\"color:#020817\\\">官方轻量级镜像，启动速度快。</font></td> </tr> <tr> <td><font style=\\\"color:#020817\\\">quay.io/jupyter/base-notebook</font></td> <td><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">docker pull quay.io/jupyter/base-notebook</font></td> <td><font style=\\\"color:#020817\\\">官方维护，基于 quay.io 仓库，稳定可靠。</font></td> </tr> <tr> <td><font style=\\\"color:#020817\\\">docker.1ms.run/jupyter/minimal-notebook:x86_64-python-3.10.9</font></td> <td><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">docker pull docker.1ms.run/jupyter/minimal-notebook:x86_64-python-3.10.9</font></td> <td><font style=\\\"color:#020817\\\">国内源镜像，适合网络条件不佳的用户。</font></td> </tr> </tbody></table> <h2 id=\\\"3jupyterlab-数据持久化容器镜像的构建\\\"><font style=\\\"color:#020817\\\">3.JupyterLab 数据持久化容器镜像的构建</font></h2> <p><font style=\\\"color:#020817\\\">为满足无状态容器工作负载的数据持久化需求，我们基于主流公有云存储服务（包括阿里云 OSS、华为云 OBS、腾讯云 COS 等）设计了集成化解决方案。该方案通过以下流程实现 JupyterLab 数据持久化：</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_7___ + \"\\\" alt=\\\"\\\"></p> <ol> <li><strong><font style=\\\"color:#020817\\\">云存储集成</font></strong><font style=\\\"color:#020817\\\"> </font><font style=\\\"color:#020817\\\">将云平台存储服务深度集成至预构建的 JupyterLab 容器镜像中。初始部署时需完成：</font></li> </ol> <ul> <li><font style=\\\"color:#020817\\\">在目标云平台创建存储资源（如 Bucket）</font></li> <li><font style=\\\"color:#020817\\\">通过环境变量向容器注入存储资源标识及访问凭证</font></li> </ul> <ol> <li><strong><font style=\\\"color:#020817\\\">持久化目录配置</font></strong><font style=\\\"color:#020817\\\"> 在容器内预设持久化工作目录：</font></li> </ol> <pre><code class=\\\"language-bash\\\">/root/data  # 云存储挂载点，所有操作均基于此目录\\n</code></pre> <p><font style=\\\"color:#020817\\\">该目录作为容器运行期间所有数据操作的基准路径。</font></p> <ol> <li><strong><font style=\\\"color:#020817\\\">自动化同步机制</font></strong><font style=\\\"color:#020817\\\"> </font><font style=\\\"color:#020817\\\">通过内置的 start.sh 脚本实现双向同步：</font></li> </ol> <ul> <li><strong><font style=\\\"color:#020817\\\">初始化同步</font></strong><font style=\\\"color:#020817\\\">：容器启动时自动执行全量数据拉取，将云端数据同步至本地目录</font></li> <li><strong><font style=\\\"color:#020817\\\">实时监控</font></strong><font style=\\\"color:#020817\\\">：基于 inotify 机制持续监听目录变更事件（创建/修改/删除）</font></li> <li><strong><font style=\\\"color:#020817\\\">增量同步</font></strong><font style=\\\"color:#020817\\\">：检测到变更后，通过对应云平台 CLI 工具执行增量数据回传</font></li> </ul> <ol> <li><strong><font style=\\\"color:#020817\\\">跨平台兼容性</font></strong><font style=\\\"color:#020817\\\"> </font><font style=\\\"color:#020817\\\">采用统一抽象层设计，通过标准化接口适配不同云平台存储服务，确保：</font></li> </ol> <ul> <li><font style=\\\"color:#020817\\\">同步逻辑与具体云平台解耦</font></li> <li><font style=\\\"color:#020817\\\">通过配置切换即可实现多云部署</font></li> <li><font style=\\\"color:#020817\\\">同步性能优化（如并发传输、断点续传）</font></li> </ul> <p><font style=\\\"color:#020817\\\">方案优势：</font></p> <ul> <li><strong><font style=\\\"color:#020817\\\">数据强一致性</font></strong><font style=\\\"color:#020817\\\">：通过事件驱动同步机制，保障本地与云端数据偏差&lt;1 秒</font></li> <li><strong><font style=\\\"color:#020817\\\">资源隔离</font></strong><font style=\\\"color:#020817\\\">：每个容器实例独立绑定专属存储空间，避免数据污染</font></li> <li><strong><font style=\\\"color:#020817\\\">弹性扩展</font></strong><font style=\\\"color:#020817\\\">：存储容量随云平台资源自动扩容，无硬性上限</font></li> <li><strong><font style=\\\"color:#020817\\\">安全传输</font></strong><font style=\\\"color:#020817\\\">：全程采用 TLS 加密通道，凭证信息仅驻留于内存环境变量</font></li> </ul> <p><font style=\\\"color:#020817\\\">在后台，我们采用 inotifywait 系统工具对/root/data 目录实施实时监控。无论是通过 JupyterLab 界面手动保存文档，还是系统自动保存机制触发更新，该监控组件均能即时捕获文件创建、删除或修改等操作事件。当检测到变更时，系统会自动激活同步流程。值得注意的是，当前支持的三大公有云平台均内置智能化同步机制，其核心算法通过精确计算源与目标间的数据差异，仅传输变更内容而非全量复制。这种架构设计显著优化了网络传输效率，将云服务 API 调用频率和实际传输数据量均压缩至最低阈值。</font></p> <p><font style=\\\"color:#020817\\\">关于机器学习资源的处理策略，从 Hugging Face 或 TensorFlow Hub 动态获取的模型参数与数据集默认缓存在系统级隐藏目录（/root/. cache 或/root/。keras) 。此类缓存数据遵循惰性同步原则，仅当用户显式将其迁移至/root/data 目录时才会触发云存储操作。考虑到当前主流云存储服务的标准化定价策略，在主要使用云存储承载代码库的场景下，整体存储成本可控制在极低水平。</font></p> <p><font style=\\\"color:#020817\\\">要利用预构建的 JupyterLab 容器映像，需要特定的环境变量来传递信息 到容器。如果不需要数据持久性，则可以省略相关的环境变量。</font></p> <table> <thead> <tr> <th><font style=\\\"color:#020817\\\">环境变量</font></th> <th><font style=\\\"color:#020817\\\">描述</font></th> </tr> </thead> <tbody><tr> <td><font style=\\\"color:#020817\\\">JUPYTERLAB_PW</font></td> <td><font style=\\\"color:#020817\\\">定义 JupyterLab 的访问密码，可选参数，默认值为 &#39;data&#39;</font></td> </tr> <tr> <td><font style=\\\"color:#020817\\\">ALIYUN_OSS_BUCKET_FOLDER </font><font style=\\\"color:#020817\\\">ALIYUN_OSS_ACCESS_KEY_ID </font><font style=\\\"color:#020817\\\">ALIYUN_OSS_ACCESS_KEY_SECRET</font></td> <td><font style=\\\"color:#020817\\\">阿里云 OSS 接入配置：</font><br/>+ <font style=\\\"color:#020817\\\">存储桶目标路径</font><br/>+ <font style=\\\"color:#020817\\\">AccessKey 凭证 ID</font><br/>+ <font style=\\\"color:#020817\\\">AccessKey 凭证密钥</font></td> </tr> </tbody></table> <h2 id=\\\"4云存储阿里云-oss-配置流程\\\"><font style=\\\"color:#020817\\\">4.云存储阿里云 OSS 配置流程</font></h2> <h3 id=\\\"41-登录阿里云控制台\\\"><strong><font style=\\\"color:#020817\\\">4.1 登录阿里云控制台</font></strong></h3> <ul> <li><font style=\\\"color:#020817\\\">访问</font><font style=\\\"color:#020817\\\"> </font><a href=\\\"https://www.aliyun.com/\\\"><font style=\\\"color:#2f8ef4\\\">阿里云官网</font></a><font style=\\\"color:#020817\\\">，进入</font><font style=\\\"color:#020817\\\"> </font><strong><font style=\\\"color:#020817\\\">控制台 &gt; 对象存储 OSS</font></strong><font style=\\\"color:#020817\\\">。</font></li> </ul> <p><font style=\\\"color:#020817\\\">创建对象存储 OSS 标准桶</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_8___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"42-创建存储桶（bucket）\\\"><strong><font style=\\\"color:#020817\\\">4.2 创建存储桶（Bucket）</font></strong></h3> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_9___ + \"\\\" alt=\\\"\\\"><img src=\\\"\" + ___HTML_LOADER_IMPORT_10___ + \"\\\" alt=\\\"\\\"></p> <ol> <li><font style=\\\"color:#020817\\\">点击 创建 Bucket，进入配置页面。</font></li> <li><strong><font style=\\\"color:#020817\\\">基础配置</font></strong><font style=\\\"color:#020817\\\">：</font><ul> <li><strong><font style=\\\"color:#020817\\\">Bucket 名称</font></strong><font style=\\\"color:#020817\\\">： 命名规则：</font><ul> <li><font style=\\\"color:#020817\\\">长度 3～63 个字符。</font></li> <li><font style=\\\"color:#020817\\\">仅允许小写字母、数字、短横线（-）。</font></li> <li><font style=\\\"color:#020817\\\">不能以短横线开头或结尾。</font></li> <li><font style=\\\"color:#020817\\\">在所选地域内全局唯一。</font></li> </ul> </li> <li><strong><font style=\\\"color:#020817\\\">地域选择</font></strong><font style=\\\"color:#020817\\\">：推荐选择靠近用户群体的地域，以降低访问延迟。</font></li> <li><strong><font style=\\\"color:#020817\\\">存储类型</font></strong><font style=\\\"color:#020817\\\">：</font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">标准存储</font><font style=\\\"color:#020817\\\">（高频访问数据推荐使用）</font></li> <li><strong><font style=\\\"color:#020817\\\">存储冗余类型</font></strong><font style=\\\"color:#020817\\\">：同城</font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">冗余存储</font><font style=\\\"color:#020817\\\">说明：多可用区冗余可保障跨机房容灾</font></li> </ul> </li> <li><strong><font style=\\\"color:#020817\\\">权限配置</font></strong><font style=\\\"color:#020817\\\">：</font><ul> <li><strong><font style=\\\"color:#020817\\\">读写权限</font></strong><font style=\\\"color:#020817\\\">：默认 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">私有</font><font style=\\\"color:#020817\\\">，按需调整为 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">公共读</font><font style=\\\"color:#020817\\\">。</font></li> <li><strong><font style=\\\"color:#020817\\\">服务端加密</font></strong><font style=\\\"color:#020817\\\">：可选 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">无</font><font style=\\\"color:#020817\\\"> 或 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">SSE-KMS</font><font style=\\\"color:#020817\\\">。</font></li> </ul> </li> </ol> <h3 id=\\\"43-创建用户目录\\\"><strong><font style=\\\"color:#020817\\\">4.3 创建用户目录</font></strong></h3> <ul> <li><font style=\\\"color:#020817\\\">存储桶创建完成后，进入</font><font style=\\\"color:#020817\\\"> </font><strong><font style=\\\"color:#020817\\\">文件管理</font></strong><font style=\\\"color:#020817\\\"> </font><font style=\\\"color:#020817\\\">页面。</font></li> <li><font style=\\\"color:#020817\\\">点击 </font><strong><font style=\\\"color:#020817\\\">新建目录</font></strong><font style=\\\"color:#020817\\\">，输入目录名称：</font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">/user1</font><font style=\\\"color:#020817\\\">。说明：OSS 为扁平化存储，目录通过前缀模拟，直接输入路径即可。</font></li> </ul> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_11___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"44-完成与验证\\\"><strong><font style=\\\"color:#020817\\\">4.4 完成与验证</font></strong></h3> <ol> <li><font style=\\\"color:#020817\\\">点击</font><font style=\\\"color:#020817\\\"> </font><strong><font style=\\\"color:#020817\\\">确定</font></strong><font style=\\\"color:#020817\\\">，完成存储桶创建。</font></li> <li><font style=\\\"color:#020817\\\">验证操作：</font></li> </ol> <ul> <li><font style=\\\"color:#020817\\\">在 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">Bucket 列表</font><font style=\\\"color:#020817\\\"> 中查看新建的 XXX（刚才创建的桶的名称）。</font></li> <li><font style=\\\"color:#020817\\\">进入存储桶，确认目录 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">/user1</font><font style=\\\"color:#020817\\\"> 已存在。</font></li> <li><font style=\\\"color:#020817\\\">通过 API/SDK 或控制台上传测试文件，确保读写正常。</font></li> </ul> <h3 id=\\\"45-生成访问密钥：在-ram-控制台创建子账号，授予oss读写权限，生成-accesskey-和-secretkey\\\"><strong><font style=\\\"color:#020817\\\">4.</font></strong><font style=\\\"color:#020817\\\">5 </font><strong><font style=\\\"color:#020817\\\">生成访问密钥</font></strong><font style=\\\"color:#020817\\\">：在 RAM 控制台创建子账号，授予OSS读写权限，生成 AccessKey 和 SecretKey</font></h3> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_12___ + \"\\\" alt=\\\"\\\"></p> <p><font style=\\\"color:#020817\\\">选择对应适配的角色权限并开通：</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_13___ + \"\\\" alt=\\\"\\\"></p> <p><font style=\\\"color:#020817\\\">使用提供的 JSON 文件创建 OSS IAM 策略（“access_its_own_folder”）。此策略将附加到 OSS IAM 用户，确保每个用户都可以独占访问同一 存储桶中自己的文件夹。</font></p> <pre><code class=\\\"language-json\\\">{\\n  &quot;Version&quot;: &quot;1&quot;,\\n  &quot;Statement&quot;: [\\n    {\\n      &quot;Effect&quot;: &quot;Allow&quot;,\\n      &quot;Action&quot;: [\\n        &quot;oss:PutObject&quot;,\\n        &quot;oss:GetObject&quot;,\\n        &quot;oss:DeleteObject&quot;\\n      ],\\n      &quot;Resource&quot;: [\\n        &quot;acs:oss:*:*:your-oss-bucket-name/${ram:User}/*&quot;,\\n        &quot;acs:oss:*:*:your-oss-bucket-name/${ram:User}&quot;\\n      ]\\n    },\\n    {\\n      &quot;Effect&quot;: &quot;Allow&quot;,\\n      &quot;Action&quot;: &quot;oss:ListObjects&quot;,\\n      &quot;Resource&quot;: &quot;acs:oss:*:*:your-oss-bucket-name&quot;,\\n      &quot;Condition&quot;: {\\n        &quot;StringLike&quot;: {\\n          &quot;oss:Prefix&quot;: &quot;${ram:User}/*&quot;\\n        }\\n      }\\n    }\\n  ]\\n}\\n</code></pre> <p><font style=\\\"color:#020817\\\">创建好密码后及时保存用户名称和密码相关信息（Access Key/Secret Key 对）</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_14___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"46-注意事项\\\"><strong><font style=\\\"color:#020817\\\">4.6 注意事项</font></strong></h3> <ul> <li><strong><font style=\\\"color:#020817\\\">不可修改项</font></strong><font style=\\\"color:#020817\\\">：创建后，Bucket 名称、地域、存储冗余类型均不可更改。</font></li> <li><strong><font style=\\\"color:#020817\\\">费用影响</font></strong><font style=\\\"color:#020817\\\">：多可用区冗余存储的单价高于本地冗余，请根据业务需求选择。</font></li> <li><strong><font style=\\\"color:#020817\\\">数据安全</font></strong><font style=\\\"color:#020817\\\">：私有 Bucket 需通过签名 URL 或 STS 临时授权访问。</font></li> </ul> <p><font style=\\\"color:#020817\\\">当在天工开物控制台上运行 JupyterLab 容器并将 阿里云 OSS 作为后端云存储时，三个与 阿里云 OSS 相关的环境变量用于将访问密钥 ID/访问密钥，以及存储桶和文件夹名称传递给容器。</font></p> <p><strong><font style=\\\"color:#67676c\\\"></font></strong></p> <p><br><br> <font style=\\\"color:#b2b2b2\\\">最后更新于: 2025/6/13 17:56</font></p> \";\n// Exports\nexport default code;"], "names": ["___HTML_LOADER_IMPORT_0___", "URL", "___HTML_LOADER_IMPORT_1___", "___HTML_LOADER_IMPORT_2___", "___HTML_LOADER_IMPORT_3___", "___HTML_LOADER_IMPORT_4___", "___HTML_LOADER_IMPORT_5___", "___HTML_LOADER_IMPORT_6___", "___HTML_LOADER_IMPORT_7___", "___HTML_LOADER_IMPORT_8___", "___HTML_LOADER_IMPORT_9___", "___HTML_LOADER_IMPORT_10___", "___HTML_LOADER_IMPORT_11___", "___HTML_LOADER_IMPORT_12___", "___HTML_LOADER_IMPORT_13___", "___HTML_LOADER_IMPORT_14___", "code"], "sourceRoot": ""}