{"version": 3, "file": "js/docs14.c92d7ec9.js", "mappings": "wHACA,IAAIA,EAA6B,IAAIC,IAAI,aACrCC,EAA6B,IAAID,IAAI,aACrCE,EAA6B,IAAIF,IAAI,aACrCG,EAA6B,IAAIH,IAAI,aACrCI,EAA6B,IAAIJ,IAAI,aACrCK,EAA6B,IAAIL,IAAI,aACrCM,EAA6B,IAAIN,IAAI,YACrCO,EAA6B,IAAIP,IAAI,YACrCQ,EAA6B,IAAIR,IAAI,aAErCS,EAAO,moBAAspBV,EAA6B,iNAA6NE,EAA6B,0JAAoKC,EAA6B,sIAAgJC,EAA6B,uHAA+HC,EAA6B,4GAAsHC,EAA6B,qcAA+dC,EAA6B,6qBAAqtBC,EAA6B,sBAA4BC,EAA6B,+jHAE53F,c", "sources": ["webpack://portal-ui/./src/docs/hunyuan-portrait.md"], "sourcesContent": ["// Imports\nvar ___HTML_LOADER_IMPORT_0___ = new URL(\"./imgs/universal1.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_1___ = new URL(\"./imgs/universal2.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_2___ = new URL(\"./imgs/hunyuan3.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_3___ = new URL(\"./imgs/hunyuan4.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_4___ = new URL(\"./imgs/hunyuan5.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_5___ = new URL(\"./imgs/hunyuan6.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_6___ = new URL(\"./imgs/hunyuan7.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_7___ = new URL(\"./imgs/hunyuan8.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_8___ = new URL(\"./imgs/hunyuan9.png\", import.meta.url);\n// Module\nvar code = \"<h1 id=\\\"容器化部署-hunyuanportrait\\\">容器化部署 HunyuanPortrait</h1> <p><font style=\\\"color:#020817\\\">本指南详细阐述了在天工开物平台上，高效部署与使用 HunyuanPortrait 项目的技术方案。HunyuanPortrait 能够基于单张肖像图片和驱动视频，精确地将面部表情和头部姿势转移到参考肖像中，生成自然流畅的动画。这意味着用户可以轻松地将自己的照片或绘画作品转化为生动的动态形象。</font></p> <h2 id=\\\"1在天工开物上运行-hunyuanportrait\\\">1.在天工开物上运行 HunyuanPortrait</h2> <p><font style=\\\"color:#020817\\\">天工开物平台提供预构建的 HunyuanPortrait 容器镜像，用户无需本地复杂环境配置，可快速完成部署并启用服务。以下是详细部署步骤：</font></p> <h3 id=\\\"11-创建部署服务\\\">1.1 创建部署服务</h3> <p><font style=\\\"color:#020817\\\">登录<a href=\\\"https://tiangongkaiwu.top/portal/#/console\\\"><font style=\\\"color:#06c\\\">天工开物控制台</font></a>，在控制台首页点击“弹性部署服务”进入管理页面。</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_0___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"12-选择-gpu-型号\\\">1.2 选择 GPU 型号</h3> <p><font style=\\\"color:#020817\\\">根据实际需求选择 GPU 型号：</font></p> <p><font style=\\\"color:#020817\\\">初次使用或调试阶段，推荐配置单张 NVIDIA RTX 4090 GPU</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_1___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"13-选择预制镜像\\\">1.3 选择预制镜像</h3> <p><font style=\\\"color:#020817\\\">在“服务配置”模块切换至“预制服务”选项卡，选择 HunyuanPortrait 官方镜像。</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_2___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"14-部署并访问服务\\\">1.4 部署并访问服务</h3> <p><font style=\\\"color:#020817\\\">点击“部署服务”，平台将自动拉取镜像并启动容器。</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_3___ + \"\\\" alt=\\\"\\\"></p> <p><font style=\\\"color:#020817\\\">部署完成后，在“快捷访问”中复制端口为 8089 的公网访问链接，后续是通过该地址调用服务。</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_4___ + \"\\\" alt=\\\"\\\"></p> <h2 id=\\\"2-快速上手\\\">2. 快速上手</h2> <p><font style=\\\"color:#020817\\\">系统架构图：</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_5___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"21-参考迁移动作视频\\\">2.1 参考迁移动作视频</h3> <p><font style=\\\"color:#020817\\\">可以直接上传自己的图像然后使其动起来~</font></p> <p><a href=\\\"videos/1.mp4\\\"><font style=\\\"color:#06c\\\">参考迁移动作视频1.mp4</font></a></p> <p><a href=\\\"videos/2.mp4\\\"><font style=\\\"color:#06c\\\">参考迁移动作视频2.mp4</font></a></p> <p><a href=\\\"videos/3.mp4\\\"><font style=\\\"color:#06c\\\">参考迁移动作视频3.mp4</font></a></p> <h3 id=\\\"22-迁移效果展示\\\">2.2 迁移效果展示</h3> <p><font style=\\\"color:#020817\\\">原图片：</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_6___ + \"\\\" alt=\\\"\\\"></p> <p><font style=\\\"color:#020817\\\">迁移动作视频：</font></p> <p><a href=\\\"videos/1_1.mp4\\\"><font style=\\\"color:#06c\\\">迁移动作视频1.mp4</font></a></p> <p><a href=\\\"videos/1_2.mp4\\\"><font style=\\\"color:#06c\\\">迁移动作视频2.mp4</font></a></p> <p><a href=\\\"videos/1_3.mp4\\\"><font style=\\\"color:#06c\\\">迁移动作视频3.mp4</font></a></p> <h3 id=\\\"23-肖像歌唱\\\">2.3 肖像歌唱</h3> <p><a href=\\\"videos/sing.mp4\\\"><font style=\\\"color:#06c\\\">肖像歌唱.mp4</font></a></p> <h3 id=\\\"24-肖像表演\\\">2.4 肖像表演</h3> <p><a href=\\\"videos/show.mp4\\\"><font style=\\\"color:#06c\\\">肖像表演.mp4</font></a></p> <h3 id=\\\"25-肖像制作脸\\\">2.5 肖像制作脸</h3> <p><a href=\\\"videos/face.mp4\\\"><font style=\\\"color:#06c\\\">肖像制作脸.mp4</font></a></p> <h2 id=\\\"3api-使用示例\\\">3.API 使用示例</h2> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_7___ + \"\\\" alt=\\\"\\\"><img src=\\\"\" + ___HTML_LOADER_IMPORT_8___ + \"\\\" alt=\\\"\\\"></p> <pre><code class=\\\"language-python\\\">import os\\nimport requests\\nimport shutil\\nfrom gradio_client import Client, handle_file\\nAPI_URL = &quot;https://d07071538-hyportrait070714-318-fwxdweue-8089.550c.cloud/&quot;\\nIMAGE_URL = &#39;https://raw.githubusercontent.com/gradio-app/gradio/main/test/test_files/bus.png&#39;\\nVIDEO_URL = &#39;https://github.com/gradio-app/gradio/raw/main/demo/video_component/files/world.mp4&#39;\\nINPUT_IMAGE_PATH = &quot;source_image.png&quot;\\nINPUT_VIDEO_PATH = &quot;driving_video.mp4&quot;\\nOUTPUT_VIDEO_PATH = &quot;generated_video.mp4&quot;\\ndef download_file(url, local_filename):\\n    &quot;&quot;&quot;从 URL 下载文件并保存到本地。&quot;&quot;&quot;\\n    if not os.path.exists(local_filename):\\n        print(f&quot;正在下载 {url} 到 {local_filename}...&quot;)\\n        try:\\n            with requests.get(url, stream=True, timeout=30) as r:\\n                r.raise_for_status()\\n                with open(local_filename, &#39;wb&#39;) as f:\\n                    for chunk in r.iter_content(chunk_size=8192):\\n                        f.write(chunk)\\n            print(&quot;下载完成。&quot;)\\n        except requests.exceptions.RequestException as e:\\n            print(f&quot;下载文件时出错：{e}&quot;)\\n            return False\\n    else:\\n        print(f&quot;{local_filename} 已存在，跳过下载。&quot;)\\n    return True\\ndef main():\\n    &quot;&quot;&quot;\\n    一个完整的 HunyuanPortrait Animation API 使用示例。\\n    &quot;&quot;&quot;\\n    print(&quot;--- 步骤 1: 准备输入文件 ---&quot;)\\n    if not (download_file(IMAGE_URL, INPUT_IMAGE_PATH) and download_file(VIDEO_URL, INPUT_VIDEO_PATH)):\\n        print(&quot;输入文件下载失败，程序终止。&quot;)\\n        return\\n    print(&quot;\\\\n--- 步骤 2: 调用 API ---&quot;)\\n    print(&quot;正在初始化 API 客户端...&quot;)\\n    try:\\n        # 初始化 Gradio 客户端\\n        client = Client(API_URL)\\n        print(&quot;客户端初始化完成。&quot;)\\n        print(&quot;正在发送请求到 API... 这可能需要一些时间。&quot;)\\n        # 调用 API 的 &#39;predict&#39; 端点\\n        # - 使用 handle_file 来处理本地文件上传。\\n        # - 根据 API 文档，&#39;video_path&#39; 参数需要一个包含 &#39;video&#39; 键的字典。\\n        result = client.predict(\\n            image=handle_file(INPUT_IMAGE_PATH),\\n            video_path={&quot;video&quot;: handle_file(INPUT_VIDEO_PATH)},\\n            api_name=&quot;/predict&quot;\\n        )\\n        # gradio_client 会将返回的视频文件保存在一个临时目录中，\\n        # &#39;result&#39; 变量就是该临时文件的路径。\\n        print(&quot;API 调用成功！&quot;)\\n        print(f&quot;结果已保存到临时路径：{result}&quot;)\\n        print(&quot;\\\\n--- 步骤 3: 保存结果 ---&quot;)\\n        # 将生成的视频从临时路径移动到当前工作目录\\n        if os.path.exists(result):\\n            print(f&quot;正在将生成视频从 &#39;{result}&#39; 移动到 &#39;{OUTPUT_VIDEO_PATH}&#39;&quot;)\\n            shutil.move(result, OUTPUT_VIDEO_PATH)\\n            print(f&quot;视频已成功保存到：{os.path.abspath(OUTPUT_VIDEO_PATH)}&quot;)\\n        else:\\n            # result 可能是一个包含文件路径的字典，例如 {&#39;video&#39;: &#39;...&#39;}\\n            # 这种情况在某些 Gradio 版本或配置中会出现\\n            if isinstance(result, dict) and &#39;video&#39; in result and os.path.exists(result[&#39;video&#39;]):\\n                temp_path = result[&#39;video&#39;]\\n                print(f&quot;正在将生成视频从 &#39;{temp_path}&#39; 移动到 &#39;{OUTPUT_VIDEO_PATH}&#39;&quot;)\\n                shutil.move(temp_path, OUTPUT_VIDEO_PATH)\\n                print(f&quot;视频已成功保存到：{os.path.abspath(OUTPUT_VIDEO_PATH)}&quot;)\\n            else:\\n                print(f&quot;错误：API 返回的路径 &#39;{result}&#39; 无效或文件不存在。&quot;)\\n    except Exception as e:\\n        print(f&quot;API 调用过程中发生错误：{e}&quot;)\\nif __name__ == &quot;__main__&quot;:\\n    main()\\n</code></pre> <p><br><br> <font style=\\\"color:#b2b2b2\\\">最后更新于: 2025/7/14 11:22</font></p> \";\n// Exports\nexport default code;"], "names": ["___HTML_LOADER_IMPORT_0___", "URL", "___HTML_LOADER_IMPORT_1___", "___HTML_LOADER_IMPORT_2___", "___HTML_LOADER_IMPORT_3___", "___HTML_LOADER_IMPORT_4___", "___HTML_LOADER_IMPORT_5___", "___HTML_LOADER_IMPORT_6___", "___HTML_LOADER_IMPORT_7___", "___HTML_LOADER_IMPORT_8___", "code"], "sourceRoot": ""}