import Vue from 'vue'
import App from './App.vue'
import router from './router'
import './assets/css/style.css'
import Toast from 'vue-toastification';
import 'vue-toastification/dist/index.css';
import {getRequest} from "@/api/api";

Vue.prototype.getRequest = getRequest;

Vue.use(Toast, {
  position: 'top-center',
  timeout: 3000,
  closeOnClick: true
});
Vue.config.productionTip = false

new Vue({
  router,
  render: h => h(App)
}).$mount('#app')
