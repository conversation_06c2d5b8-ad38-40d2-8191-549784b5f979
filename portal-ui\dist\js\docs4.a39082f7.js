"use strict";(self["webpackChunkportal_ui"]=self["webpackChunkportal_ui"]||[]).push([[6202],{7788:function(o,t,n){n.r(t);var e=new URL(n(1404),n.b),l=new URL(n(4117),n.b),r=new URL(n(8513),n.b),s=new URL(n(3419),n.b),i=new URL(n(6090),n.b),p=new URL(n(7020),n.b),c='<h1 id="容器化部署-cosyvoice">容器化部署 CosyVoice</h1> <p><font style="color:#020817">本指南详细阐述了在天工开物平台上，高效部署与使用 CosyVoice 项目的技术方案。基于新一代生成式语音大模型，CosyVoice 将文本理解和语音生成技术深度融合，能够精准解析并诠释各种文本内容，将其转化为如同真人发声般的自然语音，带来高度拟人化的自然语音合成体验。</font></p> <h2 id="1在天工开物上运行-cosyvoice"><strong>1.在天工开物上运行 CosyVoice</strong></h2> <p><font style="color:#020817">天工开物平台提供预构建的 CosyVoice 容器镜像，用户无需本地复杂环境配置，可快速完成部署并启用服务。以下是详细部署步骤：</font></p> <h3 id="11-创建部署服务"><strong>1.1 创建部署服务</strong></h3> <p><font style="color:#020817">登录<a href="https://tiangongkaiwu.top/portal/#/console"><font style="color:#06c">天工开物控制台</font></a>，在控制台首页点击“弹性部署服务”进入管理页面。</font></p> <p><img src="'+e+'" alt=""></p> <h3 id="12-选择-gpu-型号"><strong>1.2 选择 GPU 型号</strong></h3> <p><font style="color:#020817">根据实际需求选择 GPU 型号：</font></p> <p><font style="color:#020817">初次使用或调试阶段，推荐配置单张 NVIDIA RTX 4090 GPU</font></p> <p><img src="'+l+'" alt=""></p> <h3 id="13-选择预制镜像"><strong>1.3 选择预制镜像</strong></h3> <p><font style="color:#020817">在“服务配置”模块切换至“预制服务”选项卡，选择 CosyVoice 官方镜像。</font></p> <h3 id="14-部署并访问服务"><strong>1.4 部署并访问服务</strong></h3> <p><font style="color:#020817">点击“部署服务”，平台将自动拉取镜像并启动容器。</font></p> <p><img src="'+r+'" alt=""></p> <p><font style="color:#020817">部署完成后，在“快捷访问”中找到端口为 7865 的公网访问链接，点击即可在浏览器中使用 CosyVoice 的 Web 界面，或通过该地址调用 API 服务。</font></p> <h2 id="2快速上手"><strong>2.快速上手</strong></h2> <p><font style="color:#020817">使用 Safari 浏览器时，音频可能无法直接播放，需要下载后进行播放。</font></p> <p><font style="color:#020817">各模块功能如下：</font></p> <p><img src="'+s+'" alt=""></p> <h2 id="3api-调用指南">3.API 调用指南</h2> <p><font style="color:#020817">CosyVoice 提供完整的 API 接口体系，支持通过编程方式实现音乐创作全流程自动化。以下为核心接口详解与调用示范：</font></p> <p><img src="'+i+'" alt=""><img src="'+p+'" alt=""></p> <p><font style="color:#020817">使用前请确保已安装 <font style="color:#2f8ef4;background-color:#e7e9e8">gradio_client</font> 包：</font></p> <pre><code class="language-powershell">pip install gradio_client\n</code></pre> <h3 id="31-基础调用流程">3.1 基础调用流程</h3> <pre><code class="language-python">from gradio_client import Client, file\n\nclient = Client(&quot;https://d06251924-cosyvoicev200-318-qiwq719d-7860.550c.cloud/&quot;)\nseed = client.predict(api_name=&quot;/generate_seed&quot;)[0]\ninstruction = client.predict(\n    mode_checkbox_group=&quot;预训练音色&quot;,\n    api_name=&quot;/change_instruction&quot;\n)[0]\n\nresult = client.predict(\n    tts_text=&quot;这里是需要合成的文本内容&quot;,\n    mode_checkbox_group=&quot;预训练音色&quot;,\n    sft_dropdown=&quot;default&quot;,  # 替换为实际音色名\n    seed=seed,\n    prompt_wav_upload=file(&quot;/本地路径/参考音频.wav&quot;),\n    prompt_wav_record=file(&quot;/本地路径/参考音频.wav&quot;),\n    stream=&quot;false&quot;,\n    api_name=&quot;/generate_audio&quot;\n)\n\naudio_path = result[0]  # 获取生成音频路径\n</code></pre> <h3 id="32-参数使用说明">3.2 参数使用说明</h3> <h3 id="33-音频生成注意事项">3.3 音频生成注意事项</h3> <ol> <li><strong>文件要求</strong>：参考音频需满足：</li> </ol> <ul> <li>采样率 ≥16kHz</li> <li>WAV 格式最佳</li> <li>长度根据模式调整（“3s 极速复刻”需 5 秒素材）</li> </ul> <ol start="2"> <li><strong>跨语种模式</strong>：</li> </ol> <pre><code class="language-python">result = client.predict(\n    tts_text=&quot;Hello world&quot;,\n    mode_checkbox_group=&quot;跨语种复刻&quot;,\n    prompt_text=&quot;英文发音提示词&quot;,\n    api_name=&quot;/generate_audio&quot;\n)\n</code></pre> <ol> <li><strong>流式生成</strong>（适用长内容）：</li> </ol> <pre><code class="language-python">stream = &quot;true&quot;\n</code></pre> <h3 id="34-错误处理建议">3.4 错误处理建议</h3> <ul> <li><strong>音色加载失败</strong> → 检查<font style="color:#2f8ef4;background-color:#e7e9e8">sft_dropdown</font>值是否有效</li> <li><strong>音频生成中断</strong> → 降低<font style="color:#2f8ef4;background-color:#e7e9e8">speed</font>数值（1.0 为原速）</li> <li><strong>跨语种发音异常</strong> → 补充<font style="color:#2f8ef4;background-color:#e7e9e8">prompt_text</font>发音提示</li> </ul> <h3 id="35-输出结果示例">3.5 输出结果示例</h3> <pre><code class="language-python">print(result)\n\n\nimport shutil\nshutil.copyfile(&#39;/tmp/gradio/synth_audio.wav&#39;, &#39;最终成品.wav&#39;)\n</code></pre> <p><font style="color:#020817">实际调用时请根据业务场景调整模式选择和音频参数，预置音色列表可通过 Web 界面查看或在调用<font style="color:#2f8ef4;background-color:#e7e9e8">/change_instruction</font>时获取关联值。</font></p> <p><br><br> <font style="color:#b2b2b2">最后更新于: 2025/6/30 15:23</font></p> ';t["default"]=c},8513:function(o,t,n){o.exports=n.p+"img/cosy3.32176cc7.png"},3419:function(o,t,n){o.exports=n.p+"img/cosy4.76dad30b.png"},6090:function(o,t,n){o.exports=n.p+"img/cosy5.e99cc61f.png"},7020:function(o,t,n){o.exports=n.p+"img/cosy6.1d3f9f73.png"},1404:function(o,t,n){o.exports=n.p+"img/universal1.9a1b3f4b.png"},4117:function(o,t,n){o.exports=n.p+"img/universal2.4306636e.png"}}]);
//# sourceMappingURL=docs4.a39082f7.js.map