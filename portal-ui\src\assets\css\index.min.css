.index-page {
    width: 100%;
    border-bottom: 1px solid #e9e9e9
}

.index-page div[class^=am-tab-pane] {
    padding: 0
}

.index-page [data-am-widget=tabs] {
    margin: 0
}

.index-page .index-banner {
    width: 100%;
    height: 498px;
    background-size: cover
}

.index-page .index-banner .index-mask {
    height: 100%;
    background: rgba(0, 0, 0, .5);
    text-align: center;
    color: #fff;
    padding: 140px 0 0
}

.index-tab, .index-tab .am-active a {
    background-color: #fff !important
}

.index-page .index-banner .slide_simple--title {
    font-size: 46px;
    color: #fff;
    font-weight: 400
}

.index-page .index-banner .slide_simple--text {
    font-size: 18px;
    line-height: 36px;
    opacity: .6;
    margin: 28px 0 0
}

.index-page .index-banner .slide_simple--buttons {
    margin-top: 20px
}

.index-page .index-banner .slide_simple--buttons button {
    font-size: 16px;
    letter-spacing: 2px;
    padding: 9px 25px;
    border-radius: 17px
}

.index-tab {
    width: 1170px !important;
    margin: 0 auto !important
}

.index-tab .am-active {
    border-bottom: 4px solid #59bcdb
}

.index-tab a {
    position: relative;
    margin: 10px 0 10px 10px;
    border-right: 1px solid #e9e9e9
}

.index-tab a i {
    margin-top: 20px;
    color: #59bcdb;
    font-size: 38px
}

.index-tab a strong {
    font-size: 16px;
    display: inline-block;
    color: #262626;
    margin-top: 7px
}

.index-tab a p {
    color: #969696;
    font-size: 14px;
    margin-bottom: 0;
    margin-top: -10px
}

@media screen and (max-width: 640px) {
    .index-page .index-banner {
        height: auto
    }

    .index-page .index-banner .slide_simple--buttons {
        margin-bottom: 20px
    }

    .index-page .index-tab {
        width: 100% !important
    }

    .index-page .index-tab li {
        display: none !important
    }

    .index-page .index-tab .am-active {
        display: block !important
    }
}

.index-container {
    margin-top: 50px
}

.index-container .features_item--title {
    font-size: 16px;
    color: #262626;
    margin: 20px 0 -10px
}

.index-container .features_item--text {
    font-size: 15px;
    line-height: 23px;
    color: #969696
}

.index-container .index-more {
    text-align: center;
    padding: 70px 0 0
}

.index-container .index-more .am-btn-secondary {
    padding: 10px 26px;
    border-radius: 23px;
    background-color: #59bcdb;
    border-color: #59bcdb
}

.promo_detailed {
    position: relative;
    min-height: 500px;
    background: #fafafa
}

.promo_detailed .promo_detailed-container {
    position: relative;
    z-index: 10;
    height: 100%
}

.promo_detailed .am-g {
    margin-left: -15px;
    margin-right: -15px
}

.promo_detailed .am-g .promo_detailed--list {
    padding: 0 0 1px
}

.promo_detailed .am-g .promo_detailed--list .promo_detailed--list_item {
    position: relative;
    padding: 0 100px 0 80px;
    margin-top: 41px;
    list-style: none
}

.promo_detailed .am-g .promo_detailed--list .promo_detailed--list_item .promo_detailed--list_item_icon {
    display: block;
    top: 9px;
    left: 0;
    border: 2px solid #ed5151;
    border-radius: 3px;
    width: 56px;
    height: 56px;
    box-sizing: border-box;
    position: absolute
}

.promo_detailed .am-g .promo_detailed--list .promo_detailed--list_item .promo_detailed--list_item_icon i {
    color: #ed5151;
    font-size: 32px;
    top: 50%;
    left: 50%;
    margin-top: -2px;
    margin-left: 8px
}

.promo_detailed .am-g .promo_detailed--list .promo_detailed--list_item dl, .promo_detailed .am-g .promo_detailed--list .promo_detailed--list_item dl dt {
    font-size: 20px;
    color: #262626
}

.promo_detailed .am-g .promo_detailed--list .promo_detailed--list_item dl dd {
    font-size: 16px;
    line-height: 23px;
    color: #969696
}

.promo_detailed .promo_detailed--cta {
    padding: 0 25px;
    position: relative;
    height: 514px
}

.promo_detailed .promo_detailed--cta .promo_detailed--cta_wrap {
    position: absolute;
    top: 50%;
    transform: translate(0, -50%);
    text-align: center
}

.promo_detailed .promo_detailed--cta .promo_detailed--cta_wrap .promo_detailed--cta_text {
    background: rgba(27, 31, 32, .7);
    border-radius: 3px;
    padding: 38px 20px 38px 28px;
    max-width: 495px;
    color: #fff;
    font-size: 20px;
    line-height: 36px;
    font-weight: 300;
    text-align: left
}

.promo_detailed .promo_detailed--cta .promo_detailed--cta_wrap .promo_detailed--cta_footer {
    padding-top: 50px
}

.promo_detailed .promo_detailed-img {
    background-size: cover;
    width: 50%;
    height: 100%;
    background-color: #7d7e75;
    position: absolute;
    top: 0;
    right: 0
}

@media screen and (max-width: 640px) {
    .promo_detailed .promo_detailed--list {
        padding-left: 15px !important
    }

    .promo_detailed .promo_detailed--list .promo_detailed--list_item {
        padding: 0 0 0 80px !important
    }

    .promo_detailed .am-g {
        margin: 0
    }

    .promo_detailed .am-g .am-u-md-6:nth-of-type(2) {
        padding-right: 0;
        padding-left: 0
    }

    .index-container .index-more {
        padding: 35px 0 0
    }

    .index-page .index-banner .index-mask {
        padding: 70px 0 0
    }

    .index-page .index-banner .slide_simple--title {
        font-size: 40px
    }

    .promo_detailed-img {
        width: 100% !important;
        z-index: -1
    }

    .promo_detailed--cta_wrap {
        position: relative !important
    }

    .promo_detailed .promo_detailed--cta {
        height: 420px
    }
}

.index-container .service_item {
    margin-top: 20px;
    height: 270px;
    border: 1px solid #e9e9e9;
    border-radius: 3px;
    padding: 82px 15px 48px;
    position: relative
}

.index-container .service_item .service_item--icon {
    font-size: 58px;
    display: block;
    color: #59bcdb;
    position: absolute;
    top: 9px;
    left: 50%;
    transform: translate(-50%, 0)
}

.index-container .service_item .service_item--title {
    font-size: 20px;
    text-align: center;
    color: #262626
}

.index-container .service_item .service_item--text {
    font-size: 15px;
    text-align: left;
    color: #969696;
    line-height: 23px
}

.index-container .service_item .service_item--footer {
    padding-top: 14px;
    text-align: center
}

.pricing_compare {
    position: relative;
    margin: 48px 0 0;
    overflow: auto
}

.pricing_compare .pricing_compare--options {
    float: left;
    width: 45%;
    margin: 155px 0 0;
    border-top: 1px solid #e9e9e9;
    padding-left: 0
}

.pricing_compare .pricing_compare--options .pricing_compare--option {
    border-bottom: 1px solid #e9e9e9;
    font-size: 16px;
    line-height: 70px;
    padding: 0 0 0 65px;
    position: relative;
    background: #fff;
    list-style: none
}

.pricing_compare .pricing_compare--options .pricing_compare--option .pricing_compare--option_icon {
    position: absolute;
    top: 50%;
    margin-top: -2px;
    left: 33px;
    transform: translate(0, -50%);
    font-size: 19px;
    color: #a8a8a8;
    line-height: 1
}

.pricing_compare .pricing_compare--plans {
    float: left;
    width: 55%;
    margin-top: 48px
}

.pricing_compare .pricing_compare--plans .pricing_plan {
    float: left;
    width: 33.3%;
    border: 1px solid #e9e9e9;
    background: #fafafa;
    border-radius: 3px
}

.pricing_compare .pricing_compare--plans .pricing_plan:first-child {
    border-bottom-right-radius: 0;
    border-top-right-radius: 0
}

.pricing_compare .pricing_compare--plans .pricing_plan .pricing_plan--header {
    text-align: center;
    padding: 36px 0;
    height: 154px
}

.pricing_compare .pricing_compare--plans .pricing_plan .pricing_plan--header .pricing_plan--title {
    display: block;
    font-size: 18px;
    font-weight: 300;
    text-transform: uppercase
}

.pricing_compare .pricing_compare--plans .pricing_plan .pricing_plan--header .pricing_plan--title b {
    font-weight: 400
}

.pricing_compare .pricing_compare--plans .pricing_plan .pricing_plan--header .pricing_plan--label {
    background: #ed5151;
    isplay: inline-block;
    line-height: 1;
    margin: -11px 0 25px;
    text-transform: uppercase;
    font-size: 12px;
    font-weight: 500;
    border-radius: 100px;
    padding: 5px 10px 4px
}

.pricing_compare .pricing_compare--plans .pricing_plan .pricing_plan--header .pricing_plan--price {
    display: block;
    font-size: 40px;
    font-weight: 300;
    color: rgba(38, 38, 38, .6);
    margin: 24px 0 0
}

.pricing_compare .pricing_compare--plans .pricing_plan .pricing_plan--options {
    border-top: 1px solid #e9e9e9;
    padding-left: 0
}

.pricing_compare .pricing_compare--plans .pricing_plan .pricing_plan--options .pricing_plan--option {
    border-bottom: 1px solid #e9e9e9;
    font-size: 16px;
    color: #939393;
    text-align: center;
    line-height: 68px;
    list-style: none
}

.pricing_compare .pricing_compare--plans .pricing_plan .pricing_plan--options .pricing_plan--option b {
    color: #262626
}

.pricing_compare .pricing_compare--plans .popular {
    margin: -46px -1px 0;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    background: #fff
}

.pricing_compare .pricing_compare--plans .popular .pricing_plan--header {
    height: 200px
}

.pricing_compare .pricing_plan--footer {
    text-align: center;
    padding: 40px 45px
}

.pricing_compare .pricing_plan--footer .am-btn-danger, .pricing_compare .pricing_plan--footer .am-btn-secondary {
    background-color: transparent;
    color: #59bcdb;
    border: 2px solid #59bcdb;
    border-radius: 3px
}

@media screen and (max-width: 769px) {
    .pricing_compare--options {
        margin-top: 99px
    }
}

@media screen and (max-width: 992px) {
    .pricing_compare .pricing_compare--options {
        width: 100%;
        position: absolute
    }

    .pricing_compare--option {
        border-bottom: 1px solid #e9e9e9;
        font-size: 16px;
        padding: 0 0 0 65px;
        position: relative;
        background: #fff;
        line-height: 56px;
        margin-bottom: 67px
    }

    .pricing_compare--plans {
        float: none;
        width: 100% !important
    }

    .pricing_plan--header {
        padding: 18px 0 !important
    }

    .pricing_plan--price {
        font-size: 26px !important;
        margin: -15px 0 0 !important
    }

    .pricing_plan--option b {
        font-size: 14px
    }

    .pricing_compare .pricing_plan--footer {
        padding: 20px 10px
    }

    .pricing_plan--footer .am-btn-secondary {
        padding: 10px 12px
    }

    .pricing_compare .pricing_plan--option {
        padding-top: 10px;
        margin-top: 64px;
        height: 74px;
        line-height: 27px !important
    }

    .pricing_compare .pricing_plan--option b, .pricing_compare .pricing_plan--option strong {
        display: block
    }

    .pricing_compare .pricing_plan--options {
        margin-top: -47px
    }
}

@media (max-width: 500px) {
    .pricing_plan--title {
        font-size: 14px
    }
}

.promo_banner-container {
    position: relative;
    /*background: url(../images/index/promo_banner_bg.jpg) 50% 0 no-repeat;*/
    background-size: cover;
    padding: 0
}

.promo_banner-box {
    height: 100%;
    background: rgba(0, 0, 0, .5);
    padding: 83px 0 100px;
    color: #fff;
    text-align: center
}

.promo_banner-box .promo_banner--title {
    font-size: 50px;
    font-weight: 300
}

.promo_banner-box .promo_banner--text {
    font-size: 18px;
    line-height: 30px;
    opacity: .6;
    padding: 32px 0 43px;
    font-weight: 300
}

.promo_banner-box .promo_banner--footer .am-btn-secondary {
    border-radius: 4px
}

@media screen and (max-width: 640px) {
    .section.promo_banner-container {
        padding: 0 !important
    }

    .promo_banner-box .promo_banner--title {
        font-size: 34px !important
    }

    .promo_banner-box .promo_banner--text {
        font-size: 16px
    }

    .promo_banner-box {
        padding: 40px 0
    }

    .promo_banner-box .container {
        padding: 0 20px
    }
}

.customer-logo {
    padding: 7px 0
}

.customer-logo .am-g .am-u-md-2 {
    text-align: center;
    display: inline-block
}

.customer-logo .am-g .am-u-md-2 a {
    width: 100%;
    height: 100px;
    display: block;
    display: table-cell;
    vertical-align: middle
}

.customer-logo .am-g .am-u-md-2 a img {
    width: 100%;
    vertical-align: middle
}

.customer-logo .am-g .am-u-md-2 a img:nth-of-type(2) {
    display: none
}

@media screen and (max-width: 640px) {
    .customer-logo .normal-logo {
        display: none
    }

    .customer-logo .am-active {
        display: block !important
    }
}
