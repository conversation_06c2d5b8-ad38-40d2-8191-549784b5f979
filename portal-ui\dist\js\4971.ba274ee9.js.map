{"version": 3, "file": "js/4971.ba274ee9.js", "mappings": "kJAAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,UAAU,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIK,GAAG,YAAYH,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIK,GAAG,iBAAiBH,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,IAAI,CAACI,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOP,EAAIQ,WAAW,SAAS,IAAI,CAACN,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIK,GAAG,UAAUH,EAAG,IAAI,CAACI,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOP,EAAIQ,WAAW,WAAW,IAAI,CAACN,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIK,GAAG,kBAAkBH,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIK,GAAG,WAAWH,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIK,GAAG,UAAUH,EAAG,IAAI,CAACI,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOP,EAAIQ,WAAW,QAAQ,IAAI,CAACN,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIK,GAAG,YAAYH,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIK,GAAG,QAAQH,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIK,GAAG,cAAcL,EAAIS,GAAG,GAAGT,EAAIS,GAAG,GAAGT,EAAIS,GAAG,KAAKT,EAAIS,GAAG,IACtjC,EACIC,EAAkB,CAAC,WAAY,IAAIV,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIK,GAAG,YAAYH,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIK,GAAG,YAAYH,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIK,GAAG,aAAaH,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIK,GAAG,YAAYH,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIK,GAAG,kBACza,EAAE,WAAY,IAAIL,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIK,GAAG,YAAYH,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACS,MAAM,CAAC,IAAMC,EAAQ,IAAqC,IAAM,kBACtQ,EAAE,WAAY,IAAIZ,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIK,GAAG,WAAWH,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACS,MAAM,CAAC,IAAMC,EAAQ,IAAqC,IAAM,iBACrQ,EAAE,WAAY,IAAIZ,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,IAAI,CAACW,YAAY,CAAC,MAAQ,UAAU,kBAAkB,QAAQF,MAAM,CAAC,KAAO,4BAA4B,OAAS,WAAW,CAACX,EAAIK,GAAG,0BAA0BL,EAAIK,GAAG,8CAC7S,GC2EA,G,QAAA,CACAS,KAAA,SACAC,OACA,OACA,CAEA,EACAC,QAAA,CACAR,WAAAS,GAEA,KAAAC,aAAA,KAAAA,cAAAD,GACA,KAAAE,mBAAA,KAAAD,YAGA,KAAAE,WAAA,KACA,MAAAC,EAAAC,SAAAC,iBAAA,yBACAF,EAAAG,SAAAC,KACAA,EAAAC,UAAAC,SAAA,WACA,WAAAV,GAAAQ,EAAAC,UAAAC,SAAA,gBACAF,EAAAC,UAAAC,SAAA,iBACAF,EAAAC,UAAAE,IAAA,eAGAC,YAAA,KACAJ,EAAAC,UAAAI,OAAA,iBACA,KACA,IAIA,KAAAZ,YAAAD,CAAA,KAGA,KAAAC,YAAAD,EAIA,KAAAc,OAAAd,OAAAA,EACA,KAAAG,WAAA,KACAY,OAAAC,SAAA,CACAC,IAAA,EACAC,SAAA,YAEA,KAAAC,QAAAC,GAAA,OAIA,KAAAD,QAAAE,KAAArB,GACAe,OAAAC,SAAA,CACAC,IAAA,EACAC,SAAA,YAGA,KCtIwQ,I,UCQpQI,GAAY,OACd,EACAxC,EACAW,GACA,EACA,KACA,WACA,MAIF,EAAe6B,EAAiB,O,oECnBhC,IAAIxC,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAAGF,EAAIwC,SAGmyOtC,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgBE,GAAG,CAAC,WAAaN,EAAIyC,iBAAiB,UAAYzC,EAAI0C,gBAAgB,SAAW1C,EAAI2C,iBAAiB,CAACzC,EAAG,MAAM,CAACE,YAAY,uBAAuBwC,MAAO,CAAEC,UAAY,cAAuC,KAAzB7C,EAAI8C,yBAAiC9C,EAAI+C,GAAI/C,EAAIgD,cAAc,SAASC,EAAKC,GAAO,OAAOhD,EAAG,MAAM,CAACiD,IAAID,EAAM9C,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACF,EAAG,MAAM,CAACE,YAAY,mBAAmBO,MAAM,CAAC,IAAMsC,EAAKG,IAAI,IAAM,MAAMlD,EAAG,MAAM,CAACE,YAAY,wBAAwBiD,MAAM,OAASJ,EAAKK,QAAQC,UAAU,CAACrD,EAAG,KAAK,CAACE,YAAY,sBAAsBoD,SAAS,CAAC,UAAYxD,EAAIyD,GAAGR,EAAKK,QAAQI,UAAUxD,EAAG,IAAI,CAACE,YAAY,sBAAsB,CAACJ,EAAIK,GAAGL,EAAIyD,GAAGR,EAAKK,QAAQK,SAASzD,EAAG,MAAM,CAACE,YAAY,yBAAyB,EAAGJ,EAAI4D,SAAWX,EAAKK,QAAQO,cAAe3D,EAAG,IAAI,CAACE,YAAY,qCAAqCO,MAAM,CAAC,KAAO,KAAKL,GAAG,CAAC,MAAQ,SAASC,GAAgC,OAAxBA,EAAOuD,iBAAwB9D,EAAIQ,WAAWyC,EAAKK,QAAQO,cAAc,IAAI,CAAC7D,EAAIK,GAAG,IAAIL,EAAIyD,GAAGR,EAAKK,QAAQS,kBAAkB,OAAO/D,EAAIgE,MAAOhE,EAAI4D,SAAWX,EAAKK,QAAQW,YAAa/D,EAAG,IAAI,CAACE,YAAY,mCAAmCO,MAAM,CAAC,KAAO,KAAKL,GAAG,CAAC,MAAQ,SAASC,GAAgC,OAAxBA,EAAOuD,iBAAwB9D,EAAIQ,WAAWyC,EAAKK,QAAQW,YAAY,IAAI,CAACjE,EAAIK,GAAG,IAAIL,EAAIyD,GAAGR,EAAKK,QAAQY,gBAAgB,OAAOlE,EAAIgE,KAAMf,EAAKK,QAAQa,UAAWjE,EAAG,IAAI,CAACE,YAAY,8BAA8BO,MAAM,CAAC,KAAO,KAAKL,GAAG,CAAC,MAAQ,SAASC,GAAgC,OAAxBA,EAAOuD,iBAAwB9D,EAAIQ,WAAWyC,EAAKK,QAAQa,UAAU,IAAI,CAACnE,EAAIK,GAAG,IAAIL,EAAIyD,GAAGR,EAAKK,QAAQc,cAAc,OAAOpE,EAAIgE,YAAY,IAAG,GAAG9D,EAAG,MAAM,CAACE,YAAY,4BAA4BJ,EAAI+C,GAAI/C,EAAIgD,cAAc,SAASC,EAAKC,GAAO,OAAOhD,EAAG,OAAO,CAACiD,IAAID,EAAMG,MAAM,CAAEgB,OAAQrE,EAAI8C,qBAAuBI,GAAQ5C,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOP,EAAIsE,UAAUpB,EAAM,IAAI,IAAG,KAAKhD,EAAG,UAAU,CAACE,YAAY,qCAAqC,CAACJ,EAAIS,GAAG,GAAGP,EAAG,MAAM,CAACE,YAAY,mBAAmBJ,EAAI+C,GAAI/C,EAAIuE,MAAM,SAASC,EAAItB,GAAO,OAAOhD,EAAG,MAAM,CAACiD,IAAID,EAAM9C,YAAY,kBAAkBiD,MAAM,CAAE,YAAemB,EAAIC,aAAcnE,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOP,EAAIQ,WAAW,WAAW,IAAI,CAACN,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACF,EAAG,KAAK,CAACE,YAAY,mBAAmB,CAACJ,EAAIK,GAAGL,EAAIyD,GAAGe,EAAI1D,SAASZ,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAAEoE,EAAIC,YAAavE,EAAG,OAAO,CAACE,YAAY,wBAAwB,CAACJ,EAAIK,GAAG,QAAQL,EAAIgE,KAAMQ,EAAIE,MAAOxE,EAAG,OAAO,CAACE,YAAY,kBAAkB,CAACJ,EAAIK,GAAG,SAASL,EAAIgE,SAAS9D,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,OAAO,CAACE,YAAY,qBAAqB,CAACJ,EAAIK,GAAG,UAAUH,EAAG,OAAO,CAACE,YAAY,qBAAqB,CAACJ,EAAIK,GAAGL,EAAIyD,GAAGe,EAAIG,iBAAiB,eAAezE,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,OAAO,CAACE,YAAY,qBAAqB,CAACJ,EAAIK,GAAG,UAAUH,EAAG,OAAO,CAACE,YAAY,qBAAqB,CAACJ,EAAIK,GAAGL,EAAIyD,GAAGe,EAAII,eAAe,qBAAqB1E,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAAEoE,EAAIK,cAAe3E,EAAG,OAAO,CAACE,YAAY,yBAAyB,CAACJ,EAAIK,GAAG,IAAIL,EAAIyD,GAAGe,EAAIK,eAAe,QAAQ7E,EAAIgE,KAAK9D,EAAG,OAAO,CAACE,YAAY,wBAAwB,CAACJ,EAAIK,GAAG,IAAIL,EAAIyD,GAAGe,EAAIM,OAAO,WAAW,IAAG,KAAK5E,EAAG,UAAU,CAACE,YAAY,4CAA4C,CAACJ,EAAIS,GAAG,GAAGP,EAAG,MAAM,CAACE,YAAY,+BAA+B,CAACF,EAAG,MAAM,CAACE,YAAY,4BAA4B,CAACF,EAAG,QAAQ,CAACE,YAAY,2BAA2B,CAACF,EAAG,QAAQ,CAACA,EAAG,KAAK,CAACA,EAAG,KAAK,CAACF,EAAIK,GAAG,WAAWL,EAAI+C,GAAI/C,EAAI+E,gBAAgB,SAASP,GAAK,OAAOtE,EAAG,KAAK,CAACiD,IAAIqB,EAAI1D,MAAM,CAACd,EAAIK,GAAGL,EAAIyD,GAAGe,EAAI1D,QAAQ,KAAI,KAAKZ,EAAG,QAAQ,CAACA,EAAG,KAAK,CAACA,EAAG,KAAK,CAACF,EAAIK,GAAG,QAAQL,EAAI+C,GAAI/C,EAAI+E,gBAAgB,SAASP,GAAK,OAAOtE,EAAG,KAAK,CAACiD,IAAIqB,EAAI1D,MAAM,CAACd,EAAIK,GAAGL,EAAIyD,GAAGe,EAAIQ,gBAAgB,KAAI,GAAG9E,EAAG,KAAK,CAACA,EAAG,KAAK,CAACF,EAAIK,GAAG,YAAYL,EAAI+C,GAAI/C,EAAI+E,gBAAgB,SAASP,GAAK,OAAOtE,EAAG,KAAK,CAACiD,IAAIqB,EAAI1D,MAAM,CAACd,EAAIK,GAAGL,EAAIyD,GAAGe,EAAIS,mBAAmB,KAAI,GAAG/E,EAAG,KAAK,CAACA,EAAG,KAAK,CAACF,EAAIK,GAAG,YAAYL,EAAI+C,GAAI/C,EAAI+E,gBAAgB,SAASP,GAAK,OAAOtE,EAAG,KAAK,CAACiD,IAAIqB,EAAI1D,MAAM,CAACd,EAAIK,GAAGL,EAAIyD,GAAGe,EAAIU,mBAAmB,KAAI,GAAGhF,EAAG,KAAK,CAACA,EAAG,KAAK,CAACF,EAAIK,GAAG,QAAQL,EAAI+C,GAAI/C,EAAI+E,gBAAgB,SAASP,GAAK,OAAOtE,EAAG,KAAK,CAACiD,IAAIqB,EAAI1D,MAAM,CAACd,EAAIK,GAAGL,EAAIyD,GAAGe,EAAIW,UAAU,KAAI,GAAGjF,EAAG,KAAK,CAACA,EAAG,KAAK,CAACF,EAAIK,GAAG,UAAUL,EAAI+C,GAAI/C,EAAI+E,gBAAgB,SAASP,GAAK,OAAOtE,EAAG,KAAK,CAACiD,IAAIqB,EAAI1D,MAAM,CAACd,EAAIK,GAAGL,EAAIyD,GAAGe,EAAIY,cAAc,KAAI,GAAGlF,EAAG,KAAK,CAACA,EAAG,KAAK,CAACF,EAAIK,GAAG,QAAQL,EAAI+C,GAAI/C,EAAI+E,gBAAgB,SAASP,GAAK,OAAOtE,EAAG,KAAK,CAACiD,IAAIqB,EAAI1D,MAAM,CAACd,EAAIK,GAAGL,EAAIyD,GAAGe,EAAIa,aAAa,KAAI,aAAanF,EAAG,UAAU,CAACE,YAAY,0CAA0C,CAACJ,EAAIS,GAAG,GAAGP,EAAG,MAAM,CAACE,YAAY,wBAAwBJ,EAAI+C,GAAI/C,EAAIsF,aAAa,SAASC,EAAQrC,GAAO,OAAOhD,EAAG,MAAM,CAACiD,IAAID,EAAM9C,YAAY,uBAAuB,CAACF,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAACF,EAAG,IAAI,CAACmD,MAAMkC,EAAQC,SAAStF,EAAG,KAAK,CAACE,YAAY,wBAAwB,CAACJ,EAAIK,GAAGL,EAAIyD,GAAG8B,EAAQ7B,UAAUxD,EAAG,IAAI,CAACE,YAAY,uBAAuB,CAACJ,EAAIK,GAAGL,EAAIyD,GAAG8B,EAAQE,UAAU,IAAG,KAAKvF,EAAG,UAAU,CAACE,YAAY,8CAA8C,CAACJ,EAAIS,GAAG,GAAGP,EAAG,MAAM,CAACE,YAAY,4BAA4BJ,EAAI+C,GAAI/C,EAAI0F,oBAAoB,SAASC,EAAIzC,GAAO,OAAOhD,EAAG,MAAM,CAACiD,IAAID,EAAM9C,YAAY,mBAAmB,CAACF,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,MAAM,CAACS,MAAM,CAAC,IAAMgF,EAAIC,MAAM,IAAMD,EAAIjC,WAAWxD,EAAG,KAAK,CAACE,YAAY,oBAAoB,CAACJ,EAAIK,GAAGL,EAAIyD,GAAGkC,EAAIjC,WAAW,IAAG,KAAKxD,EAAG,MAAM,CAACE,YAAY,0BAA0B,CAACF,EAAG,KAAK,CAACE,YAAY,wBAAwB,CAACJ,EAAIK,GAAG,wBAAwBH,EAAG,SAAS,CAACE,YAAY,wBAAwBE,GAAG,CAAC,MAAQN,EAAI6F,mBAAmB,CAAC7F,EAAIK,GAAG,YAAYH,EAAG,aAAa,CAACS,MAAM,CAAC,KAAO,gBAAgB,CAAEX,EAAI8F,iBAAkB5F,EAAG,MAAM,CAACE,YAAY,yBAAyBE,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAGA,EAAOwF,SAAWxF,EAAOyF,cAAqB,KAAYhG,EAAIiG,kBAAkBC,MAAM,KAAMC,UAAU,IAAI,CAACjG,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,SAAS,CAACE,YAAY,qBAAqBE,GAAG,CAAC,MAAQN,EAAIiG,oBAAoB,CAACjG,EAAIK,GAAG,SAASH,EAAG,MAAM,CAACE,YAAY,0BAA0B,CAACF,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBF,EAAG,OAAO,CAACF,EAAIK,GAAGL,EAAIyD,GAAGzD,EAAIoG,YAAYtF,WAAWZ,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAACF,EAAG,IAAI,CAACE,YAAY,kBAAkBF,EAAG,OAAO,CAACF,EAAIK,GAAGL,EAAIyD,GAAGzD,EAAIoG,YAAYC,cAAcnG,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAACF,EAAG,IAAI,CAACF,EAAIK,GAAG,oBAAoBL,EAAIgE,QAAQ,GAHppb9D,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,MAAM,CAACE,YAAY,WAAW,CAACF,EAAG,MAAM,CAACE,YAAY,WAAW,CAACF,EAAG,MAAM,CAACE,YAAY,WAAWwC,MAAO,CACpTC,UAAW,cAAgB7C,EAAIsG,UAAY,IAC3CC,WAAYvG,EAAIwG,MAAQ,WAAa,SACnCxG,EAAI+C,GAAI/C,EAAIgD,cAAc,SAASC,EAAKC,GAAO,OAAOhD,EAAG,MAAM,CAACiD,IAAID,EAAM9C,YAAY,cAAc,CAACF,EAAG,MAAM,CAACS,MAAM,CAAC,IAAMsC,EAAKG,IAAI,IAAM,MAAMlD,EAAG,MAAM,CAACE,YAAY,iBAAiBiD,MAAM,OAASJ,EAAKK,QAAQC,UAAU,CAACrD,EAAG,KAAK,CAACE,YAAY,eAAeoD,SAAS,CAAC,UAAYxD,EAAIyD,GAAGR,EAAKK,QAAQI,UAAUxD,EAAG,IAAI,CAACE,YAAY,eAAe,CAACJ,EAAIK,GAAGL,EAAIyD,GAAGR,EAAKK,QAAQK,SAASzD,EAAG,MAAM,CAACE,YAAY,kBAAkB,EAAGJ,EAAI4D,SAAWX,EAAKK,QAAQO,cAAe3D,EAAG,IAAI,CAACE,YAAY,8BAA8BO,MAAM,CAAC,KAAO,KAAKL,GAAG,CAAC,MAAQ,SAASC,GAAgC,OAAxBA,EAAOuD,iBAAwB9D,EAAIQ,WAAWyC,EAAKK,QAAQO,cAAc,IAAI,CAAC7D,EAAIK,GAAG,IAAIL,EAAIyD,GAAGR,EAAKK,QAAQS,kBAAkB,OAAO/D,EAAIgE,MAAOhE,EAAI4D,SAAWX,EAAKK,QAAQW,YAAa/D,EAAG,IAAI,CAACE,YAAY,4BAA4BO,MAAM,CAAC,KAAO,KAAKL,GAAG,CAAC,MAAQ,SAASC,GAAgC,OAAxBA,EAAOuD,iBAAwB9D,EAAIQ,WAAWyC,EAAKK,QAAQW,YAAY,IAAI,CAACjE,EAAIK,GAAG,IAAIL,EAAIyD,GAAGR,EAAKK,QAAQY,gBAAgB,OAAOlE,EAAIgE,KAAMf,EAAKK,QAAQa,UAAWjE,EAAG,IAAI,CAACE,YAAY,8BAA8BO,MAAM,CAAC,KAAO,KAAKL,GAAG,CAAC,MAAQ,SAASC,GAAgC,OAAxBA,EAAOuD,iBAAwB9D,EAAIQ,WAAWyC,EAAKK,QAAQa,UAAU,IAAI,CAACnE,EAAIK,GAAG,IAAIL,EAAIyD,GAAGR,EAAKK,QAAQc,cAAc,OAAOpE,EAAIgE,UAAU,IAAG,KAAK9D,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,OAAO,CAACE,YAAY,iBAAiBE,GAAG,CAAC,MAAQN,EAAIyG,OAAO,CAACvG,EAAG,MAAM,CAACE,YAAY,qBAAqBO,MAAM,CAAC,IAAMC,EAAQ,MAA6C,IAAM,QAAQV,EAAG,OAAO,CAACE,YAAY,kBAAkBE,GAAG,CAAC,MAAQN,EAAI0G,OAAO,CAACxG,EAAG,MAAM,CAACE,YAAY,aAAaO,MAAM,CAAC,IAAMC,EAAQ,MAA6C,IAAM,UAAUV,EAAG,MAAM,CAACyG,IAAI,mBAAmBvG,YAAY,qBAAqBJ,EAAI+C,GAAI/C,EAAIgD,cAAc,SAASC,EAAKC,GAAO,OAAOhD,EAAG,OAAO,CAACiD,IAAID,EAAMG,MAAM,CAAEgB,OAAQrE,EAAI4G,aAAe1D,IAAS,IAAG,SAAShD,EAAG,UAAU,CAACE,YAAY,uBAAuB,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIS,GAAG,GAAGP,EAAG,MAAM,CAACE,YAAY,iBAAiBJ,EAAI+C,GAAI/C,EAAIuE,MAAM,SAASC,EAAItB,GAAO,OAAOhD,EAAG,MAAM,CAACiD,IAAID,EAAM9C,YAAY,WAAWiD,MAAM,CAAE,YAAemB,EAAIC,aAAcnE,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOP,EAAIQ,WAAW,WAAW,IAAI,CAACN,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,KAAK,CAACE,YAAY,YAAY,CAACJ,EAAIK,GAAGL,EAAIyD,GAAGe,EAAI1D,SAAU0D,EAAIC,YAAavE,EAAG,OAAO,CAACE,YAAY,sBAAsB,CAACJ,EAAIK,GAAG,QAAQL,EAAIgE,KAAMQ,EAAIE,MAAOxE,EAAG,OAAO,CAACE,YAAY,WAAW,CAACJ,EAAIK,GAAG,SAASL,EAAIgE,OAAO9D,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,OAAO,CAACE,YAAY,cAAc,CAACJ,EAAIK,GAAG,UAAUH,EAAG,OAAO,CAACE,YAAY,cAAc,CAACJ,EAAIK,GAAGL,EAAIyD,GAAGe,EAAIG,iBAAiB,eAAezE,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,OAAO,CAACE,YAAY,cAAc,CAACJ,EAAIK,GAAG,UAAUH,EAAG,OAAO,CAACE,YAAY,cAAc,CAACJ,EAAIK,GAAGL,EAAIyD,GAAGe,EAAII,eAAe,qBAAqB1E,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAAEoE,EAAIK,cAAe3E,EAAG,OAAO,CAACE,YAAY,kBAAkB,CAACJ,EAAIK,GAAG,IAAIL,EAAIyD,GAAGe,EAAIK,eAAe,QAAQ7E,EAAIgE,KAAK9D,EAAG,OAAO,CAACE,YAAY,iBAAiB,CAACJ,EAAIK,GAAG,KAAKH,EAAG,OAAO,CAACE,YAAY,eAAe,CAACJ,EAAIK,GAAGL,EAAIyD,GAAGe,EAAIM,UAAU9E,EAAIK,GAAG,eAAe,IAAG,OAAOH,EAAG,iBAAiBA,EAAG,UAAU,CAACE,YAAY,4BAA4B,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIS,GAAG,GAAGP,EAAG,MAAM,CAACE,YAAY,iBAAiBJ,EAAI+C,GAAI/C,EAAIsF,aAAa,SAASC,EAAQrC,GAAO,OAAOhD,EAAG,MAAM,CAACiD,IAAID,EAAM9C,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,IAAI,CAACE,YAAY,eAAeiD,MAAMkC,EAAQC,OAAOtF,EAAG,KAAK,CAACE,YAAY,iBAAiB,CAACJ,EAAIK,GAAGL,EAAIyD,GAAG8B,EAAQ7B,UAAUxD,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,IAAI,CAACF,EAAIK,GAAGL,EAAIyD,GAAG8B,EAAQE,cAAc,IAAG,OAAOvF,EAAG,UAAU,CAACE,YAAY,kBAAkB,CAACJ,EAAIS,GAAG,GAAGP,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,2BAA2B,CAACF,EAAG,MAAM,CAACE,YAAY,cAAcE,GAAG,CAAC,UAAY,SAASC,GAAQP,EAAI6G,aAAaC,OAAQ,CAAI,EAAE,WAAa,SAASvG,GAAQP,EAAI6G,aAAaC,OAAQ,CAAK,IAAI,CAAC5G,EAAG,MAAM,CAACE,YAAY,eAAeiD,MAAM,CAAE,eAAgBrD,EAAI6G,aAAaC,QAAS,CAAC5G,EAAG,MAAM,CAACS,MAAM,CAAC,IAAMX,EAAI6G,aAAajB,MAAM,IAAM5F,EAAI6G,aAAanD,WAAWxD,EAAG,MAAM,CAACE,YAAY,mBAAmBiD,MAAM,CAAE,eAAgBrD,EAAI6G,aAAaC,QAAS,CAAC9G,EAAIK,GAAGL,EAAIyD,GAAGzD,EAAI6G,aAAanD,cAAc1D,EAAI+C,GAAI/C,EAAI+G,kBAAkB,SAASpB,EAAIzC,GAAO,OAAOhD,EAAG,MAAM,CAACiD,IAAI,QAAQD,EAAM9C,YAAY,2BAA2B,CAACF,EAAG,MAAM,CAACE,YAAY,cAAcE,GAAG,CAAC,UAAY,SAASC,GAAQoF,EAAImB,OAAQ,CAAI,EAAE,WAAa,SAASvG,GAAQoF,EAAImB,OAAQ,CAAK,IAAI,CAAC5G,EAAG,MAAM,CAACE,YAAY,eAAeiD,MAAM,CAAE,eAAgBsC,EAAImB,QAAS,CAAC5G,EAAG,MAAM,CAACS,MAAM,CAAC,IAAMgF,EAAIC,MAAM,IAAMD,EAAIjC,WAAWxD,EAAG,MAAM,CAACE,YAAY,mBAAmBiD,MAAM,CAAE,eAAgBsC,EAAImB,QAAS,CAAC9G,EAAIK,GAAGL,EAAIyD,GAAGkC,EAAIjC,aAAa,IAAG1D,EAAI+C,GAAI/C,EAAIgH,eAAe,SAASrB,EAAIzC,GAAO,OAAOhD,EAAG,MAAM,CAACiD,IAAI,SAASD,EAAM9C,YAAY,4BAA4B,CAACF,EAAG,MAAM,CAACE,YAAY,cAAcE,GAAG,CAAC,UAAY,SAASC,GAAQoF,EAAImB,OAAQ,CAAI,EAAE,WAAa,SAASvG,GAAQoF,EAAImB,OAAQ,CAAK,IAAI,CAAC5G,EAAG,MAAM,CAACE,YAAY,eAAeiD,MAAM,CAAE,eAAgBsC,EAAImB,QAAS,CAAC5G,EAAG,MAAM,CAACS,MAAM,CAAC,IAAMgF,EAAIC,MAAM,IAAMD,EAAIjC,WAAWxD,EAAG,MAAM,CAACE,YAAY,mBAAmBiD,MAAM,CAAE,eAAgBsC,EAAImB,QAAS,CAAC9G,EAAIK,GAAGL,EAAIyD,GAAGkC,EAAIjC,aAAa,IAAG1D,EAAI+C,GAAI/C,EAAIiH,mBAAmB,SAAStB,EAAIzC,GAAO,OAAOhD,EAAG,MAAM,CAACiD,IAAI,eAAeD,EAAM9C,YAAY,4BAA4B,CAACF,EAAG,MAAM,CAACE,YAAY,cAAcE,GAAG,CAAC,UAAY,SAASC,GAAQoF,EAAImB,OAAQ,CAAI,EAAE,WAAa,SAASvG,GAAQoF,EAAImB,OAAQ,CAAK,IAAI,CAAC5G,EAAG,MAAM,CAACE,YAAY,eAAeiD,MAAM,CAAE,eAAgBsC,EAAImB,QAAS,CAAC5G,EAAG,MAAM,CAACS,MAAM,CAAC,IAAMgF,EAAIC,MAAM,IAAMD,EAAIjC,WAAWxD,EAAG,MAAM,CAACE,YAAY,mBAAmBiD,MAAM,CAAE,eAAgBsC,EAAImB,QAAS,CAAC9G,EAAIK,GAAGL,EAAIyD,GAAGkC,EAAIjC,aAAa,IAAGxD,EAAG,MAAM,CAACE,YAAY,2BAA2B,CAACF,EAAG,MAAM,CAACE,YAAY,cAAcE,GAAG,CAAC,UAAY,SAASC,GAAQP,EAAIkH,aAAaJ,OAAQ,CAAI,EAAE,WAAa,SAASvG,GAAQP,EAAIkH,aAAaJ,OAAQ,CAAK,IAAI,CAAC5G,EAAG,MAAM,CAACE,YAAY,eAAeiD,MAAM,CAAE,eAAgBrD,EAAIkH,aAAaJ,QAAS,CAAC5G,EAAG,MAAM,CAACS,MAAM,CAAC,IAAMX,EAAIkH,aAAatB,MAAM,IAAM5F,EAAIkH,aAAaxD,WAAWxD,EAAG,MAAM,CAACE,YAAY,mBAAmBiD,MAAM,CAAE,eAAgBrD,EAAIkH,aAAaJ,QAAS,CAAC9G,EAAIK,GAAGL,EAAIyD,GAAGzD,EAAIkH,aAAaxD,eAAe,GAAGxD,EAAG,YAAY,KAAKA,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAACF,EAAG,MAAM,CAACE,YAAY,SAAS,CAACF,EAAG,KAAK,CAACE,YAAY,gBAAgB,CAACJ,EAAIK,GAAG,wBAAwBH,EAAG,SAAS,CAACE,YAAY,kBAAkBE,GAAG,CAAC,MAAQN,EAAI6F,mBAAmB,CAAC7F,EAAIK,GAAG,cAAcH,EAAG,aAAa,CAACS,MAAM,CAAC,KAAO,SAAS,CAAEX,EAAI8F,iBAAkB5F,EAAG,MAAM,CAACE,YAAY,wBAAwBE,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAGA,EAAOwF,SAAWxF,EAAOyF,cAAqB,KAAYhG,EAAIiG,kBAAkBC,MAAM,KAAMC,UAAU,IAAI,CAACjG,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,SAAS,CAACE,YAAY,cAAcE,GAAG,CAAC,MAAQN,EAAIiG,oBAAoB,CAACjG,EAAIK,GAAG,SAASH,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBF,EAAG,OAAO,CAACF,EAAIK,GAAGL,EAAIyD,GAAGzD,EAAIoG,YAAYtF,WAAWZ,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,IAAI,CAACE,YAAY,kBAAkBF,EAAG,OAAO,CAACF,EAAIK,GAAGL,EAAIyD,GAAGzD,EAAIoG,YAAYC,cAAcnG,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,IAAI,CAACF,EAAIK,GAAG,oBAAoBL,EAAIgE,QAAQ,GAAm4MhE,EAAIwC,SAAsBxC,EAAIgE,KAAhB9D,EAAG,SAAkBA,EAAG,UAAUA,EAAG,WAAW,EACpzb,EACIQ,EAAkB,CAAC,WAAY,IAAIV,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,KAAK,CAACE,YAAY,iBAAiB,CAACJ,EAAIK,GAAG,UAAUH,EAAG,IAAI,CAACE,YAAY,uBAAuB,CAACJ,EAAIK,GAAG,8CAC3N,EAAE,WAAY,IAAIL,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,KAAK,CAACE,YAAY,iBAAiB,CAACJ,EAAIK,GAAG,UAAUH,EAAG,IAAI,CAACE,YAAY,uBAAuB,CAACJ,EAAIK,GAAG,iCACtM,EAAE,WAAY,IAAIL,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,KAAK,CAACE,YAAY,iBAAiB,CAACJ,EAAIK,GAAG,UAAUH,EAAG,IAAI,CAACE,YAAY,uBAAuB,CAACJ,EAAIK,GAAG,qBACtM,EAAE,WAAY,IAAIL,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,yBAAyB,CAACF,EAAG,KAAK,CAACE,YAAY,wBAAwB,CAACJ,EAAIK,GAAG,UAAUH,EAAG,IAAI,CAACE,YAAY,8BAA8B,CAACJ,EAAIK,GAAG,gCAC3N,EAAE,WAAY,IAAIL,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,yBAAyB,CAACF,EAAG,KAAK,CAACE,YAAY,wBAAwB,CAACJ,EAAIK,GAAG,aAAaH,EAAG,IAAI,CAACE,YAAY,8BAA8B,CAACJ,EAAIK,GAAG,oBAC9N,EAAE,WAAY,IAAIL,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,yBAAyB,CAACF,EAAG,KAAK,CAACE,YAAY,wBAAwB,CAACJ,EAAIK,GAAG,UAAUH,EAAG,IAAI,CAACE,YAAY,8BAA8B,CAACJ,EAAIK,GAAG,gCAC3N,EAAE,WAAY,IAAIL,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,yBAAyB,CAACF,EAAG,KAAK,CAACE,YAAY,wBAAwB,CAACJ,EAAIK,GAAG,UAAUH,EAAG,IAAI,CAACE,YAAY,8BAA8B,CAACJ,EAAIK,GAAG,qBAC3N,G,wCCZIN,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,UAAU,CAACE,YAAY,kCAAkC,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIS,GAAG,GAAGP,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,QAAQ,CAACA,EAAG,QAAQ,CAACA,EAAG,KAAK,CAACA,EAAG,KAAK,CAACF,EAAIK,GAAG,WAAWL,EAAI+C,GAAI/C,EAAI+E,gBAAgB,SAASP,GAAK,OAAOtE,EAAG,KAAK,CAACiD,IAAIqB,EAAI1D,MAAM,CAACd,EAAIK,GAAGL,EAAIyD,GAAGe,EAAI1D,QAAQ,KAAI,KAAKZ,EAAG,QAAQ,CAACA,EAAG,KAAK,CAACA,EAAG,KAAK,CAACF,EAAIK,GAAG,QAAQL,EAAI+C,GAAI/C,EAAI+E,gBAAgB,SAASP,GAAK,OAAOtE,EAAG,KAAK,CAACiD,IAAIqB,EAAI1D,MAAM,CAACd,EAAIK,GAAGL,EAAIyD,GAAGe,EAAIQ,gBAAgB,KAAI,GAAG9E,EAAG,KAAK,CAACA,EAAG,KAAK,CAACF,EAAIK,GAAG,YAAYL,EAAI+C,GAAI/C,EAAI+E,gBAAgB,SAASP,GAAK,OAAOtE,EAAG,KAAK,CAACiD,IAAIqB,EAAI1D,MAAM,CAACd,EAAIK,GAAGL,EAAIyD,GAAGe,EAAIS,mBAAmB,KAAI,GAAG/E,EAAG,KAAK,CAACA,EAAG,KAAK,CAACF,EAAIK,GAAG,YAAYL,EAAI+C,GAAI/C,EAAI+E,gBAAgB,SAASP,GAAK,OAAOtE,EAAG,KAAK,CAACiD,IAAIqB,EAAI1D,MAAM,CAACd,EAAIK,GAAGL,EAAIyD,GAAGe,EAAIU,mBAAmB,KAAI,GAAGhF,EAAG,KAAK,CAACA,EAAG,KAAK,CAACF,EAAIK,GAAG,QAAQL,EAAI+C,GAAI/C,EAAI+E,gBAAgB,SAASP,GAAK,OAAOtE,EAAG,KAAK,CAACiD,IAAIqB,EAAI1D,MAAM,CAACd,EAAIK,GAAGL,EAAIyD,GAAGe,EAAIW,UAAU,KAAI,GAAGjF,EAAG,KAAK,CAACA,EAAG,KAAK,CAACF,EAAIK,GAAG,UAAUL,EAAI+C,GAAI/C,EAAI+E,gBAAgB,SAASP,GAAK,OAAOtE,EAAG,KAAK,CAACiD,IAAIqB,EAAI1D,MAAM,CAACd,EAAIK,GAAGL,EAAIyD,GAAGe,EAAIY,cAAc,KAAI,GAAGlF,EAAG,KAAK,CAACA,EAAG,KAAK,CAACF,EAAIK,GAAG,QAAQL,EAAI+C,GAAI/C,EAAI+E,gBAAgB,SAASP,GAAK,OAAOtE,EAAG,KAAK,CAACiD,IAAIqB,EAAI1D,MAAM,CAACd,EAAIK,GAAGL,EAAIyD,GAAGe,EAAIa,aAAa,KAAI,YACrwC,EACI3E,EAAkB,CAAC,WAAY,IAAIV,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,KAAK,CAACE,YAAY,iBAAiB,CAACJ,EAAIK,GAAG,aAAaH,EAAG,IAAI,CAACE,YAAY,uBAAuB,CAACJ,EAAIK,GAAG,iCAC9N,G,UCkDA,GACAS,KAAA,gBACAC,OACA,OACAgE,eAAA,CACA,CACAjE,KAAA,OACAkE,aAAA,SACAC,gBAAA,aACAC,gBAAA,cACAC,OAAA,QACAC,WAAA,OACAC,UAAA,cAEA,CACAvE,KAAA,OACAkE,aAAA,QACAC,gBAAA,aACAC,gBAAA,cACAC,OAAA,QACAC,WAAA,OACAC,UAAA,YAEA,CACAvE,KAAA,QACAkE,aAAA,SACAC,gBAAA,cACAC,gBAAA,cACAC,OAAA,QACAC,WAAA,QACAC,UAAA,YAEA,CACAvE,KAAA,QACAkE,aAAA,SACAC,gBAAA,cACAC,gBAAA,cACAC,OAAA,QACAC,WAAA,QACAC,UAAA,YAEA,CACAvE,KAAA,QACAkE,aAAA,SACAC,gBAAA,eACAC,gBAAA,eACAC,OAAA,QACAC,WAAA,QACAC,UAAA,aAIA,EACA8B,UACA,KAAAC,iBACA,EACApG,QAAA,CACA,wBACA,KAEAqG,EAAAA,EAAAA,IAAA,2BAAAC,MAAAC,IAEA,KAAAxC,eAAAwC,EAAAxG,KAAAyG,KAAAC,KAAAxE,IAAA,IACAA,EACAgC,gBAAAhC,EAAAyE,gBACAxC,gBAAAjC,EAAA0E,gBACAvC,WAAAnC,EAAA2E,cACA,GAIA,OAAAC,GACAC,QAAAD,MAAA,eAAAA,EACA,CACA,IC/HsQ,I,UCQlQtF,GAAY,OACd,EACA,EACA,GACA,EACA,KACA,WACA,MAIF,EAAeA,EAAiB,Q,UCkbhC,GACAzB,KAAA,YACAiH,WAAA,CAAAC,OAAA,IAAAC,OAAA,IAAAC,MAAA,IAAAC,cAAAA,GACAC,SAAA,CACA9B,YACA,iBAAAM,WAAA,GACA,EACAhD,UACA,QAAAtC,SAAA+G,OAAAC,SAAA,cACA,EACAC,mBACA,iBAAAzF,kBACA,EACA4C,qBACA,OACA,KAAAmB,gBACA,KAAAE,oBACA,KAAAC,iBACA,KAAAC,kBACA,KAAAC,aAEA,GAEAnG,OACA,OACAyH,YAAA,EACAC,UAAA,EACAC,eAAA,GACAlG,UAAA,EACAM,mBAAA,EACAgD,kBAAA,EACAM,YAAA,CACAtF,KAAA,MACAuF,MAAA,eAEAsC,SAAA,EACA9B,aAAA,CACAnD,MAAA,OACAkC,MAAAhF,EAAA,MACAkG,OAAA,GAEAC,iBAAA,CACA,CACArD,MAAA,OACAkC,MAAAhF,EAAA,MACAkG,OAAA,GAEA,CACApD,MAAA,OACAkC,MAAAhF,EAAA,MACAkG,OAAA,IAGAE,cAAA,CACA,CACAtD,MAAA,OACAkC,MAAAhF,EAAA,KACAkG,OAAA,GAEA,CACApD,MAAA,OACAkC,MAAAhF,EAAA,MACAkG,OAAA,IAGAG,kBAAA,CACA,CACAvD,MAAA,OACAkC,MAAAhF,EAAA,KACAkG,OAAA,GAEA,CACApD,MAAA,OACAkC,MAAAhF,EAAA,MACAkG,OAAA,IAGAI,aAAA,CACAxD,MAAA,OACAkC,MAAAhF,EAAA,KACAkG,OAAA,GAEA9D,aAAA,CACA,CACAI,IAAAxC,EAAA,MACA0C,QAAA,CACAI,MAAA,OACAC,KAAA,oBACAJ,SAAA,OACAY,UAAA,WACAC,aAAA,SAGA,CACAhB,IAAAxC,EAAA,MACA0C,QAAA,CACAI,MAAA,WACAC,KAAA,qBACAJ,SAAA,OACAM,cAAA,SACAE,iBAAA,OACAE,YAAA,YACAC,eAAA,SAGA,CACAd,IAAAxC,EAAA,MACA0C,QAAA,CACAI,MAAA,WACAC,KAAA,qCACAJ,SAAA,UAIAqD,WAAA,EACAJ,OAAA,EACAoC,QAAA,GACAC,cAAA,CACAC,MAAA,EACAC,SAAA,CACAC,MAAA,IACAC,sBAAA,GAEAC,WAAA,CACAC,GAAA,qBACAC,WAAA,GAEAC,WAAA,CACAC,OAAA,sBACAC,OAAA,wBAGAjE,YAAA,CACA,CAAAkE,GAAA,EAAAhE,KAAA,iBAAA9B,MAAA,OAAA+B,KAAA,+DACA,CAAA+D,GAAA,EAAAhE,KAAA,kBAAA9B,MAAA,OAAA+B,KAAA,kDACA,CAAA+D,GAAA,EAAAhE,KAAA,iBAAA9B,MAAA,OAAA+B,KAAA,4DACA,CAAA+D,GAAA,EAAAhE,KAAA,iBAAA9B,MAAA,QAAA+B,KAAA,0DACA,CAAA+D,GAAA,EAAAhE,KAAA,sBAAA9B,MAAA,MAAA+B,KAAA,2DACA,CAAA+D,GAAA,EAAAhE,KAAA,gBAAA9B,MAAA,OAAA+B,KAAA,6CAEAV,eAAA,CACA,CACAjE,KAAA,OACAkE,aAAA,SACAC,gBAAA,aACAC,gBAAA,cACAC,OAAA,QACAC,WAAA,OACAC,UAAA,cAEA,CACAvE,KAAA,OACAkE,aAAA,SACAC,gBAAA,aACAC,gBAAA,cACAC,OAAA,QACAC,WAAA,OACAC,UAAA,cAEA,CACAvE,KAAA,OACAkE,aAAA,SACAC,gBAAA,aACAC,gBAAA,cACAC,OAAA,QACAC,WAAA,OACAC,UAAA,cAEA,CACAvE,KAAA,OACAkE,aAAA,QACAC,gBAAA,aACAC,gBAAA,cACAC,OAAA,QACAC,WAAA,OACAC,UAAA,YAEA,CACAvE,KAAA,QACAkE,aAAA,SACAC,gBAAA,cACAC,gBAAA,cACAC,OAAA,QACAC,WAAA,QACAC,UAAA,YAEA,CACAvE,KAAA,QACAkE,aAAA,SACAC,gBAAA,cACAC,gBAAA,cACAC,OAAA,QACAC,WAAA,QACAC,UAAA,YAEA,CACAvE,KAAA,QACAkE,aAAA,SACAC,gBAAA,eACAC,gBAAA,eACAC,OAAA,QACAC,WAAA,QACAC,UAAA,aAGAd,KAAA,CACA,CACAzD,KAAA,mBACA6D,gBAAA,MACAC,cAAA,OACAC,cAAA,QACAC,MAAA,OACAL,aAAA,EACAC,OAAA,GAEA,CACA5D,KAAA,kBACA6D,gBAAA,QACAC,cAAA,OACAC,cAAA,OACAC,MAAA,OACAL,aAAA,EACAC,OAAA,GAEA,CACA5D,KAAA,mBACA6D,gBAAA,MACAC,cAAA,OACAC,cAAA,QACAC,MAAA,OACAL,aAAA,EACAC,OAAA,GAEA,CACA5D,KAAA,kBACA6D,gBAAA,QACAC,cAAA,QACAC,cAAA,OACAC,MAAA,OACAL,aAAA,EACAC,OAAA,GAEA,CACA5D,KAAA,mBACA6D,gBAAA,QACAC,cAAA,QACAC,cAAA,OACAC,MAAA,OACAL,aAAA,EACAC,OAAA,GAEA,CACA5D,KAAA,kBACA6D,gBAAA,OACAC,cAAA,OACAC,cAAA,OACAC,MAAA,OACAL,aAAA,EACAC,OAAA,GAEA,CACA5D,KAAA,mBACA6D,gBAAA,QACAC,cAAA,OACAC,cAAA,OACAC,MAAA,OACAL,aAAA,EACAC,OAAA,GAEA,CACA5D,KAAA,mBACA6D,gBAAA,MACAC,cAAA,QACAC,cAAA,OACAC,MAAA,OACAL,aAAA,EACAC,OAAA,IAIA+E,YAAA,EAEA,EACAtC,UACA,KAAAuC,uBACA,MAAAC,EAAA,IAAAC,IAAA5H,OAAA6H,SAAAC,MACAC,EAAAJ,EAAAK,aAAAC,IAAA,SAEA,GAAAF,EAAA,CACA,MAAAG,EAAAC,aAAAC,QAAA,yBAEAF,GAOAlI,OAAA6H,SAAAC,KAAAH,EAAAU,OAAAV,EAAAW,SACAH,aAAAI,QAAA,oCAPAC,EAAAA,EAAAA,IAAAT,GACAI,aAAAI,QAAA,gCAQA,CACA,EACAE,UACA,KAAAC,gBACA1I,OAAA2I,iBAAA,cAAAD,eAGA,KAAAE,gBAAAC,aAAA,KACA,KAAAnE,MAAA,GACA,KAEA,KAAAoE,eAAAD,aAAA,KACA,KAAAE,YAAA,GACA,IACA,EACAC,gBACAhJ,OAAAiJ,oBAAA,cAAAP,eACAQ,cAAA,KAAAN,iBACAM,cAAA,KAAAJ,eAEA,EACA9J,QAAA,CAEA,6BACA,KACAqG,EAAAA,EAAAA,IAAA,0BAAAC,MAAAC,IACA,KAAAhD,KAAAgD,EAAAxG,KAAAyG,KAAAC,KAAAxE,IAAA,IACAA,EACAyB,MAAA,IAAAzB,EAAAkI,MACA1G,YAAA,IAAAxB,EAAAwB,eACA,GAGA,OAAAoD,GACAC,QAAAD,MAAA,eAAAA,EACA,CACA,EAEApF,iBAAA2I,GACA,KAAA5C,YAAA4C,EAAAC,QAAA,GAAAC,OACA,EAEA5I,gBAAA0I,GACA,KAAA3C,UAAA2C,EAAAC,QAAA,GAAAC,OACA,EAEA3I,iBACA,SAAA6F,cAAA,KAAAC,UAAA,OAEA,MAAA8C,EAAA,KAAA/C,YAAA,KAAAC,UAGA8C,EAAA,KAAA7C,gBACA,KAAAqC,aAIAQ,GAAA,KAAA7C,gBACA,KAAA8C,aAIA,KAAAhD,YAAA,EACA,KAAAC,UAAA,CACA,EAEA+C,aACA,KAAA1I,oBAAA,KAAAA,mBAAA,OAAAE,aAAAyI,QAAA,KAAAzI,aAAAyI,OACA,KAAAC,qBACA,EAEAX,aACA,KAAAjI,oBAAA,KAAAA,mBAAA,QAAAE,aAAAyI,OACA,KAAAC,qBACA,EAEAA,sBACAR,cAAA,KAAAJ,gBACA,KAAAA,eAAAD,aAAA,KACA,KAAAE,YAAA,GACA,IACA,EACAL,gBACA,KAAAlI,SAAAR,OAAA2J,YAAA,GACA,EACA9F,mBACA,KAAAC,kBAAA,CACA,EACAG,oBACA,KAAAH,kBAAA,CACA,EACA8F,mBAAA3I,GACAA,EAAAK,QAAA7B,MACA,KAAAW,QAAAE,KAAAW,EAAAK,QAAA7B,MAEA,KAAAoK,IAAAC,MAAA,iBAAA7I,EAAAK,QAAAI,MACA,EACAlD,WAAAS,GACA,KAAAC,aAAA,KAAAA,cAAAD,GACA,KAAAE,mBAAA,KAAAD,YAEA,KAAAE,WAAA,KACA,MAAAC,EAAAC,SAAAC,iBAAA,yBACAF,EAAAG,SAAAC,KACAA,EAAAC,UAAAC,SAAA,WACA,WAAAV,GAAAQ,EAAAC,UAAAC,SAAA,gBACAF,EAAAC,UAAAC,SAAA,iBACAF,EAAAC,UAAAE,IAAA,eAEAC,YAAA,KACAJ,EAAAC,UAAAI,OAAA,iBACA,KACA,IAGA,KAAAZ,YAAAD,CAAA,KAGA,KAAAC,YAAAD,EAGA,KAAAc,OAAAd,OAAAA,EACA,KAAAG,WAAA,KACAY,OAAAC,SAAA,CACAC,IAAA,EACAC,SAAA,YAEA,KAAAC,QAAAC,GAAA,OAGA,KAAAD,QAAAE,KAAArB,GACAe,OAAAC,SAAA,CACAC,IAAA,EACAC,SAAA,YAGA,EACAsE,OACA,KAAAG,YAAA,KAAAA,WAAA,OAAA5D,aAAAyI,QAAA,KAAAzI,aAAAyI,OACA,KAAAjF,OAAA,CACA,EACAE,OACA,KAAAE,YAAA,KAAAA,WAAA,QAAA5D,aAAAyI,OACA,KAAAjF,OAAA,CACA,EAEAlC,UAAApB,GACA,KAAAJ,mBAAAI,EAEAgI,cAAA,KAAAJ,gBACA,KAAAA,eAAAD,aAAA,KACA,KAAAE,YAAA,GACA,IACA,EACAgB,UAAA7I,GACA,KAAAyF,SAAAzF,CACA,EACA8I,OAAAxH,GACAsD,QAAAmE,IAAA,iBAAAzH,EAAA1D,OACA,EACAoL,oBACApE,QAAAmE,IAAA,sBACA,EACAE,YACArE,QAAAmE,IAAA,aACA,EACAG,YACA,KAAA3C,YAAA,KAAAA,YAAA,OAAAA,YAAA,OAAA4C,UAAAZ,OAAA,CACA,EACAa,YACA,KAAA7C,YAAA,KAAAA,YAAA,KAAA4C,UAAAZ,OAAA,OAAAhC,YAAA,GACA,EACA8C,UAAA/H,GACAsD,QAAAmE,IAAA,iBAAAzH,EAAA1D,OACA,GAEA0L,MAAA,CACA5F,WAAA6F,GACA,MAAAC,EAAA,KAAAC,MAAAC,kBAAArL,iBAAA,QACAmL,GACAA,EAAAlL,SAAA,CAAAqL,EAAA3J,KACA2J,EAAAnL,UAAAoL,OAAA,SAAA5J,IAAAuJ,EAAA,GAGA,IC16BkQ,ICQ9P,GAAY,OACd,EACA1M,EACAW,GACA,EACA,KACA,WACA,MAIF,EAAe,EAAiB,O", "sources": ["webpack://portal-ui/./src/components/common/footer/Footer.vue", "webpack://portal-ui/src/components/common/footer/Footer.vue", "webpack://portal-ui/./src/components/common/footer/Footer.vue?6062", "webpack://portal-ui/./src/components/common/footer/Footer.vue?8b5d", "webpack://portal-ui/./src/views/Index/IndexView.vue", "webpack://portal-ui/./src/views/Index/GpuComparison.vue", "webpack://portal-ui/src/views/Index/GpuComparison.vue", "webpack://portal-ui/./src/views/Index/GpuComparison.vue?25ec", "webpack://portal-ui/./src/views/Index/GpuComparison.vue?69c5", "webpack://portal-ui/src/views/Index/IndexView.vue", "webpack://portal-ui/./src/views/Index/IndexView.vue?6f88", "webpack://portal-ui/./src/views/Index/IndexView.vue?981c"], "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"footer\"},[_c('div',{staticClass:\"footer-content\"},[_c('div',{staticClass:\"footer-column\"},[_c('div',{staticClass:\"footer-title\"},[_vm._v(\"售前咨询热线\")]),_c('div',{staticClass:\"footer-phone\"},[_vm._v(\"13913283376\")]),_c('div',{staticClass:\"footer-links\"},[_c('a',{on:{\"click\":function($event){return _vm.navigateTo('/index')}}},[_c('div',{staticClass:\"footer-link\"},[_vm._v(\"首页\")])]),_c('a',{on:{\"click\":function($event){return _vm.navigateTo('/product')}}},[_c('div',{staticClass:\"footer-link\"},[_vm._v(\"AI算力市场\")])])])]),_c('div',{staticClass:\"footer-column\"},[_c('div',{staticClass:\"footer-title\"},[_vm._v(\"支持与服务\")]),_c('div',{staticClass:\"footer-links\"},[_c('div',{staticClass:\"footer-link\"},[_vm._v(\"联系我们\")]),_c('a',{on:{\"click\":function($event){return _vm.navigateTo('/help')}}},[_c('div',{staticClass:\"footer-link\"},[_vm._v(\"帮助文档\")])]),_c('div',{staticClass:\"footer-link\"},[_vm._v(\"公告\")]),_c('div',{staticClass:\"footer-link\"},[_vm._v(\"提交建议\")])])]),_vm._m(0),_vm._m(1),_vm._m(2)]),_vm._m(3)])\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"footer-column\"},[_c('div',{staticClass:\"footer-title\"},[_vm._v(\"关注天工开物\")]),_c('div',{staticClass:\"footer-links\"},[_c('div',{staticClass:\"footer-link\"},[_vm._v(\"关注天工开物\")]),_c('div',{staticClass:\"footer-link\"},[_vm._v(\"天工开物公众号\")]),_c('div',{staticClass:\"footer-link\"},[_vm._v(\"天工开物微博\")]),_c('div',{staticClass:\"footer-link\"},[_vm._v(\"天工开物支持与服务\")])])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"footer-column\"},[_c('div',{staticClass:\"footer-title\"},[_vm._v(\"联系专属客服\")]),_c('div',{staticClass:\"footer-qrcode\"},[_c('img',{attrs:{\"src\":require(\"@/assets/images/footer/wechat.jpg\"),\"alt\":\"联系专属客服二维码\"}})])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"footer-column\"},[_c('div',{staticClass:\"footer-title\"},[_vm._v(\"官方公众号\")]),_c('div',{staticClass:\"footer-qrcode\"},[_c('img',{attrs:{\"src\":require(\"@/assets/images/footer/wechat.jpg\"),\"alt\":\"官方公众号二维码\"}})])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"footer-bottom\"},[_c('div',{staticClass:\"footer-copyright\"},[_c('a',{staticStyle:{\"color\":\"inherit\",\"text-decoration\":\"none\"},attrs:{\"href\":\"https://beian.miit.gov.cn\",\"target\":\"_blank\"}},[_vm._v(\" 苏ICP备2025171841号-1 \")]),_vm._v(\" 天工开物智能科技（苏州）有限公司 ALL RIGHTS RESERVED. \")])])\n}]\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"footer\">\r\n    <div class=\"footer-content\">\r\n      <!-- First column -->\r\n      <div class=\"footer-column\">\r\n        <div class=\"footer-title\">售前咨询热线</div>\r\n        <div class=\"footer-phone\">13913283376</div>\r\n        <div class=\"footer-links\">\r\n          <a @click=\"navigateTo('/index')\"><div class=\"footer-link\">首页</div></a>\r\n          <a @click=\"navigateTo('/product')\"><div class=\"footer-link\">AI算力市场</div></a>\r\n\r\n<!--          <div class=\"footer-link\">AI算力市场</div>-->\r\n\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Second column -->\r\n      <div class=\"footer-column\">\r\n        <div class=\"footer-title\">支持与服务</div>\r\n        <div class=\"footer-links\">\r\n          <div class=\"footer-link\">联系我们</div>\r\n          <a @click=\"navigateTo('/help')\"><div class=\"footer-link\">帮助文档</div></a>\r\n          <div class=\"footer-link\">公告</div>\r\n          <div class=\"footer-link\">提交建议</div>\r\n        </div>\r\n      </div>\r\n\r\n<!--      &lt;!&ndash; Third column &ndash;&gt;-->\r\n<!--      <div class=\"footer-column\">-->\r\n<!--        <div class=\"footer-title\">账户管理</div>-->\r\n<!--        <div class=\"footer-links\">-->\r\n<!--          <div class=\"footer-link\">控制台</div>-->\r\n<!--          <div class=\"footer-link\">账号管理</div>-->\r\n<!--          <div class=\"footer-link\">充值付款</div>-->\r\n<!--          <div class=\"footer-link\">线下款 / 电汇</div>-->\r\n<!--          <div class=\"footer-link\">索取发票</div>-->\r\n<!--          <div class=\"footer-link\">合规性</div>-->\r\n<!--        </div>-->\r\n<!--      </div>-->\r\n\r\n      <!-- Fourth column -->\r\n      <div class=\"footer-column\">\r\n        <div class=\"footer-title\">关注天工开物</div>\r\n        <div class=\"footer-links\">\r\n          <div class=\"footer-link\">关注天工开物</div>\r\n          <div class=\"footer-link\">天工开物公众号</div>\r\n          <div class=\"footer-link\">天工开物微博</div>\r\n          <div class=\"footer-link\">天工开物支持与服务</div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Fifth column -->\r\n      <div class=\"footer-column\">\r\n        <div class=\"footer-title\">联系专属客服</div>\r\n        <div class=\"footer-qrcode\">\r\n          <img src=\"@/assets/images/footer/wechat.jpg\" alt=\"联系专属客服二维码\" />\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Sixth column -->\r\n      <div class=\"footer-column\">\r\n        <div class=\"footer-title\">官方公众号</div>\r\n        <div class=\"footer-qrcode\">\r\n          <img src=\"@/assets/images/footer/wechat.jpg\" alt=\"官方公众号二维码\" />\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Bottom footer links -->\r\n    <div class=\"footer-bottom\">\r\n\r\n      <div class=\"footer-copyright\">\r\n        <a href=\"https://beian.miit.gov.cn\" target=\"_blank\" style=\"color: inherit; text-decoration: none;\">\r\n          苏ICP备2025171841号-1\r\n        </a>\r\n           天工开物智能科技（苏州）有限公司 ALL RIGHTS RESERVED. </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"Footer\",\r\n  data() {\r\n    return {\r\n      // Data can be added here if needed\r\n    }\r\n  },\r\n  methods: {\r\n    navigateTo(path) {\r\n      // 记录当前活动路径作为上一个活动路径\r\n      if (this.currentPath && this.currentPath !== path) {\r\n        this.previousActivePath = this.currentPath;\r\n\r\n        // 为当前活动链接和登录按钮添加 active-exit 类\r\n        this.$nextTick(() => {\r\n          const navLinks = document.querySelectorAll('.nav-link, .btn-login');\r\n          navLinks.forEach(link => {\r\n            if ((link.classList.contains('active') ||\r\n                    (path === '/login' && link.classList.contains('btn-login'))) &&\r\n                !link.classList.contains('active-exit')) {\r\n              link.classList.add('active-exit');\r\n\r\n              // 等待动画完成后移除 active-exit 类\r\n              setTimeout(() => {\r\n                link.classList.remove('active-exit');\r\n              }, 300); // 匹配你的 CSS transition 持续时间 (0.3s)\r\n            }\r\n          });\r\n\r\n          // 更新当前路径\r\n          this.currentPath = path;\r\n        });\r\n      } else {\r\n        this.currentPath = path;\r\n      }\r\n\r\n      // 如果当前路径与目标路径相同，则重新加载页面\r\n      if (this.$route.path === path) {\r\n        this.$nextTick(() => {\r\n          window.scrollTo({\r\n            top: 0,\r\n            behavior: 'instant' // 使用即时滚动而不是平滑滚动\r\n          });\r\n          this.$router.go(0); // 刷新当前页面\r\n        });\r\n      } else {\r\n        // 不同路径，正常导航并滚动到顶部\r\n        this.$router.push(path);\r\n        window.scrollTo({\r\n          top: 0,\r\n          behavior: 'instant'\r\n        });\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.footer {\r\n  max-width: 2560px;\r\n  width: 100%;\r\n  background-color: #424242;\r\n  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;\r\n  color: white;\r\n  padding: 0;\r\n  margin: 0px;\r\n}\r\n\r\n.footer-content {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  max-width: 2560px;\r\n  margin: 0 40px;\r\n  padding: 20px 0;\r\n}\r\n\r\n.footer-column {\r\n  flex: 1;\r\n  min-width: 150px;\r\n  padding: 0 15px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.footer-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  margin-bottom: 15px;\r\n  color: white;\r\n}\r\n\r\n.footer-phone {\r\n  font-size: 16px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.footer-links {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.footer-link {\r\n  margin-bottom: 10px;\r\n  cursor: pointer;\r\n  color: white;\r\n  font-size: 14px;\r\n  transition: color 0.2s;\r\n}\r\n\r\n.footer-link:hover {\r\n  color: #1890ff;\r\n}\r\n\r\n.footer-qrcode {\r\n  width: 120px;\r\n  height: 120px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.footer-qrcode img {\r\n  max-width: 100%;\r\n  max-height: 100%;\r\n  margin-left: -18%;\r\n}\r\n\r\n.footer-bottom {\r\n  border-top: 1px solid #e8e8e8;\r\n  padding: 20px 0;\r\n  text-align: center;\r\n  max-width: 2560px;\r\n  margin: 0 40px;\r\n}\r\n\r\n.footer-bottom-links {\r\n  margin-bottom: 10px;\r\n  text-align: left;\r\n}\r\n\r\n.footer-bottom-link {\r\n  color: white;\r\n  margin: 0 10px;\r\n  font-size: 14px;\r\n  text-decoration: none;\r\n  transition: color 0.2s;\r\n}\r\n\r\n.footer-bottom-link:hover {\r\n  color: #1890ff;\r\n  text-decoration: underline;\r\n}\r\n\r\n.footer-copyright, .footer-license {\r\n  font-size: 15px;\r\n  text-align: center;\r\n  color: white;\r\n  margin-left: 10px;\r\n}\r\n</style>", "import mod from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Footer.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Footer.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Footer.vue?vue&type=template&id=2d6e9349&scoped=true&\"\nimport script from \"./Footer.vue?vue&type=script&lang=js&\"\nexport * from \"./Footer.vue?vue&type=script&lang=js&\"\nimport style0 from \"./Footer.vue?vue&type=style&index=0&id=2d6e9349&prod&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2d6e9349\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[(!_vm.isMobile)?_c('div',{staticClass:\"desktop-layout\"},[_c('div',{staticClass:\"banner-section\"},[_c('div',{staticClass:\"banner-container\"},[_c('div',{staticClass:\"big-box\"},[_c('div',{staticClass:\"img-box\"},[_c('div',{staticClass:\"show-box\",style:({\n                  transform: 'translateX(' + _vm.translate + ')',\n                  transition: _vm.tsion ? 'all 0.5s' : 'none',\n                })},_vm._l((_vm.bannerImages),function(item,index){return _c('div',{key:index,staticClass:\"slide-item\"},[_c('img',{attrs:{\"src\":item.img,\"alt\":\"\"}}),_c('div',{staticClass:\"banner-content\",class:'pos-' + item.content.position},[_c('h2',{staticClass:\"banner-title\",domProps:{\"innerHTML\":_vm._s(item.content.title)}}),_c('p',{staticClass:\"banner-text\"},[_vm._v(_vm._s(item.content.text))]),_c('div',{staticClass:\"banner-actions\"},[(!_vm.isLogin && item.content.secondaryLink)?_c('a',{staticClass:\"banner-button secondary-btn\",attrs:{\"href\":\"#\"},on:{\"click\":function($event){$event.preventDefault();return _vm.navigateTo(item.content.secondaryLink)}}},[_vm._v(\" \"+_vm._s(item.content.secondaryBtnText)+\" \")]):_vm._e(),(!_vm.isLogin && item.content.primaryLink)?_c('a',{staticClass:\"banner-button primary-btn\",attrs:{\"href\":\"#\"},on:{\"click\":function($event){$event.preventDefault();return _vm.navigateTo(item.content.primaryLink)}}},[_vm._v(\" \"+_vm._s(item.content.primaryBtnText)+\" \")]):_vm._e(),(item.content.thirdLink)?_c('a',{staticClass:\"banner-button secondary-btn\",attrs:{\"href\":\"#\"},on:{\"click\":function($event){$event.preventDefault();return _vm.navigateTo(item.content.thirdLink)}}},[_vm._v(\" \"+_vm._s(item.content.thirdBtnText)+\" \")]):_vm._e()])])])}),0)]),_c('div',{staticClass:\"arrowhead-box\"},[_c('span',{staticClass:\"nav-arrow left\",on:{\"click\":_vm.last}},[_c('img',{staticClass:\"arrow-icon rotated\",attrs:{\"src\":require(\"../../assets/images/index/right-arrow.png\"),\"alt\":\"\"}})]),_c('span',{staticClass:\"nav-arrow right\",on:{\"click\":_vm.next}},[_c('img',{staticClass:\"arrow-icon\",attrs:{\"src\":require(\"../../assets/images/index/right-arrow.png\"),\"alt\":\"\"}})])]),_c('div',{ref:\"swiperPagination\",staticClass:\"swiper-pagination\"},_vm._l((_vm.bannerImages),function(item,index){return _c('span',{key:index,class:{ active: _vm.translateX === index }})}),0)])])]),_c('section',{staticClass:\"section gpu-section\"},[_c('div',{staticClass:\"container\"},[_vm._m(0),_c('div',{staticClass:\"gpu-card-grid\"},_vm._l((_vm.gpus),function(gpu,index){return _c('div',{key:index,staticClass:\"gpu-card\",class:{ 'recommended': gpu.recommended },on:{\"click\":function($event){return _vm.navigateTo('/product')}}},[_c('div',{staticClass:\"gpu-card-header\"},[_c('h3',{staticClass:\"gpu-name\"},[_vm._v(_vm._s(gpu.name))]),(gpu.recommended)?_c('span',{staticClass:\"recommendation-tag\"},[_vm._v(\"推荐\")]):_vm._e(),(gpu.isNew)?_c('span',{staticClass:\"new-tag\"},[_vm._v(\"NEW\")]):_vm._e()]),_c('div',{staticClass:\"gpu-specs-pricing\"},[_c('div',{staticClass:\"specs-section\"},[_c('div',{staticClass:\"spec-item\"},[_c('span',{staticClass:\"spec-label\"},[_vm._v(\"单精度:\")]),_c('span',{staticClass:\"spec-value\"},[_vm._v(_vm._s(gpu.singlePrecision)+\" TFLOPS\")])]),_c('div',{staticClass:\"spec-item\"},[_c('span',{staticClass:\"spec-label\"},[_vm._v(\"半精度:\")]),_c('span',{staticClass:\"spec-value\"},[_vm._v(_vm._s(gpu.halfPrecision)+\" Tensor TFL\")])])]),_c('div',{staticClass:\"price-section\"},[_c('div',{staticClass:\"gpu-pricing\"},[(gpu.originalPrice)?_c('span',{staticClass:\"original-price\"},[_vm._v(\"¥\"+_vm._s(gpu.originalPrice)+\"/时\")]):_vm._e(),_c('span',{staticClass:\"current-price\"},[_vm._v(\"¥\"),_c('span',{staticClass:\"price-value\"},[_vm._v(_vm._s(gpu.price))]),_vm._v(\"/时\")])])])])])}),0)])]),_c('GpuComparison'),_c('section',{staticClass:\"section services-section\"},[_c('div',{staticClass:\"container\"},[_vm._m(1),_c('div',{staticClass:\"services-grid\"},_vm._l((_vm.serviceList),function(service,index){return _c('div',{key:index,staticClass:\"service-item\"},[_c('div',{staticClass:\"service-card\"},[_c('i',{staticClass:\"service-icon\",class:service.icon}),_c('h3',{staticClass:\"service-title\"},[_vm._v(_vm._s(service.title))]),_c('div',{staticClass:\"service-text\"},[_c('p',[_vm._v(_vm._s(service.desc))])])])])}),0)])]),_c('section',{staticClass:\"appsec-section\"},[_vm._m(2),_c('div',{staticClass:\"appsec-container\"},[_c('div',{staticClass:\"appsec-grid\"},[_c('div',{staticClass:\"appsec-item appsec-wide\"},[_c('div',{staticClass:\"appsec-card\",on:{\"mouseover\":function($event){_vm.firstRowWide.hover = true},\"mouseleave\":function($event){_vm.firstRowWide.hover = false}}},[_c('div',{staticClass:\"appsec-image\",class:{ 'appsec-hover': _vm.firstRowWide.hover }},[_c('img',{attrs:{\"src\":_vm.firstRowWide.image,\"alt\":_vm.firstRowWide.title}})]),_c('div',{staticClass:\"appsec-cardtitle\",class:{ 'appsec-hover': _vm.firstRowWide.hover }},[_vm._v(_vm._s(_vm.firstRowWide.title))])])]),_vm._l((_vm.firstRowTallApps),function(app,index){return _c('div',{key:'tall-'+index,staticClass:\"appsec-item appsec-tall\"},[_c('div',{staticClass:\"appsec-card\",on:{\"mouseover\":function($event){app.hover = true},\"mouseleave\":function($event){app.hover = false}}},[_c('div',{staticClass:\"appsec-image\",class:{ 'appsec-hover': app.hover }},[_c('img',{attrs:{\"src\":app.image,\"alt\":app.title}})]),_c('div',{staticClass:\"appsec-cardtitle\",class:{ 'appsec-hover': app.hover }},[_vm._v(_vm._s(app.title))])])])}),_vm._l((_vm.secondRowApps),function(app,index){return _c('div',{key:'small-'+index,staticClass:\"appsec-item appsec-small\"},[_c('div',{staticClass:\"appsec-card\",on:{\"mouseover\":function($event){app.hover = true},\"mouseleave\":function($event){app.hover = false}}},[_c('div',{staticClass:\"appsec-image\",class:{ 'appsec-hover': app.hover }},[_c('img',{attrs:{\"src\":app.image,\"alt\":app.title}})]),_c('div',{staticClass:\"appsec-cardtitle\",class:{ 'appsec-hover': app.hover }},[_vm._v(_vm._s(app.title))])])])}),_vm._l((_vm.thirdRowSmallApps),function(app,index){return _c('div',{key:'third-small-'+index,staticClass:\"appsec-item appsec-small\"},[_c('div',{staticClass:\"appsec-card\",on:{\"mouseover\":function($event){app.hover = true},\"mouseleave\":function($event){app.hover = false}}},[_c('div',{staticClass:\"appsec-image\",class:{ 'appsec-hover': app.hover }},[_c('img',{attrs:{\"src\":app.image,\"alt\":app.title}})]),_c('div',{staticClass:\"appsec-cardtitle\",class:{ 'appsec-hover': app.hover }},[_vm._v(_vm._s(app.title))])])])}),_c('div',{staticClass:\"appsec-item appsec-wide\"},[_c('div',{staticClass:\"appsec-card\",on:{\"mouseover\":function($event){_vm.thirdRowWide.hover = true},\"mouseleave\":function($event){_vm.thirdRowWide.hover = false}}},[_c('div',{staticClass:\"appsec-image\",class:{ 'appsec-hover': _vm.thirdRowWide.hover }},[_c('img',{attrs:{\"src\":_vm.thirdRowWide.image,\"alt\":_vm.thirdRowWide.title}})]),_c('div',{staticClass:\"appsec-cardtitle\",class:{ 'appsec-hover': _vm.thirdRowWide.hover }},[_vm._v(_vm._s(_vm.thirdRowWide.title))])])])],2),_c('chat-ai')],1)]),_c('div',{staticClass:\"recommendation-tag1\"},[_c('div',{staticClass:\"card1\"},[_c('h1',{staticClass:\"banner-text1\"},[_vm._v(\"为AI+千行百业，提供高性能算力服务\")]),_c('button',{staticClass:\"consult-button1\",on:{\"click\":_vm.openContactModal}},[_vm._v(\"立即咨询\")])])]),_c('transition',{attrs:{\"name\":\"fade\"}},[(_vm.showContactModal)?_c('div',{staticClass:\"contact-modal-overlay\",on:{\"click\":function($event){if($event.target !== $event.currentTarget)return null;return _vm.closeContactModal.apply(null, arguments)}}},[_c('div',{staticClass:\"contact-modal\"},[_c('button',{staticClass:\"close-modal\",on:{\"click\":_vm.closeContactModal}},[_vm._v(\" × \")]),_c('div',{staticClass:\"contact-content\"},[_c('div',{staticClass:\"contact-item\"},[_c('i',{staticClass:\"am-icon-user\"}),_c('span',[_vm._v(_vm._s(_vm.contactInfo.name))])]),_c('div',{staticClass:\"contact-item\"},[_c('i',{staticClass:\"am-icon-phone\"}),_c('span',[_vm._v(_vm._s(_vm.contactInfo.phone))])])]),_c('div',{staticClass:\"contact-note\"},[_c('p',[_vm._v(\"欢迎随时来电咨询\")])])])]):_vm._e()])],1):_c('div',{staticClass:\"mobile-layout\"},[_c('div',{staticClass:\"mobile-banner\",on:{\"touchstart\":_vm.handleTouchStart,\"touchmove\":_vm.handleTouchMove,\"touchend\":_vm.handleTouchEnd}},[_c('div',{staticClass:\"mobile-banner-slider\",style:({ transform: `translateX(${-_vm.mobileCurrentSlide * 100}%)` })},_vm._l((_vm.bannerImages),function(item,index){return _c('div',{key:index,staticClass:\"mobile-slide\"},[_c('div',{staticClass:\"mobile-slide-inner\"},[_c('img',{staticClass:\"mobile-slide-img\",attrs:{\"src\":item.img,\"alt\":\"\"}}),_c('div',{staticClass:\"mobile-banner-content\",class:'pos-' + item.content.position},[_c('h2',{staticClass:\"mobile-banner-title\",domProps:{\"innerHTML\":_vm._s(item.content.title)}}),_c('p',{staticClass:\"mobile-banner-text\"},[_vm._v(_vm._s(item.content.text))]),_c('div',{staticClass:\"mobile-banner-actions\"},[(!_vm.isLogin && item.content.secondaryLink)?_c('a',{staticClass:\"mobile-banner-button secondary-btn\",attrs:{\"href\":\"#\"},on:{\"click\":function($event){$event.preventDefault();return _vm.navigateTo(item.content.secondaryLink)}}},[_vm._v(\" \"+_vm._s(item.content.secondaryBtnText)+\" \")]):_vm._e(),(!_vm.isLogin && item.content.primaryLink)?_c('a',{staticClass:\"mobile-banner-button primary-btn\",attrs:{\"href\":\"#\"},on:{\"click\":function($event){$event.preventDefault();return _vm.navigateTo(item.content.primaryLink)}}},[_vm._v(\" \"+_vm._s(item.content.primaryBtnText)+\" \")]):_vm._e(),(item.content.thirdLink)?_c('a',{staticClass:\"banner-button secondary-btn\",attrs:{\"href\":\"#\"},on:{\"click\":function($event){$event.preventDefault();return _vm.navigateTo(item.content.thirdLink)}}},[_vm._v(\" \"+_vm._s(item.content.thirdBtnText)+\" \")]):_vm._e()])])])])}),0),_c('div',{staticClass:\"mobile-banner-pagination\"},_vm._l((_vm.bannerImages),function(item,index){return _c('span',{key:index,class:{ active: _vm.mobileCurrentSlide === index },on:{\"click\":function($event){return _vm.goToSlide(index)}}})}),0)]),_c('section',{staticClass:\"mobile-section mobile-gpu-section\"},[_vm._m(3),_c('div',{staticClass:\"mobile-gpu-list\"},_vm._l((_vm.gpus),function(gpu,index){return _c('div',{key:index,staticClass:\"mobile-gpu-card\",class:{ 'recommended': gpu.recommended },on:{\"click\":function($event){return _vm.navigateTo('/product')}}},[_c('div',{staticClass:\"mobile-gpu-header\"},[_c('h3',{staticClass:\"mobile-gpu-name\"},[_vm._v(_vm._s(gpu.name))]),_c('div',{staticClass:\"mobile-gpu-tags\"},[(gpu.recommended)?_c('span',{staticClass:\"mobile-recommend-tag\"},[_vm._v(\"推荐\")]):_vm._e(),(gpu.isNew)?_c('span',{staticClass:\"mobile-new-tag\"},[_vm._v(\"NEW\")]):_vm._e()])]),_c('div',{staticClass:\"mobile-gpu-specs\"},[_c('div',{staticClass:\"mobile-spec-item\"},[_c('span',{staticClass:\"mobile-spec-label\"},[_vm._v(\"单精度:\")]),_c('span',{staticClass:\"mobile-spec-value\"},[_vm._v(_vm._s(gpu.singlePrecision)+\" TFLOPS\")])]),_c('div',{staticClass:\"mobile-spec-item\"},[_c('span',{staticClass:\"mobile-spec-label\"},[_vm._v(\"半精度:\")]),_c('span',{staticClass:\"mobile-spec-value\"},[_vm._v(_vm._s(gpu.halfPrecision)+\" Tensor TFL\")])])]),_c('div',{staticClass:\"mobile-gpu-price\"},[(gpu.originalPrice)?_c('span',{staticClass:\"mobile-original-price\"},[_vm._v(\"¥\"+_vm._s(gpu.originalPrice)+\"/时\")]):_vm._e(),_c('span',{staticClass:\"mobile-current-price\"},[_vm._v(\"¥\"+_vm._s(gpu.price)+\"/时\")])])])}),0)]),_c('section',{staticClass:\"mobile-section mobile-comparison-section\"},[_vm._m(4),_c('div',{staticClass:\"mobile-comparison-container\"},[_c('div',{staticClass:\"mobile-comparison-scroll\"},[_c('table',{staticClass:\"mobile-comparison-table\"},[_c('thead',[_c('tr',[_c('th',[_vm._v(\"GPU型号\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('th',{key:gpu.name},[_vm._v(_vm._s(gpu.name))])})],2)]),_c('tbody',[_c('tr',[_c('td',[_vm._v(\"架构\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('td',{key:gpu.name},[_vm._v(_vm._s(gpu.architecture))])})],2),_c('tr',[_c('td',[_vm._v(\"FP16性能\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('td',{key:gpu.name},[_vm._v(_vm._s(gpu.fp16Performance))])})],2),_c('tr',[_c('td',[_vm._v(\"FP32性能\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('td',{key:gpu.name},[_vm._v(_vm._s(gpu.fp32Performance))])})],2),_c('tr',[_c('td',[_vm._v(\"显存\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('td',{key:gpu.name},[_vm._v(_vm._s(gpu.memory))])})],2),_c('tr',[_c('td',[_vm._v(\"显存类型\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('td',{key:gpu.name},[_vm._v(_vm._s(gpu.memoryType))])})],2),_c('tr',[_c('td',[_vm._v(\"带宽\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('td',{key:gpu.name},[_vm._v(_vm._s(gpu.bandwidth))])})],2)])])])])]),_c('section',{staticClass:\"mobile-section mobile-services-section\"},[_vm._m(5),_c('div',{staticClass:\"mobile-services-list\"},_vm._l((_vm.serviceList),function(service,index){return _c('div',{key:index,staticClass:\"mobile-service-card\"},[_c('div',{staticClass:\"mobile-service-icon\"},[_c('i',{class:service.icon})]),_c('h3',{staticClass:\"mobile-service-title\"},[_vm._v(_vm._s(service.title))]),_c('p',{staticClass:\"mobile-service-desc\"},[_vm._v(_vm._s(service.desc))])])}),0)]),_c('section',{staticClass:\"mobile-section mobile-applications-section\"},[_vm._m(6),_c('div',{staticClass:\"mobile-applications-grid\"},_vm._l((_vm.mobileApplications),function(app,index){return _c('div',{key:index,staticClass:\"mobile-app-item\"},[_c('div',{staticClass:\"mobile-app-image\"},[_c('img',{attrs:{\"src\":app.image,\"alt\":app.title}})]),_c('h3',{staticClass:\"mobile-app-title\"},[_vm._v(_vm._s(app.title))])])}),0)]),_c('div',{staticClass:\"mobile-consult-section\"},[_c('h3',{staticClass:\"mobile-consult-title\"},[_vm._v(\"为AI+千行百业，提供高性能算力服务\")]),_c('button',{staticClass:\"mobile-consult-button\",on:{\"click\":_vm.openContactModal}},[_vm._v(\"立即咨询\")])]),_c('transition',{attrs:{\"name\":\"mobile-fade\"}},[(_vm.showContactModal)?_c('div',{staticClass:\"mobile-contact-overlay\",on:{\"click\":function($event){if($event.target !== $event.currentTarget)return null;return _vm.closeContactModal.apply(null, arguments)}}},[_c('div',{staticClass:\"mobile-contact-modal\"},[_c('button',{staticClass:\"mobile-close-modal\",on:{\"click\":_vm.closeContactModal}},[_vm._v(\" × \")]),_c('div',{staticClass:\"mobile-contact-content\"},[_c('div',{staticClass:\"mobile-contact-item\"},[_c('i',{staticClass:\"am-icon-user\"}),_c('span',[_vm._v(_vm._s(_vm.contactInfo.name))])]),_c('div',{staticClass:\"mobile-contact-item\"},[_c('i',{staticClass:\"am-icon-phone\"}),_c('span',[_vm._v(_vm._s(_vm.contactInfo.phone))])])]),_c('div',{staticClass:\"mobile-contact-note\"},[_c('p',[_vm._v(\"欢迎随时来电咨询\")])])])]):_vm._e()])],1),(!_vm.isMobile)?_c('Mider'):_vm._e(),_c('Footer'),_c('chatAi')],1)\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"section-header\"},[_c('h2',{staticClass:\"section-title\"},[_vm._v(\"为您推荐\")]),_c('p',{staticClass:\"section-description\"},[_vm._v(\" 专注于提供高性能、稳定可靠的 GPU 算力服务，兼具灵活配置与优质性价比。 \")])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"section-header\"},[_c('h2',{staticClass:\"section-title\"},[_vm._v(\"核心优势\")]),_c('p',{staticClass:\"section-description\"},[_vm._v(\" 专业铸造优秀,天工开物企业AI变革路上的好伙伴。 \")])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"section-header\"},[_c('h2',{staticClass:\"section-title\"},[_vm._v(\"行业应用\")]),_c('p',{staticClass:\"section-description\"},[_vm._v(\" Applications \")])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"mobile-section-header\"},[_c('h2',{staticClass:\"mobile-section-title\"},[_vm._v(\"为您推荐\")]),_c('p',{staticClass:\"mobile-section-description\"},[_vm._v(\" 专注于提供高性能、稳定可靠的 GPU 算力服务 \")])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"mobile-section-header\"},[_c('h2',{staticClass:\"mobile-section-title\"},[_vm._v(\"GPU性能对比\")]),_c('p',{staticClass:\"mobile-section-description\"},[_vm._v(\" 专业GPU性能详细对比 \")])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"mobile-section-header\"},[_c('h2',{staticClass:\"mobile-section-title\"},[_vm._v(\"核心优势\")]),_c('p',{staticClass:\"mobile-section-description\"},[_vm._v(\" 专业铸造优秀,天工开物企业AI变革路上的好伙伴 \")])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"mobile-section-header\"},[_c('h2',{staticClass:\"mobile-section-title\"},[_vm._v(\"行业应用\")]),_c('p',{staticClass:\"mobile-section-description\"},[_vm._v(\" Applications \")])])\n}]\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('section',{staticClass:\"section gpu-comparison-section\"},[_c('div',{staticClass:\"container\"},[_vm._m(0),_c('div',{staticClass:\"gpu-comparison-table\"},[_c('table',[_c('thead',[_c('tr',[_c('th',[_vm._v(\"GPU型号\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('th',{key:gpu.name},[_vm._v(_vm._s(gpu.name))])})],2)]),_c('tbody',[_c('tr',[_c('td',[_vm._v(\"架构\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('td',{key:gpu.name},[_vm._v(_vm._s(gpu.architecture))])})],2),_c('tr',[_c('td',[_vm._v(\"FP16性能\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('td',{key:gpu.name},[_vm._v(_vm._s(gpu.fp16Performance))])})],2),_c('tr',[_c('td',[_vm._v(\"FP32性能\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('td',{key:gpu.name},[_vm._v(_vm._s(gpu.fp32Performance))])})],2),_c('tr',[_c('td',[_vm._v(\"显存\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('td',{key:gpu.name},[_vm._v(_vm._s(gpu.memory))])})],2),_c('tr',[_c('td',[_vm._v(\"显存类型\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('td',{key:gpu.name},[_vm._v(_vm._s(gpu.memoryType))])})],2),_c('tr',[_c('td',[_vm._v(\"带宽\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('td',{key:gpu.name},[_vm._v(_vm._s(gpu.bandwidth))])})],2)])])])])])\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"section-header\"},[_c('h2',{staticClass:\"section-title\"},[_vm._v(\"GPU性能对比\")]),_c('p',{staticClass:\"section-description\"},[_vm._v(\" 专业GPU性能详细对比，助您选择最适合的计算资源 \")])])\n}]\n\nexport { render, staticRenderFns }", "<template>\r\n  <section class=\"section gpu-comparison-section\">\r\n    <div class=\"container\">\r\n      <div class=\"section-header\">\r\n        <h2 class=\"section-title\">GPU性能对比</h2>\r\n        <p class=\"section-description\">\r\n          专业GPU性能详细对比，助您选择最适合的计算资源\r\n        </p>\r\n      </div>\r\n\r\n      <div class=\"gpu-comparison-table\">\r\n        <table>\r\n          <thead>\r\n          <tr>\r\n            <th>GPU型号</th>\r\n            <th v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.name }}</th>\r\n          </tr>\r\n          </thead>\r\n          <tbody>\r\n          <tr>\r\n            <td>架构</td>\r\n            <td v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.architecture }}</td>\r\n          </tr>\r\n          <tr>\r\n            <td>FP16性能</td>\r\n            <td v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.fp16Performance }}</td>\r\n          </tr>\r\n          <tr>\r\n            <td>FP32性能</td>\r\n            <td v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.fp32Performance }}</td>\r\n          </tr>\r\n          <tr>\r\n            <td>显存</td>\r\n            <td v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.memory }}</td>\r\n          </tr>\r\n          <tr>\r\n            <td>显存类型</td>\r\n            <td v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.memoryType }}</td>\r\n          </tr>\r\n          <tr>\r\n            <td>带宽</td>\r\n            <td v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.bandwidth }}</td>\r\n          </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n    </div>\r\n  </section>\r\n</template>\r\n\r\n<script>\r\nimport {getNotAuth} from \"@/api/login\";\r\n\r\nexport default {\r\n  name: 'GpuComparison',\r\n  data() {\r\n    return {\r\n      comparisonGpus: [\r\n        {\r\n          name: 'A100',\r\n          architecture: 'Ampere',\r\n          fp16Performance: '312 TFLOPS',\r\n          fp32Performance: '19.5 TFLOPS',\r\n          memory: '80 GB',\r\n          memoryType: 'HBM2',\r\n          bandwidth: '2,039 GB/s'\r\n        },\r\n        {\r\n          name: 'V100',\r\n          architecture: 'Volta',\r\n          fp16Performance: '125 TFLOPS',\r\n          fp32Performance: '15.7 TFLOPS',\r\n          memory: '32 GB',\r\n          memoryType: 'HBM2',\r\n          bandwidth: '900 GB/s'\r\n        },\r\n        {\r\n          name: 'A6000',\r\n          architecture: 'Ampere',\r\n          fp16Performance: '77.4 TFLOPS',\r\n          fp32Performance: '38.7 TFLOPS',\r\n          memory: '48 GB',\r\n          memoryType: 'GDDR6',\r\n          bandwidth: '768 GB/s'\r\n        },\r\n        {\r\n          name: 'A5000',\r\n          architecture: 'Ampere',\r\n          fp16Performance: '54.2 TFLOPS',\r\n          fp32Performance: '27.8 TFLOPS',\r\n          memory: '24 GB',\r\n          memoryType: 'GDDR6',\r\n          bandwidth: '768 GB/s'\r\n        },\r\n        {\r\n          name: 'A4000',\r\n          architecture: 'Ampere',\r\n          fp16Performance: '19.17 TFLOPS',\r\n          fp32Performance: '19.17 TFLOPS',\r\n          memory: '16 GB',\r\n          memoryType: 'GDDR6',\r\n          bandwidth: '448 GB/s'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  created() {\r\n    this.fetchComparison();\r\n  },\r\n  methods: {\r\n    async fetchComparison() {\r\n      try {\r\n        // console.log(\"开始获取对比数据\")\r\n        getNotAuth(\"/system/comparison/list\").then(req =>{\r\n          // console.log(\"原始数据\",req.data.rows)\r\n          this.comparisonGpus = req.data.rows.map(item => ({\r\n            ...item,\r\n            fp16Performance: item.fp16performance,\r\n            fp32Performance: item.fp32performance,\r\n            memoryType:item.memorytype\r\n          }))\r\n          // this.gpus = req.data.rows\r\n          // console.log(\"数据\",this.gpus)\r\n        })\r\n      } catch (error) {\r\n        console.error('获取GPU推荐列表失败:', error);\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.gpu-comparison-section {\r\n  /*padding-top: 10vh;*/\r\n  background-color: #f9f9f9;\r\n  width: 100%;\r\n  max-width: 2560px;\r\n  margin: 0 auto;\r\n  padding-bottom: 15vh;\r\n}\r\n\r\n.container {\r\n  width: 93%;\r\n  max-width: 2560px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.section-header {\r\n  text-align: center;\r\n  margin-bottom: 40px;\r\n}\r\n\r\n.section-title {\r\n  font-size: 28px;\r\n  color: #333;\r\n  position: relative;\r\n  display: inline-block;\r\n}\r\n\r\n.section-title::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: -5px;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  width: 50px;\r\n  height: 3px;\r\n  background-color: #2196f3;\r\n}\r\n\r\n.section-description {\r\n  color: #666;\r\n  max-width: 600px;\r\n  margin: 0 auto;\r\n  line-height: 1.6;\r\n}\r\n\r\n.gpu-comparison-table {\r\n  width: 100%;\r\n  padding: 0;\r\n  border-collapse: collapse;\r\n  margin: 0;\r\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);\r\n  border-radius: 10px;\r\n  overflow: hidden;\r\n  overflow-x: auto;\r\n}\r\n\r\n.gpu-comparison-table table {\r\n  width: 100%;\r\n  min-width: 1000px;\r\n}\r\n\r\n.gpu-comparison-table thead {\r\n  background-color: #cddfec;\r\n}\r\n\r\n.gpu-comparison-table thead tr th,\r\n.gpu-comparison-table tbody tr td {\r\n  padding: 18px 25px;\r\n  text-align: center;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  font-size: 16px;\r\n  min-width: 180px;\r\n  border-bottom: 1px solid #e0e5eb;\r\n  transition: background-color 0.3s ease;\r\n}\r\n\r\n.gpu-comparison-table thead tr th {\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.gpu-comparison-table tbody tr td {\r\n  color: #333;\r\n  font-weight: normal;\r\n}\r\n\r\n.gpu-comparison-table tbody tr:nth-child(even) {\r\n  background-color: #f9fafc;\r\n}\r\n\r\n.gpu-comparison-table tbody tr:hover {\r\n  background-color: #f1f6fd;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1200px) {\r\n  .container {\r\n    width: 98%;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .section-title {\r\n    font-size: 26px;\r\n    color: #333;\r\n  }\r\n\r\n  .section-description {\r\n    font-size: 16px;\r\n  }\r\n}\r\n</style>", "import mod from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./GpuComparison.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./GpuComparison.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./GpuComparison.vue?vue&type=template&id=fed986aa&scoped=true&\"\nimport script from \"./GpuComparison.vue?vue&type=script&lang=js&\"\nexport * from \"./GpuComparison.vue?vue&type=script&lang=js&\"\nimport style0 from \"./GpuComparison.vue?vue&type=style&index=0&id=fed986aa&prod&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"fed986aa\",\n  null\n  \n)\n\nexport default component.exports", "<template>\r\n\r\n  <div>\r\n\r\n    <!-- 电脑端布局 -->\r\n    <div v-if=\"!isMobile\" class=\"desktop-layout\">\r\n      <!-- Banner Section -->\r\n      <div class=\"banner-section\">\r\n        <div class=\"banner-container\">\r\n          <div class=\"big-box\">\r\n            <div class=\"img-box\">\r\n              <div class=\"show-box\"\r\n                   :style=\"{\r\n                    transform: 'translateX(' + translate + ')',\r\n                    transition: tsion ? 'all 0.5s' : 'none',\r\n                  }\">\r\n                <div class=\"slide-item\" v-for=\"(item, index) in bannerImages\" :key=\"index\">\r\n                  <img :src=\"item.img\" alt=\"\" />\r\n                  <div class=\"banner-content\" :class=\"'pos-' + item.content.position\">\r\n                    <h2 class=\"banner-title\" v-html=\"item.content.title\"></h2>\r\n                    <p class=\"banner-text\">{{ item.content.text }}</p>\r\n                    <div class=\"banner-actions\">\r\n                      <a v-if=\"!isLogin && item.content.secondaryLink\"\r\n                         href=\"#\"\r\n                         @click.prevent=\"navigateTo(item.content.secondaryLink)\"\r\n                         class=\"banner-button secondary-btn\">\r\n                        {{ item.content.secondaryBtnText }}\r\n                      </a>\r\n                      <a v-if=\"!isLogin && item.content.primaryLink\"\r\n                         href=\"#\"\r\n                         @click.prevent=\"navigateTo(item.content.primaryLink)\"\r\n                         class=\"banner-button primary-btn\">\r\n                        {{ item.content.primaryBtnText }}\r\n                      </a>\r\n                      <a v-if=\"item.content.thirdLink\"\r\n                         href=\"#\"\r\n                         @click.prevent=\"navigateTo(item.content.thirdLink)\"\r\n                         class=\"banner-button secondary-btn\">\r\n                        {{ item.content.thirdBtnText }}\r\n                      </a>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"arrowhead-box\">\r\n              <span @click=\"last\" class=\"nav-arrow left\">\r\n                <img src=\"../../assets/images/index/right-arrow.png\" alt=\"\" class=\"arrow-icon rotated\">\r\n              </span>\r\n              <span @click=\"next\" class=\"nav-arrow right\">\r\n                <img src=\"../../assets/images/index/right-arrow.png\" alt=\"\" class=\"arrow-icon\">\r\n              </span>\r\n            </div>\r\n            <div class=\"swiper-pagination\" ref=\"swiperPagination\">\r\n              <span v-for=\"(item, index) in bannerImages\" :key=\"index\" :class=\"{ active: translateX === index }\"></span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 为您推荐 Section  -->\r\n      <section class=\"section gpu-section\">\r\n        <div class=\"container\">\r\n          <div class=\"section-header\">\r\n            <h2 class=\"section-title\">为您推荐</h2>\r\n            <p class=\"section-description\">\r\n              专注于提供高性能、稳定可靠的 GPU 算力服务，兼具灵活配置与优质性价比。\r\n            </p>\r\n          </div>\r\n\r\n          <div class=\"gpu-card-grid\">\r\n            <div\r\n                v-for=\"(gpu, index) in gpus\"\r\n                :key=\"index\"\r\n                class=\"gpu-card\"\r\n                :class=\"{ 'recommended': gpu.recommended }\"\r\n                @click=\"navigateTo('/product')\"\r\n            >\r\n              <div class=\"gpu-card-header\">\r\n                <h3 class=\"gpu-name\">{{ gpu.name }}</h3>\r\n                <span v-if=\"gpu.recommended\" class=\"recommendation-tag\">推荐</span>\r\n                <span v-if=\"gpu.isNew\" class=\"new-tag\">NEW</span>\r\n              </div>\r\n\r\n              <div class=\"gpu-specs-pricing\">\r\n                <div class=\"specs-section\">\r\n                  <div class=\"spec-item\">\r\n                    <span class=\"spec-label\">单精度:</span>\r\n                    <span class=\"spec-value\">{{ gpu.singlePrecision }} TFLOPS</span>\r\n                  </div>\r\n                  <div class=\"spec-item\">\r\n                    <span class=\"spec-label\">半精度:</span>\r\n                    <span class=\"spec-value\">{{ gpu.halfPrecision }} Tensor TFL</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"price-section\">\r\n                  <div class=\"gpu-pricing\">\r\n                    <span class=\"original-price\" v-if=\"gpu.originalPrice\">¥{{ gpu.originalPrice }}/时</span>\r\n                    <span class=\"current-price\">¥<span class=\"price-value\">{{ gpu.price }}</span>/时</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      <GpuComparison></GpuComparison>\r\n\r\n      <!-- 核心优势 Section -->\r\n      <section class=\"section services-section\">\r\n        <div class=\"container\">\r\n          <div class=\"section-header\">\r\n            <h2 class=\"section-title\">核心优势</h2>\r\n            <p class=\"section-description\">\r\n              专业铸造优秀,天工开物企业AI变革路上的好伙伴。\r\n            </p>\r\n          </div>\r\n\r\n          <div class=\"services-grid\">\r\n            <div class=\"service-item\" v-for=\"(service,index) in serviceList\" :key=\"index\">\r\n              <div class=\"service-card\">\r\n                <i class=\"service-icon\" :class=\"service.icon\"></i>\r\n                <h3 class=\"service-title\">{{ service.title }}</h3>\r\n                <div class=\"service-text\"><p>{{service.desc}}</p></div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      <!-- Application Areas Section -->\r\n      <section class=\"appsec-section\">\r\n        <div class=\"section-header\">\r\n          <h2 class=\"section-title\">行业应用</h2>\r\n          <p class=\"section-description\">\r\n            Applications\r\n          </p>\r\n        </div>\r\n\r\n        <div class=\"appsec-container\">\r\n          <div class=\"appsec-grid\">\r\n            <!-- 第一行 -->\r\n            <!-- 宽幅项目 (2x1) -->\r\n            <div class=\"appsec-item appsec-wide\">\r\n              <div class=\"appsec-card\" @mouseover=\"firstRowWide.hover = true\" @mouseleave=\"firstRowWide.hover = false\">\r\n                <div class=\"appsec-image\" :class=\"{ 'appsec-hover': firstRowWide.hover }\">\r\n                  <img :src=\"firstRowWide.image\" :alt=\"firstRowWide.title\">\r\n                </div>\r\n                <div class=\"appsec-cardtitle\" :class=\"{ 'appsec-hover': firstRowWide.hover }\">{{ firstRowWide.title }}</div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 两个高竖幅项目 (1x2) -->\r\n            <div class=\"appsec-item appsec-tall\" v-for=\"(app, index) in firstRowTallApps\" :key=\"'tall-'+index\">\r\n              <div class=\"appsec-card\" @mouseover=\"app.hover = true\" @mouseleave=\"app.hover = false\">\r\n                <div class=\"appsec-image\" :class=\"{ 'appsec-hover': app.hover }\">\r\n                  <img :src=\"app.image\" :alt=\"app.title\">\r\n                </div>\r\n                <div class=\"appsec-cardtitle\" :class=\"{ 'appsec-hover': app.hover }\">{{ app.title }}</div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 第二行 -->\r\n            <!-- 两个小方形 (1x1) 放在第一行宽幅图片下方 -->\r\n            <div class=\"appsec-item appsec-small\" v-for=\"(app, index) in secondRowApps\" :key=\"'small-'+index\">\r\n              <div class=\"appsec-card\" @mouseover=\"app.hover = true\" @mouseleave=\"app.hover = false\">\r\n                <div class=\"appsec-image\" :class=\"{ 'appsec-hover': app.hover }\">\r\n                  <img :src=\"app.image\" :alt=\"app.title\">\r\n                </div>\r\n                <div class=\"appsec-cardtitle\" :class=\"{ 'appsec-hover': app.hover }\">{{ app.title }}</div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 第三行 -->\r\n            <!-- 两个小方形 (1x1) -->\r\n            <div class=\"appsec-item appsec-small\" v-for=\"(app, index) in thirdRowSmallApps\" :key=\"'third-small-'+index\">\r\n              <div class=\"appsec-card\" @mouseover=\"app.hover = true\" @mouseleave=\"app.hover = false\">\r\n                <div class=\"appsec-image\" :class=\"{ 'appsec-hover': app.hover }\">\r\n                  <img :src=\"app.image\" :alt=\"app.title\">\r\n                </div>\r\n                <div class=\"appsec-cardtitle\" :class=\"{ 'appsec-hover': app.hover }\">{{ app.title }}</div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 一个宽幅项目 (2x1) -->\r\n            <div class=\"appsec-item appsec-wide\">\r\n              <div class=\"appsec-card\" @mouseover=\"thirdRowWide.hover = true\" @mouseleave=\"thirdRowWide.hover = false\">\r\n                <div class=\"appsec-image\" :class=\"{ 'appsec-hover': thirdRowWide.hover }\">\r\n                  <img :src=\"thirdRowWide.image\" :alt=\"thirdRowWide.title\">\r\n                </div>\r\n                <div class=\"appsec-cardtitle\" :class=\"{ 'appsec-hover': thirdRowWide.hover }\">{{ thirdRowWide.title }}</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <chat-ai/>\r\n        </div>\r\n      </section>\r\n\r\n      <div class=\"recommendation-tag1\">\r\n        <div class=\"card1\">\r\n          <h1 class=\"banner-text1\">为AI+千行百业，提供高性能算力服务</h1>\r\n          <button class=\"consult-button1\" @click=\"openContactModal\">立即咨询</button>\r\n        </div>\r\n      </div>\r\n\r\n      <transition name=\"fade\">\r\n        <div v-if=\"showContactModal\" class=\"contact-modal-overlay\" @click.self=\"closeContactModal\">\r\n          <div class=\"contact-modal\">\r\n            <button class=\"close-modal\" @click=\"closeContactModal\">\r\n              &times;\r\n            </button>\r\n            <div class=\"contact-content\">\r\n              <div class=\"contact-item\">\r\n                <i class=\"am-icon-user\"></i>\r\n                <span>{{ contactInfo.name }}</span>\r\n              </div>\r\n              <div class=\"contact-item\">\r\n                <i class=\"am-icon-phone\"></i>\r\n                <span>{{ contactInfo.phone }}</span>\r\n              </div>\r\n            </div>\r\n            <div class=\"contact-note\">\r\n              <p>欢迎随时来电咨询</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </transition>\r\n    </div>\r\n\r\n    <!-- 移动端布局 -->\r\n    <div v-else class=\"mobile-layout\">\r\n\r\n      <div class=\"mobile-banner\"\r\n           @touchstart=\"handleTouchStart\"\r\n           @touchmove=\"handleTouchMove\"\r\n           @touchend=\"handleTouchEnd\">\r\n        <div class=\"mobile-banner-slider\" :style=\"{ transform: `translateX(${-mobileCurrentSlide * 100}%)` }\">\r\n          <div class=\"mobile-slide\" v-for=\"(item, index) in bannerImages\" :key=\"index\">\r\n            <div class=\"mobile-slide-inner\">\r\n              <img :src=\"item.img\" alt=\"\" class=\"mobile-slide-img\">\r\n              <div class=\"mobile-banner-content\" :class=\"'pos-' + item.content.position\">\r\n                <h2 class=\"mobile-banner-title\" v-html=\"item.content.title\"></h2>\r\n                <p class=\"mobile-banner-text\">{{ item.content.text }}</p>\r\n                <div class=\"mobile-banner-actions\">\r\n                  <a v-if=\"!isLogin && item.content.secondaryLink\"\r\n                     href=\"#\"\r\n                     @click.prevent=\"navigateTo(item.content.secondaryLink)\"\r\n                     class=\"mobile-banner-button secondary-btn\">\r\n                    {{ item.content.secondaryBtnText }}\r\n                  </a>\r\n                  <a v-if=\"!isLogin && item.content.primaryLink\"\r\n                     href=\"#\"\r\n                     @click.prevent=\"navigateTo(item.content.primaryLink)\"\r\n                     class=\"mobile-banner-button primary-btn\">\r\n                    {{ item.content.primaryBtnText }}\r\n                  </a>\r\n                  <a v-if=\"item.content.thirdLink\"\r\n                     href=\"#\"\r\n                     @click.prevent=\"navigateTo(item.content.thirdLink)\"\r\n                     class=\"banner-button secondary-btn\">\r\n                    {{ item.content.thirdBtnText }}\r\n                  </a>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"mobile-banner-pagination\">\r\n    <span v-for=\"(item, index) in bannerImages\"\r\n          :key=\"index\"\r\n          :class=\"{ active: mobileCurrentSlide === index }\"\r\n          @click=\"goToSlide(index)\"></span>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 移动端 GPU 推荐 -->\r\n      <section class=\"mobile-section mobile-gpu-section\">\r\n        <div class=\"mobile-section-header\">\r\n          <h2 class=\"mobile-section-title\">为您推荐</h2>\r\n          <p class=\"mobile-section-description\">\r\n            专注于提供高性能、稳定可靠的 GPU 算力服务\r\n          </p>\r\n        </div>\r\n\r\n        <div class=\"mobile-gpu-list\">\r\n          <div class=\"mobile-gpu-card\"\r\n               v-for=\"(gpu, index) in gpus\"\r\n               :key=\"index\"\r\n               :class=\"{ 'recommended': gpu.recommended }\"\r\n               @click=\"navigateTo('/product')\">\r\n            <div class=\"mobile-gpu-header\">\r\n              <h3 class=\"mobile-gpu-name\">{{ gpu.name }}</h3>\r\n              <div class=\"mobile-gpu-tags\">\r\n                <span v-if=\"gpu.recommended\" class=\"mobile-recommend-tag\">推荐</span>\r\n                <span v-if=\"gpu.isNew\" class=\"mobile-new-tag\">NEW</span>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"mobile-gpu-specs\">\r\n              <div class=\"mobile-spec-item\">\r\n                <span class=\"mobile-spec-label\">单精度:</span>\r\n                <span class=\"mobile-spec-value\">{{ gpu.singlePrecision }} TFLOPS</span>\r\n              </div>\r\n              <div class=\"mobile-spec-item\">\r\n                <span class=\"mobile-spec-label\">半精度:</span>\r\n                <span class=\"mobile-spec-value\">{{ gpu.halfPrecision }} Tensor TFL</span>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"mobile-gpu-price\">\r\n              <span class=\"mobile-original-price\" v-if=\"gpu.originalPrice\">¥{{ gpu.originalPrice }}/时</span>\r\n              <span class=\"mobile-current-price\">¥{{ gpu.price }}/时</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      <!-- 移动端 GPU 对比 -->\r\n      <section class=\"mobile-section mobile-comparison-section\">\r\n        <div class=\"mobile-section-header\">\r\n          <h2 class=\"mobile-section-title\">GPU性能对比</h2>\r\n          <p class=\"mobile-section-description\">\r\n            专业GPU性能详细对比\r\n          </p>\r\n        </div>\r\n\r\n        <div class=\"mobile-comparison-container\">\r\n          <div class=\"mobile-comparison-scroll\">\r\n            <table class=\"mobile-comparison-table\">\r\n              <thead>\r\n              <tr>\r\n                <th>GPU型号</th>\r\n                <th v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.name }}</th>\r\n              </tr>\r\n              </thead>\r\n              <tbody>\r\n              <tr>\r\n                <td>架构</td>\r\n                <td v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.architecture }}</td>\r\n              </tr>\r\n              <tr>\r\n                <td>FP16性能</td>\r\n                <td v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.fp16Performance }}</td>\r\n              </tr>\r\n              <tr>\r\n                <td>FP32性能</td>\r\n                <td v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.fp32Performance }}</td>\r\n              </tr>\r\n              <tr>\r\n                <td>显存</td>\r\n                <td v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.memory }}</td>\r\n              </tr>\r\n              <tr>\r\n                <td>显存类型</td>\r\n                <td v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.memoryType }}</td>\r\n              </tr>\r\n              <tr>\r\n                <td>带宽</td>\r\n                <td v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.bandwidth }}</td>\r\n              </tr>\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      <!-- 移动端 核心优势 -->\r\n      <section class=\"mobile-section mobile-services-section\">\r\n        <div class=\"mobile-section-header\">\r\n          <h2 class=\"mobile-section-title\">核心优势</h2>\r\n          <p class=\"mobile-section-description\">\r\n            专业铸造优秀,天工开物企业AI变革路上的好伙伴\r\n          </p>\r\n        </div>\r\n\r\n        <div class=\"mobile-services-list\">\r\n          <div class=\"mobile-service-card\" v-for=\"(service, index) in serviceList\" :key=\"index\">\r\n            <div class=\"mobile-service-icon\">\r\n              <i :class=\"service.icon\"></i>\r\n            </div>\r\n            <h3 class=\"mobile-service-title\">{{ service.title }}</h3>\r\n            <p class=\"mobile-service-desc\">{{ service.desc }}</p>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      <!-- 移动端 行业应用 -->\r\n      <section class=\"mobile-section mobile-applications-section\">\r\n        <div class=\"mobile-section-header\">\r\n          <h2 class=\"mobile-section-title\">行业应用</h2>\r\n          <p class=\"mobile-section-description\">\r\n            Applications\r\n          </p>\r\n        </div>\r\n\r\n        <div class=\"mobile-applications-grid\">\r\n          <div class=\"mobile-app-item\" v-for=\"(app, index) in mobileApplications\" :key=\"index\">\r\n            <div class=\"mobile-app-image\">\r\n              <img :src=\"app.image\" :alt=\"app.title\">\r\n            </div>\r\n            <h3 class=\"mobile-app-title\">{{ app.title }}</h3>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      <!-- 移动端 咨询按钮 -->\r\n      <div class=\"mobile-consult-section\">\r\n        <h3 class=\"mobile-consult-title\">为AI+千行百业，提供高性能算力服务</h3>\r\n        <button class=\"mobile-consult-button\" @click=\"openContactModal\">立即咨询</button>\r\n      </div>\r\n\r\n      <!-- 移动端 联系弹窗 -->\r\n      <transition name=\"mobile-fade\">\r\n        <div v-if=\"showContactModal\" class=\"mobile-contact-overlay\" @click.self=\"closeContactModal\">\r\n          <div class=\"mobile-contact-modal\">\r\n            <button class=\"mobile-close-modal\" @click=\"closeContactModal\">\r\n              &times;\r\n            </button>\r\n            <div class=\"mobile-contact-content\">\r\n              <div class=\"mobile-contact-item\">\r\n                <i class=\"am-icon-user\"></i>\r\n                <span>{{ contactInfo.name }}</span>\r\n              </div>\r\n              <div class=\"mobile-contact-item\">\r\n                <i class=\"am-icon-phone\"></i>\r\n                <span>{{ contactInfo.phone }}</span>\r\n              </div>\r\n            </div>\r\n            <div class=\"mobile-contact-note\">\r\n              <p>欢迎随时来电咨询</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </transition>\r\n    </div>\r\n    <Mider v-if=\"!isMobile\"></Mider>\r\n    <Footer></Footer>\r\n    <chatAi></chatAi>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// import Layout from \"@/components/common/Layout\";\r\nimport chatAi from \"@/components/common/mider/chatAi\";\r\nimport Mider from \"@/components/common/mider/Mider\";\r\nimport Footer from \"@/components/common/footer/Footer\";\r\nimport GpuComparison from \"@/views/Index/GpuComparison\";\r\nimport {postAnyData, getNotAuth, postNotAuth,getAnyData,postJsonData} from \"@/api/login\";\r\n\r\n\r\nimport {setToken} from \"@/utils/auth\";\r\n\r\nexport default {\r\n  name: \"IndexView\",\r\n  components: { chatAi,Footer, Mider, GpuComparison },\r\n  computed: {\r\n    translate() {\r\n      return -this.translateX * 100 + \"%\";\r\n    },\r\n    isLogin() {\r\n      return !! document.cookie.includes('Admin-Token');\r\n    },\r\n    mobileTranslateX() {\r\n      return -this.mobileCurrentSlide * 100;\r\n    },\r\n    mobileApplications() {\r\n      return [\r\n        this.firstRowWide,\r\n        ...this.firstRowTallApps,\r\n        ...this.secondRowApps,\r\n        ...this.thirdRowSmallApps,\r\n        this.thirdRowWide\r\n      ];\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      touchStartX: 0,\r\n      touchEndX: 0,\r\n      touchThreshold: 50, // 滑动阈值，单位px\r\n      isMobile: false,\r\n      mobileCurrentSlide: 0,\r\n      showContactModal: false,\r\n      contactInfo: {\r\n        name: '王先生',\r\n        phone: '13913283376'\r\n      },\r\n      tabIndex: 0,\r\n      firstRowWide: {\r\n        title: '工业制造',\r\n        image: require(\"../../assets/images/index/gongyezhizao.webp\"),\r\n        hover: false,\r\n      },\r\n      firstRowTallApps: [\r\n        {\r\n          title: '自动驾驶',\r\n          image: require(\"../../assets/images/index/zidongjiashi.webp\"),\r\n          hover: false,\r\n        },\r\n        {\r\n          title: '智能交通',\r\n          image: require(\"../../assets/images/index/zhinengjiaotong.webp\"),\r\n          hover: false,\r\n        }\r\n      ],\r\n      secondRowApps: [\r\n        {\r\n          title: '智慧农业',\r\n          image: require(\"../../assets/images/index/zhihuinongye.webp\"),\r\n          hover: false,\r\n        },\r\n        {\r\n          title: '影视渲染',\r\n          image: require(\"../../assets/images/index/yingshixuanran.webp\"),\r\n          hover: false,\r\n        }\r\n      ],\r\n      thirdRowSmallApps: [\r\n        {\r\n          title: '医疗影像',\r\n          image: require(\"../../assets/images/index/yiliaoyingxiang.webp\"),\r\n          hover: false,\r\n        },\r\n        {\r\n          title: '金融风暴',\r\n          image: require(\"../../assets/images/index/jinrongfengbao.webp\"),\r\n          hover: false,\r\n        }\r\n      ],\r\n      thirdRowWide: {\r\n        title: '能源科技',\r\n        image: require(\"../../assets/images/index/nengyuankeji.webp\"),\r\n        hover: false,\r\n      },\r\n      bannerImages: [\r\n        {\r\n          img: require(\"/public/images/back1.webp\"),\r\n          content: {\r\n            title: \"天工开物\",\r\n            text: \"构建AI应用周期服务的一站式算力云\",\r\n            position: \"left\",\r\n            thirdLink: \"/product\",\r\n            thirdBtnText: \"立即购买\"\r\n          }\r\n        },\r\n        {\r\n          img: require(\"/public/images/back2.webp\"),\r\n          content: {\r\n            title: \"专业AI训练平台\",\r\n            text: \"为AI+千行百业，提供高性能算力服务\",\r\n            position: \"left\",\r\n            secondaryLink: \"/login\",\r\n            secondaryBtnText: \"立即登录\",\r\n            primaryLink: '/register',\r\n            primaryBtnText: \"立即注册\"\r\n          }\r\n        },\r\n        {\r\n          img: require(\"/public/images/back3.webp\"),\r\n          content: {\r\n            title: \"企业级GPU集群\",\r\n            text: \"H100/H800/RTX 4090等高性能GPU随时可用，按需付费\",\r\n            position: \"left\",\r\n          }\r\n        }\r\n      ],\r\n      translateX: 0,\r\n      tsion: true,\r\n      tabList: [],\r\n      swiperOptions: {\r\n        loop: true,\r\n        autoplay: {\r\n          delay: 5000,\r\n          disableOnInteraction: false,\r\n        },\r\n        pagination: {\r\n          el: '.swiper-pagination',\r\n          clickable: true\r\n        },\r\n        navigation: {\r\n          nextEl: '.swiper-button-next',\r\n          prevEl: '.swiper-button-prev'\r\n        }\r\n      },\r\n      serviceList: [\r\n        { id: 1, icon: 'am-icon-shield', title: '数据安全', desc: '平台采取各种措施保证用户的数据安全，例如数据加密、防火墙、漏洞扫描和安全审计等措施，以防止数据泄露、篡改和丢失等风险。' },\r\n        { id: 2, icon: 'am-icon-sliders', title: '部署灵活', desc: '租用GPU服务器具备比购买更高的灵活性，用户可以根据自己的需求随时调整租赁资源的配置及数量。' },\r\n        { id: 3, icon: 'am-icon-server', title: '高可靠性', desc: '平台拥有完善的运维体系，采用丰富的数据备份、冗余技术以及定期检测等机制，保证租赁服务器的稳定性、易用性和安全性。' },\r\n        { id: 4, icon: 'am-icon-rocket', title: '高处理性能', desc: '采用先进的GPU集群架构，平台能够大大提高计算速度和处理能力，可以在科学计算、深度学习等领域有更优秀的表现。' },\r\n        { id: 5, icon: 'am-icon-credit-card', title: '低成本', desc: '购买GPU服务器需要投入较高的资金，而租赁可以让用户以较低的成本去获得相关基础设施，减轻了用户在预算上的负担。' },\r\n        { id: 6, icon: 'am-icon-phone', title: '及时服务', desc: '提供365天 7*24小时技术支持及运维服务， 工程师现场及在线响应及电话支持。' }\r\n      ],\r\n      comparisonGpus: [\r\n        {\r\n          name: \"A100\",\r\n          architecture: \"Ampere\",\r\n          fp16Performance: \"312 TFLOPS\",\r\n          fp32Performance: \"19.5 TFLOPS\",\r\n          memory: \"80 GB\",\r\n          memoryType: \"HBM2\",\r\n          bandwidth: \"2,039 GB/s\"\r\n        },\r\n        {\r\n          name: \"A100\",\r\n          architecture: \"Ampere\",\r\n          fp16Performance: \"312 TFLOPS\",\r\n          fp32Performance: \"19.5 TFLOPS\",\r\n          memory: \"80 GB\",\r\n          memoryType: \"HBM2\",\r\n          bandwidth: \"2,039 GB/s\"\r\n        },\r\n        {\r\n          name: \"A100\",\r\n          architecture: \"Ampere\",\r\n          fp16Performance: \"312 TFLOPS\",\r\n          fp32Performance: \"19.5 TFLOPS\",\r\n          memory: \"80 GB\",\r\n          memoryType: \"HBM2\",\r\n          bandwidth: \"2,039 GB/s\"\r\n        },\r\n        {\r\n          name: \"V100\",\r\n          architecture: \"Volta\",\r\n          fp16Performance: \"125 TFLOPS\",\r\n          fp32Performance: \"15.7 TFLOPS\",\r\n          memory: \"32 GB\",\r\n          memoryType: \"HBM2\",\r\n          bandwidth: \"900 GB/s\"\r\n        },\r\n        {\r\n          name: \"A6000\",\r\n          architecture: \"Ampere\",\r\n          fp16Performance: \"77.4 TFLOPS\",\r\n          fp32Performance: \"38.7 TFLOPS\",\r\n          memory: \"48 GB\",\r\n          memoryType: \"GDDR6\",\r\n          bandwidth: \"768 GB/s\"\r\n        },\r\n        {\r\n          name: \"A5000\",\r\n          architecture: \"Ampere\",\r\n          fp16Performance: \"54.2 TFLOPS\",\r\n          fp32Performance: \"27.8 TFLOPS\",\r\n          memory: \"24 GB\",\r\n          memoryType: \"GDDR6\",\r\n          bandwidth: \"768 GB/s\"\r\n        },\r\n        {\r\n          name: \"A4000\",\r\n          architecture: \"Ampere\",\r\n          fp16Performance: \"19.17 TFLOPS\",\r\n          fp32Performance: \"19.17 TFLOPS\",\r\n          memory: \"16 GB\",\r\n          memoryType: \"GDDR6\",\r\n          bandwidth: \"448 GB/s\"\r\n        }\r\n      ],\r\n      gpus: [\r\n        {\r\n          name: \"A100 SXM4 / 80GB\",\r\n          singlePrecision: \"156\",\r\n          halfPrecision: \"19.5\",\r\n          originalPrice: \"11.10\",\r\n          price: \"6.66\",\r\n          recommended: true,\r\n          isNew: false\r\n        },\r\n        {\r\n          name: \"RTX 3090 / 24GB\",\r\n          singlePrecision: \"35.58\",\r\n          halfPrecision: \"71.2\",\r\n          originalPrice: \"2.30\",\r\n          price: \"1.38\",\r\n          recommended: true,\r\n          isNew: false\r\n        },\r\n        {\r\n          name: \"A100 PCIE / 80GB\",\r\n          singlePrecision: \"156\",\r\n          halfPrecision: \"19.5\",\r\n          originalPrice: \"11.10\",\r\n          price: \"6.66\",\r\n          recommended: true,\r\n          isNew: false\r\n        },\r\n        {\r\n          name: \"RTX 4090 / 24GB\",\r\n          singlePrecision: \"82.58\",\r\n          halfPrecision: \"164.5\",\r\n          originalPrice: \"2.97\",\r\n          price: \"1.78\",\r\n          recommended: false,\r\n          isNew: true\r\n        },\r\n        {\r\n          name: \"RTX 4090D / 24GB\",\r\n          singlePrecision: \"73.54\",\r\n          halfPrecision: \"147.1\",\r\n          originalPrice: \"2.93\",\r\n          price: \"1.76\",\r\n          recommended: false,\r\n          isNew: false\r\n        },\r\n        {\r\n          name: \"RTX 3060 / 12GB\",\r\n          singlePrecision: \"12.7\",\r\n          halfPrecision: \"51.2\",\r\n          originalPrice: \"1.00\",\r\n          price: \"0.60\",\r\n          recommended: false,\r\n          isNew: false\r\n        },\r\n        {\r\n          name: \"RTX A4000 / 16GB\",\r\n          singlePrecision: \"19.17\",\r\n          halfPrecision: \"76.7\",\r\n          originalPrice: \"1.53\",\r\n          price: \"0.92\",\r\n          recommended: false,\r\n          isNew: false\r\n        },\r\n        {\r\n          name: \"Tesla P40 / 24GB\",\r\n          singlePrecision: \"5.9\",\r\n          halfPrecision: \"11.76\",\r\n          originalPrice: \"1.35\",\r\n          price: \"0.81\",\r\n          recommended: false,\r\n          isNew: false\r\n        },\r\n\r\n      ],\r\n      activeIndex: 0,\r\n    };\r\n  },\r\n  created() {\r\n    this.fetchRecommendations();\r\n    const url = new URL(window.location.href);\r\n    const token = url.searchParams.get('token');\r\n\r\n    if (token) {\r\n      const hasRefreshed = localStorage.getItem('hasRefreshedWithToken');\r\n      // console.log('需要修改的参数为', url.origin + url.pathname)\r\n      if (!hasRefreshed) {\r\n        setToken(token);\r\n        localStorage.setItem('hasRefreshedWithToken', 'true');\r\n        // 直接修改 window.location（移除所有查询参数）\r\n        // console.log('需要修改的参数为', url.origin + url.pathname)\r\n\r\n      } else {\r\n        window.location.href = url.origin + url.pathname;\r\n        localStorage.setItem('hasRefreshedWithToken', 'false');\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.checkIsMobile();\r\n    window.addEventListener('resize', this.checkIsMobile);\r\n\r\n    // Banner auto-play\r\n    this.desktopInterval = setInterval(() => {\r\n      this.next();\r\n    }, 5000);\r\n\r\n    this.mobileInterval = setInterval(() => {\r\n      this.mobileNext();\r\n    }, 5000);\r\n  },\r\n  beforeDestroy() {\r\n    window.removeEventListener('resize', this.checkIsMobile);\r\n    clearInterval(this.desktopInterval);\r\n    clearInterval(this.mobileInterval);\r\n\r\n  },\r\n  methods: {\r\n\r\n    async fetchRecommendations() {\r\n      try {\r\n        getNotAuth(\"/system/recommend/list\").then(req =>{\r\n          this.gpus = req.data.rows.map(item => ({\r\n            ...item,\r\n            isNew: item.isnew === 0,\r\n            recommended: item.recommended === 0\r\n          }))\r\n          // this.gpus = req.data.rows\r\n        })\r\n      } catch (error) {\r\n        console.error('获取GPU推荐列表失败:', error);\r\n      }\r\n    },\r\n\r\n    handleTouchStart(e) {\r\n      this.touchStartX = e.touches[0].clientX;\r\n    },\r\n\r\n    handleTouchMove(e) {\r\n      this.touchEndX = e.touches[0].clientX;\r\n    },\r\n\r\n    handleTouchEnd() {\r\n      if (!this.touchStartX || !this.touchEndX) return;\r\n\r\n      const diff = this.touchStartX - this.touchEndX;\r\n\r\n      // 向右滑动\r\n      if (diff > this.touchThreshold) {\r\n        this.mobileNext();\r\n      }\r\n\r\n      // 向左滑动\r\n      if (diff < -this.touchThreshold) {\r\n        this.mobilePrev();\r\n      }\r\n\r\n      // 重置触摸位置\r\n      this.touchStartX = 0;\r\n      this.touchEndX = 0;\r\n    },\r\n\r\n    mobilePrev() {\r\n      this.mobileCurrentSlide = (this.mobileCurrentSlide - 1 + this.bannerImages.length) % this.bannerImages.length;\r\n      this.resetMobileInterval();\r\n    },\r\n\r\n    mobileNext() {\r\n      this.mobileCurrentSlide = (this.mobileCurrentSlide + 1) % this.bannerImages.length;\r\n      this.resetMobileInterval();\r\n    },\r\n\r\n    resetMobileInterval() {\r\n      clearInterval(this.mobileInterval);\r\n      this.mobileInterval = setInterval(() => {\r\n        this.mobileNext();\r\n      }, 5000);\r\n    },\r\n    checkIsMobile() {\r\n      this.isMobile = window.innerWidth <= 768;\r\n    },\r\n    openContactModal() {\r\n      this.showContactModal = true;\r\n    },\r\n    closeContactModal() {\r\n      this.showContactModal = false;\r\n    },\r\n    handleBannerAction(item) {\r\n      if (item.content.link) {\r\n        this.$router.push(item.content.link);\r\n      }\r\n      this.$ga.event('Banner', 'click', item.content.title);\r\n    },\r\n    navigateTo(path) {\r\n      if (this.currentPath && this.currentPath !== path) {\r\n        this.previousActivePath = this.currentPath;\r\n\r\n        this.$nextTick(() => {\r\n          const navLinks = document.querySelectorAll('.nav-link, .btn-login');\r\n          navLinks.forEach(link => {\r\n            if ((link.classList.contains('active') ||\r\n                    (path === '/login' && link.classList.contains('btn-login'))) &&\r\n                !link.classList.contains('active-exit')) {\r\n              link.classList.add('active-exit');\r\n\r\n              setTimeout(() => {\r\n                link.classList.remove('active-exit');\r\n              }, 300);\r\n            }\r\n          });\r\n\r\n          this.currentPath = path;\r\n        });\r\n      } else {\r\n        this.currentPath = path;\r\n      }\r\n\r\n      if (this.$route.path === path) {\r\n        this.$nextTick(() => {\r\n          window.scrollTo({\r\n            top: 0,\r\n            behavior: 'instant'\r\n          });\r\n          this.$router.go(0);\r\n        });\r\n      } else {\r\n        this.$router.push(path);\r\n        window.scrollTo({\r\n          top: 0,\r\n          behavior: 'instant'\r\n        });\r\n      }\r\n    },\r\n    last() {\r\n      this.translateX = (this.translateX - 1 + this.bannerImages.length) % this.bannerImages.length;\r\n      this.tsion = true;\r\n    },\r\n    next() {\r\n      this.translateX = (this.translateX + 1) % this.bannerImages.length;\r\n      this.tsion = true;\r\n    },\r\n\r\n    goToSlide(index) {\r\n      this.mobileCurrentSlide = index;\r\n      // 重置自动播放计时器\r\n      clearInterval(this.mobileInterval);\r\n      this.mobileInterval = setInterval(() => {\r\n        this.mobileNext();\r\n      }, 5000);\r\n    },\r\n    changeTab(index) {\r\n      this.tabIndex = index;\r\n    },\r\n    useGpu(gpu) {\r\n      console.log(`Selected GPU: ${gpu.name}`);\r\n    },\r\n    getCustomSolution() {\r\n      console.log('Get Custom Solution');\r\n    },\r\n    contactUs() {\r\n      console.log('Contact Us');\r\n    },\r\n    prevSlide() {\r\n      this.activeIndex = (this.activeIndex > 0) ? this.activeIndex - 1 : this.slideshow.length - 1;\r\n    },\r\n    nextSlide() {\r\n      this.activeIndex = (this.activeIndex < this.slideshow.length - 1) ? this.activeIndex + 1 : 0;\r\n    },\r\n    selectGpu(gpu) {\r\n      console.log(`Selected GPU: ${gpu.name}`);\r\n    },\r\n  },\r\n  watch: {\r\n    translateX(newVal) {\r\n      const dots = this.$refs.swiperPagination?.querySelectorAll(\"span\");\r\n      if (dots) {\r\n        dots.forEach((dot, index) => {\r\n          dot.classList.toggle(\"active\", index === newVal);\r\n        });\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 通用样式 */\r\n.section-header {\r\n  text-align: center;\r\n  margin-bottom: 40px;\r\n}\r\n\r\n.section-title {\r\n  font-size: 28px;\r\n  color: #333;\r\n  position: relative;\r\n  display: inline-block;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.section-title::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: -5px;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  width: 50px;\r\n  height: 3px;\r\n  background-color: #2196f3;\r\n}\r\n\r\n.section-description {\r\n  color: #666;\r\n  max-width: 600px;\r\n  margin: 0 auto;\r\n  line-height: 1.6;\r\n}\r\n\r\n/* 电脑端样式 */\r\n.desktop-layout {\r\n  display: block;\r\n}\r\n\r\n/* Banner Section */\r\n.banner-section {\r\n  padding: 0;\r\n}\r\n\r\n.banner-container {\r\n  width: 100%;\r\n  max-width: 2560px;\r\n  max-height: 1920px;\r\n  margin: 0;\r\n  position: relative;\r\n}\r\n\r\n.big-box {\r\n  width: 100%;\r\n  height: 93vh;\r\n  overflow: hidden;\r\n  position: relative;\r\n  box-shadow: 0 4px 20px rgba(0,0,0,0.15);\r\n}\r\n\r\n.img-box {\r\n  height: 100%;\r\n  width: 100%;\r\n}\r\n\r\n.show-box {\r\n  display: flex;\r\n  height: 100%;\r\n  width: 100%;\r\n  transition: all 0.5s;\r\n}\r\n\r\n.slide-item {\r\n  position: relative;\r\n  flex-shrink: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.slide-item img {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n}\r\n\r\n.banner-content {\r\n  position: absolute;\r\n  top: 40%;\r\n  background-color: rgba(0, 0, 0, 0);\r\n  padding: 40px;\r\n  font-weight: 500;\r\n  box-shadow: 0 5px 20px rgba(0, 0, 0, 0);\r\n  max-width: 800px;\r\n  color: #fdfcfc;\r\n  z-index: 4;\r\n}\r\n\r\n.banner-title {\r\n  font-size: 7vh;\r\n  line-height: 1;\r\n  margin-bottom: 0px;\r\n  font-weight: 700;\r\n}\r\n\r\n.banner-text {\r\n  font-size: 3vh;\r\n  line-height: 3.5;\r\n  margin-bottom: 0px;\r\n  opacity: 0.9;\r\n}\r\n\r\n.banner-actions {\r\n  display: flex;\r\n  gap: 20px;\r\n  z-index: 20;\r\n}\r\n\r\n.banner-button {\r\n  padding: 10px 5px;\r\n  border-radius: 0px;\r\n  font-size: 1.5rem;\r\n  font-weight:200;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 4px 10px rgba(248, 245, 245, 0.2);\r\n  text-decoration: none;\r\n  text-align: center;\r\n  display: inline-block;\r\n  background-color: transparent;\r\n  border: 2px solid white;\r\n  color: white;\r\n  position: relative;\r\n  z-index: 3;\r\n  pointer-events: auto;\r\n  width: 150px;\r\n}\r\n\r\n.banner-button:hover {\r\n  background-color: black;\r\n}\r\n\r\n.primary-btn {\r\n  color: white;\r\n  border: 1px solid white;\r\n}\r\n\r\n.primary-btn:hover {\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.secondary-btn {\r\n  color: white;\r\n  background-color: #f67b3d;\r\n  border-color: #f67b3d;\r\n}\r\n\r\n.secondary-btn:hover {\r\n  background-color: #f67b3d;\r\n}\r\n\r\n.pos-left {\r\n  left: 8%;\r\n  transform: translateY(-50%);\r\n  text-align: left;\r\n  animation: slideInFromLeft 0.8s ease;\r\n}\r\n\r\n.pos-right {\r\n  right: 10%;\r\n  transform: translateY(-50%);\r\n  text-align: left;\r\n  animation: slideInFromRight 0.8s ease;\r\n}\r\n\r\n.pos-center {\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  text-align: center;\r\n  animation: fadeIn 0.8s ease;\r\n}\r\n\r\n.arrowhead-box {\r\n  position: absolute;\r\n  top: 50%;\r\n  width: 100%;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  transform: translateY(-50%);\r\n  padding: 0 20px;\r\n  z-index: 10;\r\n  pointer-events: none;\r\n}\r\n\r\n.nav-arrow {\r\n  display: flex;\r\n  pointer-events: auto;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 50px;\r\n  height: 50px;\r\n  border-radius: 50%;\r\n  background: rgba(0,0,0,0.3);\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.nav-arrow:hover {\r\n  background: rgba(0,0,0,0.5);\r\n}\r\n\r\n.arrow-icon {\r\n  height: 24px;\r\n  width: 24px;\r\n  transition: transform 0.3s;\r\n  filter: invert(1);\r\n}\r\n\r\n.rotated {\r\n  transform: rotate(180deg);\r\n}\r\n\r\n.swiper-pagination {\r\n  position: absolute;\r\n  bottom: 20px;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  display: flex;\r\n  gap: 10px;\r\n  z-index: 3;\r\n}\r\n\r\n.swiper-pagination span {\r\n  width: 12px;\r\n  height: 12px;\r\n  background: rgba(255,255,255,0.5);\r\n  border-radius: 50%;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.swiper-pagination span.active {\r\n  width: 30px;\r\n  border-radius: 6px;\r\n  background: #fff;\r\n}\r\n\r\n/* GPU Section */\r\n.gpu-specs-pricing {\r\n  display: flex;\r\n  height: 8vh;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 0px;\r\n}\r\n\r\n.specs-section {\r\n  flex: 1;\r\n}\r\n\r\n.price-section {\r\n  text-align: right;\r\n  min-width: 120px;\r\n}\r\n\r\n.gpu-pricing {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-end;\r\n}\r\n\r\n.original-price {\r\n  text-decoration: line-through;\r\n  color: #999;\r\n  font-size: 14px;\r\n}\r\n\r\n.current-price {\r\n  font-size: 16px;\r\n  color: #333;\r\n}\r\n\r\n.price-value {\r\n  font-size: 24px;\r\n  font-weight: 700;\r\n  color: #2196f3;\r\n}\r\n\r\n/* GPU Card Grid */\r\n.gpu-section {\r\n  background-color: #f9f9f9;\r\n  width: 100%;\r\n  max-width: 2560px;\r\n  max-height: 800px;\r\n  margin: 0 auto;\r\n  /*margin-bottom: -10vh;*/\r\n\r\n}\r\n\r\n.gpu-card-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(3, 1fr);\r\n  gap: 20px;\r\n  padding: 0 50px;\r\n  margin-bottom: 40px;\r\n}\r\n\r\n.gpu-card {\r\n  background-color: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);\r\n  padding: 20px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  position: relative;\r\n  transition: all 0.3s ease;\r\n  border: 1px solid transparent;\r\n}\r\n\r\n.gpu-card:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);\r\n  border: 1px solid #2196f3;\r\n}\r\n\r\n.gpu-card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.gpu-name {\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-left: 0px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.recommendation-tag {\r\n  background-color: #2196f3;\r\n  color: white;\r\n  font-size: 12px;\r\n  padding: 3px 8px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.new-tag {\r\n  background-color: #ff4d4f;\r\n  color: white;\r\n  font-size: 12px;\r\n  padding: 3px 8px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.spec-item {\r\n  display: flex;\r\n  margin-bottom: 8px;\r\n  font-size: 14px;\r\n}\r\n\r\n.spec-label {\r\n  color: #666;\r\n  margin-right: 5px;\r\n  flex-shrink: 0;\r\n  width: 60px;\r\n}\r\n\r\n.spec-value {\r\n  color: #333;\r\n  font-weight: 500;\r\n}\r\n\r\n/* GPU 性能对比 Section */\r\n.gpu-comparison-section {\r\n  background-color: #f9f9f9;\r\n  width: 100%;\r\n  max-width: 2560px;\r\n  margin: 0 auto;\r\n  margin-bottom: -12vh;\r\n}\r\n\r\n.gpu-comparison-table {\r\n  width: 100%;\r\n  padding: 0px 50px;\r\n  border-collapse: collapse;\r\n  margin: 0;\r\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);\r\n  border-radius: 10px;\r\n  overflow:hidden;\r\n}\r\n\r\n.gpu-comparison-table thead {\r\n  background-color: #cddfec;\r\n}\r\n\r\n.gpu-comparison-table thead tr th {\r\n  padding: 15px 20px;\r\n  text-align: center;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  font-size: 16px;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.gpu-comparison-table tbody tr td {\r\n  padding: 15px 20px;\r\n  text-align: center;\r\n  border-bottom: 1px solid #e0e5eb;\r\n  transition: background-color 0.3s ease;\r\n  font-size: 15px;\r\n  color: #333;\r\n}\r\n\r\n.gpu-comparison-table tbody tr:nth-child(even) {\r\n  background-color: #f9fafc;\r\n}\r\n\r\n.gpu-comparison-table tbody tr:hover {\r\n  background-color: #f1f6fd;\r\n}\r\n\r\n/* Services Section */\r\n.services-section {\r\n  background-color: #f9f9f9;\r\n  width: 100%;\r\n  max-width: 2560px;\r\n}\r\n\r\n.services-grid {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  padding: 0px 50px;\r\n}\r\n\r\n.service-item {\r\n  width: 33.33%;\r\n  padding: 0 11px;\r\n}\r\n\r\n.service-item:nth-child(-n+3) {\r\n  margin-bottom: 40px;\r\n}\r\n\r\n.service-card {\r\n  background-color: #fff;\r\n  border-radius: 10px;\r\n  box-shadow: 0 5px 15px rgba(0,0,0,0.05);\r\n  padding: 25px;\r\n  height: 100%;\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  text-align: left;\r\n}\r\n\r\n.service-card:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: 0 10px 25px rgba(0,0,0,0.1);\r\n}\r\n\r\n.service-icon {\r\n  font-size: 48px;\r\n  margin-bottom: -30px;\r\n  color: #2196f3;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.service-card:hover .service-icon {\r\n  transform: scale(1.1);\r\n}\r\n\r\n.service-title {\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.service-text {\r\n  font-size: 14px;\r\n  color: #666;\r\n  line-height: 2;\r\n}\r\n\r\n/* Application Areas Section */\r\n.appsec-section {\r\n  background-color: #f8f9fa;\r\n  max-width: 2560px;\r\n  max-height: 1000px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.appsec-container {\r\n  margin: 0 auto;\r\n  max-height: 1000px;\r\n  width: 100%;\r\n  padding: 0 50px;\r\n}\r\n\r\n.appsec-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(4, 1fr);\r\n  grid-auto-rows: minmax(180px, auto);\r\n  gap: 20px;\r\n}\r\n\r\n.appsec-item {\r\n  position: relative;\r\n  overflow: hidden;\r\n  border-radius: 4px;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.appsec-wide {\r\n  grid-column: span 2;\r\n  grid-row: span 1;\r\n  height: 180px;\r\n}\r\n\r\n.appsec-tall {\r\n  grid-column: span 1;\r\n  grid-row: span 2;\r\n  height: 380px;\r\n}\r\n\r\n.appsec-small {\r\n  grid-column: span 1;\r\n  grid-row: span 1;\r\n  height: 180px;\r\n}\r\n\r\n.appsec-card {\r\n  position: relative;\r\n  height: 100%;\r\n  width: 100%;\r\n  cursor: pointer;\r\n  overflow: hidden;\r\n}\r\n\r\n.appsec-image {\r\n  height: 100%;\r\n  width: 100%;\r\n  transition: transform 0.5s ease;\r\n}\r\n\r\n.appsec-image img {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  transition: transform 0.5s ease;\r\n}\r\n\r\n.appsec-cardtitle {\r\n  position: absolute;\r\n  bottom: 20px;\r\n  left: 20px;\r\n  color: white;\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6);\r\n  z-index: 2;\r\n  transition: transform 0.3s ease, font-size 0.3s ease;\r\n}\r\n\r\n.appsec-card::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 50%;\r\n  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);\r\n  z-index: 1;\r\n}\r\n\r\n.appsec-hover {\r\n  transform: scale(1.05);\r\n}\r\n\r\n/* Recommendation Section */\r\n.recommendation-tag1 {\r\n  margin-top: 3vh;\r\n  position: relative;\r\n  width: 100%;\r\n  height: 120px;\r\n  background-image: url(\"../../assets/images/index/back3.png\");\r\n  overflow: hidden;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.card1 {\r\n  position: relative;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  width: 100%;\r\n  max-width: 1200px;\r\n  padding: 0 20px;\r\n  z-index: 2;\r\n}\r\n\r\n.banner-text1 {\r\n  color: white;\r\n  font-size: 2rem;\r\n  font-weight: bold;\r\n  margin: 0;\r\n  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.consult-button1 {\r\n  background-color: white;\r\n  color: #0d47a1;\r\n  border: none;\r\n  border-radius: 25px;\r\n  padding: 10px 25px;\r\n  font-size: 1.7rem;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.consult-button1:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);\r\n  background-color: #0a69ff;\r\n  color: white;\r\n}\r\n\r\n/* Contact Modal */\r\n.contact-modal-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 1000;\r\n}\r\n\r\n.contact-modal {\r\n  position: relative;\r\n  background-color: white;\r\n  padding: 30px;\r\n  border-radius: 8px;\r\n  width: 90%;\r\n  max-width: 250px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\r\n  text-align: center;\r\n}\r\n\r\n.close-modal {\r\n  position: absolute;\r\n  top: 10px;\r\n  right: 15px;\r\n  font-size: 30px;\r\n  background: none;\r\n  border: none;\r\n  cursor: pointer;\r\n  color: #999;\r\n}\r\n\r\n.contact-content {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.contact-item {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-bottom: 15px;\r\n  font-size: 18px;\r\n}\r\n\r\n.contact-item i {\r\n  margin-right: 10px;\r\n  color: #2196f3;\r\n}\r\n\r\n.contact-note {\r\n  color: #666;\r\n  font-size: 14px;\r\n  line-height: 1.6;\r\n}\r\n\r\n/* Animations */\r\n@keyframes slideInFromLeft {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateX(-50px) translateY(-50%);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateX(0) translateY(-50%);\r\n  }\r\n}\r\n\r\n@keyframes slideInFromRight {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateX(50px) translateY(-50%);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateX(0) translateY(-50%);\r\n  }\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n  }\r\n  to {\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n/* 移动端样式 */\r\n.mobile-layout {\r\n  display: none;\r\n}\r\n\r\n/* 移动端响应式设计 */\r\n@media (max-width: 768px) {\r\n  .desktop-layout {\r\n    display: none;\r\n\r\n  }\r\n  .container, .banner-section, .services-grid, .appsec-container {\r\n    max-width: 10%;\r\n    padding-left: 15px;\r\n    padding-right: 15px;\r\n    overflow-x: hidden;\r\n  }\r\n\r\n  .mobile-layout {\r\n    display: block;\r\n    overflow-x: hidden;\r\n\r\n    /*padding: 0 15px;*/\r\n  }\r\n\r\n\r\n    /* 修改移动端轮播图样式 */\r\n  .mobile-banner {\r\n    position: relative;\r\n    width: 100%;\r\n    height: 100%;\r\n    min-height: 30vh;\r\n    overflow: hidden;\r\n    touch-action: pan-y;\r\n    user-select: none;\r\n    /*margin-bottom: -5vh;*/\r\n  }\r\n\r\n  .mobile-banner-slider {\r\n    display: flex;\r\n    height: 100%;\r\n    transition: transform 0.5s ease;\r\n  }\r\n\r\n  .mobile-slide {\r\n    flex: 0 0 100%;\r\n    width: 100%;\r\n    height: 70%;\r\n    position: relative;\r\n  }\r\n\r\n  .mobile-slide-inner {\r\n    width: 100%;\r\n    height: 100%;\r\n    position: relative;\r\n  }\r\n\r\n  .mobile-slide-img {\r\n    width: 100%;\r\n    height: 100%;\r\n    object-fit: cover;\r\n  }\r\n\r\n  .mobile-banner-content {\r\n    position: absolute;\r\n    bottom: 0;\r\n    left: 0;\r\n    right: 0;\r\n    padding: 15px;\r\n    /*background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);*/\r\n    color: white;\r\n    z-index: 2;\r\n  }\r\n\r\n  .mobile-banner-title {\r\n    font-size: 1.5rem;\r\n    margin-bottom: 8px;\r\n    font-weight: bold;\r\n    line-height: 1.3;\r\n  }\r\n\r\n  .mobile-banner-text {\r\n    font-size: 0.9rem;\r\n    margin-bottom: 12px;\r\n    line-height: 1.4;\r\n  }\r\n\r\n  .mobile-banner-actions {\r\n    display: flex;\r\n    gap: 10px;\r\n    flex-wrap: wrap;\r\n  }\r\n\r\n  .mobile-banner-button {\r\n    padding: 8px 12px;\r\n    font-size: 14px;\r\n    border-radius: 4px;\r\n    text-decoration: none;\r\n    font-weight: 500;\r\n  }\r\n\r\n  .mobile-banner-button.primary-btn {\r\n    background-color: transparent;\r\n    border: 1px solid white;\r\n    color: white;\r\n  }\r\n\r\n  .mobile-banner-button.secondary-btn {\r\n    background-color: #f67b3d;\r\n    color: white;\r\n    border: none;\r\n  }\r\n\r\n  .mobile-banner-pagination {\r\n    position: absolute;\r\n    bottom: 10px;\r\n    left: 0;\r\n    right: 0;\r\n    display: flex;\r\n    justify-content: center;\r\n    gap: 8px;\r\n    z-index: 3;\r\n  }\r\n\r\n  .mobile-banner-pagination span {\r\n    display: block;\r\n    width: 8px;\r\n    height: 8px;\r\n    border-radius: 50%;\r\n    background-color: rgba(255,255,255,0.5);\r\n    transition: all 0.3s ease;\r\n    cursor: pointer;\r\n  }\r\n\r\n  .mobile-banner-pagination span.active {\r\n    background-color: white;\r\n    width: 20px;\r\n    border-radius: 4px;\r\n  }\r\n\r\n\r\n\r\n  /* 移动端 GPU 推荐 */\r\n  .mobile-section {\r\n    padding: 30px 0;\r\n  }\r\n\r\n  .mobile-section-header {\r\n    text-align: center;\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .mobile-section-title {\r\n    font-size: 22px;\r\n    color: #333;\r\n    position: relative;\r\n    display: inline-block;\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .mobile-section-title::after {\r\n    content: '';\r\n    position: absolute;\r\n    bottom: -5px;\r\n    left: 50%;\r\n    transform: translateX(-50%);\r\n    width: 40px;\r\n    height: 2px;\r\n    background-color: #2196f3;\r\n  }\r\n\r\n  .mobile-section-description {\r\n    font-size: 14px;\r\n    color: #666;\r\n    max-width: 80%;\r\n    margin: 0 auto;\r\n  }\r\n\r\n  .mobile-gpu-list {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 15px;\r\n  }\r\n\r\n  .mobile-gpu-card {\r\n    background-color: white;\r\n    border-radius: 8px;\r\n    padding: 15px;\r\n    box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n  }\r\n\r\n  .mobile-gpu-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .mobile-gpu-name {\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    color: #333;\r\n    margin: 0;\r\n  }\r\n\r\n  .mobile-gpu-tags {\r\n    display: flex;\r\n    gap: 5px;\r\n  }\r\n\r\n  .mobile-recommend-tag {\r\n    background-color: #2196f3;\r\n    color: white;\r\n    font-size: 10px;\r\n    padding: 2px 6px;\r\n    border-radius: 3px;\r\n  }\r\n\r\n  .mobile-new-tag {\r\n    background-color: #ff4d4f;\r\n    color: white;\r\n    font-size: 10px;\r\n    padding: 2px 6px;\r\n    border-radius: 3px;\r\n  }\r\n\r\n  .mobile-gpu-specs {\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .mobile-spec-item {\r\n    display: flex;\r\n    margin-bottom: 5px;\r\n    font-size: 13px;\r\n  }\r\n\r\n  .mobile-spec-label {\r\n    color: #666;\r\n    margin-right: 5px;\r\n    width: 60px;\r\n  }\r\n\r\n  .mobile-spec-value {\r\n    color: #333;\r\n    font-weight: 500;\r\n  }\r\n\r\n  .mobile-gpu-price {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n  }\r\n\r\n  .mobile-original-price {\r\n    font-size: 12px;\r\n    color: #999;\r\n    text-decoration: line-through;\r\n  }\r\n\r\n  .mobile-current-price {\r\n    font-size: 16px;\r\n    font-weight: bold;\r\n    color: #2196f3;\r\n  }\r\n\r\n  /* 移动端 GPU 对比 */\r\n  .mobile-comparison-container {\r\n    overflow-x: auto;\r\n    -webkit-overflow-scrolling: touch;\r\n    margin: 0 -15px;\r\n    padding: 0 15px;\r\n  }\r\n\r\n  .mobile-comparison-scroll {\r\n    min-width: 600px;\r\n  }\r\n\r\n  .mobile-comparison-table {\r\n    width: 100%;\r\n    border-collapse: collapse;\r\n    font-size: 13px;\r\n  }\r\n\r\n  .mobile-comparison-table th,\r\n  .mobile-comparison-table td {\r\n    padding: 10px;\r\n    text-align: center;\r\n    border: 1px solid #eee;\r\n  }\r\n\r\n  .mobile-comparison-table thead {\r\n    background-color: #f5f5f5;\r\n  }\r\n\r\n  .mobile-comparison-table th {\r\n    font-weight: 600;\r\n    color: #333;\r\n  }\r\n\r\n  /* 移动端 核心优势 */\r\n  .mobile-services-list {\r\n    display: grid;\r\n    grid-template-columns: repeat(2, 1fr);\r\n    gap: 15px;\r\n  }\r\n\r\n  .mobile-service-card {\r\n    background-color: white;\r\n    border-radius: 8px;\r\n    padding: 15px;\r\n    box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n    text-align: center;\r\n  }\r\n\r\n  .mobile-service-icon {\r\n    font-size: 30px;\r\n    color: #2196f3;\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .mobile-service-title {\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    color: #333;\r\n    margin-bottom: 8px;\r\n  }\r\n\r\n  .mobile-service-desc {\r\n    font-size: 12px;\r\n    color: #666;\r\n    line-height: 1.5;\r\n  }\r\n\r\n  /* 移动端 行业应用 */\r\n  .mobile-applications-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(2, 1fr);\r\n    gap: 15px;\r\n  }\r\n\r\n  .mobile-app-item {\r\n    position: relative;\r\n    border-radius: 8px;\r\n    overflow: hidden;\r\n    height: 120px;\r\n  }\r\n\r\n  .mobile-app-image {\r\n    width: 100%;\r\n    height: 100%;\r\n  }\r\n\r\n  .mobile-app-image img {\r\n    width: 100%;\r\n    height: 100%;\r\n    object-fit: cover;\r\n    transition: transform 0.5s ease;\r\n  }\r\n\r\n  .mobile-app-item:hover .mobile-app-image img {\r\n    transform: scale(1.05);\r\n  }\r\n\r\n  .mobile-app-title {\r\n    position: absolute;\r\n    bottom: 0;\r\n    left: 0;\r\n    right: 0;\r\n    padding: 10px;\r\n    background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);\r\n    color: white;\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    margin: 0;\r\n  }\r\n\r\n  /* 移动端 咨询按钮 */\r\n  .mobile-consult-section {\r\n    background-color: #2196f3;\r\n    padding: 20px;\r\n    text-align: center;\r\n    margin: 30px -15px 0;\r\n  }\r\n\r\n  .mobile-consult-title {\r\n    color: white;\r\n    font-size: 18px;\r\n    margin-bottom: 15px;\r\n  }\r\n\r\n  .mobile-consult-button {\r\n    background-color: white;\r\n    color: #2196f3;\r\n    border: none;\r\n    border-radius: 25px;\r\n    padding: 10px 25px;\r\n    font-size: 16px;\r\n    font-weight: 500;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n  }\r\n\r\n  .mobile-consult-button:hover {\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 4px 8px rgba(0,0,0,0.1);\r\n  }\r\n\r\n  /* 移动端 联系弹窗 */\r\n  .mobile-contact-overlay {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background-color: rgba(0,0,0,0.5);\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    z-index: 1000;\r\n  }\r\n\r\n  .mobile-contact-modal {\r\n    position: relative;\r\n    background-color: white;\r\n    border-radius: 10px;\r\n    width: 80%;\r\n    max-width: 300px;\r\n    padding: 20px;\r\n    text-align: center;\r\n  }\r\n\r\n  .mobile-close-modal {\r\n    position: absolute;\r\n    top: 10px;\r\n    right: 15px;\r\n    font-size: 24px;\r\n    background: none;\r\n    border: none;\r\n    color: #999;\r\n    cursor: pointer;\r\n  }\r\n\r\n  .mobile-contact-content {\r\n    margin: 20px 0;\r\n  }\r\n\r\n  .mobile-contact-item {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin-bottom: 15px;\r\n    font-size: 16px;\r\n  }\r\n\r\n  .mobile-contact-item i {\r\n    margin-right: 10px;\r\n    color: #2196f3;\r\n  }\r\n\r\n  .mobile-contact-note {\r\n    color: #666;\r\n    font-size: 14px;\r\n  }\r\n\r\n  /* 移动端动画 */\r\n  @keyframes mobile-fade {\r\n    from {\r\n      opacity: 0;\r\n      transform: translateY(20px);\r\n    }\r\n    to {\r\n      opacity: 1;\r\n      transform: translateY(0);\r\n    }\r\n  }\r\n\r\n  .mobile-fade-enter-active, .mobile-fade-leave-active {\r\n    transition: all 0.3s ease;\r\n  }\r\n\r\n  .mobile-fade-enter, .mobile-fade-leave-to {\r\n    opacity: 0;\r\n    transform: translateY(20px);\r\n  }\r\n}\r\n\r\n/* 响应式设计 - 平板 */\r\n@media (min-width: 769px) and (max-width: 1024px) {\r\n  .gpu-card-grid {\r\n    grid-template-columns: repeat(2, 1fr);\r\n  }\r\n\r\n\r\n\r\n  .services-grid {\r\n    grid-template-columns: repeat(2, 1fr);\r\n  }\r\n\r\n  .appsec-grid {\r\n    grid-template-columns: repeat(2, 1fr);\r\n  }\r\n\r\n  .appsec-wide {\r\n    grid-column: span 2;\r\n  }\r\n\r\n  .appsec-tall {\r\n    grid-column: span 1;\r\n    grid-row: span 1;\r\n    height: 180px;\r\n  }\r\n}\r\n\r\n\r\n\r\n</style>\r\n", "import mod from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./IndexView.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./IndexView.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./IndexView.vue?vue&type=template&id=fd9455b6&scoped=true&\"\nimport script from \"./IndexView.vue?vue&type=script&lang=js&\"\nexport * from \"./IndexView.vue?vue&type=script&lang=js&\"\nimport style0 from \"./IndexView.vue?vue&type=style&index=0&id=fd9455b6&prod&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"fd9455b6\",\n  null\n  \n)\n\nexport default component.exports"], "names": ["render", "_vm", "this", "_c", "_self", "staticClass", "_v", "on", "$event", "navigateTo", "_m", "staticRenderFns", "attrs", "require", "staticStyle", "name", "data", "methods", "path", "currentPath", "previousActivePath", "$nextTick", "navLinks", "document", "querySelectorAll", "for<PERSON>ach", "link", "classList", "contains", "add", "setTimeout", "remove", "$route", "window", "scrollTo", "top", "behavior", "$router", "go", "push", "component", "isMobile", "handleTouchStart", "handleTouchMove", "handleTouchEnd", "style", "transform", "mobileCurrentSlide", "_l", "bannerImages", "item", "index", "key", "img", "class", "content", "position", "domProps", "_s", "title", "text", "is<PERSON>ogin", "secondaryLink", "preventDefault", "secondaryBtnText", "_e", "primaryLink", "primaryBtnText", "thirdLink", "thirdBtnText", "active", "goToSlide", "gpus", "gpu", "recommended", "isNew", "singlePrecision", "halfPrecision", "originalPrice", "price", "comparisonGpus", "architecture", "fp16Performance", "fp32Performance", "memory", "memoryType", "bandwidth", "serviceList", "service", "icon", "desc", "mobileApplications", "app", "image", "openContactModal", "showContactModal", "target", "currentTarget", "closeContactModal", "apply", "arguments", "contactInfo", "phone", "translate", "transition", "tsion", "last", "next", "ref", "translateX", "firstRowWide", "hover", "firstRowTallApps", "secondRowApps", "thirdRowSmallApps", "thirdRowWide", "created", "fetchComparison", "getNotAuth", "then", "req", "rows", "map", "fp16performance", "fp32performance", "memorytype", "error", "console", "components", "chatAi", "Footer", "<PERSON><PERSON>", "GpuComparison", "computed", "cookie", "includes", "mobileTranslateX", "touchStartX", "touchEndX", "touchThreshold", "tabIndex", "tabList", "swiperOptions", "loop", "autoplay", "delay", "disableOnInteraction", "pagination", "el", "clickable", "navigation", "nextEl", "prevEl", "id", "activeIndex", "fetchRecommendations", "url", "URL", "location", "href", "token", "searchParams", "get", "hasRefreshed", "localStorage", "getItem", "origin", "pathname", "setItem", "setToken", "mounted", "checkIsMobile", "addEventListener", "desktopInterval", "setInterval", "mobileInterval", "mobileNext", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "clearInterval", "isnew", "e", "touches", "clientX", "diff", "mobilePrev", "length", "resetMobileInterval", "innerWidth", "handleBannerAction", "$ga", "event", "changeTab", "useGpu", "log", "getCustomSolution", "contactUs", "prevSlide", "slideshow", "nextSlide", "selectGpu", "watch", "newVal", "dots", "$refs", "swiperPagination", "dot", "toggle"], "sourceRoot": ""}