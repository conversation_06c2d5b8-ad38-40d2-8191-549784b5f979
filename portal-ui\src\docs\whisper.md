# <font style="color:rgb(2, 8, 23);">容器化部署 Whisper</font>
<font style="color:rgb(2, 8, 23);">本指南详细阐述了在天工开物上部署 openai 开源的语音识别 Whisper 项目的解决方案。</font>

## <font style="color:rgb(2, 8, 23);">1.部署步骤</font>
<font style="color:rgb(2, 8, 23);">我们在</font>**<font style="color:rgb(2, 8, 23);">服务配置——预制镜像</font>**<font style="color:rgb(2, 8, 23);">中提供了预构建的 Whisper 容器映像，旨在满足一般要求。您可以选择直接在天工开物上运行这些容器以执行任务。或者，您还可以通过使用我们资源管理——镜像仓库所提供的免费 Docker 镜像仓库服务，来方便管理您自身的 Docker 镜像。</font>

<font style="color:rgb(2, 8, 23);">下面是部署步骤：</font>

### <font style="color:rgb(2, 8, 23);">1.1 访问</font>[天工开物控制台](https://tiangongkaiwu.top/#/console)<font style="color:rgb(2, 8, 23);">，点击新增部署。</font>
![](./imgs/universal1.png)

### <font style="color:rgb(2, 8, 23);">1.2 基于自身需要进行配置，参考配置为单卡 4090（初次使用进行调试）。</font>
![](./imgs/universal2.png)

### <font style="color:rgb(2, 8, 23);">1.3 选择服务配置中的预制镜像 选择我们打包好的 Whisper 镜像快速开启服务</font>
![](./imgs/whisper3.png)

### <font style="color:rgb(2, 8, 23);">1.4 点击部署服务，耐心等待节点拉取镜像并启动 第一次访问会下载模型，所以需要稍等一会</font>
![](./imgs/whisper4.png)

### <font style="color:rgb(2, 8, 23);">1.5 节点启动后，你所在“公开访问”中看到的内容可能如下：</font>
![](./imgs/whisper5.png)

<font style="color:rgb(2, 8, 23);">通过查看节点列表——查看详情 通过容器信息</font>**<font style="color:rgb(2, 8, 23);">确定模型加载完成</font>**<font style="color:rgb(2, 8, 23);">即可正常进入服务</font>

![](./imgs/whisper6.png)


### <font style="color:rgb(2, 8, 23);">1.6 我们可以点击“9000”端口的链接，测试 whisper 部署情况，系统会自动分配一个可公网访问的域名，接下来我们即可自由地使用 whisper 的服务</font>
## <font style="color:rgb(2, 8, 23);">2.使用教程</font>
<font style="color:rgb(2, 8, 23);">这个项目提供了 2 个 http 接口：</font>

<font style="color:rgb(2, 8, 23);">1./asr：语音识别接口，上传语音或者视频文件，输出文字。</font>

<font style="color:rgb(2, 8, 23);">2./detect-language：语言检测接口，上传语音或者视频文件，输出语言。</font>

### <font style="color:rgb(2, 8, 23);">生产环境</font>
<font style="color:rgb(2, 8, 23);">以下是针对生产环境使用的说明，包含完整的请求命令、响应时间预估及返回结果示例：</font>

#### **<font style="color:rgb(2, 8, 23);">2.1.1 语音识别接口 (</font>****<font style="color:rgb(2, 8, 23);">/asr</font>****<font style="color:rgb(2, 8, 23);">)</font>**
<font style="color:rgb(2, 8, 23);">请求命令（CURL）</font>

```plain
# 英文音频/视频转文字  
curl -X POST "http://api.example.com/asr?language=en" \  
     -H "Authorization: Bearer YOUR_API_KEY" \  # 若需认证  
     -H "Content-Type: multipart/form-data" \  
     -F "file=@audio_en.mp3"  

# 中文音频/视频转文字  
curl -X POST "http://api.example.com/asr?language=zh" \  
     -H "Content-Type: multipart/form-data" \  
     -F "file=@video_zh.mp4"
```

**<font style="color:rgb(2, 8, 23);">参数说明</font>**

+ <font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">language</font><font style="color:rgb(2, 8, 23);">：必填，指定音频语言（</font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">en</font><font style="color:rgb(2, 8, 23);">为英文，</font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">zh</font><font style="color:rgb(2, 8, 23);">为中文）。</font>
+ <font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">file</font><font style="color:rgb(2, 8, 23);">：必填，支持格式：</font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">.mp3</font><font style="color:rgb(2, 8, 23);">, </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">.wav</font><font style="color:rgb(2, 8, 23);">, </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">.mp4</font><font style="color:rgb(2, 8, 23);">, </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">.mov</font><font style="color:rgb(2, 8, 23);">等。</font>

**<font style="color:rgb(2, 8, 23);">响应时间参考</font>**

| <font style="color:rgb(2, 8, 23);">文件大小</font> | <font style="color:rgb(2, 8, 23);">预估响应时间</font> |
| --- | --- |
| <font style="color:rgb(2, 8, 23);"><10MB</font> | <font style="color:rgb(2, 8, 23);">3-8 秒</font> |
| <font style="color:rgb(2, 8, 23);">10MB-50MB</font> | <font style="color:rgb(2, 8, 23);">10-25 秒</font> |
| <font style="color:rgb(2, 8, 23);">>50MB</font> | <font style="color:rgb(2, 8, 23);">异步处理（返回任务 ID）</font> |


**<font style="color:rgb(103, 103, 108);">注</font>**<font style="color:rgb(103, 103, 108);">：大文件建议分片上传或使用异步接口，同步请求超时时间默认为 30 秒。</font>

**<font style="color:rgb(2, 8, 23);">返回结果示例</font>**

```plain
// 成功  
{  
  "status": "success",  
  "text": "This is the transcribed text from your audio file."  
}  

// 失败（如语言参数错误）  
{  
  "status": "error",  
  "code": 400,  
  "message": "Invalid language parameter. Supported: en, zh."  
}
```

---

#### **<font style="color:rgb(2, 8, 23);">2.1.2 语言检测接口 (</font>****<font style="color:rgb(2, 8, 23);">/detect-language</font>****<font style="color:rgb(2, 8, 23);">)</font>**
<font style="color:rgb(2, 8, 23);">请求命令（CURL）</font>

```plain
curl -X POST "http://api.example.com/detect-language" \  
     -H "Content-Type: multipart/form-data" \  
     -F "file=@unknown_audio.wav"
```

<font style="color:rgb(2, 8, 23);">参数说明</font>

+ <font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">file</font><font style="color:rgb(2, 8, 23);">：必填，支持格式同上。</font>

<font style="color:rgb(2, 8, 23);">响应时间参考</font>

| <font style="color:rgb(2, 8, 23);">文件大小</font> | <font style="color:rgb(2, 8, 23);">预估响应时间</font> |
| --- | --- |
| <font style="color:rgb(2, 8, 23);"><10MB</font> | <font style="color:rgb(2, 8, 23);">2-5 秒</font> |
| <font style="color:rgb(2, 8, 23);">10MB-50MB</font> | <font style="color:rgb(2, 8, 23);">5-10 秒</font> |
| <font style="color:rgb(2, 8, 23);">>50MB</font> | <font style="color:rgb(2, 8, 23);">仅分析前 1 分钟内容</font> |


**<font style="color:rgb(103, 103, 108);">注</font>**<font style="color:rgb(103, 103, 108);">：大文件默认截取前 1 分钟内容进行检测以加速响应。</font>

**<font style="color:rgb(2, 8, 23);">返回结果示例</font>**

```plain
// 成功  
{  
  "status": "success",  
  "language": "fr",  
  "confidence": 0.92  // 检测置信度（0-1）  
}  

// 失败（如文件格式不支持）  
{  
  "status": "error",  
  "code": 415,  
  "message": "Unsupported media type. Allowed: audio/*, video/*."  
}
```

---

**<font style="color:rgb(2, 8, 23);">关键说明</font>**

1. **<font style="color:rgb(2, 8, 23);">认证</font>**<font style="color:rgb(2, 8, 23);">：若需 API 密钥，需在 Header 中添加 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">Authorization: Bearer YOUR_API_KEY</font><font style="color:rgb(2, 8, 23);">。</font>
2. **<font style="color:rgb(2, 8, 23);">异步处理</font>**<font style="color:rgb(2, 8, 23);">：</font>
    - <font style="color:rgb(2, 8, 23);">大文件可调用 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">/asr/async?language=en</font><font style="color:rgb(2, 8, 23);"> 提交任务，返回 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">{"task_id": "123"}</font><font style="color:rgb(2, 8, 23);">。</font>
    - <font style="color:rgb(2, 8, 23);">通过 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">/tasks/123</font><font style="color:rgb(2, 8, 23);"> 查询结果。</font>
3. **<font style="color:rgb(2, 8, 23);">限速</font>**<font style="color:rgb(2, 8, 23);">：默认限制 10 请求/分钟/IP，生产环境需联系调整配额。</font>

<font style="color:rgb(2, 8, 23);">按需直接复制命令即可集成到脚本或应用程序中。</font>

### <font style="color:rgb(2, 8, 23);">网页服务</font>
#### <font style="color:rgb(2, 8, 23);">2.2.1 英文音频转文字</font>
<font style="color:rgb(2, 8, 23);">点击</font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">/asr：语音识别接口</font><font style="color:rgb(2, 8, 23);">后点击页面右上角</font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">Try it out</font><font style="color:rgb(2, 8, 23);"> 开始使用</font>

![](./imgs/whisper7.png)

<font style="color:rgb(2, 8, 23);">先用一个英文 mp3 音频看看效果，可以先照抄截图中的参数看看效果，后面会介绍每个参数的意思。</font>

![](./imgs/whisper8.png)

<font style="color:rgb(2, 8, 23);">稍等一会即可转换完成，在 response body 中可看到转换结果。</font>

![](./imgs/whisper9.png)

<font style="color:rgb(2, 8, 23);">下面是复制出来的文本，可以看到效果是非常好的：</font>

```json
Wilbur and Orville Wright are the American inventors who made a small engine-powered flying machine.
They proved that flight without the aid of gas-filled balloons was possible.
Wilbur Wright was born in 1867 near Melville, Indiana.
This brother Orville was born four years later in Dayton, Ohio.
As they grew up, the Wright brothers experimented with mechanical things.
Later, the Wright brothers began to design their own flying machine.
They used ideas they had developed from earlier experiments with a toy helicopter, Kites,
the printing machine, and bicycles.
Soon they needed a place to test their ideas about flight.
The best place with best wind conditions seemed to be a piece of sandy land in North Carolina
along the coast of the Atlantic Ocean.
It was called Kill Devil Hill near the town of Kitty Hawk.
The Wright brothers did many tests with gliders at Kitty Hawk.
With these tests, they learned how to solve many problems.
By the autumn of 1903, Wilbur and Orville had designed and built an airplane powered
by a gasoline engine.
The plane had wings twelve meters across.
It weighed about 340 kilograms, including the pilot.
On December 17, 1903, they made the world's first flight in a machine that was heavier
than air and powered by an engine.
Orville flew the plane thirty-six meters.
He was in the air for twelve seconds.
The two brothers made three more flights that day.
For other men watched the Wright brothers' first flights, one of the men took pictures.
Few newspapers, however, noted the event.
It was almost five years before the Wright brothers became famous.
In 1908, Wilbur went to France.
He gave demonstration flights at heights of ninety meters.
A French company agreed to begin making the Wright brothers flying machine.
Orville made successful flights in the United States.
At the time, Wilbur was in France.
The United States War Department agreed to buy a Wright brothers plane.
Orville and Orville suddenly became world heroes.
But the brothers were not seeking fame.
They returned to Dayton where they continued to improve their airplanes.
They taught many others how to fly.
Wilbur Wright died of typhoid fever in 1912.
Orville Wright continued designing and inventing until he died many years later in 1948.
Today, the Wright brothers' first airplane is in the Air and Space Museum in Washington,
DC.
Visitors to the museum can look at the Wright brothers' small plane.
Then they can walk to another area and see space vehicles and a rock collected from the
moon.
The world has changed a lot since Wilbur and Orville Wright began the modern age of flight
over one hundred years ago.
I'm John Russell.
```

#### <font style="color:rgb(2, 8, 23);">2.2.2 中文视频转文字</font>
<font style="color:rgb(2, 8, 23);">与上面操作一样，只是选文件的时候选一个中文视频就可以了，然后提示词这里写上简体中文的要求（默认会输出繁体）</font>

![](./imgs/whisper10.png)

<font style="color:rgb(2, 8, 23);">下面是复制出来的文本，可以看到效果是非常好的：</font>

```json
我们正站在一个计算力重新定义世界的时代
算力，这个曾经只出现在服务器参数表上的词，正在悄无声息地进入每个人的生活
你打开手机的一次语音助手唤醒，你看的一段实时翻译视频
你在社交媒体刷到的一张 AI 合成图——它们背后，都有庞大算力在悄悄支撑
但大多数人，并不知道这一切意味着什么
有人说，算力是新时代的“水与电”
它不是新闻，却支撑着所有新闻的传输；它不是观点，却加速了每一个观点的扩散
每一次模型的训练、每一次搜索引擎的响应、每一帧超清视频的稳定播放，都是算力在负重前行
而在过去的几年，算力不再只是“快与慢”的比较，它变成了谁拥有未来生产力的标尺
国家之间在比拼数据中心的布局、芯片的迭代速度，城市之间在竞逐智能基础设施的落地率
连一座山谷是否适合建 AI 工厂，背后考量的也不只是地理和气候，而是它能否撑起千万亿次的计算吞吐
在这个时代，有人说数据是新的石油，但我们越来越清楚
真正决定谁能炼出“智能”的，不只是数据的多寡，而是你有没有足够的算力
这很冷酷，也很真实
但也正因为如此，才更值得我们去关注
当一切都在拼算力时，是否还有人为算力背后的能耗、伦理、地域不平衡发出一点声音
当一个个模型被迅速部署、落地、取代时
我们是否还能停下来想一想
到底是我们在使用算力，还是某种不可见的秩序，正借由算力改写世界的运转方式
没人能给出答案。我们只能继续看，继续记录
```

#### <font style="color:rgb(2, 8, 23);">2.2.3 语言检测</font>
<font style="color:rgb(2, 8, 23);">不识别文字，只检测一下是什么语言，大文件只会检查前 30 秒。</font>

![](./imgs/whisper11.png)

<font style="color:rgb(2, 8, 23);">结果展示：</font>

![](./imgs/whisper12.png)

## <font style="color:rgb(2, 8, 23);">3.参数解释</font>
### **<font style="color:rgb(2, 8, 23);">3.1</font>****<font style="color:rgb(2, 8, 23);"> </font>****<font style="color:rgb(2, 8, 23);">encode（编码预处理）</font>**
+ **<font style="color:rgb(2, 8, 23);">作用</font>**<font style="color:rgb(2, 8, 23);">：自动通过 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">ffmpeg</font><font style="color:rgb(2, 8, 23);"> 对音视频文件进行预处理</font>
+ **<font style="color:rgb(2, 8, 23);">必填</font>**<font style="color:rgb(2, 8, 23);">：</font><font style="color:rgb(2, 8, 23);">✅</font><font style="color:rgb(2, 8, 23);"> 是（推荐始终设为 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">true</font><font style="color:rgb(2, 8, 23);">）</font>
+ **<font style="color:rgb(2, 8, 23);">场景说明</font>**<font style="color:rgb(2, 8, 23);">：</font>
    - **<font style="color:rgb(2, 8, 23);">true</font>**<font style="color:rgb(2, 8, 23);">：对非标准音频格式（如 MP4/MKV 中的音轨）提取为 PCM/WAV 格式</font>
    - **<font style="color:rgb(2, 8, 23);">false</font>**<font style="color:rgb(2, 8, 23);">：仅当输入为原始音频（如已解压的 WAV 文件）时使用</font>
+ **<font style="color:rgb(2, 8, 23);">示例错误</font>**<font style="color:rgb(2, 8, 23);">：</font>

```bash
# 若上传 MP4 但设为 false 会报错：
Error: Audio extraction failed - unsupported container format
```

### **<font style="color:rgb(2, 8, 23);">3.2</font>****<font style="color:rgb(2, 8, 23);"> </font>****<font style="color:rgb(2, 8, 23);">task（任务模式）</font>**
| <font style="color:rgb(2, 8, 23);">模式</font> | <font style="color:rgb(2, 8, 23);">功能说明</font> | <font style="color:rgb(2, 8, 23);">输出示例</font> |
| --- | --- | --- |
| <font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">transcribe</font> | <font style="color:rgb(2, 8, 23);">语音转文字（源语言→同语言文本）</font> | <font style="color:rgb(2, 8, 23);">中文音频 → 中文文本</font> |
| <font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">translate</font> | <font style="color:rgb(2, 8, 23);">语音翻译（任何语言→英文文本）</font> | <font style="color:rgb(2, 8, 23);">中文音频 → 英文文本</font> |


+ **<font style="color:rgb(2, 8, 23);">注意</font>**<font style="color:rgb(2, 8, 23);">：</font>
    - <font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">translate</font><font style="color:rgb(2, 8, 23);"> 仅支持输出英文，不可指定其他目标语言</font>
    - <font style="color:rgb(2, 8, 23);">教学场景建议优先用</font><font style="color:#2F8EF4;"> </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">transcribe</font><font style="color:#2F8EF4;"> </font><font style="color:rgb(2, 8, 23);">保留原语言语义</font>

### **<font style="color:rgb(2, 8, 23);">3.3</font>****<font style="color:rgb(2, 8, 23);"> </font>****<font style="color:rgb(2, 8, 23);">language（源语言指定）</font>**
+ **<font style="color:rgb(2, 8, 23);">作用</font>**<font style="color:rgb(2, 8, 23);">：声明输入音频的语言（ISO 639-1 代码，如</font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">ch</font><font style="color:rgb(2, 8, 23);">/</font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">en</font><font style="color:rgb(2, 8, 23);">）</font>
+ **<font style="color:rgb(2, 8, 23);">必填</font>**<font style="color:rgb(2, 8, 23);">：</font><font style="color:rgb(2, 8, 23);">❌</font><font style="color:rgb(2, 8, 23);"> 否（自动检测模式）</font>
+ **<font style="color:rgb(2, 8, 23);">使用策略</font>**<font style="color:rgb(2, 8, 23);">：</font>

| <font style="color:rgb(2, 8, 23);">场景</font> | <font style="color:rgb(2, 8, 23);">推荐操作</font> |
| --- | --- |
| <font style="color:rgb(2, 8, 23);">单一语言录音</font> | <font style="color:rgb(2, 8, 23);">留空（自动检测更准确）</font> |
| <font style="color:rgb(2, 8, 23);">混合语言学术会议</font> | <font style="color:rgb(2, 8, 23);">强制指定主语言（如 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">en</font><font style="color:rgb(2, 8, 23);">）</font> |


+ **<font style="color:rgb(2, 8, 23);">错误示例</font>**<font style="color:rgb(2, 8, 23);">：</font>

```python
# 中文音频指定 language='en' 会导致：
"text": "Yangyang, will you give me money..."  # 拼音化乱码
```

### **<font style="color:rgb(2, 8, 23);">3.4 </font>**i**<font style="color:rgb(2, 8, 23);">nitial_prompt（上下文提示）</font>**
+ **<font style="color:rgb(2, 8, 23);">作用</font>**<font style="color:rgb(2, 8, 23);">：提供领域关键词提升识别精度（类似 ChatGPT 的 system prompt）</font>
+ **<font style="color:rgb(2, 8, 23);">格式</font>**<font style="color:rgb(2, 8, 23);">：英文短语或术语列表（即使处理中文音频也需用英文填写）</font>
+ **<font style="color:rgb(2, 8, 23);">经典用例</font>**<font style="color:rgb(2, 8, 23);">：</font>
    - <font style="color:rgb(2, 8, 23);">识别准确率提升 12-13%（针对专业术语）</font>
    - <font style="color:rgb(2, 8, 23);">支持动态更新词库（通过 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">X-KeyPool </font><font style="color:rgb(2, 8, 23);">请求头注入）</font>

```json
{
  "initial_prompt": "machine learning, convolutional neural networks, GPT-4"
}
```

### **<font style="color:rgb(2, 8, 23);">3.5 word_timestamps（时间戳控制）</font>**
+ **<font style="color:rgb(2, 8, 23);">作用</font>**<font style="color:rgb(2, 8, 23);">：控制输出是否包含词级时间标注</font>
+ **<font style="color:rgb(2, 8, 23);">兼容性</font>**<font style="color:rgb(2, 8, 23);">：</font>

| <font style="color:rgb(2, 8, 23);">输出格式</font> | <font style="color:rgb(2, 8, 23);">时间戳表现</font> |
| --- | --- |
| <font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">json</font> | <font style="color:rgb(2, 8, 23);">完整时间戳（精确到 10ms）</font> |
| <font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">srt</font> | <font style="color:rgb(2, 8, 23);">句子级分段（自动聚合词级数据）</font> |
| <font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">txt</font> | <font style="color:rgb(2, 8, 23);">不生效</font> |


### **<font style="color:rgb(2, 8, 23);">3.6</font>****<font style="color:rgb(2, 8, 23);"> </font>****<font style="color:rgb(2, 8, 23);">output（输出格式）</font>**
| <font style="color:rgb(2, 8, 23);">格式</font> | <font style="color:rgb(2, 8, 23);">适用场景</font> | <font style="color:rgb(2, 8, 23);">示例片段</font> |
| --- | --- | --- |
| <font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">txt</font> | <font style="color:rgb(2, 8, 23);">快速预览</font> | <font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">Yangyang, will you give me...</font> |
| <font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">srt</font> | <font style="color:rgb(2, 8, 23);">视频字幕嵌入</font> | <font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">1↵00:00:02,140 → 00:00:04,320↵Yangyang, will you...</font> |
| <font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">json</font> | <font style="color:rgb(2, 8, 23);">开发者分析（含置信度等元数据）</font> | <font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">{"text": "...", "words": [{"word": "Yangyang", "start": 2.14, "end": 2.87, "confidence": 0.92}]}</font> |
| <font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">vtt</font> | <font style="color:rgb(2, 8, 23);">流媒体兼容字幕</font> | <font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">WEBVTT↵↵00:02.140 --> 00:04.320↵Yangyang, will you...</font> |


## <font style="color:rgb(2, 8, 23);">4.项目介绍</font>
<font style="color:rgb(2, 8, 23);">拥有 ChatGPT 语言模型的 OpenAI 公司，开源了 Whisper 自动语音识别系统，OpenAI 强调 Whisper 的语音识别能力已达到人类水准。</font>

<font style="color:rgb(2, 8, 23);">Whisper 是一个通用的语音识别模型，它使用了大量的多语言和多任务的监督数据来训练，能够在英语语音识别上达到接近人类水平的鲁棒性和准确性。Whisper 还可以进行多语言语音识别、语音翻译和语言识别等任务。Whisper 的架构是一个简单的端到端方法，采用了编码器 - 解码器的 Transformer 模型，将输入的音频转换为对应的文本序列，并根据特殊的标记来指定不同的任务。</font>

![](./imgs/whisper13.png)

<font style="color:rgb(2, 8, 23);">Whisper 是一个自动语音识别（ASR，Automatic Speech Recognition）系统，OpenAI 通过从网络上收集了 68 万小时的多语言（98 种语言）和多任务（multitask）监督数据对 Whisper 进行了训练。OpenAI 认为使用这样一个庞大而多样的数据集，可以提高对口音、背景噪音和技术术语的识别能力。除了可以用于语音识别，Whisper 还能实现多种语言的转录，以及将这些语言翻译成英语。OpenAI 开放模型和推理代码，希望开发者可以将 Whisper 作为建立有用的应用程序和进一步研究语音处理技术的基础。</font>

<font style="color:rgb(2, 8, 23);">项目地址：</font>[<font style="color:#2F8EF4;">https://github.com/openai/whisper</font>](https://github.com/openai/whisper)

## <font style="color:rgb(2, 8, 23);">5.天工开物打包好的 whisper 项目镜像的优势：</font>
### <font style="color:rgb(2, 8, 23);">5.1 本地转写的麻烦之处：</font>
<font style="color:rgb(2, 8, 23);">笔记本上缺乏好的硬件：纯 CPU 模式跑 Whisper 速度非常慢，3 小时的音频可能需要十几个小时才能转写完毕。如果想用 GPU 加速，根据 Whisper 模型显存需求表格，官方的 Whisper-large 模型需要 10G 显存，普通的核显本实在是力不从心。后续的 Whisper cpp 项目 倒是大幅降低了内存需求，但这也引出了第二个问题。</font>

<font style="color:rgb(2, 8, 23);">环境部署复杂：OpenAI 开放的毕竟是项目代码，自己写代码适配的成本还是有点高。Whisper 的 GUI 客户端在 Mac 上不少（Whisper Transcription、MacWhisper..），Windows 上也有 Buzz，然而要找到一个支持 GPU 加速的客户端依然十分困难。</font>

### <font style="color:rgb(2, 8, 23);">5.2 利用 OpenAI 的 Whisper API 云端转写的问题：</font>
<font style="color:rgb(2, 8, 23);">利用 OpenAI 的 Whisper API 云端转写的优势在于不会受到本地机器性能的限制，且速度相对较快。但它存在两个问题：</font>

<font style="color:rgb(2, 8, 23);">项目处理流程复杂：OpenAI 的 Whisper API 限制单次请求的音频大小为 25Mb，而一节 3h 的音频通常都会有大几十 MB。这就需要对音频先做分段处理，再请求结果，最后合并结果。如果是 mp4 文件则还需要从中抽取音频文件，这个过程里没少踩坑。</font>

<font style="color:rgb(2, 8, 23);">成本问题：OpenAI 的 Whisper 模型 1min 收费 0.006 美元，1h 的音频按照 7.3 的汇率需要收费 2.7 元。坦白讲，Whisper 的 API 价格非常便宜了，几乎只是 Google Speech2Text API 的四分之一。但是，如果我们假设有 5 门课程，每堂课长 3 小时，每周有一次课，那么每个月的转写成本 = 5 x 3 x 4 x 2.7 = 162 元，这个价格还是有点肉疼。</font>

### <font style="color:rgb(2, 8, 23);">5.3 天工开物打包好的 Whisper 镜像的核心优势</font>
<font style="color:rgb(2, 8, 23);">针对传统音频转写场景的三大核心痛点，天工开物通过技术创新实现</font>**<font style="color:rgb(2, 8, 23);">"资源重构、效率跃升、体验革新"</font>**<font style="color:rgb(2, 8, 23);">，具体优势对比如下：</font>

#### **<font style="color:rgb(2, 8, 23);">5.3.1 核显笔记本运行 Whisper-large 的算力解放</font>**
**<font style="color:rgb(2, 8, 23);">▎痛点场景</font>**

<font style="color:rgb(2, 8, 23);">普通笔记本用 CPU 运行大型模型，3 小时音频需耗时 15 小时</font>

**<font style="color:rgb(2, 8, 23);">▎传统方案</font>**

+ <font style="color:rgb(2, 8, 23);">忍受超长等待或花费数万元升级显卡</font>

**<font style="color:rgb(2, 8, 23);">▎天工开物方案</font>**

+ **<font style="color:rgb(2, 8, 23);">智能路由</font>**<font style="color:rgb(2, 8, 23);">：自动接入算力池闲置 4090 显卡</font>
+ **<font style="color:rgb(2, 8, 23);">显存池化</font>**<font style="color:rgb(2, 8, 23);">：突破单卡物理限制，动态聚合多节点显存资源</font>
+ **<font style="color:rgb(2, 8, 23);">效能提升</font>**<font style="color:rgb(2, 8, 23);">：3 小时音频转写时间从 15 小时压缩至 55 分钟</font>

#### **<font style="color:rgb(2, 8, 23);">5.3.2 本地 GPU 资源不足的柔性调度</font>**
**<font style="color:rgb(2, 8, 23);">▎痛点场景</font>**

<font style="color:rgb(2, 8, 23);">官方要求 10G 显存，普通设备无法满足</font>

**<font style="color:rgb(2, 8, 23);">▎传统方案</font>**

+ <font style="color:rgb(2, 8, 23);">被迫采购高配显卡（如 RTX 4090）</font>

**<font style="color:rgb(2, 8, 23);">▎天工开物方案</font>**

+ **<font style="color:rgb(2, 8, 23);">碎片化显存调用</font>**<font style="color:rgb(2, 8, 23);">：将多台设备显存组合为 10G 逻辑显卡</font>
+ **<font style="color:rgb(2, 8, 23);">分布式推理</font>**<font style="color:rgb(2, 8, 23);">：把 Whisper-large 模型拆解到多卡并行计算</font>
+ **<font style="color:rgb(2, 8, 23);">成本规避</font>**<font style="color:rgb(2, 8, 23);">：零硬件投入即可获得专业级计算能力</font>

#### **<font style="color:rgb(2, 8, 23);">5.3.3 跨平台 GUI 的终极统一方案</font>**
**<font style="color:rgb(2, 8, 23);">▎痛点场景</font>**

<font style="color:rgb(2, 8, 23);">Windows/Mac 客户端功能割裂，且缺乏 GPU 加速支持</font>

**<font style="color:rgb(2, 8, 23);">▎传统方案</font>**

+ <font style="color:rgb(2, 8, 23);">在不同平台反复配置环境，手动处理兼容性问题</font>

**<font style="color:rgb(2, 8, 23);">▎天工开物方案</font>**

+ **<font style="color:rgb(2, 8, 23);">浏览器即工作站</font>**<font style="color:rgb(2, 8, 23);">：通过 Web 界面一键操作（上传/转写/导出）</font>
+ **<font style="color:rgb(2, 8, 23);">无缝衔接</font>**<font style="color:rgb(2, 8, 23);">：无论 Chromium 还是 Safari 内核，均可获得一致体验</font>

### **<font style="color:rgb(2, 8, 23);">5.4 技术价值提炼</font>**
| **<font style="color:rgb(2, 8, 23);">维度</font>** | **<font style="color:rgb(2, 8, 23);">传统方案</font>** | **<font style="color:rgb(2, 8, 23);">天工开物创新价值</font>** |
| --- | --- | --- |
| **<font style="color:rgb(2, 8, 23);">硬件门槛</font>** | <font style="color:rgb(2, 8, 23);">被设备性能锁死生产力</font> | <font style="color:rgb(2, 8, 23);">算力资源"无感穿透"，让核显本拥有 A100 级能力</font> |
| **<font style="color:rgb(2, 8, 23);">部署成本</font>** | <font style="color:rgb(2, 8, 23);">动辄上万元的硬件投入</font> | <font style="color:rgb(2, 8, 23);">按需付费，单次任务最低 0.5 元起</font> |
| **<font style="color:rgb(2, 8, 23);">运维复杂度</font>** | <font style="color:rgb(2, 8, 23);">跨平台调试耗时 3 小时+/次</font> | <font style="color:rgb(2, 8, 23);">浏览器打开即用，全程无需技术背景</font> |

<font style="color:rgb(2, 8, 23);">通过将复杂的 GPU 资源调度、模型优化、跨平台兼容等底层技术封装为开箱即用的标准化服务，天工开物让专业级语音转写从</font>**<font style="color:rgb(2, 8, 23);">实验室特权</font>**<font style="color:rgb(2, 8, 23);">真正转变为</font>**<font style="color:rgb(2, 8, 23);">基础教育工具</font>**<font style="color:rgb(2, 8, 23);">。</font>

**<font style="color:rgb(103, 103, 108);"></font>**

<br><br>
<font style="color:rgba(178,178,178,1);">最后更新于: 2025/6/17 17:52</font>
