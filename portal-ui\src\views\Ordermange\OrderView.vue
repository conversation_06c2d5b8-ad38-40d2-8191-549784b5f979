<template>
  <div>
    <SlideNotification
        v-if="showNotification"
        :message="notificationMessage"
        :type="notificationType"
        :duration="3000"
        :minHeight= minHeight
        @close="showNotification = false"
    />
    <div style="width: 100%">
      <div class="fee-center-container">
        <div class="navigation-sidebar">
          <h2 class="nav-title">费用中心</h2>
          <ul class="nav-list">

            <li class="nav-item" :class="{ active: currentSection === 'transactions' }">
              <a href="#" @click.prevent="changeSection('transactions')">
                <i class="el-icon-money"></i>
                <span>收支明细</span>
              </a>
            </li>

            <li class="nav-item" :class="{ active: currentSection === 'recharge' }">
              <a href="#" @click.prevent="changeSection('recharge')">
                <i class="el-icon-wallet"></i>
                <span>充值</span>
              </a>
            </li>
          </ul>
        </div>

        <!-- Main Content Area -->
        <div class="main-content">
          <!-- Orders Management Section -->
          <div v-if="currentSection === 'orders'">
            <!-- Order List View -->
            <div class="tab-content" v-show="!showOrderDetails">
              <div class="search-section">
                <div class="search-bar">
                  <input type="text" placeholder="搜索订单号" v-model="orderSearchQuery" />
                  <button class="search-button" @click="searchOrders">
                    <i class="el-icon-search"></i>
                  </button>
                  <button v-if="orderSearchQuery" class="clear-button" @click="clearOrderSearch">
                    <i class="el-icon-close"></i>
                  </button>
                </div>
                <div class="currency-display">
                  金额单位: ¥
                  <span class="flow-count">订单总数: {{ currentOrderTotal }}</span>
                </div>
              </div>

              <div class="table-container">
                <div v-if="orderLoading" class="loading-state">
                  <i class="el-icon-loading"></i>
                  <span>正在加载订单数据...</span>
                </div>

                <div v-if="orderError" class="error-state">
                  <i class="el-icon-error"></i>
                  <span>{{ orderError }}</span>
                  <button @click="fetchOrders">重试</button>
                </div>
                <table class="data-table">
                  <thead>
                  <tr>
                    <th>订单号 <i class="el-icon-sort" @click="sortBy('order_number')"></i></th>
                    <th>订单创建时间 <i class="el-icon-sort" @click="sortBy('created_at')"></i></th>
                    <th>支付状态 <i class="el-icon-sort" @click="sortBy('payment_status')"></i></th>
                    <th>单价 <i class="el-icon-sort" @click="sortBy('unit_price')"></i></th>
                    <th>时长 <i class="el-icon-sort" @click="sortBy('duration')"></i></th>
                    <th>付款方式 <i class="el-icon-sort" @click="sortBy('payment_method')"></i></th>
                    <th>合计</th>
                    <th>操作</th>
                  </tr>
                  </thead>
                  <tbody>
                  <tr v-for="(order, index) in paginatedOrders" :key="'order-'+index">
                    <td>{{ order.order_number }}</td>
                    <td>{{ formatDateTime(order.created_at) }}</td>
                    <td>
                      <span class="status-tag" :class="getPaymentStatusClass(order.payment_status)">
                        {{ getPaymentStatusText(order.payment_status) }}
                      </span>
                    </td>
                    <td>{{ formatPrice(order.unit_price) }}</td>
                    <td>{{ order.duration }}</td>
                    <td>
                      <span class="payment-method-tag" :class="getPaymentMethodClass(order.payment_method)">
                        {{ getPaymentMethodText(order.payment_method) }}
                      </span>
                    </td>
                    <td>{{ formatPrice(order.total_price) }}</td>
                    <td>
                      <span class="operation-link" @click="viewOrderDetails(order)">查看详情</span>
                    </td>
                  </tr>
                  <tr v-if="filteredOrderData.length === 0 && !orderLoading">
                    <td colspan="8" class="empty-state">
                      <div class="empty-container">
                        <i class="el-icon-document empty-icon"></i>
                        <div class="empty-text">没有找到匹配的订单</div>
                      </div>
                    </td>
                  </tr>
                  </tbody>
                </table>
              </div>

              <common-pagination
                  :current-page="currentPage"
                  :total="filteredOrderData.length"
                  :page-size="pageSize"
                  @change-page="goToPage"
                  @change-page-size="handlePageSizeChange"
              />
            </div>

            <!-- Order Details View -->
            <div class="order-details" v-show="showOrderDetails">
              <div class="detail-card">
                <div class="detail-header">
                  <h2 class="detail-title">订单详情</h2>
                  <button class="back-button" @click="showOrderList">
                    <i class="el-icon-back"></i> 返回列表
                  </button>
                </div>

                <div class="detail-content">
                  <h3 class="detail-subtitle">订单概况</h3>
                  <div class="detail-section">
                    <div class="detail-row">
                      <div class="detail-item">
                        <div class="detail-label">订单号:</div>
                        <div class="detail-value">{{ selectedOrder.order_number }}</div>
                      </div>
                      <div class="detail-item">
                        <div class="detail-label">订单状态:</div>
                        <div class="detail-value">
                          <span class="status-tag" :class="getPaymentStatusClass(selectedOrder.payment_status)">
                            {{ getPaymentStatusText(selectedOrder.payment_status) }}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div class="detail-row">
                      <div class="detail-item">
                        <div class="detail-label">支付方式:</div>
                        <div class="detail-value">
                          <span class="payment-method-tag" :class="getPaymentMethodClass(selectedOrder.payment_method)">
                            {{ getPaymentMethodText(selectedOrder.payment_method) }}
                          </span>
                        </div>
                      </div>
                      <div class="detail-item">
                        <div class="detail-label">单价:</div>
                        <div class="detail-value">¥ {{ formatPrice(selectedOrder.unit_price) }}</div>
                      </div>
                    </div>

                    <div class="detail-row">
                      <div class="detail-item">
                        <div class="detail-label">订单创建时间:</div>
                        <div class="detail-value">{{ formatDateTime(selectedOrder.created_at) }}</div>
                      </div>
                      <div class="detail-item">
                        <div class="detail-label">订单金额:</div>
                        <div class="detail-value">¥ {{ formatPrice(selectedOrder.total_price) }}</div>
                      </div>
                    </div>
                  </div>

                  <h3 class="detail-subtitle">GPU信息</h3>
                  <div class="detail-section">
                    <div class="detail-row">
                      <div class="detail-item">
                        <div class="detail-label">型号:</div>
                        <div class="detail-value">{{ selectedOrder.gpu_model }}</div>
                      </div>
                      <div class="detail-item">
                        <div class="detail-label">地区:</div>
                        <div class="detail-value">{{ selectedOrder.region }}</div>
                      </div>
                    </div>

                    <div class="detail-row">
                      <div class="detail-item">
                        <div class="detail-label">显卡数量:</div>
                        <div class="detail-value">{{ selectedOrder.gpu_count }} 个</div>
                      </div>
                      <div class="detail-item">
                        <div class="detail-label">显存:</div>
                        <div class="detail-value">{{ selectedOrder.video_memory }} GB</div>
                      </div>
                    </div>

                    <div class="detail-row">
                      <div class="detail-item">
                        <div class="detail-label">VCPU核数:</div>
                        <div class="detail-value">{{ selectedOrder.cpu_cores }} 核</div>
                      </div>
                      <div class="detail-item">
                        <div class="detail-label">系统盘:</div>
                        <div class="detail-value">{{ selectedOrder.system_disk }} SSD</div>
                      </div>
                    </div>

                    <div class="detail-row">
                      <div class="detail-item">
                        <div class="detail-label">云盘:</div>
                        <div class="detail-value">{{ selectedOrder.cloud_disk }} GB</div>
                      </div>
                      <div class="detail-item">
                        <div class="detail-label">内存:</div>
                        <div class="detail-value">{{ selectedOrder.memory }} GB</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Transactions Section -->
          <div v-if="currentSection === 'transactions'">
            <div class="tab-content">
              <div class="search-section">
                <div class="date-range-picker">
                  <select v-model="transactionDateRange">
                    <option value="7">最近7天</option>
                    <option value="30">最近一个月</option>
                    <option value="90">最近三个月</option>
                    <option value="custom">自定义时间段</option>
                  </select>

                  <div v-if="transactionDateRange === 'custom'" class="custom-date-range">
                    <input type="date" v-model="customDateStart" />
                    <span>至</span>
                    <input type="date" v-model="customDateEnd" />
<!--                    <button class="apply-button" @click="applyCustomDateRange">确认</button>-->
                  </div>
                </div>

                <div class="search-filters">
                  <select v-model="transactionType">
                    <option value="">全部类型</option>
                    <option value="income">收入</option>
                    <option value="expense">支出</option>
                  </select>

                  <input
                      type="text"
                      placeholder="搜索流水号"
                      v-model="transactionSearchQuery"
                      @keyup.enter="searchTransactions"
                  />
                  <button class="search-button" @click="searchTransactions">
                    <i class="el-icon-search"></i>
                  </button>
                </div>
              </div>

              <div class="transaction-summary">
                <div class="summary-card">
                  <div class="summary-title">总充值</div>
                  <div class="summary-value income">¥ {{ formatPrice(summaryData.totalRecharge) }}</div>
                </div>
                <div class="summary-card">
                  <div class="summary-title">总消费</div>
                  <div class="summary-value expense">¥ {{ formatPrice(summaryData.totalExpense) }}</div>
                </div>
                <div class="summary-card">
                  <div class="summary-title">账户余额</div>
                  <div class="summary-value">¥ {{ formatPrice(userBalance) }}</div>
                </div>
              </div>

              <div class="table-container">
                <div v-if="transactionLoading" class="loading-state">
                  <i class="el-icon-loading"></i>
                  <span>正在加载交易数据...</span>
                </div>

                <div v-if="transactionError" class="error-state">
                  <i class="el-icon-error"></i>
                  <span>{{ transactionError }}</span>
                  <button @click="fetchTransactions">重试</button>
                </div>

                <table class="data-table">
                  <thead>
                  <tr>
                    <th>流水号</th>
                    <th>交易时间</th>
                    <th>收支类型</th>
                    <th>交易类型</th>
                    <th>金额</th>
                    <th>交易渠道</th>
                    <th>备注</th>
                  </tr>
                  </thead>
                  <tbody>
                  <tr v-for="(transaction, index) in paginatedTransactions" :key="'transaction-'+index">
                    <td>{{ transaction.transaction_id }}</td>
                    <td>{{ formatDateTime(transaction.created_at) }}</td>
                    <td>
                      <span class="transaction-type" :class="getTransactionTypeClass(transaction.type)">
                        {{ getTransactionTypeName(transaction.type) }}
                      </span>
                    </td>
                    <td>{{ getTransactionTypeNamePay(transaction.pay_type) }}</td>
                    <td :class="transaction.type === 'expense' ? 'expense-amount' : 'income-amount'">
                      {{ transaction.type === 'expense' ? '￥ - ' : '￥ + ' }}{{ formatPrice(transaction.amount) }}
                    </td>

                    <td :class="transaction.type === 'expense' ? 'expense-zhifu' : 'income-shouru'">
                      {{ getPaymentMethodText(transaction.payment_channel) }}
                    </td>
                    <td>{{ transaction.description }}</td>
                  </tr>
                  <tr v-if="filteredTransactionData.length === 0 && !transactionLoading">
                    <td colspan="8" class="empty-state">
                      <div class="empty-container">
                        <i class="el-icon-money empty-icon"></i>
                        <div class="empty-text">没有找到匹配的交易记录</div>
                      </div>
                    </td>
                  </tr>
                  </tbody>
                </table>
              </div>

              <common-pagination
                  :current-page="transactionPage"
                  :total="filteredTransactionData.length"
                  :page-size="pageSize"
                  @change-page="(page) => transactionPage = page"
                  @change-page-size="(size) => { pageSize = size; transactionPage = 1; }"
              />
            </div>
          </div>

          <!-- Usage Records Section -->
          <div v-if="currentSection === 'usage'">
            <div class="tab-content">
              <div class="search-section">
                <div class="date-range-picker">
                  <select v-model="usageDateRange">
                    <option value="7">最近7天</option>
                    <option value="30">最近一个月</option>
                    <option value="90">最近三个月</option>
                    <option value="custom">自定义时间段</option>
                  </select>

                  <div v-if="usageDateRange === 'custom'" class="custom-date-range">
                    <input type="date" v-model="customUsageDateStart" />
                    <span>至</span>
                    <input type="date" v-model="customUsageDateEnd" />
<!--                    <button class="apply-button" @click="applyUsageCustomDateRange">确认</button>-->
                  </div>
                </div>

                <div class="search-filters">
                  <select v-model="usageFilterGpu">
                    <option value="">全部GPU型号</option>
                    <option v-for="gpu in gpuModels" :key="gpu.id" :value="gpu.id">
                      {{ gpu.name }}
                    </option>
                  </select>
                </div>
              </div>

              <div class="table-container">
                <table class="data-table">
                  <thead>
                  <tr>
                    <th>GPU型号</th>
                    <th>状态</th>
                    <th>开始时间</th>
                    <th>结束时间</th>
                    <th>使用时长</th>
                    <th>计费金额</th>
                    <th>操作</th>
                  </tr>
                  </thead>
                  <tbody>
                  <tr v-for="(record, index) in paginatedUsageRecords" :key="'usage-'+index">
                    <td>{{ record.gpu_model }}</td>
                    <td>
              <span class="status-tag" :class="getUsageStatusClass(record.status)">
                {{ getUsageStatusText(record.status) }}
              </span>
                    </td>
                    <td>{{ formatDateTime(record.start_time) }}</td>
                    <td>{{ record.end_time ? formatDateTime(record.end_time) : '--' }}</td>
                    <td>{{ calculateDuration(record.start_time, record.end_time) }}</td>
                    <td>¥ {{ formatPrice(record.cost / 10000) }}</td>
                    <td>
                      <span class="operation-link" @click="navigateToRecharge">续费</span>
                      <span v-if="record.status === 'scheduled'" class="operation-link cancel-link" @click="cancelReservation(record)">取消</span>
                    </td>
                  </tr>
                  <tr v-if="filteredUsageData.length === 0">
                    <td colspan="7" class="empty-state">
                      <div class="empty-container">
                        <i class="el-icon-time empty-icon"></i>
                        <div class="empty-text">没有找到匹配的使用记录</div>
                      </div>
                    </td>
                  </tr>
                  </tbody>
                </table>
              </div>

              <common-pagination
                  :current-page="usagePage"
                  :total="filteredUsageData.length"
                  :page-size="pageSize"
                  @change-page="(page) => usagePage = page"
                  @change-page-size="(size) => { pageSize = size; usagePage = 1; }"
              />
            </div>
          </div>

          <!-- Recharge Section -->
          <div v-if="currentSection === 'recharge'">
            <div class="tab-content">
              <div class="account-balance">
                <div class="balance-info">
                  <div class="balance-label">当前账户余额</div>
                  <div class="balance-value">¥ {{ formatPrice(userBalance) }}</div>
                </div>
              </div>

              <div class="recharge-options">
                <h3 class="recharge-title">选择充值金额</h3>

                <div class="amount-options">
                  <div
                      v-for="amount in rechargeAmounts"
                      :key="'amount-'+amount"
                      :class="['amount-option', { selected: rechargeAmount === amount }]"
                      @click="rechargeAmount = amount"
                  >
                    {{ amount }}元
                  </div>
                  <div class="amount-option custom-amount">
                    <input
                        type="number"
                        placeholder="其他金额"
                        v-model="customRechargeAmount"
                        @focus="rechargeAmount = null"
                        @input="handleCustomAmountInput"
                    />
                  </div>
                </div>

                <h3 class="recharge-title">选择支付方式</h3>

                <div class="payment-methods">
                  <div
                      :class="['payment-method', { selected: paymentMethod === 'alipay' }]"
                      @click="paymentMethod = 'alipay'"
                  >
                    <img src="../../assets/images/payment/alipay.svg" alt="支付宝" class="payment-icon">
                    <span>支付宝</span>
                  </div>
<!--                  <div-->
<!--                      :class="['payment-method', { selected: paymentMethod === 'wechat' }]"-->
<!--                      @click="paymentMethod = 'wechat'"-->
<!--                  >-->
<!--                    <i class="el-icon-wechat"></i>-->
<!--                    <span>微信支付</span>-->
<!--                  </div>-->
                </div>

                <div class="recharge-action">
                  <button
                      class="recharge-button"
                      @click="submitRecharge"
                      :disabled="!canRecharge"
                  >
                    立即充值
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Layout from "@/components/common/layout-fee";
import CommonPagination from "@/views/Ordermange/CommonPagination";
import SlideNotification from '@/components/common/header/SlideNotification.vue';
import { postAnyData, getAnyData } from "@/api/login";
import { getToken } from "@/utils/auth";

export default {
  name: "OrderView",
  components: { SlideNotification, Layout, CommonPagination },
  data() {
    return {
      // General
      currentSection: 'transactions',
      pageSize: 6,
      userBalance: 0,
      showNotification: false,
      notificationMessage: '',
      notificationType: 'success',

      // Orders
      orderData: [],
      orderSearchQuery: '',
      currentPage: 1,
      showOrderDetails: false,
      selectedOrder: {},
      orderLoading: false,
      orderError: null,

      // Transactions
      transactionData: [],
      transactionSearchQuery: '',
      transactionDateRange: '30',
      transactionType: '',
      transactionPage: 1,
      customDateStart: '',
      customDateEnd: '',
      transactionLoading: false,
      transactionError: null,
      summaryData: {
        totalRecharge: 0,
        totalExpense: 0,
        balance: 0
      },

      // Usage
      customUsageDateStart: '',
      customUsageDateEnd: '',
      usageData: [],
      usageDateRange: '30',
      usageFilterGpu: '',
      usageLoading: false,
      usageError: null,
      usagePage: 1,
      gpuModels: [
        { id: 'a100', name: 'NVIDIA A100' },
        { id: 'v100', name: 'NVIDIA V100' },
        { id: 'rtx3090', name: 'NVIDIA RTX 3090' }
      ],

      // Recharge
      rechargeAmounts: [100, 200, 500, 1000, 2000],
      rechargeAmount: 100,
      customRechargeAmount: null,
      paymentMethod: 'alipay',
      rechargeLoading: false
    };
  },

  computed: {
    // Orders
    filteredOrderData() {
      if (!this.orderSearchQuery) return this.orderData;

      const query = this.orderSearchQuery.toLowerCase();
      return this.orderData.filter(order =>
          order.order_number.toLowerCase().includes(query)
      );
    },

    paginatedOrders() {
      const startIndex = (this.currentPage - 1) * this.pageSize;
      const endIndex = startIndex + this.pageSize;
      return this.filteredOrderData.slice(startIndex, endIndex);
    },

    currentOrderTotal() {
      return this.filteredOrderData.length;
    },

    // Transactions
    filteredTransactionData() {
      let filtered = [...this.transactionData];

      // Filter by date range
      if (this.transactionDateRange !== 'custom') {
        const days = parseInt(this.transactionDateRange);
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - days);

        filtered = filtered.filter(transaction => {
          const transactionDate = new Date(transaction.created_at);
          return transactionDate >= cutoffDate;
        });
      } else if (this.customDateStart && this.customDateEnd) {
        const startDate = new Date(this.customDateStart);
        const endDate = new Date(this.customDateEnd);
        endDate.setHours(23, 59, 59, 999); // End of the day

        filtered = filtered.filter(transaction => {
          const transactionDate = new Date(transaction.created_at);
          return transactionDate >= startDate && transactionDate <= endDate;
        });
      }

      // Filter by type
      if (this.transactionType) {
        filtered = filtered.filter(transaction =>
            transaction.type === this.transactionType
        );
      }

      // Filter by search query
      if (this.transactionSearchQuery) {
        const query = this.transactionSearchQuery.toLowerCase();
        filtered = filtered.filter(transaction =>
            transaction.transaction_id.toLowerCase().includes(query)
        );
      }

      return filtered;
    },

    paginatedTransactions() {
      const startIndex = (this.transactionPage - 1) * this.pageSize;
      const endIndex = startIndex + this.pageSize;
      return this.filteredTransactionData.slice(startIndex, endIndex);
    },


// usage
    filteredUsageData() {
      if (!this.usageData || this.usageData.length === 0) return [];

      let filtered = [...this.usageData];

      // 按日期范围过滤
      if (this.usageDateRange !== 'custom') {
        const days = parseInt(this.usageDateRange);
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - days);

        filtered = filtered.filter(record => {
          const recordDate = new Date(record.start_time || record.created_at);
          return recordDate >= cutoffDate;
        });
      } else if (this.customUsageDateStart && this.customUsageDateEnd) {
        const startDate = new Date(this.customUsageDateStart);
        const endDate = new Date(this.customUsageDateEnd);
        endDate.setHours(23, 59, 59, 999); // 设置为当天的最后一刻

        filtered = filtered.filter(record => {
          const recordDate = new Date(record.start_time || record.created_at);
          return recordDate >= startDate && recordDate <= endDate;
        });
      }

      // 按GPU型号过滤
      if (this.usageFilterGpu) {
        filtered = filtered.filter(record =>
            record.gpu_model.toLowerCase().includes(this.usageFilterGpu.toLowerCase())
        );
      }

      return filtered;
    },

    paginatedUsageRecords() {
      const startIndex = (this.usagePage - 1) * this.pageSize;
      const endIndex = startIndex + this.pageSize;
      return this.filteredUsageData.slice(startIndex, endIndex);
    },

    // Recharge
    canRecharge() {
      const amount = this.getRechargeAmount();
      return amount && amount > 0 && this.paymentMethod && !this.rechargeLoading;
    }
  },

  watch: {
    $route(to) {
      // 当路由变化时检查activeTab参数
      const activeTab = to.query.activeTab;
      if (activeTab && ['orders', 'transactions', 'usage', 'recharge'].includes(activeTab)) {
        this.currentSection = activeTab;
      }
    },
    currentSection(newVal) {
      if (newVal === 'transactions') {
        this.fetchTransactions();
        this.fetchSummaryData();
      } else if (newVal === 'orders') {
        this.fetchOrders();
      } else if (newVal === 'recharge') {
        this.fetchUserBalance();
      }else if (newVal === 'transactions') {
        this.fetchTransactions();
        // this.currentSection = 'transactions';
        this.loadSectionData('transactions');
      }
    }
  },

  created() {
    this.fetchUserBalance();
    this.fetchTransactions();
    const activeTab = this.$route.query.activeTab || 'transactions';
    // console.log("activetab",activeTab)
    this.currentSection = activeTab;

    // 根据当前激活的标签页加载对应数据
    this.loadSectionData(activeTab);
    this.$watch(
        () => this.$route.query,
        (newQuery) => {
          if (newQuery.activeTab && ['orders', 'transactions', 'usage', 'recharge'].includes(newQuery.activeTab)) {
            this.currentSection = newQuery.activeTab;
          }
        },
        { immediate: true }
    );
    this.fetchOrders().then(() => {
      // 默认按创建时间排序
      this.sortBy('created_at');

      // 检查URL中的activeTab参数
      const activeTab = this.$route.query.activeTab;
      if (activeTab && ['orders', 'transactions', 'usage', 'recharge'].includes(activeTab)) {
        this.currentSection = activeTab;
      }
    });
    this.fetchSummaryData();
  },
  beforeDestroy() {
    this.$emit('refresh-header')
  },

  methods: {

    showNotificationMessage(message, type = 'info') {
      this.notificationMessage = message;
      this.notificationType = type;
      this.showNotification = true;
    },

    changeSection(section) {
      // 更新当前标签页
      this.currentSection = section;

      // 更新 URL，确保刷新后仍然能恢复
      this.$router.replace({
        query: {
          ...this.$route.query, // 保留其他查询参数
          activeTab: section,   // 更新 activeTab
        },
      });

      // 加载对应数据
      if (section === 'orders') {
        this.fetchOrders();
      } else if (section === 'transactions') {
        this.fetchTransactions();
        this.fetchSummaryData();
      } else if (section === 'usage') {
        this.fetchUsageData();
      } else if (section === 'recharge') {
        this.fetchUserBalance();
      }
    },
    loadSectionData(section) {
      switch(section) {
        case 'orders':
          this.fetchOrders();
          break;
        case 'transactions':
          this.fetchTransactions();
          this.fetchSummaryData();
          break;
        case 'usage':
          this.fetchUsageData();
          break;
        case 'recharge':
          this.fetchUserBalance();
          break;
      }},
    // 获取用户余额
    async fetchUserBalance() {
      try {
        const response = await postAnyData("/logout/cilent/getInfo");
        if (response.data.code === 200) {
          this.userBalance = response.data.data.balance || 0;
          this.$emit('refresh-header')
        }
      } catch (error) {
        this.$message.error('获取用户余额失败');
      }
    },

    applyUsageCustomDateRange() {
      if (!this.customUsageDateStart || !this.customUsageDateEnd) {
        this.$message.error('请选择开始和结束日期');
        return;
      }

      const startDate = new Date(this.customUsageDateStart);
      const endDate = new Date(this.customUsageDateEnd);

      if (startDate > endDate) {
        this.$message.error('开始日期不能晚于结束日期');
        return;
      }

      this.usagePage = 1; // 重置到第一页
      this.fetchUsageData(); // 重新获取数据
    },

    // 格式化日期时间（带时区处理）
    formatDateTimeusage(dateTimeString) {
      if (!dateTimeString) return '--';

      try {
        const date = new Date(dateTimeString);
        // 转换为本地时间字符串
        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          hour12: false
        }).replace(/\//g, '-');
      } catch (e) {
        return '--';
      }
    },

    // 计算运行时长（带时区处理）
    calculateDuration(startTime, endTime) {
      if (!startTime) return "0分钟";

      try {
        const start = new Date(startTime);
        const end = endTime ? new Date(endTime) : new Date();

        // 检查时间是否有效
        if (isNaN(start.getTime())) return "时间无效";
        if (endTime && isNaN(end.getTime())) return "时间无效";

        // 如果开始时间在未来
        if (start > new Date()) {
          return "未开始";
        }

        // 如果结束时间在未来（仍在运行中）
        if (!endTime || end > new Date()) {
          const diffMs = new Date() - start;
          const hours = Math.floor(diffMs / (1000 * 60 * 60));
          const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

          return `${hours}小时${minutes}分钟 `;
        }

        // 正常计算时长
        const diffMs = end - start;
        const hours = Math.floor(diffMs / (1000 * 60 * 60));
        const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

        if (hours > 0) {
          return `${hours}小时${minutes}分钟`;
        }
        return `${minutes}分钟`;
      } catch (e) {
        return "--";
      }
    },



    navigateToRecharge() {
      // 使用 replace 而不是 push 避免路由历史堆积
      this.$router.replace({
        path: '/userorder',
        query: {
          activeTab: 'recharge',
          timestamp: Date.now() // 确保每次导航都是唯一的
        }
      });
    },

    // 从后端获取订单数据
    async fetchOrders() {
      this.orderLoading = true;
      this.orderError = null;
      try {
        const response = await postAnyData("/system/order/getOrderAndProduct");

        // 添加排序逻辑
        this.orderData = response.data.data.map(order => ({
          id: order.name,
          order_number: order.order_name,
          payment_status: this.mapOrderStatus(order.order_staus),
          payment_method: order.order_payment_method || '余额支付',
          total_price: order.order_price,
          unit_price: this.calculateUnitPrice(order),
          duration: order.order_buy_time,
          created_at: order.create_time,
          gpu_model: order.name || '未知型号',
          region: order.Region || '未知地区',
          gpu_count: order.graphics_card_number || 0,
          video_memory: order.video_memory || '未知',
          cpu_cores: order.gpu_nuclear_number || 0,
          system_disk: order.system_disk || '未知',
          cloud_disk: order.data_disk || '未知',
          memory: order.internal_memory || '未知',
          rawData: order
        })).sort((a, b) => {
          // 按创建时间降序排序（最新的在前）
          return new Date(b.created_at) - new Date(a.created_at);
        });
      } catch (error) {
        this.orderError = '获取订单数据失败，请稍后重试';
        // this.$message.error(this.orderError);
      } finally {
        this.orderLoading = false;
      }
    },

    // 获取收支明细数据
    async fetchTransactions() {
      this.transactionLoading = true;
      this.transactionError = null;
      try {
        const response = await postAnyData("/system/order/getConsumptionOrder");
        const rawData = response.data.data;

        // 用map去重，确保不会因为相同order_id重复
        const uniqueMap = new Map();
        rawData.forEach(transaction => {
          // 用topup_id或gpu_order_id作为唯一主键
          const uniqueId = transaction.topup_id || transaction.gpu_order_id;

          if (!uniqueMap.has(uniqueId)) {
            uniqueMap.set(uniqueId, {
              transaction_id: transaction.topup_order_id || transaction.gpu_order_name,
              type: transaction.source_table === "收入" ? 'income' : 'expense',
              pay_type: this.mapPayType(transaction.source_table),
              amount: transaction.topup_topup || transaction.gpu_order_price,
              created_at: transaction.create_time,
              payment_channel: transaction.payment_method || '未知',
              status: this.mapTransactionStatus(transaction.order_staus),
              description: transaction.description || '无'
            });
          }
        });

        this.transactionData = Array.from(uniqueMap.values());
      } catch (error) {
        this.transactionLoading = '暂无记录';
      } finally {
        this.transactionLoading = false;
      }
    },


    async fetchUsageData() {
      this.usageLoading = true;
      this.usageError = null;
      try {
        let params = {};

        // 添加日期范围参数
        if (this.usageDateRange === 'custom' && this.customUsageDateStart && this.customUsageDateEnd) {
          params.start_date = this.customUsageDateStart;
          params.end_date = this.customUsageDateEnd;
        } else if (this.usageDateRange !== 'custom') {
          // 处理预设日期范围
          const days = parseInt(this.usageDateRange);
          const cutoffDate = new Date();
          cutoffDate.setDate(cutoffDate.getDate() - days);
          params.start_date = cutoffDate.toISOString().split('T')[0];
        }

        // 添加GPU过滤参数
        if (this.usageFilterGpu) {
          params.gpu_model = this.usageFilterGpu;
        }
        // 这里应该改为实际的API调用，目前使用模拟数据
        const response = await postAnyData("/system/order/getProductResumption");

        // 如果API已准备好，应该使用实际数据
        // this.usageData = response.data.data;

        // 临时使用模拟数据
        const mockData = [

        ];

        this.usageData = mockData;
        return this.usageData;

      } catch (error) {
        this.usageError = '加载使用记录失败，请稍后重试';
        // this.$message.error(this.usageError);
        return []; // 出错时返回空数组
      } finally {
        this.usageLoading = false;
      }
    },





    // 获取汇总数据
    // 获取汇总数据 (去重版)
    async fetchSummaryData() {
      try {
        const response = await postAnyData("/system/order/getConsumptionOrder");
        const transactions = response.data.data || [];

        // 先做去重，避免重复计算
        const uniqueMap = new Map();
        transactions.forEach(t => {
          const uniqueId = t.topup_id || t.gpu_order_id;
          if (!uniqueMap.has(uniqueId)) {
            uniqueMap.set(uniqueId, t);
          }
        });

        const uniqueTransactions = Array.from(uniqueMap.values());

        // 计算总充值
        const totalRecharge = uniqueTransactions
            .filter(t => t.source_table === "收入")
            .reduce((sum, t) => sum + (parseFloat(t.topup_topup) || 0), 0);

        // 计算总消费
        const totalExpense = uniqueTransactions
            .filter(t => t.source_table === "支出")
            .reduce((sum, t) => sum + (parseFloat(t.gpu_order_price) || 0), 0);

        this.summaryData = {
          totalRecharge,
          totalExpense,
          balance: this.userBalance
        };
      } catch (error) {
        this.$message.error('获取账户汇总数据失败');
      }
    },

    // 映射订单状态
    mapOrderStatus(status) {
      const statusMap = {
        '1': 'paid',     // 支付成功
        '2': 'failed',   // 支付失败
        '3': 'unpaid'    // 未支付
      };
      return statusMap[status] || status;
    },

    // 映射交易类型
    mapPayType(type) {
      return type === "收入" ? '充值' : '消费';
    },

    // 映射交易状态
    mapTransactionStatus(status) {
      const statusMap = {
        '1': 'success',    // 成功
        '2': 'failed',     // 失败
        '3': 'processing'  // 处理中
      };
      return statusMap[status] || status;
    },

    // 计算单价（根据商品信息和订单时长）
    calculateUnitPrice(order) {
      if (!order.computing) return order.order_price;

      // 根据购买单位选择对应的价格
      switch(order.order_unit) {
        case 'hour':
          return order.computing.price_bour;
        case 'day':
          return order.computing.price_day;
        case 'month':
          return order.computing.price_mouth;
        case 'year':
          return order.computing.price_year;
        default:
          return order.order_price;
      }
    },

    // Formatting methods
    formatPrice(price) {
      if (isNaN(price)) return '0.00';
      return parseFloat(price).toFixed(2);
    },

    formatDate(dateString) {
      return new Date(dateString).toLocaleDateString();
    },

    formatDateTime(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleString();
    },

    // Order methods
    getPaymentStatusClass(status) {
      return {
        'paid': 'status-success',
        'unpaid': 'status-pending',
        'refunded': 'status-info',
        'failed': 'status-error',
        'cancelled': 'status-warning'
      }[status] || '';
    },

    getPaymentStatusText(status) {
      return {
        'paid': '已支付',
        'unpaid': '未支付',
        'refunded': '已退款',
        'failed': '支付失败',
        'cancelled': '已取消'
      }[status] || status;
    },

    getPaymentMethodClass(method) {
      return {
        '支付宝': 'payment-alipay',
        '微信支付': 'payment-wechat',
        '银行卡': 'payment-bank',
        '余额支付': 'payment-balance',
        '未支付': 'payment-warning'
      }[method] || '';
    },

    getPaymentMethodText(method) {
      return {
        'alipay': '支付宝',
        'wechat': '微信支付',
        'bank': '银行卡',
        'balance': '余额'
      }[method] || method;
    },

    searchOrders() {
      this.currentPage = 1;
    },

    clearOrderSearch() {
      this.orderSearchQuery = '';
      this.currentPage = 1;
    },

    sortBy(field) {
      if (field === 'created_at') {
        this.orderData.sort((a, b) => {
          return new Date(b[field]) - new Date(a[field]);
        });
      } else {
        this.orderData.sort((a, b) => {
          return a[field] > b[field] ? 1 : -1;
        });
      }
    },

    viewOrderDetails(order) {
      this.selectedOrder = order;
      this.showOrderDetails = true;
      this.$nextTick(() => {
        document.querySelector('.order-details').scrollIntoView({ behavior: 'smooth' });
      });
    },

    showOrderList() {
      this.showOrderDetails = false;
      window.scrollTo({ top: 0, behavior: 'smooth' });
    },

    goToPage(page) {
      this.currentPage = page;
    },

    handlePageSizeChange(size) {
      this.pageSize = size;
      this.currentPage = 1;
    },

    // Transaction methods
    searchTransactions() {
      this.transactionPage = 1;
    },

    applyCustomDateRange() {
      if (this.customDateStart && this.customDateEnd) {
        this.transactionPage = 1;
      }
    },

    getTransactionTypeClass(type) {
      return {
        'income': 'income-type',
        'expense': 'expense-type',
        'refund': 'refund-type'
      }[type] || '';
    },

    getTransactionTypeName(type) {
      return {
        'income': '收入',
        'expense': '支出',
        'refund': '退款'
      }[type] || type;
    },

    getTransactionTypeNamePay(payType) {
      return {
        'income': '收入',
        'expense': '支出',
      }[payType] || payType;
    },

    getTransactionStatusClass(status) {
      return {
        'success': 'status-success',
        'pending': 'status-pending',
        'failed': 'status-error',
        'processing': 'status-info'
      }[status] || '';
    },

    getTransactionStatusName(status) {
      return {
        'success': '成功',
        'pending': '处理中',
        'failed': '失败',
        'processing': '处理中'
      }[status] || status;
    },

    // Usage methods
    getUsageStatusClass(status) {
      return {
        'active': 'status-success',
        'running': 'status-running',
        'about_to_expire': 'status-warning',
        'expired': 'status-error',
        'completed': 'status-complete',
        'paused': 'status-info'
      }[status] || '';
    },

    getUsageStatusText(status) {
      return {
        'active': '可用',
        'running': '使用中',
        'about_to_expire': '即将到期',
        'completed': '已结束',
        'paused': '已暂停'
      }[status] || status;
    },

    renewService(record) {
      this.$message.success(`正在为 ${record.gpu_model} 续费`);
    },

    // Recharge methods
    getRechargeAmount() {
      return this.rechargeAmount || this.customRechargeAmount;
    },

    handleCustomAmountInput() {
      this.rechargeAmount = null;
    },

    async submitRecharge() {
      const amount = this.getRechargeAmount();
      if (!amount || amount <= 0) {
        // this.$message.error('请输入有效的充值金额');
        this.showNotificationMessage('请输入有效的充值金额', 'error');

        return;
      }

      this.rechargeLoading = true;

      try {
        // 调用充值API
        const response = await getAnyData('/yun/scanPay', { amount: amount });
        if (response.data.includes('充值金额不足')) {
          // 提取需要充值的金额（正则表达式匹配数字和小数点）
          this.showNotificationMessage(response.data, 'error');
          return;
        }
        // 处理支付表单
        const tempDiv = document.createElement('div');

        tempDiv.innerHTML = response.data;
        document.body.appendChild(tempDiv);
        const form = tempDiv.querySelector('form');

        // console.log(response)



        if (form) {
          this.showNotificationMessage('正在跳转到支付页面...', 'success');
          form.target = '_blank'; // 设置表单提交目标为新窗口
          form.submit();
          // this.$message.success('正在跳转到支付页面...');

          // 支付成功后刷新余额和交易记录
          setTimeout(() => {
            this.fetchUserBalance();
            this.fetchTransactions();
          }, 3000);
        } else {
          this.showNotificationMessage('支付表单生成失败', 'error');
        }
      } catch (error) {
        this.showNotificationMessage('充值请求失败，请稍后重试', 'error');
      } finally {
        this.rechargeLoading = false;
      }
    }
  }
};
</script>

<style scoped>
.fee-center-container {
  display: flex;
  max-width: 2560px;
  background-color: #f5f7fa;
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

.navigation-sidebar {
  width: 200px;
  background-color: #fff;
  border-right: 1px solid #ebeef5;
  position: fixed;
  height: 100%;
  z-index: 10;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.nav-title {
  font-size: 18px;
  color: #303133;
  padding: 20px 16px;
  margin: 0;
  border-bottom: 1px solid #ebeef5;
}

.nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-item {
  padding: 14px 16px;
  cursor: pointer;
  transition: all 0.3s;
}

.nav-item a {
  display: flex;
  align-items: center;
  color: #303133;
  text-decoration: none;
}

.nav-item i {
  margin-right: 8px;
  font-size: 16px;
}

.nav-item.active {
  background-color: #ecf5ff;
}

.nav-item.active a {
  color: #409eff;
}

.nav-item:hover {
  background-color: #f5f7fa;
}

.main-content {
  flex: 1;
  margin-left: 200px;
  padding: 20px;
}

.tab-content {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  padding: 20px;
  margin-bottom: 20px;
}

.search-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.search-bar {
  position: relative;
  width: 300px;
}

.search-bar input {
  width: 100%;
  height: 36px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 0 30px 0 12px;
  font-size: 14px;
  transition: border-color 0.3s;
}

.search-bar input:focus {
  border-color: #409eff;
  outline: none;
}

.search-button, .clear-button {
  position: absolute;
  right: 8px;
  top: 8px;
  background: none;
  border: none;
  color: #606266;
  cursor: pointer;
}

.clear-button {
  right: 30px;
}

.currency-display {
  font-size: 14px;
  color: #606266;
}

.flow-count {
  margin-left: 16px;
}

.table-container {
  overflow-x: auto;
  margin-bottom: 20px;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th, .data-table td {
  padding: 12px 8px;
  text-align: left;
  font-size: 14px;
  border-bottom: 1px solid #ebeef5;
}

.data-table th {
  color: #909399;
  font-weight: 500;
  white-space: nowrap;
}

.data-table td {
  color: #606266;
}

.data-table th i {
  margin-left: 4px;
  cursor: pointer;
  color: #c0c4cc;
}

.data-table th i:hover {
  color: #409eff;
}

.status-tag {
  width: 10vh;
  text-align: center;
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-success {
  background-color: #f0f9eb;
  color: #67c23a;
}

.status-pending {
  background-color: #fdf6ec;
  color: #e6a23c;
}

.status-error {
  background-color: #fef0f0;
  color: #f56c6c;
}

.status-info {
  background-color: #f4f4f5;
  color: #909399;
}

.status-warning {
  background-color: #fdf6ec;
  color: #e6a23c;
}

.status-running {
  background-color: #ecf5ff;
  color: #409eff;
}
.status-complete {
  background-color: #ecf5ff;
  color: #e6a23c;
}
.payment-method-tag {
  width: 10vh;
  text-align: center;
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.payment-alipay {
  background-color: #e6f7ff;
  color: #1890ff;
}

.payment-wechat {
  background-color: #f6ffed;
  color: #52c41a;
}

.payment-bank {
  background-color: #fff2e8;
  color: #fa8c16;
}

.payment-balance {
  background-color: #f9f0ff;
  color: #722ed1;
}

.payment-warning {
  background-color: #fdf6ec;
  color: #e6a23c;
}

.operation-link {
  color: #409eff;
  cursor: pointer;
  transition: color 0.2s;
}

.operation-link:hover {
  color: #66b1ff;
  text-decoration: underline;
}

.expense-amount {
  color: #e52a2a !important;
  font-weight: bold !important;
}
.income-amount {
  color: #4fc44f !important;
  font-weight: bold !important;
}

.empty-state {
  padding: 40px 0;
  text-align: center;
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.empty-icon {
  font-size: 48px;
  color: #c0c4cc;
  margin-bottom: 16px;
}

.empty-text {
  color: #909399;
  font-size: 14px;
}

.order-details {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  padding: 20px;
  margin-bottom: 20px;
}

.detail-card {
  padding: 20px;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 16px;
}

.detail-title {
  font-size: 18px;
  color: #303133;
  margin: 0;
}

.back-button {
  display: flex;
  align-items: center;
  background: none;
  border: none;
  color: #409eff;
  cursor: pointer;
  font-size: 14px;
  padding: 6px 12px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.back-button:hover {
  background-color: #ecf5ff;
}

.back-button i {
  margin-right: 4px;
}

.detail-content {
  padding: 0 16px;
  max-height: 395px;
}

.detail-subtitle {
  font-size: 16px;
  color: #303133;
  margin: 24px 0 16px;
  font-weight: 500;
}

.detail-section {
  margin-bottom: 24px;
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 16px;
}

.detail-row {
  display: flex;
  margin-bottom: 16px;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-item {
  flex: 1;
  display: flex;
  align-items: center;
}

.detail-label {
  width: 120px;
  color: #909399;
  font-size: 14px;
}

.detail-value {
  color: #303133;
  font-size: 14px;
}

.transaction-summary, .usage-summary {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
}

.summary-card {
  flex: 1;
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 16px;
  margin-right: 16px;
  text-align: center;
  transition: transform 0.2s;
}

.summary-card:hover {
  transform: translateY(-2px);
}

.summary-card:last-child {
  margin-right: 0;
}

.summary-title {
  color: #909399;
  font-size: 14px;
  margin-bottom: 8px;
}

.summary-value {
  color: #303133;
  font-size: 24px;
  font-weight: 500;
}

.summary-value.income {
  color: #67c23a;
}

.summary-value.expense {
  color: #f56c6c;
}

.transaction-type {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.income-type {
  background-color: #f0f9eb;
  color: #67c23a;
}

.expense-type {
  background-color: #fef0f0;
  color: #f56c6c;
}

.refund-type {
  background-color: #ecf5ff;
  color: #409eff;
}

.date-range-picker {
  display: flex;
  align-items: center;
}

.date-range-picker select,
.search-filters select,
.search-filters input {
  height: 36px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 0 12px;
  margin-right: 12px;
  font-size: 14px;
  color: #606266;
  transition: border-color 0.2s;
}

.date-range-picker select:focus,
.search-filters select:focus,
.search-filters input:focus {
  border-color: #409eff;
  outline: none;
}

.custom-date-range {
  display: flex;
  align-items: center;
  margin-left: 12px;
}

.custom-date-range input {
  width: 140px;
  margin-right: 8px;
  height: 36px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 0 12px;
}

.custom-date-range span {
  margin: 0 8px;
  color: #606266;
}

.apply-button {
  background-color: #409eff;
  color: #fff;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.apply-button:hover {
  background-color: #66b1ff;
}

.account-balance {
  margin-bottom: 24px;
}

.balance-info {
  display: flex;
  align-items: baseline;
  justify-content: flex-start;
  background-color: #fff;
  padding: 16px 24px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  width: 100%;
}

.balance-label {
  font-size: 20px;
  color: #606266;
  margin-right: 12px;
}

.balance-value {
  font-size: 22px;
  color: #409eff;
  font-weight: 500;
}

.recharge-options {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
}

.recharge-title {
  font-size: 16px;
  color: #303133;
  margin: 0 0 16px 0;
  font-weight: 500;
}

.amount-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 24px;
}

.amount-option {
  width: calc(20% - 16px);
  min-width: 100px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  color: #606266;
  transition: all 0.2s;
}

.amount-option:hover {
  border-color: #c6e2ff;
  color: #409eff;
}

.amount-option.selected {
  border-color: #409eff;
  color: #409eff;
  background-color: #ecf5ff;
}

.custom-amount {
  width: calc(20% - 16px);
  min-width: 100px;
}

.custom-amount input {
  width: 100%;
  height: 100%;
  border: none;
  outline: none;
  text-align: center;
  font-size: 16px;
  color: #606266;
}

.payment-methods {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
}

.payment-method {
  width: 140px;
  height: 60px;
  display: flex;
  align-items: center;  /* 垂直居中 */
  justify-content: center;  /* 水平居中 */
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  padding: 0 16px;  /* 添加内边距 */
}

.payment-icon {
  width: 24px;
  height: 24px;
  margin-right: 8px;  /* 图标和文字之间的间距 */
}

.payment-text {
  font-size: 14px;
  color: #606266;
}

.payment-method:hover {
  border-color: #c6e2ff;
}

.payment-method.selected {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.payment-method.selected .payment-icon,
.payment-method.selected .payment-text {
  color: #409eff;
}

.recharge-action {
  display: flex;
  justify-content: center;
  margin-top: 32px;
}

.recharge-button {
  width: 200px;
  height: 40px;
  background-color: #409eff;
  color: #fff;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s;
}

.recharge-button:hover {
  background-color: #66b1ff;
}

.recharge-button:disabled {
  background-color: #a0cfff;
  cursor: not-allowed;
}

.loading-state, .error-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  margin-bottom: 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.loading-state i {
  font-size: 18px;
  margin-right: 8px;
  color: #409eff;
  animation: rotating 2s linear infinite;
}

.error-state i {
  font-size: 18px;
  margin-right: 8px;
  color: #f56c6c;
}

.error-state button {
  margin-left: 12px;
  padding: 4px 12px;
  background-color: #f56c6c;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.payment-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .navigation-sidebar {
    width: 180px;
  }

  .main-content {
    margin-left: 180px;
  }

  .amount-option {
    width: calc(25% - 16px);
  }
}

@media (max-width: 768px) {
  .navigation-sidebar {
    width: 160px;
  }

  .main-content {
    margin-left: 160px;
  }

  .search-section {
    flex-direction: column;
    align-items: flex-start;
  }

  .search-bar {
    width: 100%;
    margin-bottom: 12px;
  }

  .amount-option {
    width: calc(33.33% - 16px);
  }
}

@media (max-width: 576px) {
  .navigation-sidebar {
    width: 100%;
    position: static;
    height: auto;
    margin-bottom: 20px;
  }

  .main-content {
    margin-left: 0;
  }

  .transaction-summary, .usage-summary {
    flex-direction: column;
  }

  .summary-card {
    margin-right: 0;
    margin-bottom: 16px;
  }

  .summary-card:last-child {
    margin-bottom: 0;
  }

  .amount-option {
    width: calc(50% - 16px);
  }

  .payment-methods {
    flex-direction: column;
  }

  .payment-method {
    width: 100%;
  }
}
</style>
