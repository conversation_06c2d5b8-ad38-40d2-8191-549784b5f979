"use strict";(self["webpackChunkportal_ui"]=self["webpackChunkportal_ui"]||[]).push([[9198],{8723:function(o,t,l){l.r(t);var n=new URL(l(1404),l.b),p=new URL(l(4117),l.b),e=new URL(l(404),l.b),r=new URL(l(291),l.b),i=new URL(l(5680),l.b),s=new URL(l(2645),l.b),f=new URL(l(5959),l.b),a='<h1 id="容器化部署-dailyhot">容器化部署 DailyHot</h1> <p><font style="color:#020817">本指南详细阐述了在天工开物平台上，高效部署与使用 DailyHot 项目的技术方案，旨在解决用户一站式浏览全网多平台热榜信息、开发者便捷调用热榜 API 的需求。DailyHot 是一个聚合全网几十个平台（如微博、知乎、B 站等）热榜信息的项目，提供 Web 界面直接浏览与 API 接口开放调用两种使用方式。</font></p> <h2 id="1在天工开物上运行-dailyhot"><strong>1.在天工开物上运行 DailyHot</strong></h2> <p><font style="color:#020817">天工开物平台提供预构建的 DailyHot 容器镜像，用户无需本地复杂环境配置，可快速完成部署并启用服务。以下是详细部署步骤：</font></p> <h3 id="11-创建部署服务"><strong>1.1 创建部署服务</strong></h3> <p><font style="color:#020817">登录<a href="https://tiangongkaiwu.top/portal/#/console">天工开物控制台</a>，在控制台首页点击“弹性部署服务”进入管理页面。</font></p> <p><img src="'+n+'" alt=""></p> <h3 id="12-选择-gpu-型号"><strong>1.2 选择 GPU 型号</strong></h3> <p><font style="color:#020817">根据实际需求选择 GPU 型号：</font></p> <ul> <li>初次使用或调试阶段，推荐配置单张 NVIDIA RTX 4090 GPU（性价比高，满足中小规模热榜聚合需求）</li> </ul> <p><img src="'+p+'" alt=""></p> <p><font style="color:#020817">若需支持更多平台热榜同步或高并发 API 调用，可升级为多卡配置，提升处理吞吐量。</font></p> <h3 id="13-选择预制镜像"><strong>1.3 选择预制镜像</strong></h3> <p><font style="color:#020817">在“服务配置”模块切换至“预制服务”选项卡，选择 DailyHot 官方镜像。该镜像已集成热榜抓取、数据清洗、API 服务等核心功能依赖。</font></p> <p><img src="'+e+'" alt=""></p> <h3 id="14-部署并访问服务"><strong>1.4 部署并访问服务</strong></h3> <p><font style="color:#020817">点击“部署服务”，平台将自动拉取镜像并启动容器。</font></p> <p><img src="'+r+'" alt=""></p> <p><font style="color:#020817">部署完成后，在“部署详情 - 公开访问”中找到端口为 80（Web 界面）或 6688（API 接口）的公网访问链接，点击即可在浏览器中使用 DailyHot 的 Web 界面，或通过该地址调用 API 服务。</font></p> <hr> <h2 id="2快速上手"><strong>2.快速上手</strong></h2> <h3 id="21-浏览热榜信息（web-界面-80-端口）"><strong>2.1 浏览热榜信息（Web 界面 80 端口）</strong></h3> <p><font style="color:#020817">通过公网链接访问 DailyHot Web 界面，默认展示微博、知乎、B 站等主流平台当日热榜。</font></p> <p><img src="'+i+'" alt=""></p> <h3 id="22-调用-api-接口（6688-端口）"><strong>2.2 调用 API 接口（6688 端口）</strong></h3> <p><font style="color:#020817">通过 6688 端口公网地址调用 API，支持以下核心接口：</font></p> <p><img src="'+s+'" alt=""></p> <ul> <li><font style="color:#2f8ef4;background-color:#e7e9e8">/all/{platform}</font>：获取指定平台热榜（如<font style="color:#2f8ef4;background-color:#e7e9e8">/all/weibo</font>返回微博热榜）</li> <li><font style="color:#2f8ef4;background-color:#e7e9e8">/all</font>：获取所有平台当日热榜合集</li> </ul> <p><strong>调用示例（CURL）</strong>：</p> <pre><code class="language-powershell">curl -X GET &quot;https://您的公网地址/all/zhihu&quot;\n</code></pre> <p><font style="color:#020817">返回结果为 JSON 格式，包含热榜标题、链接、热度值等信息。</font></p> <h3 id="23-基础配置设置"><strong>2.3 基础配置设置</strong></h3> <p><font style="color:#020817">首次使用可通过 Web 界面“设置”模块调整基础参数：</font></p> <p><img src="'+f+'" alt=""></p> <ul> <li>热榜更新频率：默认每 30 分钟同步一次，可自定义为 10 分钟（高频）或 1 小时（低频）</li> <li>平台开关：关闭无需关注的平台（如“豆瓣”“虎扑”），减少资源占用</li> </ul> <p><br><br> <font style="color:#b2b2b2">最后更新于: 2025/6/30 14:08</font></p> ';t["default"]=a},404:function(o,t,l){o.exports=l.p+"img/dailyhot3.ee239cc9.png"},291:function(o,t,l){o.exports=l.p+"img/dailyhot4.818ef0e0.png"},5680:function(o,t,l){o.exports=l.p+"img/dailyhot5.e4a0c92f.png"},2645:function(o,t,l){o.exports=l.p+"img/dailyhot6.fd3a6954.png"},5959:function(o,t,l){o.exports=l.p+"img/dailyhot7.25990a01.png"},1404:function(o,t,l){o.exports=l.p+"img/universal1.9a1b3f4b.png"},4117:function(o,t,l){o.exports=l.p+"img/universal2.4306636e.png"}}]);
//# sourceMappingURL=docs5.5fea48b6.js.map