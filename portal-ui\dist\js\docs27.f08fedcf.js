"use strict";(self["webpackChunkportal_ui"]=self["webpackChunkportal_ui"]||[]).push([[6193],{7507:function(o,t,n){n.r(t);var e=new URL(n(1404),n.b),p=new URL(n(4117),n.b),i=new URL(n(6590),n.b),f=new URL(n(9794),n.b),l=new URL(n(7594),n.b),r=new URL(n(1909),n.b),s=new URL(n(8037),n.b),c='<h1 id="容器化部署-stablediffusion15-webui-应用"><font style="color:#020817">容器化部署 StableDiffusion1.5-WebUI 应用</font></h1> <h2 id="1-部署步骤"><font style="color:#020817">1 部署步骤</font></h2> <p><font style="color:#020817">我们提供了构建完毕的 Stable-Diffusion-WebUI 镜像，您可以直接部署使用。</font></p> <h3 id="11-访问天工开物控制台，点击新增部署。"><font style="color:#020817">1.1 访问</font><a href="https://tiangongkaiwu.top/#/console">天工开物控制台</a><font style="color:#020817">，点击新增部署。</font></h3> <p><img src="'+e+'" alt=""></p> <h3 id="12-基于自身需要进行配置，参考配置为单卡-4090-和-1-个节点（初次使用进行调试）。"><font style="color:#020817">1.2 基于自身需要进行配置，参考配置为单卡 4090 和 1 个节点（初次使用进行调试）。</font></h3> <p><img src="'+p+'" alt=""></p> <h3 id="13-选择相应预制镜像"><font style="color:#020817">1.3 选择相应预制镜像</font></h3> <p><img src="'+i+'" alt=""></p> <h3 id="14-点击部署服务，耐心等待节点拉取镜像并启动。"><font style="color:#020817">1.4 点击部署服务，耐心等待节点拉取镜像并启动。</font></h3> <p><img src="'+f+'" alt=""></p> <h3 id="15-节点启动后，你所在任务详情页中看到的内容可能如下："><font style="color:#020817">1.5 节点启动后，你所在“任务详情页”中看到的内容可能如下：</font></h3> <p><img src="'+l+'" alt=""></p> <h3 id="16-我们可以点击快捷访问下方7860端口的链接，测试-gradio-运行情况"><font style="color:#020817">1.6 我们可以点击快捷访问下方“7860”端口的链接，测试 Gradio 运行情况</font></h3> <p><font style="color:#020817">接下来填写 prompt，描述我们希望图片的内容。</font></p> <p><img src="'+r+'" alt=""></p> <p><font style="color:#020817">最后点击生成按钮，接下来我们耐心稍等片刻，可以看到图片已经生成。</font></p> <p><img src="'+s+'" alt=""></p> <p><br><br> <font style="color:#b2b2b2">最后更新于: 2025/6/18 11:29</font></p> ';t["default"]=c},1404:function(o,t,n){o.exports=n.p+"img/universal1.9a1b3f4b.png"},4117:function(o,t,n){o.exports=n.p+"img/universal2.4306636e.png"},6590:function(o,t,n){o.exports=n.p+"img/webui1-5v3.c7b4e60c.png"},9794:function(o,t,n){o.exports=n.p+"img/webui1-5v4.30d2bf24.png"},7594:function(o,t,n){o.exports=n.p+"img/webui1-5v5.28f33845.png"},1909:function(o,t,n){o.exports=n.p+"img/webui1-5v6.f8aee07b.png"},8037:function(o,t,n){o.exports=n.p+"img/webui1-5v7.88595262.png"}}]);
//# sourceMappingURL=docs27.f08fedcf.js.map