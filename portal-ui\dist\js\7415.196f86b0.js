"use strict";(self["webpackChunkportal_ui"]=self["webpackChunkportal_ui"]||[]).push([[7415],{4300:function(t,i,a){a.d(i,{Z:function(){return u}});var s=function(){var t=this,i=t._self._c;return i("main",{staticClass:"page-wrapper"},[t._t("default"),i("Mider"),i("chatAi"),i("Footer")],2)},e=[],n=a(2711),o=a(3644),c=a(9484),l={name:"Layout",components:{Footer:n.Z,Mider:o.Z,chatAi:c.Z}},r=l,v=a(1001),d=(0,v.Z)(r,s,e,!1,null,"0ce69a34",null),u=d.exports},2711:function(t,i,a){a.d(i,{Z:function(){return r}});var s=function(){var t=this,i=t._self._c;return i("div",{staticClass:"footer"},[i("div",{staticClass:"footer-content"},[i("div",{staticClass:"footer-column"},[i("div",{staticClass:"footer-title"},[t._v("售前咨询热线")]),i("div",{staticClass:"footer-phone"},[t._v("13913283376")]),i("div",{staticClass:"footer-links"},[i("a",{on:{click:function(i){return t.navigateTo("/index")}}},[i("div",{staticClass:"footer-link"},[t._v("首页")])]),i("a",{on:{click:function(i){return t.navigateTo("/product")}}},[i("div",{staticClass:"footer-link"},[t._v("AI算力市场")])])])]),i("div",{staticClass:"footer-column"},[i("div",{staticClass:"footer-title"},[t._v("支持与服务")]),i("div",{staticClass:"footer-links"},[i("div",{staticClass:"footer-link"},[t._v("联系我们")]),i("a",{on:{click:function(i){return t.navigateTo("/help")}}},[i("div",{staticClass:"footer-link"},[t._v("帮助文档")])]),i("div",{staticClass:"footer-link"},[t._v("公告")]),i("div",{staticClass:"footer-link"},[t._v("提交建议")])])]),t._m(0),t._m(1),t._m(2)]),t._m(3)])},e=[function(){var t=this,i=t._self._c;return i("div",{staticClass:"footer-column"},[i("div",{staticClass:"footer-title"},[t._v("关注天工开物")]),i("div",{staticClass:"footer-links"},[i("div",{staticClass:"footer-link"},[t._v("关注天工开物")]),i("div",{staticClass:"footer-link"},[t._v("天工开物公众号")]),i("div",{staticClass:"footer-link"},[t._v("天工开物微博")]),i("div",{staticClass:"footer-link"},[t._v("天工开物支持与服务")])])])},function(){var t=this,i=t._self._c;return i("div",{staticClass:"footer-column"},[i("div",{staticClass:"footer-title"},[t._v("联系专属客服")]),i("div",{staticClass:"footer-qrcode"},[i("img",{attrs:{src:a(46),alt:"联系专属客服二维码"}})])])},function(){var t=this,i=t._self._c;return i("div",{staticClass:"footer-column"},[i("div",{staticClass:"footer-title"},[t._v("官方公众号")]),i("div",{staticClass:"footer-qrcode"},[i("img",{attrs:{src:a(46),alt:"官方公众号二维码"}})])])},function(){var t=this,i=t._self._c;return i("div",{staticClass:"footer-bottom"},[i("div",{staticClass:"footer-copyright"},[i("a",{staticStyle:{color:"inherit","text-decoration":"none"},attrs:{href:"https://beian.miit.gov.cn",target:"_blank"}},[t._v(" 苏ICP备2025171841号-1 ")]),t._v(" 天工开物智能科技（苏州）有限公司 ALL RIGHTS RESERVED. ")])])}],n=(a(7658),{name:"Footer",data(){return{}},methods:{navigateTo(t){this.currentPath&&this.currentPath!==t?(this.previousActivePath=this.currentPath,this.$nextTick((()=>{const i=document.querySelectorAll(".nav-link, .btn-login");i.forEach((i=>{(i.classList.contains("active")||"/login"===t&&i.classList.contains("btn-login"))&&!i.classList.contains("active-exit")&&(i.classList.add("active-exit"),setTimeout((()=>{i.classList.remove("active-exit")}),300))})),this.currentPath=t}))):this.currentPath=t,this.$route.path===t?this.$nextTick((()=>{window.scrollTo({top:0,behavior:"instant"}),this.$router.go(0)})):(this.$router.push(t),window.scrollTo({top:0,behavior:"instant"}))}}}),o=n,c=a(1001),l=(0,c.Z)(o,s,e,!1,null,"2d6e9349",null),r=l.exports},442:function(t,i,a){a.r(i),a.d(i,{default:function(){return v}});var s=function(){var t=this,i=t._self._c;return i("Layout",[i("div",{staticClass:"layout-container",staticStyle:{width:"100%"}},[i("div",{staticClass:"page-header"},[i("div",{staticClass:"am-container"},[i("h1",{staticClass:"page-header-title"},[t._v("客户案例")])])]),i("div",{staticClass:"breadcrumb-box"},[i("div",{staticClass:"am-container"},[i("ol",{staticClass:"am-breadcrumb"},[i("li",[i("router-link",{attrs:{to:"/"}},[t._v("首页")])],1),i("li",{staticClass:"am-active"},[t._v("客户案例")])])])])]),i("div",{staticClass:"section example"},[i("div",{staticClass:"container",staticStyle:{"max-width":"1160px",margin:"0 auto"}},[i("div",{staticClass:"section--header"},[i("h2",{staticClass:"section--title"},[t._v("全球首创 自主创新")]),i("p",{staticClass:"section--description"},[t._v(" Enterplorer Studio是一套面向企业级移动信息化建设的开发平台。集聚开发、测试、 "),i("br"),t._v("打包、发布于一体的移动化开发综合平台。 ")])]),i("div",{staticClass:"example-container"},[i("div",{staticClass:"am-tabs"},[i("ul",{staticClass:"am-tabs-nav am-nav am-nav-tabs am-g"},t._l(t.tabList,(function(a,s){return i("li",{key:s,staticClass:"am-u-md-2",class:t.tabIndex===s?"am-active":"",on:{click:function(i){return i.preventDefault(),t.changeTab(s)}}},[i("a",{attrs:{href:"#"}},[i("i",{class:a.icon}),t._v(t._s(a.name))])])})),0),i("div",{staticClass:"tabs"},t._l(t.list,(function(t,a){return i("div",{key:a,staticClass:"tab"},[i("img",{attrs:{src:t,alt:""}})])})),0)])])])])])},e=[],n=(a(7658),a(4300)),o={name:"ExampleView",components:{Layout:n.Z},data(){return{tabIndex:0,tabList:[{id:1,name:"主要案例",icon:"am-icon-map-o"},{id:1,name:"客户案例一",icon:"am-icon-scribd"},{id:1,name:"客户案例二",icon:"am-icon-odnoklassniki"},{id:1,name:"客户案例三",icon:"am-icon-building-o"},{id:1,name:"客户案例四",icon:"am-icon-hand-scissors-o"},{id:1,name:"客户案例五",icon:"am-icon-camera"}],list:[]}},mounted(){this.findExampleByExampleType(0)},methods:{changeTab(t){this.tabIndex=t,this.findExampleByExampleType(t)},findExampleByExampleType(t){this.getRequest(`/findExampleByExampleType/${t}`).then((t=>{if(t){const i=t.data.data,a=[];for(let t=1;t<17;t++)null!=i["image"+t]&&a.push(i["image"+t]);this.list=a}}))}}},c=o,l=a(1001),r=(0,l.Z)(c,s,e,!1,null,"3ae80592",null),v=r.exports}}]);
//# sourceMappingURL=7415.196f86b0.js.map