<template>
  <Layout>
    <!-- 顶部Banner -->
    <div class="about-banner">
      <!-- 背景图层 -->
      <div
        class="about-banner-bg"
        style="background: url('images/earth.gif') center/cover; opacity: 0.7; z-index: 1; position: absolute; top: 0; left: 0; right: 0; bottom: 0;"
      ></div>

      <!-- 动态网格背景 -->
      <!-- <div class="grid-background"></div> -->

      <!-- 粒子效果 -->
      <div class="particles-container">
        <div class="particle" v-for="n in 50" :key="n" :style="getParticleStyle()"></div>
      </div>

      <!-- 数据流效果 -->
      <div class="data-streams">
        <div class="data-stream" v-for="n in 8" :key="n"></div>
      </div>

      <!-- 算力节点连接线 -->
      <!-- <div class="network-nodes">
        <div class="node" v-for="n in 12" :key="n" :style="getNodeStyle()">
          <div class="node-pulse"></div>
        </div>
        <svg class="connection-lines" viewBox="0 0 100 100">
          <line v-for="line in connectionLines" :key="line.id"
                :x1="line.x1" :y1="line.y1" :x2="line.x2" :y2="line.y2"
                class="connection-line"></line>
        </svg>
      </div> -->

      <!-- 光效层 -->
      <div class="light-effects">
        <div class="light-beam light-beam-1"></div>
        <div class="light-beam light-beam-2"></div>
        <div class="light-beam light-beam-3"></div>
      </div>

      <div class="banner-content">
        <h1 class="banner-title">
          <span class="title-word" data-text="承天工之智">承天工之智</span>
          <span class="title-separator">，</span>
          <span class="title-word" data-text="启万物之能">启万物之能</span>
        </h1>
        <div class="banner-subtitle-container">
          <p class="banner-subtitle typing-effect">我们相信</p>
          <p class="banner-subtitle typing-effect" style="animation-delay: 1s;">人类无需再围成一台机器</p>
          <p class="banner-subtitle typing-effect" style="animation-delay: 2s;">而是用智能连接彼此，释放算力的真正价值</p>
        </div>

      </div>
    </div>

    <!-- 关于我们 -->
    <section class="about-section">
      <div class="container">
        <div class="am-g">
          <div class="am-u-md-6">
            <div class="our-company-text">
              <h1>关于我们</h1>
              <p style="font-size: 17px;">
                天工开物智能科技（苏州）有限公司，致力于打造面向企业级用户的高性能计算解决方案，
                围绕"高效调度、低门槛使用、专业保障"的核心理念，为 AI、大模型、图形渲染、科研计算等场景提供灵活、稳定、弹性的算力支持。
              </p>
              <p style="font-size: 17px;">
                我们基于全国分布式算力网络，自主构建智算调度平台，整合GPU资源与数据中心节点，
                为企业提供从算力资源租用、模型部署优化到全流程运维服务的一站式专业方案。
              </p>
              <p style="font-size: 17px;">
                在智能时代的浪潮中，天工开物致力于打造企业级专业算力服务平台，
                以全国分布式高性能计算网络为基础，提供稳定、高效、灵活可控的算力解决方案。
              </p>
            </div>
          </div>
          <div class="am-u-md-6">
            <div class="our-company-quote">
              <div class="our-company-img">
                <img src="images/tgkw_about.jpg" alt="天工开物智能科技" loading="lazy">
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 数据统计 -->
    <!-- <section class="stats-section">
      <div class="container">
        <div class="am-g">
          <div class="am-u-sm-6 am-u-md-3">
            <div class="stat-item">
              <div class="stat-number">32%</div>
              <div class="stat-label">市场占有率</div>
            </div>
          </div>
          <div class="am-u-sm-6 am-u-md-3">
            <div class="stat-item">
              <div class="stat-number">27%</div>
              <div class="stat-label">年增长率</div>
            </div>
          </div>
          <div class="am-u-sm-6 am-u-md-3">
            <div class="stat-item">
              <div class="stat-number">3000+</div>
              <div class="stat-label">服务客户</div>
            </div>
          </div>
          <div class="am-u-sm-6 am-u-md-3">
            <div class="stat-item">
              <div class="stat-number">10000+</div>
              <div class="stat-label">GPU节点</div>
            </div>
          </div>
        </div>
      </div>
    </section> -->

    <!-- 选择我们的理由 -->
    <section class="our-mission">
      <div class="container">
        <div class="section--header">
          <h2 class="section--title">选择我们的理由</h2>
        </div>
        <div class="am-g">
          <div class="am-u-sm-12 am-u-md-6 am-u-lg-3">
            <div class="our_mission--item">
              <div class="our_mission--item_media">
                <i class="am-icon-server" style="font-size: 48px; color: #1470FF;"></i>
              </div>
              <h4 class="our_mission--item_title">企业级专业服务</h4>
              <div class="our_mission--item_body">
                <p>为AI、科研、图形渲染、工业仿真等场景，提供稳定高效的高性能计算支持</p>
              </div>
            </div>
          </div>
          <div class="am-u-sm-12 am-u-md-6 am-u-lg-3">
            <div class="our_mission--item">
              <div class="our_mission--item_media">
                <i class="am-icon-globe" style="font-size: 48px; color: #1470FF;"></i>
              </div>
              <h4 class="our_mission--item_title">全国分布式节点布局</h4>
              <div class="our_mission--item_body">
                <p>多地部署，动态调度，资源灵活，负载均衡，响应迅速</p>
              </div>
            </div>
          </div>
          <div class="am-u-sm-12 am-u-md-6 am-u-lg-3">
            <div class="our_mission--item">
              <div class="our_mission--item_media">
                <i class="am-icon-cogs" style="font-size: 48px; color: #1470FF;"></i>
              </div>
              <h4 class="our_mission--item_title">灵活弹性 + 高性价比</h4>
              <div class="our_mission--item_body">
                <p>自研调度平台，支持定制，与大客户深度合作</p>
              </div>
            </div>
          </div>
          <div class="am-u-sm-12 am-u-md-6 am-u-lg-3">
            <div class="our_mission--item">
              <div class="our_mission--item_media">
                <i class="am-icon-users" style="font-size: 48px; color: #1470FF;"></i>
              </div>
              <h4 class="our_mission--item_title">更懂企业的算力伙伴</h4>
              <div class="our_mission--item_body">
                <p>从需求对接、技术支持到运维保障，全流程一对一服务</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 核心团队 -->
    <section class="our-team">
      <div class="container">
        <div class="section--header">
          <h2 class="section--title">核心团队</h2>
          <p class="section--description">
            核心团队来自知名AI云计算厂商、IDC运维专家与高校科研机构
          </p>
        </div>
        <div class="am-g">
          <div class="am-u-sm-12 am-u-md-4">
            <div class="team-box">
              <div class="our-team-img">
                <img src="images/techteam.png" alt="技术团队" loading="lazy">
              </div>
              <div class="team_member--body">
                <h4 class="team_member--name">技术研发</h4>
                <span class="team_member--position">专业的研发团队，深耕AI算力调度与优化</span>
              </div>
            </div>
          </div>
          <div class="am-u-sm-12 am-u-md-4">
            <div class="team-box">
              <div class="our-team-img">
                <img src="images/yunwei.png" alt="运维团队" loading="lazy">
              </div>
              <div class="team_member--body">
                <h4 class="team_member--name">运维保障</h4>
                <span class="team_member--position">5x8小时专业运维，确保服务稳定可靠</span>
              </div>
            </div>
          </div>
          <div class="am-u-sm-12 am-u-md-4">
            <div class="team-box">
              <div class="our-team-img">
                <img src="images/khjl.png" alt="客服团队" loading="lazy">
              </div>
              <div class="team_member--body">
                <h4 class="team_member--name">客户服务</h4>
                <span class="team_member--position">专业客户经理，提供一对一贴心服务</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 联系我们 -->
    <section class="contact-section">
      <div class="container">
        <div class="am-g">
          <div class="am-u-md-4">
            <div class="contact-item">
              <i class="am-icon-phone"></i>
              <h4>联系电话</h4>
              <p>13913283376</p>
            </div>
          </div>
          <div class="am-u-md-4">
            <div class="contact-item">
              <i class="am-icon-envelope"></i>
              <h4>官方公众号</h4>
              <p>昆山新质创新数字技术研究院</p>
            </div>
          </div>
          <div class="am-u-md-4">
            <div class="contact-item">
              <i class="am-icon-map-marker"></i>
              <h4>公司地址</h4>
              <p>江苏省苏州市昆山市玉山镇祖冲之路1699号昆山工业技术研究院综合南楼1404</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 立即开始 -->
    <section class="cta-section">
      <div class="cta-content">
        <h2>连接智算未来，让高性能计算像水电一样可得、可控、可负担</h2>
        <div class="cta-buttons">
          <button class="am-btn am-btn-primary" @click="startTrial">立即开始</button>
        </div>
      </div>
    </section>
  </Layout>
</template>

<script>
import Layout from "@/components/common/Layout";

export default {
  name: 'AboutView',
  components: { Layout },
  data() {
    return {
      connectionLines: []
    }
  },
  mounted() {
    this.generateConnectionLines();
    this.startCounterAnimation();
    this.optimizeForMobile();
    this.handleOrientationChange();
  },
  methods: {
    startTrial() {
      this.$router.push('/product');
    },
    contactUs() {
      // 可以添加联系我们的逻辑
      console.log('联系我们');
    },
    getParticleStyle() {
      return {
        left: Math.random() * 100 + '%',
        top: Math.random() * 100 + '%',
        animationDelay: Math.random() * 10 + 's',
        animationDuration: (Math.random() * 20 + 10) + 's'
      };
    },
    getNodeStyle() {
      return {
        left: Math.random() * 90 + 5 + '%',
        top: Math.random() * 90 + 5 + '%',
        animationDelay: Math.random() * 3 + 's'
      };
    },
    generateConnectionLines() {
      const lines = [];
      for (let i = 0; i < 15; i++) {
        lines.push({
          id: i,
          x1: Math.random() * 100,
          y1: Math.random() * 100,
          x2: Math.random() * 100,
          y2: Math.random() * 100
        });
      }
      this.connectionLines = lines;
    },
    startCounterAnimation() {
      const counters = document.querySelectorAll('.stat-number');
      counters.forEach(counter => {
        const target = parseInt(counter.getAttribute('data-target'));
        const increment = target / 100;
        let current = 0;

        const timer = setInterval(() => {
          current += increment;
          if (current >= target) {
            counter.textContent = target;
            clearInterval(timer);
          } else {
            counter.textContent = Math.floor(current);
          }
        }, 50);
      });
    },

    // 移动端优化
    optimizeForMobile() {
      // 检测是否为移动设备
      const isMobile = window.innerWidth <= 768;

      if (isMobile) {
        // 减少动画复杂度
        this.reduceMobileAnimations();

        // 优化触摸事件
        this.optimizeTouchEvents();

        // 预加载关键图片
        this.preloadCriticalImages();
      }
    },

    reduceMobileAnimations() {
      // 减少粒子数量
      const particles = document.querySelectorAll('.particle');
      particles.forEach((particle, index) => {
        if (index > 20) { // 只保留前20个粒子
          particle.style.display = 'none';
        }
      });

      // 简化数据流动画
      const dataStreams = document.querySelectorAll('.data-stream');
      dataStreams.forEach((stream, index) => {
        if (index > 4) { // 只保留前4个数据流
          stream.style.display = 'none';
        }
      });
    },

    optimizeTouchEvents() {
      // 为移动端优化触摸反馈
      const touchElements = document.querySelectorAll('.our_mission--item, .team-box, .contact-item, .am-btn');

      touchElements.forEach(element => {
        element.addEventListener('touchstart', () => {
          element.style.transform = 'translateY(-2px)';
        }, { passive: true });

        element.addEventListener('touchend', () => {
          setTimeout(() => {
            element.style.transform = '';
          }, 150);
        }, { passive: true });
      });
    },

    preloadCriticalImages() {
      const criticalImages = [
        '/images/tiangonghead.jpeg',
        '/images/back1.webp'
      ];

      criticalImages.forEach(src => {
        const img = new Image();
        img.src = src;
      });
    },

    handleOrientationChange() {
      // 处理屏幕方向变化
      window.addEventListener('orientationchange', () => {
        setTimeout(() => {
          // 重新计算布局
          this.generateConnectionLines();

          // 重新优化移动端设置
          this.optimizeForMobile();
        }, 500);
      });
    }
  }
}
</script>

<style scoped>
/* 顶部Banner */
.about-banner {
  background:
    radial-gradient(ellipse at top, rgba(20, 112, 255, 0.3) 0%, transparent 70%),
    radial-gradient(ellipse at bottom, rgba(74, 144, 255, 0.2) 0%, transparent 70%),
    linear-gradient(135deg, #0a0a0a 0%, #1470FF 30%, #4A90FF 50%, #1470FF 70%, #0a0a0a 100%);
  color: white;
  padding: 200px 0 100px;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.about-banner-bg {
  pointer-events: none;
}

/* 动态网格背景 */
/* .grid-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(20, 112, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(20, 112, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridMove 20s linear infinite;
  z-index: 1;
}

@keyframes gridMove {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
} */

/* 粒子效果 */
.particles-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2;
}

.particle {
  position: absolute;
  width: 2px;
  height: 2px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: particleFloat 15s linear infinite;
}

.particle:nth-child(3n) {
  background: rgba(20, 112, 255, 0.8);
  width: 3px;
  height: 3px;
}

.particle:nth-child(5n) {
  background: rgba(74, 144, 255, 0.6);
  width: 1px;
  height: 1px;
}

@keyframes particleFloat {
  0% {
    transform: translateY(100vh) translateX(0) scale(0);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) translateX(100px) scale(1);
    opacity: 0;
  }
}


/* 数据流效果 */
.data-streams {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2;
}

.data-stream {
  position: absolute;
  width: 2px;
  height: 100px;
  background: linear-gradient(to bottom, transparent, rgba(20, 112, 255, 0.8), transparent);
  animation: dataFlow 3s linear infinite;
}

.data-stream:nth-child(1) { left: 10%; animation-delay: 0s; }
.data-stream:nth-child(2) { left: 25%; animation-delay: 0.5s; }
.data-stream:nth-child(3) { left: 40%; animation-delay: 1s; }
.data-stream:nth-child(4) { left: 55%; animation-delay: 1.5s; }
.data-stream:nth-child(5) { left: 70%; animation-delay: 2s; }
.data-stream:nth-child(6) { left: 85%; animation-delay: 2.5s; }
.data-stream:nth-child(7) { left: 15%; animation-delay: 1.2s; }
.data-stream:nth-child(8) { left: 75%; animation-delay: 0.8s; }

@keyframes dataFlow {
  0% {
    transform: translateY(-100px);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateY(calc(100vh + 100px));
    opacity: 0;
  }
}

/* 算力节点连接线 */
/* .network-nodes {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2;
}

.node {
  position: absolute;
  width: 8px;
  height: 8px;
  background: rgba(20, 112, 255, 0.8);
  border-radius: 50%;
  animation: nodePulse 2s ease-in-out infinite;
}

.node-pulse {
  position: absolute;
  top: -4px;
  left: -4px;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(20, 112, 255, 0.4);
  border-radius: 50%;
  animation: pulseRing 2s ease-out infinite;
}

@keyframes nodePulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.8; }
}

@keyframes pulseRing {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

.connection-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.connection-line {
  stroke: rgba(20, 112, 255, 0.3);
  stroke-width: 0.5;
  animation: lineGlow 3s ease-in-out infinite alternate;
}

@keyframes lineGlow {
  0% { stroke-opacity: 0.3; }
  100% { stroke-opacity: 0.8; }
} */

/* 光效层 */
.light-effects {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 3;
  pointer-events: none;
}

.light-beam {
  position: absolute;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: lightSweep 8s ease-in-out infinite;
}

.light-beam-1 {
  top: 20%;
  left: -100%;
  width: 200%;
  height: 2px;
  animation-delay: 0s;
}

.light-beam-2 {
  top: 60%;
  left: -100%;
  width: 200%;
  height: 1px;
  animation-delay: 2s;
}

.light-beam-3 {
  top: 80%;
  left: -100%;
  width: 200%;
  height: 3px;
  animation-delay: 4s;
}

@keyframes lightSweep {
  0% { transform: translateX(-100%); opacity: 0; }
  50% { opacity: 1; }
  100% { transform: translateX(100%); opacity: 0; }
}

.banner-content {
  position: relative;
  z-index: 10;
  /* padding-top: 100px; */
}

/* 标题效果 */
.banner-title {
  font-size: 48px;
  margin-bottom: 30px;
  font-weight: 700;
  line-height: 1.2;
  position: relative;
}

.title-word {
  display: inline-block;
  position: relative;
  background: linear-gradient(45deg, #ffffff, #8fb9fd, #ffffff);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: titleShimmer 3s ease-in-out infinite;
}

.title-word::before {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255,1), transparent);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: textGlow 2s ease-in-out infinite alternate;
}

@keyframes titleShimmer {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes textGlow {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

.title-separator {
  color: rgba(255, 255, 255, 0.8);
}

/* 字幕打字效果 */
.banner-subtitle-container {
  margin-bottom: 130px;
}

.banner-subtitle {
  font-size: 24px;
  margin-bottom: 10px;
  opacity: 0;
  transform: translateY(20px);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.typing-effect {
  animation: typeIn 1s ease-out forwards;
}

@keyframes typeIn {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 0.9;
    transform: translateY(0);
  }
}

/* 关于我们部分 */
.about-section {
  padding: 60px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e6f2ff 100%);
  position: relative;
}

/* 移动端文本优化 */
.our-company-text h3 {
  color: #333;
  font-weight: 600;
  margin-bottom: 25px;
}

.our-company-text p {
  color: #555;
  line-height: 1.7;
  margin-bottom: 20px;
  font-size: 16px;
}

/* 图片优化 */
.our-company-img {
  text-align: center;
  margin-top: 20px;
}

.our-company-img img {
  max-width: 100%;
  height: auto;
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(20, 112, 255, 0.15);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.our-company-img img:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(20, 112, 255, 0.25);
}

.about-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%23d4e8ff" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  z-index: 1;
}

.about-section .container {
  position: relative;
  z-index: 2;
}

.container {
  width: 95%;
  max-width: 1170px;
  margin: 0 auto;
  padding: 0 15px;
}

/* 数据统计
.stats-section {
  background: linear-gradient(135deg, #f8f9fa 0%, #e6f2ff 100%);
  padding: 60px 0;
  position: relative;
}

.stats-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23d4e8ff" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
  z-index: 1;
}

.stats-section .container {
  position: relative;
  z-index: 2;
}

.stat-item {
  text-align: center;
  padding: 30px 15px;
  transition: transform 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-5px);
}

.stat-number {
  font-size: 48px;
  font-weight: 700;
  color: #1470FF;
  margin-bottom: 10px;
  text-shadow: 0 2px 4px rgba(20, 112, 255, 0.1);
}

.stat-label {
  font-size: 16px;
  color: #555;
  font-weight: 500;
} */

/* 选择我们的理由 */
.our-mission {
  padding: 60px 0;
  background: #fff;
  position: relative;
}

.our-mission::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 49%, rgba(20, 112, 255, 0.02) 50%, transparent 51%);
  z-index: 1;
}

.our-mission .container {
  position: relative;
  z-index: 2;
}

.section--header {
  text-align: center;
  /* margin-bottom: 60px; */
}

.section--title {
  font-size: 36px;
  color: #333;
  margin-bottom: 20px;
  font-weight: 600;
  position: relative;
}

.section--title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #1470FF, #4A90FF);
  border-radius: 2px;
}

.section--description {
  font-size: 18px;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.our_mission--item {
  text-align: center;
  padding: 40px 20px;
  transition: all 0.3s ease;
  border-radius: 10px;
  position: relative;
  overflow: hidden;
}

.our_mission--item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(20, 112, 255, 0.05) 0%, rgba(74, 144, 255, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.our_mission--item:hover::before {
  opacity: 1;
}

.our_mission--item:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 35px rgba(20, 112, 255, 0.15);
}

.our_mission--item_media {
  margin-bottom: 25px;
  position: relative;
  z-index: 2;
}

.our_mission--item_media i {
  transition: all 0.3s ease;
}

.our_mission--item:hover .our_mission--item_media i {
  color: #1470FF !important;
  transform: scale(1.1);
}

.our_mission--item_title {
  font-size: 20px;
  color: #333;
  margin-bottom: 15px;
  font-weight: 600;
  position: relative;
  z-index: 2;
}

.our_mission--item_body {
  position: relative;
  z-index: 2;
}

.our_mission--item_body p {
  font-size: 15px;
  color: #666;
  line-height: 1.6;
  margin: 0;
}

/* 核心团队 */
.our-team {
  padding: 60px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e6f2ff 100%);
  position: relative;
}

.our-team::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="%23d4e8ff" opacity="0.5"/><circle cx="80" cy="20" r="2" fill="%23d4e8ff" opacity="0.5"/><circle cx="20" cy="80" r="2" fill="%23d4e8ff" opacity="0.5"/><circle cx="80" cy="80" r="2" fill="%23d4e8ff" opacity="0.5"/></svg>');
  z-index: 1;
}

.our-team .container {
  position: relative;
  z-index: 2;
}

.team-box {
  background: #fff;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(20, 112, 255, 0.1);
  transition: all 0.3s ease;
  margin-bottom: 30px;
  border: 1px solid rgba(20, 112, 255, 0.1);
}

.team-box:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(20, 112, 255, 0.2);
  border-color: rgba(20, 112, 255, 0.3);
}

.our-team-img {
  position: relative;
  overflow: hidden;
}

.our-team-img::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(20, 112, 255, 0.1) 0%, rgba(74, 144, 255, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.team-box:hover .our-team-img::after {
  opacity: 1;
}

.our-team-img img {
  width: 100%;
  height: 250px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.team-box:hover .our-team-img img {
  transform: scale(1.05);
}

.team_member--body {
  padding: 30px 25px;
  text-align: center;
  position: relative;
}

.team_member--name {
  font-size: 20px;
  color: #333;
  margin-bottom: 10px;
  font-weight: 600;
}

.team_member--position {
  font-size: 15px;
  color: #666;
  line-height: 1.5;
}

/* 联系我们 */
.contact-section {
  padding: 20px 0;
  background: #fff;
  position: relative;
}

.contact-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 49%, rgba(20, 112, 255, 0.02) 50%, transparent 51%);
  z-index: 1;
}

.contact-section .container {
  position: relative;
  z-index: 2;
}

.contact-item {
  text-align: center;
  padding: 30px 20px;
  transition: all 0.3s ease;
  border-radius: 10px;
  position: relative;
}

.contact-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(20, 112, 255, 0.05) 0%, rgba(74, 144, 255, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 10px;
}

.contact-item:hover::before {
  opacity: 1;
}

.contact-item:hover {
  transform: translateY(-5px);
}

.contact-item i {
  font-size: 48px;
  color: #1470FF;
  /* margin-bottom: 20px; */
  transition: all 0.3s ease;
}

.contact-item:hover i {
  color: #4A90FF;
  transform: scale(1.1);
}

.contact-item h4 {
  font-size: 20px;
  color: #333;
  margin-bottom: 15px;
  font-weight: 600;
  position: relative;
  z-index: 2;
}

.contact-item p {
  font-size: 16px;
  color: #666;
  margin: 0;
  position: relative;
  z-index: 2;
}

/* 立即开始 */
.cta-section {
  background-image: url("../../assets/images/index/back3.png");
  width: 100%;
  height: 120px;
  color: white;
  padding: 20px 0;
  text-align: center;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cta-content {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  max-width: 1200px;
  padding: 0 20px;
}

.cta-content h2 {
  color: white;
  font-size: 2rem;
  font-weight: bold;
  margin: 0;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.cta-content p {
  display: none; /* 隐藏描述文字，保持和首页一致 */
}

.cta-buttons {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
}

.cta-buttons .am-btn {
  background-color: white;
  color: #0d47a1;
  border: none;
  border-radius: 25px;
  padding: 10px 25px;
  font-size: 1.7rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.am-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  background-color: #0a69ff;
  color: white;
}

/* 移动端适配 */
@media screen and (max-width: 768px) {
  .cta-section {
    height: auto;
    padding: 20px;
  }

  .cta-content {
    flex-direction: column;
    text-align: center;
  }

  .cta-content h2 {
    font-size: 18px;
    margin-bottom: 15px;
  }

  .cta-buttons .am-btn {
    font-size: 16px;
    padding: 10px 25px;
  }
}

.am-btn:focus,
.am-btn:active {
  outline: none;
  box-shadow: none;
}

/* 响应式设计 */
/* 平板设备 */
@media (max-width: 1024px) {
  .banner-title {
    font-size: 40px;
  }

  .banner-subtitle {
    font-size: 20px;
  }

  .about-banner {
    padding: 100px 0 70px;
  }

  .section--title {
    font-size: 32px;
  }

  .our_mission--item_media i {
    font-size: 42px !important;
  }

  .contact-item i {
    font-size: 42px;
  }
}

/* 移动端 - 大屏手机 */
@media (max-width: 768px) {
  .container {
    width: 100%;
    padding: 0 20px;
  }

  .banner-title {
    font-size: 32px;
    line-height: 1.3;
    margin-bottom: 25px;
  }

  .banner-subtitle {
    font-size: 18px;
    margin-bottom: 8px;
  }

  .about-banner {
    padding: 80px 0 60px;
    min-height: 70vh;
  }

  .banner-subtitle-container {
    margin-bottom: 30px;
  }

  /* 优化动画效果性能 */
  .particle {
    display: none; /* 移动端隐藏粒子效果以提升性能 */
  }

  .data-stream {
    width: 1px;
    height: 50px;
  }

  /* .node {
    width: 6px;
    height: 6px;
  } */

  /* .node-pulse {
    width: 12px;
    height: 12px;
    top: -3px;
    left: -3px;
  } */

  /* 减少光效以提升性能 */
  .light-beam {
    display: none;
  }

  /* 各个区块的移动端适配 */
  .about-section,
  .our-mission,
  .our-team,
  .contact-section {
    padding: 50px 0;
  }



  .section--title {
    font-size: 28px;
    margin-bottom: 15px;
  }

  .section--description {
    font-size: 16px;
    padding: 0 10px;
  }

  /* 关于我们文本区域 */
  .our-company-text {
    margin-bottom: 30px;
  }

  .our-company-text h3 {
    font-size: 24px;
    margin-bottom: 20px;
    text-align: center;
  }

  .our-company-text p {
    font-size: 15px;
    line-height: 1.6;
    margin-bottom: 15px;
    text-align: justify;
  }

  .our-company-img img {
    max-width: 100%;
    height: auto;
    border-radius: 10px;
  }

  /* 选择我们的理由 */
  .our_mission--item {
    margin-bottom: 30px;
    padding: 30px 15px;
  }

  .our_mission--item_media i {
    font-size: 40px !important;
  }

  .our_mission--item_title {
    font-size: 18px;
    margin-bottom: 12px;
  }

  .our_mission--item_body p {
    font-size: 14px;
    line-height: 1.5;
  }

  /* 团队区域 */
  .team-box {
    margin-bottom: 25px;
  }

  .our-team-img img {
    height: 200px;
  }

  .team_member--body {
    padding: 20px 15px;
  }

  .team_member--name {
    font-size: 18px;
  }

  .team_member--position {
    font-size: 14px;
  }

  /* 联系我们 */
  .contact-item {
    padding: 30px 15px;
    margin-bottom: 20px;
  }

  .contact-item i {
    font-size: 40px;
    margin-bottom: 15px;
  }

  .contact-item h4 {
    font-size: 18px;
    margin-bottom: 10px;
  }

  .contact-item p {
    font-size: 15px;
  }

  /* CTA区域 */
  .cta-content h2 {
    font-size: 28px;
    margin-bottom: 15px;
  }

  .cta-content p {
    font-size: 16px;
    margin-bottom: 30px;
    padding: 0 10px;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }

  .cta-buttons .am-btn {
    width: 220px;
    padding: 12px 30px;
    font-size: 15px;
  }
}

/* 移动端 - 小屏手机 */
@media (max-width: 480px) {
  .container {
    padding: 0 15px;
  }

  .banner-title {
    font-size: 26px;
    line-height: 1.2;
  }

  .banner-subtitle {
    font-size: 16px;
  }

  .about-banner {
    padding: 60px 0 40px;
    min-height: 60vh;
  }

  .section--title {
    font-size: 24px;
  }

  .section--description {
    font-size: 15px;
  }

  /* 关于我们文本 */
  .our-company-text h3 {
    font-size: 22px;
  }

  .our-company-text p {
    font-size: 14px;
    line-height: 1.5;
  }

  /* 选择我们的理由 */
  .our_mission--item {
    padding: 25px 10px;
  }

  .our_mission--item_media i {
    font-size: 36px !important;
  }

  .our_mission--item_title {
    font-size: 16px;
  }

  .our_mission--item_body p {
    font-size: 13px;
  }

  /* 团队区域 */
  .our-team-img img {
    height: 180px;
  }

  .team_member--body {
    padding: 15px 10px;
  }

  .team_member--name {
    font-size: 16px;
  }

  .team_member--position {
    font-size: 13px;
  }

  /* 联系我们 */
  .contact-item {
    padding: 25px 10px;
  }

  .contact-item i {
    font-size: 36px;
  }

  .contact-item h4 {
    font-size: 16px;
  }

  .contact-item p {
    font-size: 14px;
  }

  /* CTA区域 */
  .cta-content h2 {
    font-size: 24px;
  }

  .cta-content p {
    font-size: 15px;
    padding: 0 5px;
  }

  .cta-buttons .am-btn {
    width: 200px;
    padding: 10px 25px;
    font-size: 14px;
  }
}

/* 超小屏设备 */
@media (max-width: 360px) {
  .container {
    padding: 0 10px;
  }

  .banner-title {
    font-size: 22px;
  }

  .banner-subtitle {
    font-size: 14px;
  }

  .section--title {
    font-size: 20px;
  }

  .our_mission--item_media i {
    font-size: 32px !important;
  }

  .contact-item i {
    font-size: 32px;
  }

  .cta-buttons .am-btn {
    width: 180px;
    font-size: 13px;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .our_mission--item:hover,
  .team-box:hover,
  .contact-item:hover {
    transform: none;
  }

  .our_mission--item:active,
  .team-box:active,
  .contact-item:active {
    transform: translateY(-3px);
    transition: transform 0.1s ease;
  }

  .cta-buttons .am-btn:hover {
    transform: none;
  }

  .cta-buttons .am-btn:active {
    transform: translateY(-2px) scale(0.98);
    transition: transform 0.1s ease;
  }
}

/* 横屏模式优化 */
@media (max-width: 768px) and (orientation: landscape) {
  .about-banner {
    padding: 40px 0 30px;
    min-height: 50vh;
  }

  .banner-title {
    font-size: 28px;
  }

  .banner-subtitle {
    font-size: 16px;
  }

  .about-section,
  .our-mission,
  .our-team,
  .contact-section {
    padding: 40px 0;
  }
}

/* 移动端性能优化 */
@media (max-width: 768px) {
  /* 启用硬件加速 */
  .about-banner,
  .our_mission--item,
  .team-box,
  .contact-item,
  .am-btn {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
  }

  /* 优化滚动性能 */
  .about-banner {
    -webkit-overflow-scrolling: touch;
  }

  /* 减少重绘 */
  .particle,
  .data-stream,
  .node {
    will-change: transform;
  }

  /* 触摸优化 */
  .our_mission--item,
  .team-box,
  .contact-item,
  .am-btn {
    -webkit-tap-highlight-color: rgba(20, 112, 255, 0.1);
  }

  /* 防止文本选择 */
  .banner-title,
  .section--title,
  .our_mission--item_title,
  .team_member--name,
  .contact-item h4 {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .our-company-img img,
  .our-team-img img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* 减少动画的偏好设置支持 */
@media (prefers-reduced-motion: reduce) {
  .particle,
  .data-stream,
  .node,
  .light-beam,
  .typing-effect,
  .title-word {
    animation: none !important;
  }

  .our_mission--item:hover,
  .team-box:hover,
  .contact-item:hover,
  .am-btn:hover {
    transform: none !important;
    transition: none !important;
  }
}
</style>