<template>
  <section class="section gpu-comparison-section">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">GPU性能对比</h2>
        <p class="section-description">
          专业GPU性能详细对比，助您选择最适合的计算资源
        </p>
      </div>

      <div class="gpu-comparison-table">
        <table>
          <thead>
          <tr>
            <th>GPU型号</th>
            <th v-for="gpu in comparisonGpus" :key="gpu.name">{{ gpu.name }}</th>
          </tr>
          </thead>
          <tbody>
          <tr>
            <td>架构</td>
            <td v-for="gpu in comparisonGpus" :key="gpu.name">{{ gpu.architecture }}</td>
          </tr>
          <tr>
            <td>FP16性能</td>
            <td v-for="gpu in comparisonGpus" :key="gpu.name">{{ gpu.fp16Performance }}</td>
          </tr>
          <tr>
            <td>FP32性能</td>
            <td v-for="gpu in comparisonGpus" :key="gpu.name">{{ gpu.fp32Performance }}</td>
          </tr>
          <tr>
            <td>显存</td>
            <td v-for="gpu in comparisonGpus" :key="gpu.name">{{ gpu.memory }}</td>
          </tr>
          <tr>
            <td>显存类型</td>
            <td v-for="gpu in comparisonGpus" :key="gpu.name">{{ gpu.memoryType }}</td>
          </tr>
          <tr>
            <td>带宽</td>
            <td v-for="gpu in comparisonGpus" :key="gpu.name">{{ gpu.bandwidth }}</td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>
  </section>
</template>

<script>
import {getNotAuth} from "@/api/login";

export default {
  name: 'GpuComparison',
  data() {
    return {
      comparisonGpus: [
        {
          name: 'A100',
          architecture: 'Ampere',
          fp16Performance: '312 TFLOPS',
          fp32Performance: '19.5 TFLOPS',
          memory: '80 GB',
          memoryType: 'HBM2',
          bandwidth: '2,039 GB/s'
        },
        {
          name: 'V100',
          architecture: 'Volta',
          fp16Performance: '125 TFLOPS',
          fp32Performance: '15.7 TFLOPS',
          memory: '32 GB',
          memoryType: 'HBM2',
          bandwidth: '900 GB/s'
        },
        {
          name: 'A6000',
          architecture: 'Ampere',
          fp16Performance: '77.4 TFLOPS',
          fp32Performance: '38.7 TFLOPS',
          memory: '48 GB',
          memoryType: 'GDDR6',
          bandwidth: '768 GB/s'
        },
        {
          name: 'A5000',
          architecture: 'Ampere',
          fp16Performance: '54.2 TFLOPS',
          fp32Performance: '27.8 TFLOPS',
          memory: '24 GB',
          memoryType: 'GDDR6',
          bandwidth: '768 GB/s'
        },
        {
          name: 'A4000',
          architecture: 'Ampere',
          fp16Performance: '19.17 TFLOPS',
          fp32Performance: '19.17 TFLOPS',
          memory: '16 GB',
          memoryType: 'GDDR6',
          bandwidth: '448 GB/s'
        }
      ]
    }
  },
  created() {
    this.fetchComparison();
  },
  methods: {
    async fetchComparison() {
      try {
        // console.log("开始获取对比数据")
        getNotAuth("/system/comparison/list").then(req =>{
          // console.log("原始数据",req.data.rows)
          this.comparisonGpus = req.data.rows.map(item => ({
            ...item,
            fp16Performance: item.fp16performance,
            fp32Performance: item.fp32performance,
            memoryType:item.memorytype
          }))
          // this.gpus = req.data.rows
          // console.log("数据",this.gpus)
        })
      } catch (error) {
        console.error('获取GPU推荐列表失败:', error);
      }
    }
  }
}
</script>

<style scoped>
.gpu-comparison-section {
  /*padding-top: 10vh;*/
  background-color: #f9f9f9;
  width: 100%;
  max-width: 2560px;
  margin: 0 auto;
  padding-bottom: 15vh;
}

.container {
  width: 93%;
  max-width: 2560px;
  margin: 0 auto;
}

.section-header {
  text-align: center;
  margin-bottom: 40px;
}

.section-title {
  font-size: 28px;
  color: #333;
  position: relative;
  display: inline-block;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background-color: #2196f3;
}

.section-description {
  color: #666;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.gpu-comparison-table {
  width: 100%;
  padding: 0;
  border-collapse: collapse;
  margin: 0;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  border-radius: 10px;
  overflow: hidden;
  overflow-x: auto;
}

.gpu-comparison-table table {
  width: 100%;
  min-width: 1000px;
}

.gpu-comparison-table thead {
  background-color: #cddfec;
}

.gpu-comparison-table thead tr th,
.gpu-comparison-table tbody tr td {
  padding: 18px 25px;
  text-align: center;
  font-weight: 600;
  color: #2c3e50;
  font-size: 16px;
  min-width: 180px;
  border-bottom: 1px solid #e0e5eb;
  transition: background-color 0.3s ease;
}

.gpu-comparison-table thead tr th {
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.gpu-comparison-table tbody tr td {
  color: #333;
  font-weight: normal;
}

.gpu-comparison-table tbody tr:nth-child(even) {
  background-color: #f9fafc;
}

.gpu-comparison-table tbody tr:hover {
  background-color: #f1f6fd;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .container {
    width: 98%;
  }
}

@media (max-width: 768px) {
  .section-title {
    font-size: 26px;
    color: #333;
  }

  .section-description {
    font-size: 16px;
  }
}
</style>