<template>
  <div class="custom-pagination">
    <span class="pagination-total">共 {{ total }} 条</span>

    <button
        class="pagination-prev"
        @click="changePage(currentPage - 1)"
        :disabled="currentPage === 1"
    >
      &lt;
    </button>

    <span class="pagination-current">{{ currentPage }}</span>

    <button
        class="pagination-next"
        @click="changePage(currentPage + 1)"
        :disabled="currentPage === totalPages"
    >
      &gt;
    </button>

    <span class="pagination-size">{{ pageSize }}条 / 页</span>

    <div class="pagination-jump">
      <span>前往</span>
      <input
          type="text"
          v-model.number="inputPage"
          min="1"
          :max="totalPages"
          @blur="handleJump"
          @keyup.enter="handleJump"
      >
      <span>页</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CommonPagination',
  props: {
    currentPage: {
      type: Number,
      required: true
    },
    total: {
      type: Number,
      required: true
    },
    pageSize: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      inputPage: this.currentPage
    }
  },
  watch: {
    currentPage(newVal) {
      this.inputPage = newVal;
    }
  },
  computed: {
    totalPages() {
      return Math.ceil(this.total / this.pageSize);
    }
  },
  methods: {
    changePage(page) {
      if (page >= 1 && page <= this.totalPages) {
        this.$emit('change-page', page);
      }
    },
    handleJump() {
      // 验证输入是否合法
      if (this.inputPage && this.inputPage >= 1 && this.inputPage <= this.totalPages) {
        if (this.inputPage !== this.currentPage) {
          this.changePage(this.inputPage);
        }
      } else {
        // 不合法则重置为当前页
        this.inputPage = this.currentPage;
      }
    }
  }
}
</script>

<style scoped>
.custom-pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 15px;
  color: #888;
  margin-top: 15px;
  gap: 8px;
}

.pagination-total,
.pagination-size,
.pagination-current {
  font-size: 15px;
  color: #999;
}

.pagination-prev,
.pagination-next {
  background: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 2px;
  padding: 2px 8px;
  font-size: 15px;
  color: #666;
  cursor: pointer;
  transition: all 0.3s;
}

.pagination-prev:hover,
.pagination-next:hover {
  background: #e8e8e8;
  border-color: #ccc;
}

.pagination-prev:disabled,
.pagination-next:disabled {
  color: #ccc;
  cursor: not-allowed;
  background: #f9f9f9;
}

.pagination-current {
  font-weight: normal;
  padding: 0 5px;
}

.pagination-jump {
  display: flex;
  align-items: center;
  gap: 5px;
}

.pagination-jump input {
  width: 40px;
  height: 22px;
  border: 1px solid #ddd;
  border-radius: 2px;
  text-align: center;
  font-size: 15px;
  color: #666;
  padding: 0 5px;
}

.pagination-jump input:focus {
  outline: none;
  border-color: #a0cfff;
}
</style>