{"version": 3, "file": "js/docs11.22786028.js", "mappings": "yHACA,IAAIA,EAAO,i+IAEX,c", "sources": ["webpack://portal-ui/./src/docs/gpu-selection.md"], "sourcesContent": ["// Module\nvar code = \"<h1 id=\\\"如何选择gpu\\\">如何选择GPU</h1> <h2 id=\\\"gpu选型\\\">GPU选型</h2> <p><font style=\\\"color:#020817\\\">平台分配GPU、CPU、内存的机制为：按购买的GPU数量成比例分配CPU和内存，GPU云容器显示的CPU和内存均为每GPU分配的CPU和内存，如果租用两块GPU，那么CPU和内存就x2。此外GPU非共享，每个实例都是独占GPU资源。</font></p> <p><font style=\\\"color:#020817\\\">GPU的数量选择与训练任务有关。一般我们认为模型的一次训练应当在24小时内完成，这样隔天就能训练改进之后的模型。以下是选择多GPU的一些建议：</font></p> <ul> <li><strong>1块GPU</strong>：适合一些数据集较小的训练任务，如Pascal VOC等。</li> <li><strong>2块GPU</strong>：同单块GPU，但是你可以一次跑两组参数或者把Batchsize扩大。</li> <li><strong>4块GPU</strong>：适合一些中等数据集的训练任务，如MS COCO等。</li> <li><strong>8块GPU</strong>：经典永流传的配置！适合各种训练任务，也非常方便复现论文结果。</li> </ul> <h2 id=\\\"选择内存\\\">选择内存</h2> <ul> <li><strong>充足的内存</strong>：确保所选GPU实例的内存容量充足，避免因内存不足导致程序中断。如果对内存容量有较高要求，建议选择分配内存更多的主机或租用多GPU实例。</li> <li><strong>监控内存使用</strong>：在不确定内存使用情况时，可以通过实例监控功能观察内存使用情况，以便及时调整配置。</li> </ul> <h2 id=\\\"常见gpu型号性能对比表\\\">常见GPU型号性能对比表</h2> <table> <thead> <tr> <th align=\\\"left\\\">型号</th> <th align=\\\"left\\\">显存大小</th> <th align=\\\"left\\\">计算能力 (TFLOPS)</th> <th align=\\\"left\\\">适用场景</th> </tr> </thead> <tbody><tr> <td align=\\\"left\\\">A100 SXM4 / 80GB</td> <td align=\\\"left\\\">80GB</td> <td align=\\\"left\\\">19.5 (FP32), 312 (TF32)</td> <td align=\\\"left\\\">大规模数据中心、高性能计算、深度学习训练</td> </tr> <tr> <td align=\\\"left\\\">RTX 3090 / 24GB</td> <td align=\\\"left\\\">24GB</td> <td align=\\\"left\\\">35.58 (FP32)</td> <td align=\\\"left\\\">游戏、内容创作、中小型深度学习训练</td> </tr> <tr> <td align=\\\"left\\\">A100 PCIE / 80GB</td> <td align=\\\"left\\\">80GB</td> <td align=\\\"left\\\">19.5 (FP32), 312 (TF32)</td> <td align=\\\"left\\\">数据中心、高性能计算、深度学习训练（性能略低于SXM4）</td> </tr> <tr> <td align=\\\"left\\\">RTX 4090 / 24GB</td> <td align=\\\"left\\\">24GB</td> <td align=\\\"left\\\">82.58 (FP32)</td> <td align=\\\"left\\\">游戏、内容创作、深度学习训练、AI绘画</td> </tr> <tr> <td align=\\\"left\\\">RTX 4090D / 24GB</td> <td align=\\\"left\\\">24GB</td> <td align=\\\"left\\\">73.6 (FP32)</td> <td align=\\\"left\\\">游戏、内容创作、深度学习训练（中国特供版，性能略低于RTX 4090）</td> </tr> <tr> <td align=\\\"left\\\">RTX 3060 / 12GB</td> <td align=\\\"left\\\">12GB</td> <td align=\\\"left\\\">12.7 (FP32)</td> <td align=\\\"left\\\">游戏、入门级深度学习训练</td> </tr> <tr> <td align=\\\"left\\\">RTX A4000 / 16GB</td> <td align=\\\"left\\\">16GB</td> <td align=\\\"left\\\">19.2 (FP32)</td> <td align=\\\"left\\\">专业图形工作站、内容创作、中小型深度学习训练</td> </tr> <tr> <td align=\\\"left\\\">Tesla P40 / 24GB</td> <td align=\\\"left\\\">24GB</td> <td align=\\\"left\\\">12 (FP32), 47 (INT8)</td> <td align=\\\"left\\\">深度学习推理、数据分析、科学计算</td> </tr> </tbody></table> <p><strong>注：</strong></p> <ul> <li>计算能力TFLOPS为理论峰值性能，实际性能会因应用和环境而异。</li> <li>TF32是NVIDIA A100特有的TensorFloat32格式，用于加速AI训练。</li> <li>INT8是8位整数运算，常用于深度学习推理。</li> </ul> <h2 id=\\\"推荐配置\\\">推荐配置</h2> <p><font style=\\\"color:#020817\\\">根据不同的应用场景和需求，以下是一些推荐的GPU配置：</font></p> <h3 id=\\\"深度学习模型训练\\\">深度学习模型训练</h3> <p><font style=\\\"color:#020817\\\">深度学习模型训练对GPU的计算能力和显存容量有较高要求，尤其是训练大型模型时。</font></p> <ul> <li><p><strong>入门级训练（小型数据集，如Pascal VOC）</strong>：</p> <ul> <li><strong>推荐GPU</strong>：RTX 3060 / 12GB</li> <li><strong>数量建议</strong>：1块GPU</li> <li><strong>说明</strong>：对于数据集较小、模型复杂度不高的训练任务，RTX 3060的12GB显存和不错的计算能力足以应对。单块GPU可以满足基本需求。</li> </ul> </li> <li><p><strong>中等规模训练（中等数据集，如MS COCO）</strong>：</p> <ul> <li><strong>推荐GPU</strong>：RTX 3090 / 24GB 或 RTX 4090 / 24GB</li> <li><strong>数量建议</strong>：2块或4块GPU</li> <li><strong>说明</strong>：RTX 3090和RTX 4090拥有更大的显存和更强的计算能力，适合处理中等规模的数据集。多块GPU可以显著加速训练过程，允许更大的Batchsize或同时进行多组参数的实验。RTX 4090在性能上优于RTX 3090，是更优的选择。</li> </ul> </li> <li><p><strong>大规模/复杂模型训练（大型数据集、前沿研究）</strong>：</p> <ul> <li><strong>推荐GPU</strong>：A100 SXM4 / 80GB 或 A100 PCIE / 80GB</li> <li><strong>数量建议</strong>：多块GPU（根据需求选择）</li> <li><strong>说明</strong>：A100系列GPU专为高性能计算和深度学习设计，拥有超大的显存和极高的计算能力，特别适合训练超大型模型和处理海量数据集。SXM4版本在持续性能输出和多卡互联方面表现更优，PCIE版本则提供更灵活的部署方式。对于追求极致性能和效率的场景，A100是目前的首选。</li> </ul> </li> </ul> <h3 id=\\\"深度学习推理\\\">深度学习推理</h3> <p><font style=\\\"color:#020817\\\">深度学习推理对GPU的实时响应和吞吐量有较高要求，尤其是在边缘设备或实时应用中。</font></p> <ul> <li><p><strong>通用推理</strong>：</p> <ul> <li><strong>推荐GPU</strong>：RTX 3060 / 12GB 或 RTX A4000 / 16GB</li> <li><strong>说明</strong>：RTX 3060和RTX A4000在性能和显存方面都能满足大多数通用推理任务的需求。RTX A4000作为专业卡，在稳定性和长时间运行方面可能表现更好。</li> </ul> </li> <li><p><strong>高性能推理（对延迟和吞吐量要求高）</strong>：</p> <ul> <li><strong>推荐GPU</strong>：Tesla P40 / 24GB 或 A100系列</li> <li><strong>说明</strong>：Tesla P40以其出色的INT8推理能力和24GB显存，在处理复杂的深度学习模型时能实现实时响应。A100系列在推理方面同样表现出色，尤其适合需要处理大规模并发请求的场景。</li> </ul> </li> </ul> <h3 id=\\\"内容创作与专业图形工作站\\\">内容创作与专业图形工作站</h3> <p><font style=\\\"color:#020817\\\">这类应用对GPU的图形渲染能力、显存容量和稳定性有较高要求。</font></p> <ul> <li><strong>推荐GPU</strong>：RTX 4090 / 24GB 或 RTX A4000 / 16GB</li> <li><strong>说明</strong>：RTX 4090在游戏和内容创作方面都表现出色，其强大的性能和24GB显存足以应对高分辨率渲染、视频编辑和3D建模等任务。RTX A4000作为专业图形卡，在驱动优化和稳定性方面更具优势，适合长时间高强度专业工作。</li> </ul> <p><br><br> <font style=\\\"color:#b2b2b2\\\">最后更新于: 2025/6/10 13:56</font></p> \";\n// Exports\nexport default code;"], "names": ["code"], "sourceRoot": ""}