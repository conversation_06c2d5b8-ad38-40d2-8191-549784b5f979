<template>
  <div class="login-page">
    <SlideNotification
        v-if="showCodeSent"
        message="验证码已发送，可能会有延迟，请耐心等待！"
        type="success"
        :min-height="notificationMinHeight"
        @close="showCodeSent = false"
    />


    <div class="left-side">
      <backgroundlogin />
    </div>

    <div class="right-side">
      <div class="login-form-container">
        <h3>重置统一登录密码</h3>

        <div class="form-container">
          <div class="login-form">
            <p class="form-note">请输入手机号接收验证码</p>

            <div class="input-group" :class="{ 'error': errors.phone }">
              <input
                  type="text"
                  v-model="resetForm.phone"
                  placeholder="请输入手机号"
                  @blur="validatePhone"
              />
              <div class="error-container">
                <div v-if="errors.phone" class="error-message">{{ errors.phone }}</div>
              </div>
            </div>

            <div class="input-group verification-code" :class="{ 'error': errors.code }">
              <div class="code-input-container">
                <input
                    type="text"
                    v-model="resetForm.code"
                    placeholder="请输入验证码"
                    @blur="validateCode"
                />
                <button
                    class="get-code-btn-inline"
                    @click="getVerificationCode"
                    :disabled="!resetForm.phone || errors.phone || codeSent"
                >
                  {{ codeSent ? `${countdown}秒后重试` : '获取验证码' }}
                </button>
              </div>
              <div class="error-container">
                <div v-if="errors.code" class="error-message">{{ errors.code }}</div>
              </div>
            </div>

            <div class="input-group" :class="{ 'error': errors.newPassword }">
              <div class="password-input-container">
                <input
                    :type="passwordVisible ? 'text' : 'password'"
                    v-model="resetForm.newPassword"
                    placeholder="请输入新密码"
                    @blur="validateNewPassword"
                />
                <span class="password-toggle" @click="togglePasswordVisibility">
                  <i :class="['eye-icon', passwordVisible ? 'visible' : '']"></i>
                </span>
              </div>
              <div class="error-container">
                <div v-if="errors.newPassword" class="error-message">{{ errors.newPassword }}</div>
              </div>
            </div>

            <div class="input-group" :class="{ 'error': errors.confirmPassword }">
              <div class="password-input-container">
                <input
                    :type="confirmPasswordVisible ? 'text' : 'password'"
                    v-model="resetForm.confirmPassword"
                    placeholder="请再次输入新密码"
                    @blur="validateConfirmPassword"
                />
                <span class="password-toggle" @click="toggleConfirmPasswordVisibility">
                  <i :class="['eye-icon', confirmPasswordVisible ? 'visible' : '']"></i>
                </span>
              </div>
              <div class="error-container">
                <div v-if="errors.confirmPassword" class="error-message">{{ errors.confirmPassword }}</div>
              </div>
            </div>

            <button
                class="login-btn"
                @click="resetPassword"
                :disabled="!isFormValid || isVerifying"
            >
              {{ isVerifying ? '验证中...' : '重置密码' }}
            </button>
            <div class="login-link">
              <a href="#" @click="goToLogin">返回登录</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {postAnyData, postLogin, postNotAuth} from "@/api/login";
import SlideNotification from '@/components/common/header/SlideNotification.vue';
import backgroundlogin from '@/views/Login/backgroundlogin.vue';


export default {
  name: "forgetpass",
  components: {
    SlideNotification,
    backgroundlogin
  },
  data() {
    return {
      resetForm: {
        phone: '',
        code: '',
        newPassword: '',
        confirmPassword: ''
      },
      passwordVisible: false,
      confirmPasswordVisible: false,
      errors: {
        phone: '',
        code: '',
        newPassword: '',
        confirmPassword: ''
      },
      codeSent: false,
      countdown: 60,
      timer: null,
      showCodeSent: false, // 验证码发送提示状态
      isVerifying: false,  // 验证中状态
      showVerifying: false, // 验证中提示状态
      notificationMinHeight: '50px' // 自定义通知高度
    }
  },
  computed: {
    isFormValid() {
      return this.resetForm.phone &&
          this.resetForm.code &&
          this.resetForm.newPassword &&
          this.resetForm.confirmPassword &&
          !this.errors.phone &&
          !this.errors.code &&
          !this.errors.newPassword &&
          !this.errors.confirmPassword;
    }
  },
  created() {
    // 页面创建时检查本地存储中的计时器状态
    this.$emit('hiden-layout')
  },

  methods: {
    validatePhone() {
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!this.resetForm.phone) {
        this.errors.phone = '请输入手机号';
      } else if (!phoneRegex.test(this.resetForm.phone)) {
        this.errors.phone = '请输入有效的手机号';
      } else {
        this.errors.phone = '';
      }
    },

    validateCode() {
      if (!this.resetForm.code) {
        this.errors.code = '请输入验证码';
      } else if (this.resetForm.code.length !== 4 || !/^\d+$/.test(this.resetForm.code)) {
        this.errors.code = '验证码格式不正确';
      } else {
        this.errors.code = '';
      }
    },

    validateNewPassword() {
      const newPassword = this.resetForm.newPassword;
      this.errors.newPassword = ''; // 重置错误信息

      if (!newPassword) {
        this.errors.newPassword = '请输入新密码';
        return;
      }

      const hasMinLength = newPassword.length >= 8;
      if (!hasMinLength) {
        this.errors.newPassword = '密码长度至少为8位';
        return;
      }

      // 下面是三选二的强度项
      const hasLetter = /[a-zA-Z]/.test(newPassword);
      const hasNumber = /[0-9]/.test(newPassword);
      const hasSymbol = /[!@#$%^&*()_+\-=$${};':"\\|,.<>\/?]/.test(newPassword);
      const hasUpperLower = /[A-Z]/.test(newPassword) && /[a-z]/.test(newPassword);

      let strengthCount = 0;
      if (hasLetter && hasNumber) strengthCount++; // 字母+数字
      if (hasSymbol) strengthCount++;
      if (hasUpperLower) strengthCount++;

      if (strengthCount >= 2) {
        this.errors.newPassword = ''; // 密码符合要求
      } else {
        const missingRequirements = [];
        if (!(hasLetter && hasNumber)) missingRequirements.push('包含数字和字母');
        if (!hasSymbol) missingRequirements.push('包含特殊符号');
        if (!hasUpperLower) missingRequirements.push('包含大小写字母');

        this.errors.newPassword = `密码强度不足，请满足以下至少两项要求：${missingRequirements.join('、')}`;
      }

      // 校验确认密码是否一致
      if (this.resetForm.confirmPassword) {
        this.validateConfirmPassword();
      }
    },


    validateConfirmPassword() {
      if (!this.resetForm.confirmPassword) {
        this.errors.confirmPassword = '请确认新密码';
      } else if (this.resetForm.confirmPassword !== this.resetForm.newPassword) {
        this.errors.confirmPassword = '两次输入的密码不一致';
      } else {
        this.errors.confirmPassword = '';
      }
    },

    async getVerificationCode() {
      this.validatePhone();
      if (this.errors.phone) return;

      // 调用发送验证码API
      try {
        // 显示发送中状态
        this.codeSent = true;
        this.isSendingCode = true;
        // 调用发送验证码接口
        const response = await postLogin("/auth/sendCode", {
          phone: this.resetForm.phone
        });

        // 处理响应
        if (response.data.code === 200) {
          this.showCodeSent = true;
          setTimeout(() => {
            this.showCodeSent = false;
          }, 3000);
          this.startCountdown();
        } else {
          this.errors.code = response.data.msg || '验证码发送失败';
          this.codeSent = false; // 发送失败时可重新发送
        }
      } catch (error) {
        this.errors.code = '验证码发送失败，请稍后重试';
        this.codeSent = false;
      } finally {
        this.isSendingCode = false;
      }
    },

    startCountdown() {
      // 清除可能存在的旧定时器
      if (this.timer) {
        clearInterval(this.timer);
      }

      this.countdown = 60;
      // 使用固定的时间间隔
      this.timer = setInterval(() => {
        if (this.countdown <= 1) {
          clearInterval(this.timer);
          this.codeSent = false;
          this.countdown = 60;
        } else {
          this.countdown--;
        }
      }, 1000);
    },

    // 验证验证码是否正确
    verifyCode() {
      return new Promise((resolve, reject) => {
        this.isVerifying = true;
        this.showVerifying = true;

        postAnyData("/auth/verifyCode", {
          phone: this.resetForm.phone,
          code: this.resetForm.code
        }).then(res => {
          this.showVerifying = false;
          if (res.data && res.data.code === 200) {
            resolve(true);
          } else {
            this.errors.code = res.data.msg || '验证码验证失败';
            reject(new Error(res.data.msg || '验证码验证失败'));
          }
        }).catch(err => {
          this.showVerifying = false;
          this.errors.code = '验证码验证失败';
          reject(err);
        }).finally(() => {
          this.isVerifying = false;
        });
      });
    },

    resetPassword() {
      // 验证所有字段
      this.validatePhone();
      this.validateCode();
      this.validateNewPassword();
      this.validateConfirmPassword();

      if (!this.isFormValid) return;

      // 验证验证码
      this.verifyCode().then(verified => {
        if (verified) {
          // 验证码正确，继续重置密码
          postLogin("/auth/resetPassword", {
            phone: this.resetForm.phone,
            code: this.resetForm.code,
            password: this.resetForm.newPassword,
          }).then(res => {
            if (res.data && res.data.code === 200) {
              this.goToLogin();
            } else {
              this.errors.code= res.data.message || '密码重置失败';
            }
          }).catch(err => {
            this.errors.code= res.data.message || '网络异常，密码重置失败';
          });
        }
      }).catch(err => {
      });
    },

    goToLogin() {
      this.$router.push('/login');
    },

    togglePasswordVisibility() {
      this.passwordVisible = !this.passwordVisible;
    },

    toggleConfirmPasswordVisibility() {
      this.confirmPasswordVisible = !this.confirmPasswordVisible;
    }
  },
  beforeDestroy() {

    this.$emit('hiden-layout')
  }
}
</script>

<style scoped>
/* Full page layout */
.login-page {
  display: flex;
  min-height: 100vh;
  overflow: hidden;
}

/* Left side styling */
.left-side {
  flex: 1;
  position: relative;
  background: linear-gradient(135deg, #cdb3e5, #5127d5);
  display: flex;
  flex-direction: column;
  /*padding: 2rem;*/
  color: #030303;
}

.logo-container {
  z-index: 2;
  padding: 1rem 0;
}

.logo {
  font-size: 15px;
  font-weight: bold;
  margin: 0;
  color: white;
}

.logo-subtitle {
  font-size: 14px;
  margin: 0;
}

.headline-container {
  margin-top: 10px;
  text-align: left;
  z-index: 2;
  max-width: 80%;
  margin-left: 100px;
}

.headline {
  font-size: 25px;
  font-weight: 500;
  margin-bottom: 1rem;
}

.subheadline {
  font-size: 14px;
  line-height: 1.6;
  opacity: 0.9;
  margin-bottom: 1rem
}

/* Animated background */
.animated-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  opacity: 0.4;
}

.hex-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.hex {
  position: absolute;
  width: 100px;
  height: 110px;
  background: rgba(255, 255, 255, 0.15);
  clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
}

.hex1 {
  top: 20%;
  left: 10%;
  transform: scale(1.5);
  animation: float 8s infinite ease-in-out;
}

.hex2 {
  top: 60%;
  left: 20%;
  transform: scale(1.2);
  animation: float 7s infinite ease-in-out reverse;
}

.hex3 {
  top: 30%;
  left: 50%;
  transform: scale(1.3);
  animation: float 10s infinite ease-in-out 1s;
}

.hex4 {
  top: 70%;
  left: 70%;
  transform: scale(1.1);
  animation: float 6s infinite ease-in-out 2s;
}

.hex5 {
  top: 40%;
  left: 80%;
  transform: scale(1.4);
  animation: float 9s infinite ease-in-out 3s;
}

.floating-cube {
  position: absolute;
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.2);
  top: 25%;
  left: 30%;
  animation: float 8s infinite ease-in-out, rotate 15s infinite linear;
}

.floating-sphere {
  position: absolute;
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.15);
  top: 50%;
  left: 40%;
  animation: float 10s infinite ease-in-out reverse;
}

.floating-diamond {
  position: absolute;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  transform: rotate(45deg);
  top: 65%;
  left: 60%;
  animation: float 7s infinite ease-in-out 2s;
}

.ripple-effect {
  position: absolute;
  width: 200px;
  height: 200px;
  border-radius: 50%;
  border: 3px solid rgba(255, 255, 255, 0.1);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation: ripple 6s infinite linear;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(20px);
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes ripple {
  0% {
    width: 0;
    height: 0;
    opacity: 0.8;
  }
  100% {
    width: 300px;
    height: 300px;
    opacity: 0;
  }
}

/* Right side styling */
.right-side {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f8f9fa;
  padding: 2rem;
}

.login-form-container {
  background-color: white;
  border-radius: 8px;
  padding: 2rem;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.login-form-container h3 {
  font-size: 24px;
  font-weight: 500;
  margin-bottom: 20px;
  text-align: center;
  color: #333;
}

/* 表单容器，定义固定高度 */
.form-container {
  min-height: 300px;
  position: relative;
}

.login-form {
  margin-top: 20px;
  width: 100%;
}

.form-note {
  color: #999;
  font-size: 12px;
  margin-bottom: 15px;
}

.input-group {
  margin-bottom: 20px;
  position: relative;
}

.input-group input {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s;
}

.input-group input:focus {
  outline: none;
  border-color: #6a26cd;
}

/*错误输入框变红*/
.input-group.error input {
  outline: none;
  border: 2px solid #ff4d4f; /* 红色边框 */
}

/* 错误信息容器，固定高度 */
.error-container {
  min-height: 10px;
  display: block;
}

/* 验证码输入框与按钮在同一行 */
.verification-code .code-input-container {
  display: flex;
  gap: 10px;
}

.verification-code input {
  flex: 1;
  margin-right: 0; /* 移除原有的右边距 */
}

.error-container {
  order: 3; /* 将错误容器放在最下方 */
  width: 100%;
  margin-top: 4px;
}

.get-code-btn-inline {
  flex-shrink: 0;
  width: 130px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  background-color: #2196f3;
  color: #ffffff;
}

/* 密码输入容器，确保图标垂直居中 */
.password-input-container {
  position: relative;
  margin-top: 30px;
  /*margin-bottom: 20px;*/
}

.password-toggle {
  position: absolute;

  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}
input[type="password"]::-ms-reveal,
input[type="password"]::-webkit-credentials-auto-fill-button,
input[type="password"]::-webkit-clear-button {
  display: none !important;
  pointer-events: none;
}

.eye-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path><circle cx="12" cy="12" r="3"></circle></svg>');
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0.5;
}

.eye-icon.visible {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path><circle cx="12" cy="12" r="3"></circle><line x1="1" y1="1" x2="23" y2="23"></line></svg>');
}

.login-btn {
  width: 100%;
  padding: 12px 0;
  background-color: #2196f3;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.login-btn:hover:not(:disabled) {
  background-color: #043ef1;
}

.login-btn:disabled {
  background-color: #2196f3;
  cursor: not-allowed;
}

/*错误提示词提示*/
.error-message {
  color: #f44336;
  font-size: 12px;
  margin-top: 0px;
  max-height: 10px;
}

/* Login link */
.login-link {
  margin-top: 15px;
  text-align: center;
  font-size: 14px;
}

.login-link a {
  color: #4169E1;
  text-decoration: none;
}
.logo-area {
  flex: 0 0 auto;
  margin-right: 10px;
}

.logo-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  cursor: pointer;
}

.logo-area img {
  height: 30px;
  max-width: 100%;
}

.logo-text {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-left: 10px;
}
@media screen and (max-width: 768px) {
  .left-side {
    display: none; /* 在手机端隐藏左侧背景 */
  }

  .right-side {
    flex: 1 0 100%; /* 让右侧占据全部宽度 */
    padding: 1rem; /* 减少内边距以适应小屏幕 */
  }

  .login-form-container {
    max-width: 100%; /* 让登录表单占据全部可用宽度 */
    box-shadow: none; /* 移除阴影以节省空间 */
    padding: 1.5rem; /* 调整内边距 */
  }

}
</style>
