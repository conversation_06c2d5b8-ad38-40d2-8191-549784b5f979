<template>
  <div class="mider-container">
    <!-- Floating sidebar with icons -->
    <div class="mider-sidebar">
<!--      <div class="coupon-tag">-->
<!--        <span>领</span>-->
<!--        <span>优</span>-->
<!--        <span>惠</span>-->
<!--        <span>券</span>-->
<!--      </div>-->

      <div class="icon-wrapper">
        <div class="icon-item" @mouseenter="showPopup('wechat')" @mouseleave="hidePopup('wechat')">
          <i class="iconfont icon-wechat">
            <svg viewBox="0 0 1024 1024" width="24" height="24">
              <path d="M690.1 377.4c5.9 0 11.8 0.2 17.6 0.5-24.4-128.7-158.3-227.1-319.9-227.1C209 150.8 64 271.4 64 420.2c0 81.1 43.6 154.2 111.9 203.6 5.5 3.9 9.1 10.3 9.1 17.6 0 2.4-0.5 4.6-1.1 6.9-5.5 20.3-14.2 52.8-14.6 54.3-0.7 2.6-1.7 5.2-1.7 7.9 0 5.9 4.8 10.8 10.8 10.8 2.3 0 4.2-0.9 6.2-2l70.9-40.9c5-2.9 10.5-4.6 16.3-4.6 3 0 5.9 0.5 8.7 1.4 35.9 10.5 74.6 16.2 114.2 16.2 9.8 0 19.5-0.3 29-1C624.9 597 702.1 497.6 702.1 378.1c0-0.2-0.1-0.4-0.1-0.7 0 0-0.1-0.3-0.1-0.5-0.8 0-1.8-0.1-2.8-0.1-3.1 0-6.2 0.2-9 0.6z" fill="#82c91e"></path>
              <path d="M380.5 503.3c-27.9 0-50.7-22.8-50.7-50.7s22.8-50.7 50.7-50.7 50.7 22.8 50.7 50.7-22.8 50.7-50.7 50.7zM534.3 503.3c-27.9 0-50.7-22.8-50.7-50.7s22.8-50.7 50.7-50.7 50.7 22.8 50.7 50.7-22.8 50.7-50.7 50.7z" fill="#82c91e"></path>
              <path d="M683.5 673.6c82.1 0 151.2-56 171.5-131.8 4.1-15.2-9.3-29.5-24.9-26.6-3.5 0.6-7.1 1-10.7 1-34.4 0-62.4-28-62.4-62.4 0-1.6 0.1-3.3 0.2-4.9 0.8-19.3-24.5-28.7-35.7-13.5-39.1 53-102.7 87.5-174.5 87.5-12.3 0-24.4-1-36.1-3-5.2-0.9-10.5 0.8-14.1 4.5-4.6 4.6-16.7 17.2-16.7 17.2l-0.6 0.6c-5.4 5.4-8.7 12.3-9.5 19.8-0.9 8.5 1.9 17.1 7.7 23.6l1.3 1.3c6.3 7.5 15.6 11.7 25.6 11.7 7.1 0 14.1-2.3 19.7-6.5l10.6-7.8c6.2-4.5 14.8-3.3 19.3 2.8 4.3 5.8 3.4 14-2.1 18.8-14.4 12.6-32.9 19.7-51.9 19.7-4.3 0-8.7-0.3-13-1-21.3-3.5-40.2-14.4-54.3-31.3l-0.3-0.3c-12.9-15.7-19.4-34.9-18.3-54.5 0.8-14.5 6.4-28.5 16.1-39.7 1.2-1.4 2.5-2.6 3.8-3.9l31.5-31.5c1.9-1.9 3.8-3.9 5.4-6.1 5.8-7.7 8.9-17 8.9-26.8 0-11.3-4.2-22.1-11.8-30.4-12.4-13.5-31.5-15.2-48.5-11.7-91.3 18.9-160.7 100.1-160.8 196.9 0 109.5 89.1 198.6 198.6 198.6 36.7 0 71.9-9.9 102.8-28.5 5.7-3.4 12.8-3.3 18.5 0.2 36.7 22.7 79.1 35.3 123.2 35.3z" fill="#82c91e"></path>
              <path d="M770.9 576.9m-50.7 0a50.7 50.7 0 1 0 101.4 0 50.7 50.7 0 1 0-101.4 0Z" fill="#82c91e"></path>
              <path d="M602.4 576.9m-50.7 0a50.7 50.7 0 1 0 101.4 0 50.7 50.7 0 1 0-101.4 0Z" fill="#82c91e"></path>
            </svg>
          </i>
          <!-- WeChat QR code popup -->
          <div class="popup-container wechat-popup" v-show="activePopup === 'wechat'">
            <div class="popup-content">
              <h3>微信扫码咨询客服</h3>
              <div class="qr-code">
                <img :src="wechatQRCode" alt="微信客服二维码">
              </div>
<!--              <div class="popup-footer">-->
<!--                <button class="btn-consult">桌面版微信点击咨询客服</button>-->
<!--              </div>-->
            </div>
          </div>
        </div>

        <div class="icon-item" @mouseenter="showPopup('contact')" @mouseleave="hidePopup('contact')">
          <i class="iconfont icon-phone">
            <svg viewBox="0 0 1024 1024" width="24" height="24">
              <path d="M877.1 238.7L770.6 132.3c-13-13-30.4-20.3-48.8-20.3s-35.8 7.2-48.8 20.3L558.3 246.8c-13 13-20.3 30.5-20.3 48.9 0 18.5 7.2 35.8 20.3 48.9l89.6 89.7c-20.6 47.8-49.6 90.6-86.4 127.3-36.7 36.9-79.6 66-127.2 86.6l-89.6-89.7c-13-13-30.4-20.3-48.8-20.3-18.5 0-35.8 7.2-48.8 20.3L132.3 673c-13 13-20.3 30.5-20.3 48.9 0 18.5 7.2 35.8 20.3 48.9l106.4 106.4c22.2 22.2 52.8 34.9 84.2 34.9 6.5 0 12.8-0.5 19.2-1.6 132.4-21.8 263.8-92.3 369.9-198.3C818 606 888.4 474.6 910.4 342.1c6.3-37.6-6.3-76.3-33.3-103.4z" fill="#1677ff"></path>
            </svg>
          </i>
          <!-- Contact information popup -->
          <div class="popup-container contact-popup" v-show="activePopup === 'contact'">
            <div class="popup-content">
              <h3>商务合作请联系电话</h3>
              <p class="phone-number">13913283376</p>
              <p>使用问题请咨询微信客服</p>
              <div class="qr-code">
                <img :src="contactQRCode" alt="联系电话二维码">
              </div>
            </div>
          </div>
        </div>

        <div class="icon-item" @click="showFeedbackModal" @mouseenter="showTooltip" @mouseleave="hideTooltip">
          <i class="iconfont icon-feedback">
            <svg viewBox="0 0 1024 1024" width="24" height="24">
              <path d="M573 421c-23.1 0-41 17.9-41 40s17.9 40 41 40c21.1 0 39-17.9 39-40s-17.9-40-39-40zM293 421c-23.1 0-41 17.9-41 40s17.9 40 41 40c21.1 0 39-17.9 39-40s-17.9-40-39-40z" fill="#fa8c16"></path>
              <path d="M894 345c-48.1-66-115.3-110.1-189-130v0.1c-17.1-19-36.4-36.5-58-52.1-163.7-119-393.5-82.7-513 81-96.3 133-92.2 311.9 6 439l0.8 132.6c0 3.2 0.5 6.4 1.5 9.4 5.3 16.9 23.3 26.2 40.1 20.9L309 806c33.4 11.9 68.4 18 104.7 18 72.2 0 143.1-21.4 205.3-61.8 57.4-37.3 104-89.9 131.8-150.1 23.9-52 35.2-106.8 33.6-160.9 28.8-29.9 50.4-64.7 63.6-103 17.8-52.7 13.1-108.7-14-155.1z m-68 106.7c-25.8 53.9-71.3 89.7-126.2 99.8-5.7 1-9.7 3.9-13 10.3-8.1 16.1-19.1 30.6-32.7 43.1-11.8 10.9-24.9 20.5-42.9 14.6-9.7-3.2-15.2-9.4-19.1-20.8-6.6-19.2-12.9-18.2-27.2-9.9-81.5 47.5-165.2 37.3-232.5-25.3-30.5-28.6-54.2-65.1-62.7-109-8.4-44 0.4-89.6 26.7-130.5 22.9-35.6 52.4-62 89.9-79.3 24.8-11.5 50.9-18.7 78.5-20.5 101.5-6.2 197.2 41.2 244.1 132.2 12.8 24.8 25.6 60.7 30.4 87.3 5.5 30.3 2.9 53.5-13.3 87z" fill="#fa8c16"></path>
            </svg>
          </i>
          <!-- Tooltip for feedback icon -->
          <div class="tooltip" v-show="showFeedbackTooltip">
            反馈与建议
          </div>
        </div>
      </div>

    </div>

    <!-- Feedback modal - Reduced size -->
    <div class="modal-overlay" v-if="showModal" @click.self="closeFeedbackModal">
      <div class="feedback-modal">
        <div class="modal-header">
          <h3>反馈与建议</h3>
          <span class="close-btn" @click="closeFeedbackModal">×</span>
        </div>

        <div class="modal-body">
          <div class="alert alert-warning">
            <i class="iconfont icon-warning">
              <svg viewBox="0 0 1024 1024" width="16" height="16">
                <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296z m32 440c-26.5 0-48-21.5-48-48s21.5-48 48-48 48 21.5 48 48-21.5 48-48 48z" fill="#faad14"></path>
              </svg>
            </i>
            您的反馈我们将认真对待，不断优化产品功能和体验
          </div>

          <div class="form-group">
            <label class="required">问题类型：</label>
            <div class="select-wrapper">
              <select v-model="feedback.type" required>
                <option value="">请选择</option>
                <option value="功能建议">功能建议</option>
                <option value="产品故障">产品故障</option>
                <option value="体验不佳">体验不佳</option>
                <option value="账户相关">账户相关</option>
                <option value="其他">其他</option>
              </select>
            </div>
            <p class="error-text" v-if="!feedback.type && showErrors">请选择问题类型</p>
          </div>

          <div class="form-group">
            <label class="required">问题描述：</label>
            <textarea v-model="feedback.description" placeholder="请输入" required></textarea>
            <p class="error-text" v-if="!feedback.description && showErrors">请输入问题描述</p>
          </div>


          <div class="form-group">
            <label class="required1">问题截图：</label>
            <div
                class="image-uploader"
                @click="triggerFileUpload"
                @dragover.prevent
                @drop.prevent="onFileDrop"
            >
              <input
                  type="file"
                  ref="fileInput"
                  accept="image/*"
                  @change="onFileChange"
                  style="display: none"
              >
              <div v-if="!feedback.image" class="upload-placeholder">
                <i class="iconfont icon-upload">
                  <svg viewBox="0 0 1024 1024" width="28" height="28">
                    <path d="M518.3 459c-3.2-4.1-9.4-4.1-12.6 0l-112 141.7c-4.1 5.2-0.4 12.9 6.3 12.9h73.9V856c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V613.7H624c6.7 0 10.4-7.7 6.3-12.9L518.3 459z" fill="#bfbfbf"></path>
                    <path d="M811.4 366.7C765.6 245.9 648.9 160 512.2 160S258.8 245.8 213 366.6C127.3 389.1 64 467.2 64 560c0 110.5 89.5 200 199.9 200H304c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8h-40.1c-33.7 0-65.4-13.4-89-37.7-23.5-24.2-36-56.8-34.9-90.6 0.9-26.4 9.9-51.2 26.2-72.1 16.7-21.3 40.1-36.8 66.1-43.7l37.9-9.9 13.9-36.6c8.6-22.8 20.6-44.1 35.7-63.4 14.9-19.2 32.6-35.9 52.4-49.9 41.1-28.9 89.5-44.2 140-44.2s98.9 15.3 140 44.2c19.9 14 37.5 30.8 52.4 49.9 15.1 19.3 27.1 40.7 35.7 63.4l13.8 36.5 37.8 10c26.1 6.9 49.6 22.5 66.3 43.8 16.4 21 25.4 45.9 26.3 72.3 1.1 33.9-11.4 66.5-34.9 90.7-23.6 24.4-55.3 37.7-89 37.7h-40c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h40.1C758.5 760 848 670.5 848 560c0-92.7-63.1-170.7-148.6-193.3z" fill="#bfbfbf"></path>
                  </svg>
                </i>
                <p>点击/拖拽至此处添加图片</p>
              </div>
              <div v-else class="preview-container">
                <img :src="feedback.imagePreview" alt="问题截图预览" class="image-preview">
                <div class="remove-image" @click.stop="removeImage">×</div>
              </div>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <button class="btn btn-cancel" @click="closeFeedbackModal">取消</button>
          <button class="btn btn-submit" @click="confirmSubmit">提交</button>
        </div>
      </div>
    </div>

    <!-- Confirmation Dialog -->
    <div class="modal-overlay" v-if="showConfirmation">
      <div class="confirmation-dialog">
        <div class="confirmation-icon">
          <svg viewBox="0 0 1024 1024" width="32" height="32">
            <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m193.5 301.7l-210.6 292a31.8 31.8 0 0 1-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z" fill="#52c41a"></path>
          </svg>
        </div>
        <div class="confirmation-title">提交成功</div>
        <div class="confirmation-message">感谢您的反馈，我们会尽快处理</div>
        <div class="confirmation-actions">
          <button class="btn btn-primary" @click="closeConfirmation">确定</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Mider',
  data() {
    return {
      activePopup: null,
      showModal: false,
      showErrors: false,
      showFeedbackTooltip: false,
      showConfirmation: false,
      wechatQRCode: require('@/assets/images/footer/wechat.jpg'), // 替换为实际路径
      contactQRCode: require('@/assets/images/footer/wechat.jpg'), // 替换为实际路径
      feedback: {
        type: '',
        description: '',
        instanceId: '',
        image: null,
        imagePreview: null
      }
    }
  },
  methods: {
    showPopup(type) {
      this.activePopup = type;
    },
    hidePopup(type) {
      if (this.activePopup === type) {
        this.activePopup = null;
      }
    },
    showTooltip() {
      this.showFeedbackTooltip = true;
    },
    hideTooltip() {
      this.showFeedbackTooltip = false;
    },
    showFeedbackModal() {
      this.showModal = true;
      this.showErrors = false;
    },
    closeFeedbackModal() {
      this.showModal = false;
      // Reset form
      this.feedback = {
        type: '',
        description: '',
        instanceId: '',
        image: null,
        imagePreview: null
      };
      this.showErrors = false;
    },
    triggerFileUpload() {
      this.$refs.fileInput.click();
    },
    onFileChange(event) {
      const file = event.target.files[0];
      if (file) {
        this.processImage(file);
      }
    },
    onFileDrop(event) {
      const file = event.dataTransfer.files[0];
      if (file && file.type.match('image.*')) {
        this.processImage(file);
      }
    },
    processImage(file) {
      if (file && file.type.match('image.*')) {
        this.feedback.image = file;

        // Create preview
        const reader = new FileReader();
        reader.onload = (e) => {
          this.feedback.imagePreview = e.target.result;
        };
        reader.readAsDataURL(file);
      }
    },
    removeImage() {
      this.feedback.image = null;
      this.feedback.imagePreview = null;
    },
    confirmSubmit() {
      // Validate form
      this.showErrors = true;
      if (!this.feedback.type || !this.feedback.description) {
        return;
      }

      // Submit feedback
      this.submitFeedback();
    },
    submitFeedback() {
      // Here you would typically send the data to your backend

      // Create FormData object if there's an image
      if (this.feedback.image) {
        const formData = new FormData();
        formData.append('type', this.feedback.type);
        formData.append('description', this.feedback.description);
        formData.append('instanceId', this.feedback.instanceId);
        formData.append('image', this.feedback.image);

        // Send formData to your API
        // this.$axios.post('/api/feedback', formData)
      }

      // Close feedback modal and show confirmation dialog
      this.showModal = false;
      this.showConfirmation = true;

      // Reset form data
      this.feedback = {
        type: '',
        description: '',
        instanceId: '',
        image: null,
        imagePreview: null
      };
    },
    closeConfirmation() {
      this.showConfirmation = false;
    }
  }
}
</script>

<style scoped>
.mider-container {
  position: relative;

}

/* Sidebar styles */
.mider-sidebar {
  /* 原有定位属性 */
  position: fixed;
  right: 0;
  top: 85%;             /* 当前纵向定位 */
  transform: translateY(-50%);
  z-index: 1000;

  /* 新增尺寸控制 */
  width: 42px;         /* 固定宽度 */
  height: 50vh;         /* 视口高度的50% */
  max-width: 90%;       /* 防溢出保护 */
  max-height: 80vh;     /* 高度上限 */
  min-height: 200px;    /* 高度下限 */
  /*box-sizing: 0; !* 包含内边距 *!*/

  /* 布局优化 */
  display: flex;
  flex-direction: column;
}


.coupon-tag {
  background-color: #ff6b6b;
  color: white;
  padding: 8px 12px;
  border-radius: 8px 0 0 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 14px;
  margin-bottom: 10px;
}

.icon-wrapper {
  display: flex;
  flex-direction: column;
  background-color: white;
  border-radius: 8px 0 0 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.icon-item {
  position: relative;
  padding: 15px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
}

.icon-item:last-child {
  border-bottom: none;
}

.icon-item i {
  font-size: 24px;
  color: #666;
  display: flex;
  justify-content: center;
  align-items: center;
}

.icon-item:hover i {
  color: #1890ff;
}

.icon-item:hover i svg path {
  fill: #1890ff;
}

/* Tooltip styles */
.tooltip {
  position: absolute;
  left: -90px;
  top: 50%;
  transform: translateY(-50%);
  background-color: #fff;
  color: #333;
  padding: 6px 10px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  font-size: 12px;
  white-space: nowrap;
  z-index: 1000;
}

.tooltip:after {
  content: '';
  position: absolute;
  right: -6px;
  top: 50%;
  transform: translateY(-50%);
  border-width: 6px 0 6px 6px;
  border-style: solid;
  border-color: transparent transparent transparent white;
}

/* Popup styles */
.popup-container {
  position: absolute;
  right: 60px;
  top: 50%;
  transform: translateY(-50%);
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  width: 240px;
  z-index: 1000;
}

.popup-container:after {
  content: '';
  position: absolute;
  right: -10px;
  top: 50%;
  transform: translateY(-50%);
  border-width: 10px 0 10px 10px;
  border-style: solid;
  border-color: transparent transparent transparent white;
}

.popup-content {
  padding: 20px;
  text-align: center;
}

.popup-content h3 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.qr-code {
  margin: 10px 0;
}

.qr-code img {
  width: 150px;
  height: 150px;
}

.phone-number {
  font-size: 18px;
  font-weight: bold;
  margin: 5px 0;
}

.popup-footer {
  margin-top: 10px;
}

.btn-consult {
  background-color: #f5f5f5;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  color: #333;
  width: 100%;
}

/* Modal styles - Reduced size */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1001;
}

.feedback-modal {
  background-color: white;
  border-radius: 8px;
  width: 500px;
  max-width: 100vw;
  max-height: 100vh;
  display: flex;
  flex-direction: column;
}

.modal-header {
  padding: 14px 20px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.close-btn {
  cursor: pointer;
  font-size: 20px;
  color: #999;
}

.modal-body {
  padding: 20px;
  overflow-y: auto;
}

.alert {
  padding: 8px 12px;
  border-radius: 4px;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  font-size: 13px;
}

.alert-warning {
  background-color: #fffbe6;
  border: 1px solid #ffe58f;
  font-size: 10px;
  color: #d48806;
}

.alert i {
  margin-right: 8px;
  display: flex;
  align-items: center;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-size: 13px;
  color: #333;
}

.required:before {
  content: '*';
  color: #ff4d4f;
  margin-left: 4px;
}
.required1:before {
  content: '';
  color: #ff4d4f;
  margin-left: 9px;
}

input, select, textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 13px;
  transition: all 0.3s;
}

input:focus, select:focus, textarea:focus {
  outline: none;
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

textarea {
  min-height: 70px;
  resize: vertical;
}

.select-wrapper {
  position: relative;
}

.select-wrapper:after {
  content: '';
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 12px;
  color: #999;
  pointer-events: none;
}

.error-text {
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 4px;
}

.image-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  background-color: #fafafa;
  min-height: 120px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.image-uploader:hover {
  border-color: #40a9ff;
}

.upload-placeholder {
  color: #999;
}

.upload-placeholder i {
  font-size: 32px;
  display: block;
  margin-bottom: 8px;
}

.preview-container {
  position: relative;
  width: 100%;
}

.image-preview {
  max-width: 100%;
  max-height: 200px;
  border-radius: 4px;
}

.remove-image {
  position: absolute;
  top: -10px;
  right: -10px;
  width: 24px;
  height: 24px;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.modal-footer {
  padding: 10px 24px 24px;
  text-align: right;
}

.btn {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
  border: none;
}

.btn-cancel {
  background-color: white;
  border: 1px solid #d9d9d9;
  color: #333;
  margin-right: 12px;
}

.btn-cancel:hover {
  color: #40a9ff;
  border-color: #40a9ff;
}

.btn-submit {
  background-color: #1890ff;
  color: white;
}

.btn-submit:hover {
  background-color: #40a9ff;
}

/* For responsiveness */
@media (max-width: 768px) {
  .popup-container {
    width: 200px;
  }

  .qr-code img {
    width: 120px;
    height: 120px;
  }
}

.confirmation-dialog {
  background-color: white;
  border-radius: 8px;
  padding: 24px;
  /*margin-left: 50%;*/
  /*text-align: left;*/
  width: 500px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.confirmation-icon {
  margin-left: 1%;
  margin-bottom: -37px;
}

.confirmation-title {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 8px;
  margin-left: 10%;
  color: #333;
}

.confirmation-message {
  color: #666;
  font-size: 14px;
  margin-left: 10%;
  margin-bottom: 24px;
}

.confirmation-actions {
  display: flex;
  justify-content: center;
}

.btn-primary {
  background-color: #1677ff;
  /*margin-right: 2px;*/
  color: white;
  cursor: pointer;
  border: none;
  border-radius: 4px;
  padding: 8px 32px;
  font-size: 14px;
  margin-left: 80%;
}

.btn-primary:hover {
  background-color: #4096ff;
}

/* Form styles */
.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: #333;
}

.required:after {
  content: '*';
  color: #f5222d;
  margin-left: 4px;
}

.select-wrapper {
  position: relative;
}

.select-wrapper:after {
  content: '▼';
  font-size: 10px;
  color: #999;
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
}

select {
  width: 100%;
  padding: 8px 10px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background-color: white;
  font-size: 14px;
  appearance: none;
}

textarea {
  width: 100%;
  padding: 8px 10px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  min-height: 100px;
  resize: vertical;
  font-size: 14px;
}

.error-text {
  color: #f5222d;
  font-size: 12px;
  margin-top: 4px;
}

.image-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  padding: 16px;
  text-align: center;
  cursor: pointer;
  background-color: #fafafa;
  transition: border-color 0.3s;
}

.image-uploader:hover {
  border-color: #1890ff;
}

.upload-placeholder {
  color: #bfbfbf;
}

.upload-placeholder p {
  margin-top: 8px;
  font-size: 13px;
}

.preview-container {
  position: relative;
}

.image-preview {
  max-width: 100%;
  max-height: 200px;
}

.remove-image {
  position: absolute;
  top: -10px;
  right: -10px;
  width: 20px;
  height: 20px;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.modal-footer {
  padding: 16px 20px;
  border-top: 1px solid #f0f0f0;
  text-align: right;
}

.btn {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
}

.btn-cancel {
  background-color: white;
  border: 1px solid #d9d9d9;
  color: #666;
  margin-right: 8px;
}

.btn-cancel:hover {
  color: #1890ff;
  border-color: #1890ff;
}

.btn-submit {
  background-color: #1677ff;
  border: none;
  color: white;
}

.btn-submit:hover {
  background-color: #4096ff;
}
</style>