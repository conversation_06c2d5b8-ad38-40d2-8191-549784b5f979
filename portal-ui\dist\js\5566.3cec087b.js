"use strict";(self["webpackChunkportal_ui"]=self["webpackChunkportal_ui"]||[]).push([[5566],{4300:function(t,s,a){a.d(s,{Z:function(){return v}});var i=function(){var t=this,s=t._self._c;return s("main",{staticClass:"page-wrapper"},[t._t("default"),s("Mider"),s("chatAi"),s("Footer")],2)},e=[],o=a(2711),n=a(3644),c=a(9484),r={name:"Layout",components:{Footer:o.Z,Mider:n.Z,chatAi:c.Z}},l=r,m=a(1001),d=(0,m.Z)(l,i,e,!1,null,"0ce69a34",null),v=d.exports},2711:function(t,s,a){a.d(s,{Z:function(){return l}});var i=function(){var t=this,s=t._self._c;return s("div",{staticClass:"footer"},[s("div",{staticClass:"footer-content"},[s("div",{staticClass:"footer-column"},[s("div",{staticClass:"footer-title"},[t._v("售前咨询热线")]),s("div",{staticClass:"footer-phone"},[t._v("13913283376")]),s("div",{staticClass:"footer-links"},[s("a",{on:{click:function(s){return t.navigateTo("/index")}}},[s("div",{staticClass:"footer-link"},[t._v("首页")])]),s("a",{on:{click:function(s){return t.navigateTo("/product")}}},[s("div",{staticClass:"footer-link"},[t._v("AI算力市场")])])])]),s("div",{staticClass:"footer-column"},[s("div",{staticClass:"footer-title"},[t._v("支持与服务")]),s("div",{staticClass:"footer-links"},[s("div",{staticClass:"footer-link"},[t._v("联系我们")]),s("a",{on:{click:function(s){return t.navigateTo("/help")}}},[s("div",{staticClass:"footer-link"},[t._v("帮助文档")])]),s("div",{staticClass:"footer-link"},[t._v("公告")]),s("div",{staticClass:"footer-link"},[t._v("提交建议")])])]),t._m(0),t._m(1),t._m(2)]),t._m(3)])},e=[function(){var t=this,s=t._self._c;return s("div",{staticClass:"footer-column"},[s("div",{staticClass:"footer-title"},[t._v("关注天工开物")]),s("div",{staticClass:"footer-links"},[s("div",{staticClass:"footer-link"},[t._v("关注天工开物")]),s("div",{staticClass:"footer-link"},[t._v("天工开物公众号")]),s("div",{staticClass:"footer-link"},[t._v("天工开物微博")]),s("div",{staticClass:"footer-link"},[t._v("天工开物支持与服务")])])])},function(){var t=this,s=t._self._c;return s("div",{staticClass:"footer-column"},[s("div",{staticClass:"footer-title"},[t._v("联系专属客服")]),s("div",{staticClass:"footer-qrcode"},[s("img",{attrs:{src:a(46),alt:"联系专属客服二维码"}})])])},function(){var t=this,s=t._self._c;return s("div",{staticClass:"footer-column"},[s("div",{staticClass:"footer-title"},[t._v("官方公众号")]),s("div",{staticClass:"footer-qrcode"},[s("img",{attrs:{src:a(46),alt:"官方公众号二维码"}})])])},function(){var t=this,s=t._self._c;return s("div",{staticClass:"footer-bottom"},[s("div",{staticClass:"footer-copyright"},[s("a",{staticStyle:{color:"inherit","text-decoration":"none"},attrs:{href:"https://beian.miit.gov.cn",target:"_blank"}},[t._v(" 苏ICP备2025171841号-1 ")]),t._v(" 天工开物智能科技（苏州）有限公司 ALL RIGHTS RESERVED. ")])])}],o=(a(7658),{name:"Footer",data(){return{}},methods:{navigateTo(t){this.currentPath&&this.currentPath!==t?(this.previousActivePath=this.currentPath,this.$nextTick((()=>{const s=document.querySelectorAll(".nav-link, .btn-login");s.forEach((s=>{(s.classList.contains("active")||"/login"===t&&s.classList.contains("btn-login"))&&!s.classList.contains("active-exit")&&(s.classList.add("active-exit"),setTimeout((()=>{s.classList.remove("active-exit")}),300))})),this.currentPath=t}))):this.currentPath=t,this.$route.path===t?this.$nextTick((()=>{window.scrollTo({top:0,behavior:"instant"}),this.$router.go(0)})):(this.$router.push(t),window.scrollTo({top:0,behavior:"instant"}))}}}),n=o,c=a(1001),r=(0,c.Z)(n,i,e,!1,null,"2d6e9349",null),l=r.exports},4140:function(t,s,a){a.r(s),a.d(s,{default:function(){return m}});var i=function(){var t=this,s=t._self._c;return s("Layout",[s("div",{staticClass:"about-banner"},[s("div",{staticClass:"about-banner-bg",staticStyle:{background:"url('images/earth.gif') center/cover",opacity:"0.7","z-index":"1",position:"absolute",top:"0",left:"0",right:"0",bottom:"0"}}),s("div",{staticClass:"particles-container"},t._l(50,(function(a){return s("div",{key:a,staticClass:"particle",style:t.getParticleStyle()})})),0),s("div",{staticClass:"data-streams"},t._l(8,(function(t){return s("div",{key:t,staticClass:"data-stream"})})),0),s("div",{staticClass:"light-effects"},[s("div",{staticClass:"light-beam light-beam-1"}),s("div",{staticClass:"light-beam light-beam-2"}),s("div",{staticClass:"light-beam light-beam-3"})]),s("div",{staticClass:"banner-content"},[s("h1",{staticClass:"banner-title"},[s("span",{staticClass:"title-word",attrs:{"data-text":"承天工之智"}},[t._v("承天工之智")]),s("span",{staticClass:"title-separator"},[t._v("，")]),s("span",{staticClass:"title-word",attrs:{"data-text":"启万物之能"}},[t._v("启万物之能")])]),s("div",{staticClass:"banner-subtitle-container"},[s("p",{staticClass:"banner-subtitle typing-effect"},[t._v("我们相信")]),s("p",{staticClass:"banner-subtitle typing-effect",staticStyle:{"animation-delay":"1s"}},[t._v("人类无需再围成一台机器")]),s("p",{staticClass:"banner-subtitle typing-effect",staticStyle:{"animation-delay":"2s"}},[t._v("而是用智能连接彼此，释放算力的真正价值")])])])]),s("section",{staticClass:"about-section"},[s("div",{staticClass:"container"},[s("div",{staticClass:"am-g"},[s("div",{staticClass:"am-u-md-6"},[s("div",{staticClass:"our-company-text"},[s("h1",[t._v("关于我们")]),s("p",{staticStyle:{"font-size":"17px"}},[t._v(' 天工开物智能科技（苏州）有限公司，致力于打造面向企业级用户的高性能计算解决方案， 围绕"高效调度、低门槛使用、专业保障"的核心理念，为 AI、大模型、图形渲染、科研计算等场景提供灵活、稳定、弹性的算力支持。 ')]),s("p",{staticStyle:{"font-size":"17px"}},[t._v(" 我们基于全国分布式算力网络，自主构建智算调度平台，整合GPU资源与数据中心节点， 为企业提供从算力资源租用、模型部署优化到全流程运维服务的一站式专业方案。 ")]),s("p",{staticStyle:{"font-size":"17px"}},[t._v(" 在智能时代的浪潮中，天工开物致力于打造企业级专业算力服务平台， 以全国分布式高性能计算网络为基础，提供稳定、高效、灵活可控的算力解决方案。 ")])])]),s("div",{staticClass:"am-u-md-6"},[s("div",{staticClass:"our-company-quote"},[s("div",{staticClass:"our-company-img"},[s("img",{attrs:{src:"images/tgkw_about.jpg",alt:"天工开物智能科技",loading:"lazy"}})])])])])])]),s("section",{staticClass:"our-mission"},[s("div",{staticClass:"container"},[s("div",{staticClass:"section--header"},[s("h2",{staticClass:"section--title"},[t._v("选择我们的理由")])]),s("div",{staticClass:"am-g"},[s("div",{staticClass:"am-u-sm-12 am-u-md-6 am-u-lg-3"},[s("div",{staticClass:"our_mission--item"},[s("div",{staticClass:"our_mission--item_media"},[s("i",{staticClass:"am-icon-server",staticStyle:{"font-size":"48px",color:"#1470FF"}})]),s("h4",{staticClass:"our_mission--item_title"},[t._v("企业级专业服务")]),s("div",{staticClass:"our_mission--item_body"},[s("p",[t._v("为AI、科研、图形渲染、工业仿真等场景，提供稳定高效的高性能计算支持")])])])]),s("div",{staticClass:"am-u-sm-12 am-u-md-6 am-u-lg-3"},[s("div",{staticClass:"our_mission--item"},[s("div",{staticClass:"our_mission--item_media"},[s("i",{staticClass:"am-icon-globe",staticStyle:{"font-size":"48px",color:"#1470FF"}})]),s("h4",{staticClass:"our_mission--item_title"},[t._v("全国分布式节点布局")]),s("div",{staticClass:"our_mission--item_body"},[s("p",[t._v("多地部署，动态调度，资源灵活，负载均衡，响应迅速")])])])]),s("div",{staticClass:"am-u-sm-12 am-u-md-6 am-u-lg-3"},[s("div",{staticClass:"our_mission--item"},[s("div",{staticClass:"our_mission--item_media"},[s("i",{staticClass:"am-icon-cogs",staticStyle:{"font-size":"48px",color:"#1470FF"}})]),s("h4",{staticClass:"our_mission--item_title"},[t._v("灵活弹性 + 高性价比")]),s("div",{staticClass:"our_mission--item_body"},[s("p",[t._v("自研调度平台，支持定制，与大客户深度合作")])])])]),s("div",{staticClass:"am-u-sm-12 am-u-md-6 am-u-lg-3"},[s("div",{staticClass:"our_mission--item"},[s("div",{staticClass:"our_mission--item_media"},[s("i",{staticClass:"am-icon-users",staticStyle:{"font-size":"48px",color:"#1470FF"}})]),s("h4",{staticClass:"our_mission--item_title"},[t._v("更懂企业的算力伙伴")]),s("div",{staticClass:"our_mission--item_body"},[s("p",[t._v("从需求对接、技术支持到运维保障，全流程一对一服务")])])])])])])]),s("section",{staticClass:"our-team"},[s("div",{staticClass:"container"},[s("div",{staticClass:"section--header"},[s("h2",{staticClass:"section--title"},[t._v("核心团队")]),s("p",{staticClass:"section--description"},[t._v(" 核心团队来自知名AI云计算厂商、IDC运维专家与高校科研机构 ")])]),s("div",{staticClass:"am-g"},[s("div",{staticClass:"am-u-sm-12 am-u-md-4"},[s("div",{staticClass:"team-box"},[s("div",{staticClass:"our-team-img"},[s("img",{attrs:{src:"images/techteam.png",alt:"技术团队",loading:"lazy"}})]),s("div",{staticClass:"team_member--body"},[s("h4",{staticClass:"team_member--name"},[t._v("技术研发")]),s("span",{staticClass:"team_member--position"},[t._v("专业的研发团队，深耕AI算力调度与优化")])])])]),s("div",{staticClass:"am-u-sm-12 am-u-md-4"},[s("div",{staticClass:"team-box"},[s("div",{staticClass:"our-team-img"},[s("img",{attrs:{src:"images/yunwei.png",alt:"运维团队",loading:"lazy"}})]),s("div",{staticClass:"team_member--body"},[s("h4",{staticClass:"team_member--name"},[t._v("运维保障")]),s("span",{staticClass:"team_member--position"},[t._v("5x8小时专业运维，确保服务稳定可靠")])])])]),s("div",{staticClass:"am-u-sm-12 am-u-md-4"},[s("div",{staticClass:"team-box"},[s("div",{staticClass:"our-team-img"},[s("img",{attrs:{src:"images/khjl.png",alt:"客服团队",loading:"lazy"}})]),s("div",{staticClass:"team_member--body"},[s("h4",{staticClass:"team_member--name"},[t._v("客户服务")]),s("span",{staticClass:"team_member--position"},[t._v("专业客户经理，提供一对一贴心服务")])])])])])])]),s("section",{staticClass:"contact-section"},[s("div",{staticClass:"container"},[s("div",{staticClass:"am-g"},[s("div",{staticClass:"am-u-md-4"},[s("div",{staticClass:"contact-item"},[s("i",{staticClass:"am-icon-phone"}),s("h4",[t._v("联系电话")]),s("p",[t._v("13913283376")])])]),s("div",{staticClass:"am-u-md-4"},[s("div",{staticClass:"contact-item"},[s("i",{staticClass:"am-icon-envelope"}),s("h4",[t._v("官方公众号")]),s("p",[t._v("昆山新质创新数字技术研究院")])])]),s("div",{staticClass:"am-u-md-4"},[s("div",{staticClass:"contact-item"},[s("i",{staticClass:"am-icon-map-marker"}),s("h4",[t._v("公司地址")]),s("p",[t._v("江苏省苏州市昆山市玉山镇祖冲之路1699号昆山工业技术研究院综合南楼1404")])])])])])]),s("section",{staticClass:"cta-section"},[s("div",{staticClass:"cta-content"},[s("h2",[t._v("连接智算未来，让高性能计算像水电一样可得、可控、可负担")]),s("div",{staticClass:"cta-buttons"},[s("button",{staticClass:"am-btn am-btn-primary",on:{click:t.startTrial}},[t._v("立即开始")])])])])])},e=[],o=(a(7658),a(4300)),n={name:"AboutView",components:{Layout:o.Z},data(){return{connectionLines:[]}},mounted(){this.generateConnectionLines(),this.startCounterAnimation(),this.optimizeForMobile(),this.handleOrientationChange(),this.optimizeForLargeScreen()},methods:{startTrial(){this.$router.push("/product")},contactUs(){console.log("联系我们")},getParticleStyle(){return{left:100*Math.random()+"%",top:100*Math.random()+"%",animationDelay:10*Math.random()+"s",animationDuration:20*Math.random()+10+"s"}},getNodeStyle(){return{left:90*Math.random()+5+"%",top:90*Math.random()+5+"%",animationDelay:3*Math.random()+"s"}},generateConnectionLines(){const t=[];for(let s=0;s<15;s++)t.push({id:s,x1:100*Math.random(),y1:100*Math.random(),x2:100*Math.random(),y2:100*Math.random()});this.connectionLines=t},startCounterAnimation(){const t=document.querySelectorAll(".stat-number");t.forEach((t=>{const s=parseInt(t.getAttribute("data-target")),a=s/100;let i=0;const e=setInterval((()=>{i+=a,i>=s?(t.textContent=s,clearInterval(e)):t.textContent=Math.floor(i)}),50)}))},optimizeForMobile(){const t=window.innerWidth<=768;t&&(this.reduceMobileAnimations(),this.optimizeTouchEvents(),this.preloadCriticalImages())},reduceMobileAnimations(){const t=document.querySelectorAll(".particle");t.forEach(((t,s)=>{s>20&&(t.style.display="none")}));const s=document.querySelectorAll(".data-stream");s.forEach(((t,s)=>{s>4&&(t.style.display="none")}))},optimizeTouchEvents(){const t=document.querySelectorAll(".our_mission--item, .team-box, .contact-item, .am-btn");t.forEach((t=>{t.addEventListener("touchstart",(()=>{t.style.transform="translateY(-2px)"}),{passive:!0}),t.addEventListener("touchend",(()=>{setTimeout((()=>{t.style.transform=""}),150)}),{passive:!0})}))},preloadCriticalImages(){const t=["/images/tiangonghead.jpeg","/images/back1.webp"];t.forEach((t=>{const s=new Image;s.src=t}))},handleOrientationChange(){window.addEventListener("orientationchange",(()=>{setTimeout((()=>{this.generateConnectionLines(),this.optimizeForMobile(),this.optimizeForLargeScreen()}),500)}))},optimizeForLargeScreen(){const t=window.innerWidth>=1920;t?(document.body.classList.add("large-screen-mode"),this.optimizeBackgroundGif(),this.enhanceAnimationsForLargeScreen()):document.body.classList.remove("large-screen-mode")},optimizeBackgroundGif(){const t=document.querySelector(".about-banner-bg");t&&window.innerWidth>=1920&&(t.style.backgroundSize="contain",t.style.backgroundRepeat="no-repeat",t.style.backgroundPosition="center")},enhanceAnimationsForLargeScreen(){if(window.innerWidth>=1920){const t=document.querySelector(".particles-container");if(t&&t.children.length<80)for(let s=50;s<80;s++){const s=document.createElement("div");s.className="particle",s.style.cssText=`\n              left: ${100*Math.random()}%;\n              top: ${100*Math.random()}%;\n              animation-delay: ${10*Math.random()}s;\n              animation-duration: ${20*Math.random()+10}s;\n            `,t.appendChild(s)}}}},beforeDestroy(){document.body.classList.remove("large-screen-mode")}},c=n,r=a(1001),l=(0,r.Z)(c,i,e,!1,null,"0c27527e",null),m=l.exports}}]);
//# sourceMappingURL=5566.3cec087b.js.map