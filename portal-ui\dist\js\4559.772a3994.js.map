{"version": 3, "file": "js/4559.772a3994.js", "mappings": "kJAAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,OAAO,CAACE,YAAY,gBAAgB,CAACJ,EAAIK,GAAG,WAAWH,EAAG,SAASA,EAAG,UAAUA,EAAG,WAAW,EACpK,EACII,EAAkB,G,8BCctB,GACAC,KAAA,SACAC,WAAA,CAAAC,OAAA,IAAAC,MAAA,IAAAC,OAAAA,EAAAA,IClB+P,I,UCQ3PC,GAAY,OACd,EACAb,EACAO,GACA,EACA,KACA,WACA,MAIF,EAAeM,EAAiB,O,uDCnBhC,IAAIb,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,UAAU,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIa,GAAG,YAAYX,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIa,GAAG,iBAAiBX,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,IAAI,CAACY,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOf,EAAIgB,WAAW,SAAS,IAAI,CAACd,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIa,GAAG,UAAUX,EAAG,IAAI,CAACY,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOf,EAAIgB,WAAW,WAAW,IAAI,CAACd,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIa,GAAG,kBAAkBX,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIa,GAAG,WAAWX,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIa,GAAG,UAAUX,EAAG,IAAI,CAACY,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOf,EAAIgB,WAAW,QAAQ,IAAI,CAACd,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIa,GAAG,YAAYX,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIa,GAAG,QAAQX,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIa,GAAG,cAAcb,EAAIiB,GAAG,GAAGjB,EAAIiB,GAAG,GAAGjB,EAAIiB,GAAG,KAAKjB,EAAIiB,GAAG,IACtjC,EACIX,EAAkB,CAAC,WAAY,IAAIN,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIa,GAAG,YAAYX,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIa,GAAG,YAAYX,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIa,GAAG,aAAaX,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIa,GAAG,YAAYX,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIa,GAAG,kBACza,EAAE,WAAY,IAAIb,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIa,GAAG,YAAYX,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACgB,MAAM,CAAC,IAAMC,EAAQ,IAAqC,IAAM,kBACtQ,EAAE,WAAY,IAAInB,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIa,GAAG,WAAWX,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACgB,MAAM,CAAC,IAAMC,EAAQ,IAAqC,IAAM,iBACrQ,EAAE,WAAY,IAAInB,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,IAAI,CAACkB,YAAY,CAAC,MAAQ,UAAU,kBAAkB,QAAQF,MAAM,CAAC,KAAO,4BAA4B,OAAS,WAAW,CAAClB,EAAIa,GAAG,0BAA0Bb,EAAIa,GAAG,8CAC7S,GC2EA,G,QAAA,CACAN,KAAA,SACAc,OACA,OACA,CAEA,EACAC,QAAA,CACAN,WAAAO,GAEA,KAAAC,aAAA,KAAAA,cAAAD,GACA,KAAAE,mBAAA,KAAAD,YAGA,KAAAE,WAAA,KACA,MAAAC,EAAAC,SAAAC,iBAAA,yBACAF,EAAAG,SAAAC,KACAA,EAAAC,UAAAC,SAAA,WACA,WAAAV,GAAAQ,EAAAC,UAAAC,SAAA,gBACAF,EAAAC,UAAAC,SAAA,iBACAF,EAAAC,UAAAE,IAAA,eAGAC,YAAA,KACAJ,EAAAC,UAAAI,OAAA,iBACA,KACA,IAIA,KAAAZ,YAAAD,CAAA,KAGA,KAAAC,YAAAD,EAIA,KAAAc,OAAAd,OAAAA,EACA,KAAAG,WAAA,KACAY,OAAAC,SAAA,CACAC,IAAA,EACAC,SAAA,YAEA,KAAAC,QAAAC,GAAA,OAIA,KAAAD,QAAAE,KAAArB,GACAe,OAAAC,SAAA,CACAC,IAAA,EACAC,SAAA,YAGA,KCtIwQ,I,UCQpQ7B,GAAY,OACd,EACAb,EACAO,GACA,EACA,KACA,WACA,MAIF,EAAeM,EAAiB,O,oECnBhC,IAAIb,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,SAAS,CAACA,EAAG,MAAM,CAACE,YAAY,mBAAmBgB,YAAY,CAAC,MAAQ,SAAS,CAAClB,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,KAAK,CAACE,YAAY,qBAAqB,CAACJ,EAAIa,GAAG,cAAcX,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,KAAK,CAACE,YAAY,iBAAiB,CAACF,EAAG,KAAK,CAACA,EAAG,cAAc,CAACgB,MAAM,CAAC,GAAK,MAAM,CAAClB,EAAIa,GAAG,SAAS,GAAGX,EAAG,KAAK,CAACE,YAAY,aAAa,CAACJ,EAAIa,GAAG,kBAAkBX,EAAG,MAAM,CAACE,YAAY,WAAW,CAACF,EAAG,MAAM,CAACE,YAAY,YAAYgB,YAAY,CAAC,YAAY,WAAW,CAAClB,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,KAAK,CAACE,YAAY,kBAAkB,CAACJ,EAAIa,GAAG,UAAUX,EAAG,IAAI,CAACE,YAAY,wBAAwB,CAACJ,EAAIa,GAAG,2CAA2CX,EAAG,MAAMF,EAAIa,GAAG,mCAAmCX,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,QAAQ,CAACF,EAAG,MAAM,CAACE,YAAY,QAAQJ,EAAI6C,GAAI7C,EAAI8C,SAASC,SAAS,SAASC,EAAQC,GAAO,OAAO/C,EAAG,MAAM,CAACgD,IAAID,EAAM7C,YAAY,gCAAgC,CAACF,EAAG,MAAM,CAACE,YAAY,WAAW,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACgB,MAAM,CAAC,IAAM8B,EAAQG,MAAM,IAAM,QAAQjD,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,KAAK,CAACA,EAAG,cAAc,CAACgB,MAAM,CAAC,GAAK,CAACX,KAAK,cAAc6C,OAAO,CAACC,OAAOL,EAAQM,YAAY,IAAM,KAAK,CAACtD,EAAIa,GAAGb,EAAIuD,GAAGP,EAAQQ,WAAW,GAAGtD,EAAG,KAAK,CAACE,YAAY,iBAAiB,CAACF,EAAG,KAAK,CAACE,YAAY,4BAA4B,CAACJ,EAAIa,GAAGb,EAAIuD,GAAGP,EAAQS,mBAAmBvD,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,IAAI,CAACF,EAAIa,GAAGb,EAAIuD,GAAGP,EAAQU,mBAAmBxD,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,cAAc,CAACE,YAAY,OAAOc,MAAM,CAAC,GAAK,CAACX,KAAK,cAAc6C,OAAO,CAACC,OAAOL,EAAQM,cAAc,CAACtD,EAAIa,GAAG,WAAW,MAAM,IAAG,GAAGX,EAAG,KAAK,CAACE,YAAY,gBAAgBgB,YAAY,CAAC,aAAa,WAAW,CAAClB,EAAG,KAAK,CAACyD,MAAwB,IAAlB3D,EAAI4D,UAAkB,cAAc,GAAG9C,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOf,EAAI6D,YAAY7D,EAAI4D,UAAY,EAAE,IAAI,CAAC1D,EAAG,IAAI,CAACgB,MAAM,CAAC,KAAO,MAAM,CAAClB,EAAIa,GAAG,SAASb,EAAI6C,GAAI7C,EAAI8C,SAASgB,OAAO,SAASC,EAAEd,GAAO,OAAO/C,EAAG,KAAK,CAACgD,IAAID,EAAMU,MAAM3D,EAAI4D,YAAcG,EAAI,YAAY,GAAGjD,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOf,EAAI6D,YAAYE,EAAE,IAAI,CAAC7D,EAAG,IAAI,CAACgB,MAAM,CAAC,KAAO,MAAM,CAAClB,EAAIa,GAAGb,EAAIuD,GAAGQ,OAAO,IAAG7D,EAAG,KAAK,CAACyD,MAAM3D,EAAI4D,YAAc5D,EAAI8C,SAASgB,MAAQ,cAAc,GAAGhD,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOf,EAAI6D,YAAY7D,EAAI4D,UAAY,EAAE,IAAI,CAAC1D,EAAG,IAAI,CAACgB,MAAM,CAAC,KAAO,MAAM,CAAClB,EAAIa,GAAG,UAAU,YACz7E,EACIP,EAAkB,G,UCgFtB,GACAC,KAAA,WACAC,WAAA,CAAAwD,OAAAA,EAAAA,GACA3C,OACA,OACAyB,SAAA,GACAc,UAAA,EAEA,EACAK,UACA,KAAAC,WAAA,EACA,EACA5C,QAAA,CACA4C,WAAAN,GACA,KAAAO,WAAA,iBAAAP,KAAAQ,MAAAC,IACAA,IACA,KAAAvB,SAAAuB,EAAAhD,KAAAA,KACAiD,QAAAC,IAAA,KAAAzB,UAEA,GAEA,EACAe,YAAAE,GACA,IAAAA,EACA,KAAAH,UAAA,EACAG,IAAA,KAAAjB,SAAAgB,MAAA,EACA,KAAAF,UAAA,KAAAd,SAAAgB,OAEA,KAAAF,UAAAG,EACA,KAAAG,WAAAH,GAEA,ICjHwP,I,UCOpPnD,GAAY,OACd,EACAb,EACAO,GACA,EACA,KACA,WACA,MAIF,EAAeM,EAAiB,O", "sources": ["webpack://portal-ui/./src/components/common/Layout.vue", "webpack://portal-ui/src/components/common/Layout.vue", "webpack://portal-ui/./src/components/common/Layout.vue?a648", "webpack://portal-ui/./src/components/common/Layout.vue?e255", "webpack://portal-ui/./src/components/common/footer/Footer.vue", "webpack://portal-ui/src/components/common/footer/Footer.vue", "webpack://portal-ui/./src/components/common/footer/Footer.vue?6062", "webpack://portal-ui/./src/components/common/footer/Footer.vue?8b5d", "webpack://portal-ui/./src/views/NewsView.vue", "webpack://portal-ui/src/views/NewsView.vue", "webpack://portal-ui/./src/views/NewsView.vue?0f71", "webpack://portal-ui/./src/views/NewsView.vue?978c"], "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('main',{staticClass:\"page-wrapper\"},[_vm._t(\"default\"),_c('Mider'),_c('chatAi'),_c('Footer')],2)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n\t<main class=\"page-wrapper\">\r\n\t\t<!-- <Header/> -->\r\n\t\t\t<slot></slot>\r\n    <Mider/>\r\n    <chatAi/>\r\n\t\t<Footer/>\r\n\t</main>\r\n</template>\r\n\r\n<script>\r\n// import Header from \"@/components/common/header/Header\";\r\nimport Footer from \"@/components/common/footer/Footer\";\r\nimport Mider from \"@/components/common/mider/Mider\";\r\nimport chatAi from \"@/components/common/mider/chatAi\";\r\n\r\nexport default {\r\n\tname: \"Layout\",\r\n\tcomponents:{Footer, Mider, chatAi}\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.main-content{\r\n\twidth: 100%;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Layout.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Layout.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Layout.vue?vue&type=template&id=0ce69a34&scoped=true&\"\nimport script from \"./Layout.vue?vue&type=script&lang=js&\"\nexport * from \"./Layout.vue?vue&type=script&lang=js&\"\nimport style0 from \"./Layout.vue?vue&type=style&index=0&id=0ce69a34&prod&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0ce69a34\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"footer\"},[_c('div',{staticClass:\"footer-content\"},[_c('div',{staticClass:\"footer-column\"},[_c('div',{staticClass:\"footer-title\"},[_vm._v(\"售前咨询热线\")]),_c('div',{staticClass:\"footer-phone\"},[_vm._v(\"13913283376\")]),_c('div',{staticClass:\"footer-links\"},[_c('a',{on:{\"click\":function($event){return _vm.navigateTo('/index')}}},[_c('div',{staticClass:\"footer-link\"},[_vm._v(\"首页\")])]),_c('a',{on:{\"click\":function($event){return _vm.navigateTo('/product')}}},[_c('div',{staticClass:\"footer-link\"},[_vm._v(\"AI算力市场\")])])])]),_c('div',{staticClass:\"footer-column\"},[_c('div',{staticClass:\"footer-title\"},[_vm._v(\"支持与服务\")]),_c('div',{staticClass:\"footer-links\"},[_c('div',{staticClass:\"footer-link\"},[_vm._v(\"联系我们\")]),_c('a',{on:{\"click\":function($event){return _vm.navigateTo('/help')}}},[_c('div',{staticClass:\"footer-link\"},[_vm._v(\"帮助文档\")])]),_c('div',{staticClass:\"footer-link\"},[_vm._v(\"公告\")]),_c('div',{staticClass:\"footer-link\"},[_vm._v(\"提交建议\")])])]),_vm._m(0),_vm._m(1),_vm._m(2)]),_vm._m(3)])\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"footer-column\"},[_c('div',{staticClass:\"footer-title\"},[_vm._v(\"关注天工开物\")]),_c('div',{staticClass:\"footer-links\"},[_c('div',{staticClass:\"footer-link\"},[_vm._v(\"关注天工开物\")]),_c('div',{staticClass:\"footer-link\"},[_vm._v(\"天工开物公众号\")]),_c('div',{staticClass:\"footer-link\"},[_vm._v(\"天工开物微博\")]),_c('div',{staticClass:\"footer-link\"},[_vm._v(\"天工开物支持与服务\")])])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"footer-column\"},[_c('div',{staticClass:\"footer-title\"},[_vm._v(\"联系专属客服\")]),_c('div',{staticClass:\"footer-qrcode\"},[_c('img',{attrs:{\"src\":require(\"@/assets/images/footer/wechat.jpg\"),\"alt\":\"联系专属客服二维码\"}})])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"footer-column\"},[_c('div',{staticClass:\"footer-title\"},[_vm._v(\"官方公众号\")]),_c('div',{staticClass:\"footer-qrcode\"},[_c('img',{attrs:{\"src\":require(\"@/assets/images/footer/wechat.jpg\"),\"alt\":\"官方公众号二维码\"}})])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"footer-bottom\"},[_c('div',{staticClass:\"footer-copyright\"},[_c('a',{staticStyle:{\"color\":\"inherit\",\"text-decoration\":\"none\"},attrs:{\"href\":\"https://beian.miit.gov.cn\",\"target\":\"_blank\"}},[_vm._v(\" 苏ICP备2025171841号-1 \")]),_vm._v(\" 天工开物智能科技（苏州）有限公司 ALL RIGHTS RESERVED. \")])])\n}]\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"footer\">\r\n    <div class=\"footer-content\">\r\n      <!-- First column -->\r\n      <div class=\"footer-column\">\r\n        <div class=\"footer-title\">售前咨询热线</div>\r\n        <div class=\"footer-phone\">13913283376</div>\r\n        <div class=\"footer-links\">\r\n          <a @click=\"navigateTo('/index')\"><div class=\"footer-link\">首页</div></a>\r\n          <a @click=\"navigateTo('/product')\"><div class=\"footer-link\">AI算力市场</div></a>\r\n\r\n<!--          <div class=\"footer-link\">AI算力市场</div>-->\r\n\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Second column -->\r\n      <div class=\"footer-column\">\r\n        <div class=\"footer-title\">支持与服务</div>\r\n        <div class=\"footer-links\">\r\n          <div class=\"footer-link\">联系我们</div>\r\n          <a @click=\"navigateTo('/help')\"><div class=\"footer-link\">帮助文档</div></a>\r\n          <div class=\"footer-link\">公告</div>\r\n          <div class=\"footer-link\">提交建议</div>\r\n        </div>\r\n      </div>\r\n\r\n<!--      &lt;!&ndash; Third column &ndash;&gt;-->\r\n<!--      <div class=\"footer-column\">-->\r\n<!--        <div class=\"footer-title\">账户管理</div>-->\r\n<!--        <div class=\"footer-links\">-->\r\n<!--          <div class=\"footer-link\">控制台</div>-->\r\n<!--          <div class=\"footer-link\">账号管理</div>-->\r\n<!--          <div class=\"footer-link\">充值付款</div>-->\r\n<!--          <div class=\"footer-link\">线下款 / 电汇</div>-->\r\n<!--          <div class=\"footer-link\">索取发票</div>-->\r\n<!--          <div class=\"footer-link\">合规性</div>-->\r\n<!--        </div>-->\r\n<!--      </div>-->\r\n\r\n      <!-- Fourth column -->\r\n      <div class=\"footer-column\">\r\n        <div class=\"footer-title\">关注天工开物</div>\r\n        <div class=\"footer-links\">\r\n          <div class=\"footer-link\">关注天工开物</div>\r\n          <div class=\"footer-link\">天工开物公众号</div>\r\n          <div class=\"footer-link\">天工开物微博</div>\r\n          <div class=\"footer-link\">天工开物支持与服务</div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Fifth column -->\r\n      <div class=\"footer-column\">\r\n        <div class=\"footer-title\">联系专属客服</div>\r\n        <div class=\"footer-qrcode\">\r\n          <img src=\"@/assets/images/footer/wechat.jpg\" alt=\"联系专属客服二维码\" />\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Sixth column -->\r\n      <div class=\"footer-column\">\r\n        <div class=\"footer-title\">官方公众号</div>\r\n        <div class=\"footer-qrcode\">\r\n          <img src=\"@/assets/images/footer/wechat.jpg\" alt=\"官方公众号二维码\" />\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Bottom footer links -->\r\n    <div class=\"footer-bottom\">\r\n\r\n      <div class=\"footer-copyright\">\r\n        <a href=\"https://beian.miit.gov.cn\" target=\"_blank\" style=\"color: inherit; text-decoration: none;\">\r\n          苏ICP备2025171841号-1\r\n        </a>\r\n           天工开物智能科技（苏州）有限公司 ALL RIGHTS RESERVED. </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"Footer\",\r\n  data() {\r\n    return {\r\n      // Data can be added here if needed\r\n    }\r\n  },\r\n  methods: {\r\n    navigateTo(path) {\r\n      // 记录当前活动路径作为上一个活动路径\r\n      if (this.currentPath && this.currentPath !== path) {\r\n        this.previousActivePath = this.currentPath;\r\n\r\n        // 为当前活动链接和登录按钮添加 active-exit 类\r\n        this.$nextTick(() => {\r\n          const navLinks = document.querySelectorAll('.nav-link, .btn-login');\r\n          navLinks.forEach(link => {\r\n            if ((link.classList.contains('active') ||\r\n                    (path === '/login' && link.classList.contains('btn-login'))) &&\r\n                !link.classList.contains('active-exit')) {\r\n              link.classList.add('active-exit');\r\n\r\n              // 等待动画完成后移除 active-exit 类\r\n              setTimeout(() => {\r\n                link.classList.remove('active-exit');\r\n              }, 300); // 匹配你的 CSS transition 持续时间 (0.3s)\r\n            }\r\n          });\r\n\r\n          // 更新当前路径\r\n          this.currentPath = path;\r\n        });\r\n      } else {\r\n        this.currentPath = path;\r\n      }\r\n\r\n      // 如果当前路径与目标路径相同，则重新加载页面\r\n      if (this.$route.path === path) {\r\n        this.$nextTick(() => {\r\n          window.scrollTo({\r\n            top: 0,\r\n            behavior: 'instant' // 使用即时滚动而不是平滑滚动\r\n          });\r\n          this.$router.go(0); // 刷新当前页面\r\n        });\r\n      } else {\r\n        // 不同路径，正常导航并滚动到顶部\r\n        this.$router.push(path);\r\n        window.scrollTo({\r\n          top: 0,\r\n          behavior: 'instant'\r\n        });\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.footer {\r\n  max-width: 2560px;\r\n  width: 100%;\r\n  background-color: #424242;\r\n  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;\r\n  color: white;\r\n  padding: 0;\r\n  margin: 0px;\r\n}\r\n\r\n.footer-content {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  max-width: 2560px;\r\n  margin: 0 40px;\r\n  padding: 20px 0;\r\n}\r\n\r\n.footer-column {\r\n  flex: 1;\r\n  min-width: 150px;\r\n  padding: 0 15px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.footer-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  margin-bottom: 15px;\r\n  color: white;\r\n}\r\n\r\n.footer-phone {\r\n  font-size: 16px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.footer-links {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.footer-link {\r\n  margin-bottom: 10px;\r\n  cursor: pointer;\r\n  color: white;\r\n  font-size: 14px;\r\n  transition: color 0.2s;\r\n}\r\n\r\n.footer-link:hover {\r\n  color: #1890ff;\r\n}\r\n\r\n.footer-qrcode {\r\n  width: 120px;\r\n  height: 120px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.footer-qrcode img {\r\n  max-width: 100%;\r\n  max-height: 100%;\r\n  margin-left: -18%;\r\n}\r\n\r\n.footer-bottom {\r\n  border-top: 1px solid #e8e8e8;\r\n  padding: 20px 0;\r\n  text-align: center;\r\n  max-width: 2560px;\r\n  margin: 0 40px;\r\n}\r\n\r\n.footer-bottom-links {\r\n  margin-bottom: 10px;\r\n  text-align: left;\r\n}\r\n\r\n.footer-bottom-link {\r\n  color: white;\r\n  margin: 0 10px;\r\n  font-size: 14px;\r\n  text-decoration: none;\r\n  transition: color 0.2s;\r\n}\r\n\r\n.footer-bottom-link:hover {\r\n  color: #1890ff;\r\n  text-decoration: underline;\r\n}\r\n\r\n.footer-copyright, .footer-license {\r\n  font-size: 15px;\r\n  text-align: center;\r\n  color: white;\r\n  margin-left: 10px;\r\n}\r\n</style>", "import mod from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Footer.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Footer.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Footer.vue?vue&type=template&id=2d6e9349&scoped=true&\"\nimport script from \"./Footer.vue?vue&type=script&lang=js&\"\nexport * from \"./Footer.vue?vue&type=script&lang=js&\"\nimport style0 from \"./Footer.vue?vue&type=style&index=0&id=2d6e9349&prod&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2d6e9349\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('Layout',[_c('div',{staticClass:\"layout-container\",staticStyle:{\"width\":\"100%\"}},[_c('div',{staticClass:\"page-header\"},[_c('div',{staticClass:\"am-container\"},[_c('h1',{staticClass:\"page-header-title\"},[_vm._v(\"公司动态\")])])]),_c('div',{staticClass:\"breadcrumb-box\"},[_c('div',{staticClass:\"am-container\"},[_c('ol',{staticClass:\"am-breadcrumb\"},[_c('li',[_c('router-link',{attrs:{\"to\":\"/\"}},[_vm._v(\"首页\")])],1),_c('li',{staticClass:\"am-active\"},[_vm._v(\"公司动态\")])])])])]),_c('div',{staticClass:\"section\"},[_c('div',{staticClass:\"container\",staticStyle:{\"max-width\":\"1160px\"}},[_c('div',{staticClass:\"section--header\"},[_c('h2',{staticClass:\"section--title\"},[_vm._v(\"最近新闻\")]),_c('p',{staticClass:\"section--description\"},[_vm._v(\" 云适配与中建材信息技术股份有限公司（以下简称“中建信息”）联合举办的“战略 \"),_c('br'),_vm._v(\"合作签约仪式暨全国跨屏行动启动大会”在北京成功举办。 \")])]),_c('div',{staticClass:\"news-contaier\"},[_c('div',{staticClass:\"blog\"},[_c('div',{staticClass:\"am-g\"},_vm._l((_vm.articles.records),function(article,index){return _c('div',{key:index,staticClass:\"am-u-lg-4 am-u-md-6 am-u-end\"},[_c('div',{staticClass:\"article\"},[_c('div',{staticClass:\"article-img\"},[_c('img',{attrs:{\"src\":article.cover,\"alt\":\"\"}})]),_c('div',{staticClass:\"article-header\"},[_c('h2',[_c('router-link',{attrs:{\"to\":{name:'newsDetails',params:{newsId:article.articleId}},\"rel\":\"\"}},[_vm._v(_vm._s(article.title))])],1),_c('ul',{staticClass:\"article--meta\"},[_c('li',{staticClass:\"article--meta_item -date\"},[_vm._v(_vm._s(article.createTime))])])]),_c('div',{staticClass:\"article--content\"},[_c('p',[_vm._v(_vm._s(article.introduction))])]),_c('div',{staticClass:\"article--footer\"},[_c('router-link',{staticClass:\"link\",attrs:{\"to\":{name:'newsDetails',params:{newsId:article.articleId}}}},[_vm._v(\"查看更多\")])],1)])])}),0),_c('ul',{staticClass:\"am-pagination\",staticStyle:{\"text-align\":\"center\"}},[_c('li',{class:_vm.pageIndex === 1 ? 'am-disabled':'',on:{\"click\":function($event){return _vm.changeIndex(_vm.pageIndex - 1)}}},[_c('a',{attrs:{\"href\":\"#\"}},[_vm._v(\"«\")])]),_vm._l((_vm.articles.pages),function(p,index){return _c('li',{key:index,class:_vm.pageIndex === p ? 'am-active':'',on:{\"click\":function($event){return _vm.changeIndex(p)}}},[_c('a',{attrs:{\"href\":\"#\"}},[_vm._v(_vm._s(p))])])}),_c('li',{class:_vm.pageIndex === _vm.articles.pages ? 'am-disabled':'',on:{\"click\":function($event){return _vm.changeIndex(_vm.pageIndex + 1)}}},[_c('a',{attrs:{\"href\":\"#\"}},[_vm._v(\"»\")])])],2)])])])])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n\t<Layout>\r\n\t\t<div class=\"layout-container\" style=\"width: 100%\">\r\n\t\t\t<div class=\"page-header\">\r\n\t\t\t\t<div class=\"am-container\">\r\n\t\t\t\t\t<h1 class=\"page-header-title\">公司动态</h1>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\r\n\t\t\t<div class=\"breadcrumb-box\">\r\n\t\t\t\t<div class=\"am-container\">\r\n\t\t\t\t\t<ol class=\"am-breadcrumb\">\r\n\t\t\t\t\t\t<li><router-link to=\"/\">首页</router-link></li>\r\n\t\t\t\t\t\t<li class=\"am-active\">公司动态</li>\r\n\t\t\t\t\t</ol>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\r\n\t\t<div class=\"section\">\r\n\t\t\t<div class=\"container\" style=\"max-width: 1160px\">\r\n\t\t\t\t<div class=\"section--header\">\r\n\t\t\t\t\t<h2 class=\"section--title\">最近新闻</h2>\r\n\t\t\t\t\t<p class=\"section--description\">\r\n\t\t\t\t\t\t云适配与中建材信息技术股份有限公司（以下简称“中建信息”）联合举办的“战略\r\n\t\t\t\t\t\t<br>合作签约仪式暨全国跨屏行动启动大会”在北京成功举办。\r\n\t\t\t\t\t</p>\r\n\t\t\t\t</div>\r\n\r\n\t\t\t\t<div class=\"news-contaier\">\r\n\t\t\t\t\t<div class=\"blog\">\r\n\t\t\t\t\t\t<div class=\"am-g\">\r\n\t\t\t\t\t\t\t<div class=\"am-u-lg-4 am-u-md-6 am-u-end\" v-for=\"(article,index) in articles.records\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t<div class=\"article\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"article-img\">\r\n\t\t\t\t\t\t\t\t\t\t<img :src=\"article.cover\" alt=\"\" />\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"article-header\">\r\n\t\t\t\t\t\t\t\t\t\t<h2><router-link :to=\"{name:'newsDetails',params:{newsId:article.articleId}}\" rel=\"\">{{article.title}}</router-link></h2>\r\n\t\t\t\t\t\t\t\t\t\t<ul class=\"article--meta\">\r\n\t\t\t\t\t\t\t\t\t\t\t<li class=\"article--meta_item -date\">{{article.createTime}}</li>\r\n\t\t\t\t\t\t\t\t\t\t</ul>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"article--content\">\r\n\t\t\t\t\t\t\t\t\t\t<p>{{article.introduction}}</p>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"article--footer\">\r\n\t\t\t\t\t\t\t\t\t\t<router-link :to=\"{name:'newsDetails',params:{newsId:article.articleId}}\" class=\"link\">查看更多</router-link>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t<ul class=\"am-pagination\" style=\"text-align: center;\">\r\n\t\t\t\t\t\t\t<li :class=\"pageIndex === 1 ? 'am-disabled':''\"\r\n\t\t\t\t\t\t\t\t@click=\"changeIndex(pageIndex - 1)\">\r\n\t\t\t\t\t\t\t\t<a href=\"#\">&laquo;</a>\r\n\t\t\t\t\t\t\t</li>\r\n\r\n\t\t\t\t\t\t\t<li v-for=\"(p,index) in articles.pages\"\r\n\t\t\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\t\t\t@click=\"changeIndex(p)\"\r\n\t\t\t\t\t\t\t\t:class=\"pageIndex === p ? 'am-active':''\">\r\n\t\t\t\t\t\t\t\t<a href=\"#\">{{p}}</a>\r\n\t\t\t\t\t\t\t</li>\r\n\r\n\t\t\t\t\t\t\t<li :class=\"pageIndex === articles.pages ? 'am-disabled':''\"\r\n\t\t\t\t\t\t\t\t@click=\"changeIndex(pageIndex + 1)\">\r\n\t\t\t\t\t\t\t\t<a href=\"#\">&raquo;</a>\r\n\t\t\t\t\t\t\t</li>\r\n\t\t\t\t\t\t</ul>\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\r\n\t</Layout>\r\n</template>\r\n\r\n<script>\r\nimport Layout from \"@/components/common/Layout\";\r\nexport default {\r\n\tname: \"NewsView\",\r\n\tcomponents: {Layout},\r\n\tdata(){\r\n\t\treturn{\r\n\t\t\tarticles: {},\r\n\t\t\tpageIndex: 1,\r\n\t\t}\r\n\t},\r\n\tmounted() {\r\n\t\tthis.getArticle(1)\r\n\t},\r\n\tmethods:{\r\n\t\tgetArticle(pageIndex){\r\n\t\t\tthis.getRequest(`/findArticles/${pageIndex}`).then(resp =>{\r\n\t\t\t\tif (resp){\r\n\t\t\t\t\tthis.articles = resp.data.data\r\n\t\t\t\t\tconsole.log(this.articles)\r\n\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tchangeIndex(p){\r\n\t\t\tif (p === 0){\r\n\t\t\t\tthis.pageIndex = 1\r\n\t\t\t}else if (p === this.articles.pages + 1){\r\n\t\t\t\tthis.pageIndex = this.articles.pages\r\n\t\t\t}else{\r\n\t\t\t\tthis.pageIndex = p;\r\n\t\t\t\tthis.getArticle(p)\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>\r\n", "import mod from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./NewsView.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./NewsView.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./NewsView.vue?vue&type=template&id=33f898a4&scoped=true&\"\nimport script from \"./NewsView.vue?vue&type=script&lang=js&\"\nexport * from \"./NewsView.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"33f898a4\",\n  null\n  \n)\n\nexport default component.exports"], "names": ["render", "_vm", "this", "_c", "_self", "staticClass", "_t", "staticRenderFns", "name", "components", "Footer", "<PERSON><PERSON>", "chatAi", "component", "_v", "on", "$event", "navigateTo", "_m", "attrs", "require", "staticStyle", "data", "methods", "path", "currentPath", "previousActivePath", "$nextTick", "navLinks", "document", "querySelectorAll", "for<PERSON>ach", "link", "classList", "contains", "add", "setTimeout", "remove", "$route", "window", "scrollTo", "top", "behavior", "$router", "go", "push", "_l", "articles", "records", "article", "index", "key", "cover", "params", "newsId", "articleId", "_s", "title", "createTime", "introduction", "class", "pageIndex", "changeIndex", "pages", "p", "Layout", "mounted", "getArticle", "getRequest", "then", "resp", "console", "log"], "sourceRoot": ""}