{"version": 3, "file": "js/2842.c3349973.js", "mappings": "uJAAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACA,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,oBAAoBC,GAAG,CAAC,WAAaL,EAAIM,cAAc,WAAaN,EAAIO,iBAAiB,CAACL,EAAG,mBAAmB,CAACE,YAAY,mBAAmBI,MAAM,CAAC,KAAO,QAAQ,IAAM,QAAQR,EAAIS,GAAIT,EAAIU,WAAW,SAASC,EAASC,GAAO,OAAOV,EAAG,MAAM,CAACW,WAAW,CAAC,CAACC,KAAK,OAAOC,QAAQ,SAASC,MAAOhB,EAAIiB,uBAAyBL,EAAOM,WAAW,mCAAmCC,IAAIR,EAASP,YAAY,gBAAgBC,GAAG,CAAC,MAAQ,SAASe,GAAQ,OAAOpB,EAAIqB,qBAAqBV,EAAS,EAAE,WAAa,SAASS,GAAQ,OAAOpB,EAAIsB,MAAMV,EAAM,IAAI,CAACZ,EAAIuB,GAAG,IAAIvB,EAAIwB,GAAGb,GAAU,MAAM,IAAG,IAAI,GAAGT,EAAG,MAAM,CAACE,YAAY,YAAYqB,MAAM,CAAE,mBAAoBzB,EAAI0B,UAAWrB,GAAG,CAAC,MAAQL,EAAI2B,aAAa,CAACzB,EAAG,IAAI,CAACE,YAAY,uBAAuBF,EAAG,MAAM,CAACW,WAAW,CAAC,CAACC,KAAK,OAAOC,QAAQ,SAASC,MAAOhB,EAAI0B,SAAUR,WAAW,aAAad,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAI4B,GAAG,GAAG1B,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,IAAI,CAACE,YAAY,eAAeC,GAAG,CAAC,MAAQL,EAAI2B,kBAAkBzB,EAAG,MAAM,CAAC2B,IAAI,oBAAoBzB,YAAY,iBAAiB,CAACJ,EAAIS,GAAIT,EAAI8B,UAAU,SAASC,EAAQnB,GAAO,OAAOV,EAAG,MAAM,CAACiB,IAAIP,EAAMa,MAAM,CAAC,UAAWM,EAAQC,OAAO,CAAmB,QAAjBD,EAAQC,KAAgB9B,EAAG,MAAM,CAACE,YAAY,UAAU,CAACF,EAAG,IAAI,CAACE,YAAY,mBAAmBJ,EAAIiC,KAAK/B,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe8B,SAAS,CAAC,UAAYlC,EAAIwB,GAAGxB,EAAImC,cAAcJ,EAAQK,UAAUlC,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIuB,GAAGvB,EAAIwB,GAAGxB,EAAIqC,WAAWN,EAAQO,aAAa,IAAItC,EAAIuC,QAASrC,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,MAAM,CAACE,YAAY,QAAQF,EAAG,MAAM,CAACE,YAAY,QAAQF,EAAG,MAAM,CAACE,YAAY,UAAUJ,EAAIiC,MAAM,GAAG/B,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,QAAQ,CAACW,WAAW,CAAC,CAACC,KAAK,QAAQC,QAAQ,UAAUC,MAAOhB,EAAIwC,UAAWtB,WAAW,cAAcV,MAAM,CAAC,KAAO,OAAO,YAAc,aAAa,SAAWR,EAAIuC,SAASL,SAAS,CAAC,MAASlC,EAAIwC,WAAYnC,GAAG,CAAC,MAAQ,SAASe,GAAQ,OAAIA,EAAOY,KAAKS,QAAQ,QAAQzC,EAAI0C,GAAGtB,EAAOuB,QAAQ,QAAQ,GAAGvB,EAAOD,IAAI,SAAgB,KAAYnB,EAAI4C,YAAYC,MAAM,KAAMC,UAAU,EAAE,MAAQ,SAAS1B,GAAWA,EAAO2B,OAAOC,YAAiBhD,EAAIwC,UAAUpB,EAAO2B,OAAO/B,MAAK,KAAKd,EAAG,SAAS,CAACM,MAAM,CAAC,SAAWR,EAAIuC,UAAYvC,EAAIwC,UAAUS,QAAQ5C,GAAG,CAAC,MAAQL,EAAI4C,cAAc,CAAC1C,EAAG,IAAI,CAACE,YAAY,8BAC36E,EACI8C,EAAkB,CAAC,WAAY,IAAIlD,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBF,EAAG,OAAO,CAACF,EAAIuB,GAAG,WACnK,GC6EA,G,QAAA,CACAT,KAAA,SAEAqC,OACA,OACAzB,UAAA,EACAc,UAAA,GACAV,SAAA,CACA,CACAE,KAAA,MACAI,KAAA,uBACAE,KAAA,IAAAc,OAGAb,SAAA,EACAc,gBAAA,GACA3C,UAAA,CACA,aACA,YACA,aAEAO,qBAAA,EACAqC,cAAA,KACAC,iBAAA,IACAC,UAAA,EAEA,EACAC,gBACA,KAAAC,eACA,EACAC,UAGA,GAFA,KAAAC,iBAEAC,SAAAC,eAAA,iBACA,MAAAC,EAAAF,SAAAG,cAAA,QACAD,EAAAE,GAAA,eACAF,EAAAG,IAAA,aACAH,EAAAI,KAAA,6EACAN,SAAAO,KAAAC,YAAAN,EACA,CACA,EAEAO,QAAA,CACAhD,MAAAV,GACA,KAAAK,qBAAAL,EACA,KAAAN,eACA,EACAsD,gBACA,IAAAW,EAAA,KACA,KAAAb,gBACA,KAAAJ,cAAAkB,aAAA,KACAD,EAAAf,WACA,KAAAvC,sBACA,KAAAA,qBAAA,QAAAP,UAAA+D,OACAC,QAAAC,IAAA,UAAA1D,sBACAyD,QAAAC,IAAA,WAAAJ,EAAAf,UACA,GACA,KAAAD,iBACA,EACAjD,gBACA,KAAAkD,UAAA,CACA,EACAjD,iBACA,KAAAiD,UAAA,CACA,EACAE,gBACA,KAAAJ,gBACAsB,cAAA,KAAAtB,eACA,KAAAA,cAAA,KAEA,EAEAjC,qBAAAV,GACA,KAAA6B,UAAA7B,EACA,KAAAiC,aACA,EACAjB,aACA,KAAAD,UAAA,KAAAA,SAEA,KAAAA,UACA,KAAAmD,WAAA,KACA,KAAAC,gBAAA,GAGA,EAEA,oBACA,SAAAtC,UAAAS,QAAA,KAAAV,QAAA,OAGA,KAAAT,SAAAiD,KAAA,CACA/C,KAAA,OACAI,KAAA,KAAAI,UACAF,KAAA,IAAAc,OAGA,MAAA4B,EAAA,KAAAxC,UACA,KAAAA,UAAA,GACA,KAAAD,SAAA,EAEA,KAAAc,gBAAA0B,KAAA,CACAE,KAAA,OACAC,QAAAF,IAIA,MAAAG,EAAA,CACAC,MAAA,eACAtD,SAAA,EAAAmD,KAAA,SAAAC,QAAA,+HAAA7B,iBACAgC,QAAA,EACAC,QAAA,CACAC,iBAAA,IACAC,kBAAA,IAEAC,KAAA,QAIA,KAAAZ,WAAA,KACA,KAAAC,gBAAA,IAGA,IAGA,MAAAY,QAAAC,MAAA,kDACAC,OAAA,OACAC,QAAA,CACAC,cAAA,6DACA,mCAEAC,KAAAC,KAAAC,UAAAd,KAIAe,EAAAR,EAAAK,KAAAI,YACAC,EAAA,IAAAC,YACAC,EAAA,KAAAxE,SAAAiD,KAAA,CACA/C,KAAA,MACAI,KAAA,GACAE,KAAA,IAAAc,OACA,EACA,SACA,WAAAmD,EAAA,MAAAvF,SAAAkF,EAAAM,OACA,GAAAD,EAAA,MAGA,MAAAE,EAAAL,EAAAM,OAAA1F,GACA2F,EAAAF,EAAAG,MAAA,MAAAC,QAAAC,GAAAA,EAAA7D,SAEA,UAAA6D,KAAAH,EACA,IACA,MAAAI,EAAAD,EAAAE,MAAA,GAAA/D,OACA,QAAA8D,GAAA,WAAAA,EAAA,SAEA,IAAA5D,EAAA6C,KAAAiB,MAAAF,GACA,GAAA5D,EAAA+D,QAAA,CACA,SAAA/D,EAAA+D,QAAA,GAAAC,MAAAC,kBACA,SAEA,WAAAjE,EAAA+D,QAAA,GAAAC,MAAAjC,QACA,SAEA,KAAApD,SAAAwE,GAAAlE,MAAAe,EAAA+D,QAAA,GAAAC,MAAAjC,OACA,CACA,OAAAmC,GACA,CAEA,CACA,KAAAhE,gBAAA0B,KAAA,CACAE,KAAA,YACAC,QAAA,KAAApD,SAAAwE,GAAAlE,MASA,OAAAkF,GAGA,KAAAxF,SAAAiD,KAAA,CACA/C,KAAA,MACAI,KAAA,qBACAE,KAAA,IAAAc,MAEA,SACA,KAAAb,SAAA,EAGA,KAAAsC,WAAA,KACA,KAAAC,gBAAA,GAEA,CACA,EAGA,kBAAA/C,GAeA,aAbA,IAAAwF,SAAAC,GAAAC,WAAAD,EAAA,OAaA,YAAAzF,0BACA,EAEA+C,iBACA,MAAA4C,EAAA,KAAAC,MAAAC,kBACAF,EAAAG,UAAAH,EAAAI,YACA,EAEAzF,WAAA0F,GACA,WAAA3E,KAAA2E,GAAAC,mBAAA,IAAAC,KAAA,UAAAC,OAAA,WACA,EAEA/F,cAAAC,GAEA,OAAAA,EACA+F,QAAA,cACAA,QAAA,6DACA,KCvTwQ,I,UCQpQC,GAAY,OACd,EACArI,EACAmD,GACA,EACA,KACA,WACA,MAIF,EAAekF,EAAiB,O,oECnBhC,IAAIrI,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAAEF,EAAIqI,QAASnI,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACJ,EAAI4B,GAAG,GAAG1B,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACJ,EAAIuB,GAAG,UAAUrB,EAAG,MAAM,CAACE,YAAY,gBAAgBJ,EAAIS,GAAIT,EAAIsI,gBAAgB,SAASC,EAAO3H,GAAO,OAAOV,EAAG,MAAM,CAACiB,IAAIP,EAAMR,YAAY,cAAcqB,MAAM,CAAE,OAAUzB,EAAIwI,wBAA0BD,EAAOvH,OAAQX,GAAG,CAAC,MAAQ,SAASe,GAAQpB,EAAIwI,sBAAwBD,EAAOvH,KAAK,IAAI,CAAChB,EAAIuB,GAAG,IAAIvB,EAAIwB,GAAG+G,EAAOE,OAAO,MAAM,IAAG,GAAGvI,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACJ,EAAIuB,GAAG,UAAUrB,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAACJ,EAAI4B,GAAG,GAAG1B,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACF,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACJ,EAAIuB,GAAGvB,EAAIwB,GAAGxB,EAAI0I,OAAO5H,SAASZ,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACJ,EAAIuB,GAAGvB,EAAIwB,GAAGxB,EAAI0I,OAAOC,oBAAoB,OAAOzI,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACJ,EAAIuB,GAAGvB,EAAIwB,GAAGxB,EAAI0I,OAAOE,aAAa,QAAQ1I,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACJ,EAAIuB,GAAGvB,EAAIwB,GAAGxB,EAAI0I,OAAOG,kBAAkB,OAAO3I,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACJ,EAAIuB,GAAGvB,EAAIwB,GAAGxB,EAAI0I,OAAOI,gBAAgB,QAAQ5I,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACJ,EAAIuB,GAAGvB,EAAIwB,GAAGxB,EAAI0I,OAAOK,YAAY,UAAU7I,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIuB,GAAG,KAAKvB,EAAIwB,GAAGxB,EAAI0I,OAAO1I,EAAIwI,yBAAyBtI,EAAG,OAAO,CAACE,YAAY,cAAc,CAACJ,EAAIuB,GAAG,IAAIvB,EAAIwB,GAAGxB,EAAIgJ,oBAAoB9I,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACJ,EAAIuB,GAAG,UAAUrB,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAACJ,EAAI4B,GAAG,GAAG1B,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACF,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACJ,EAAIuB,GAAGvB,EAAIwB,GAAGxB,EAAI0I,OAAO5H,SAASZ,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACJ,EAAIuB,GAAGvB,EAAIwB,GAAGxB,EAAI0I,OAAOG,kBAAkB,QAAQ3I,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACJ,EAAIuB,GAAGvB,EAAIwB,GAAGxB,EAAI0I,OAAOI,gBAAgB,QAAQ5I,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACJ,EAAIuB,GAAGvB,EAAIwB,GAAGxB,EAAI0I,OAAOK,YAAY,QAAQ7I,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACJ,EAAIuB,GAAG,KAAKvB,EAAIwB,GAAGxB,EAAI0I,OAAOO,UAAU,gBAAgB/I,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACJ,EAAIuB,GAAG,WAAWrB,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACF,EAAG,SAAS,CAACW,WAAW,CAAC,CAACC,KAAK,QAAQC,QAAQ,UAAUC,MAAOhB,EAAIkJ,iBAAkBhI,WAAW,qBAAqBd,YAAY,kBAAkBC,GAAG,CAAC,OAAS,SAASe,GAAQ,IAAI+H,EAAgBC,MAAMC,UAAUxC,OAAOyC,KAAKlI,EAAO2B,OAAOuC,SAAQ,SAASiE,GAAG,OAAOA,EAAEC,QAAQ,IAAGC,KAAI,SAASF,GAAG,IAAIG,EAAM,WAAYH,EAAIA,EAAEI,OAASJ,EAAEvI,MAAM,OAAO0I,CAAG,IAAI1J,EAAIkJ,iBAAiB9H,EAAO2B,OAAO6G,SAAWT,EAAgBA,EAAc,EAAE,IAAInJ,EAAIS,GAAIT,EAAI6J,wBAAwB,SAAStB,EAAO3H,GAAO,OAAOV,EAAG,SAAS,CAACiB,IAAIP,EAAMsB,SAAS,CAAC,MAAQqG,EAAOvH,QAAQ,CAAChB,EAAIuB,GAAG,IAAIvB,EAAIwB,GAAG+G,EAAOE,OAAO,MAAM,IAAG,OAAOvI,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIuB,GAAG,WAAWrB,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIuB,GAAG,KAAKvB,EAAIwB,GAAGxB,EAAI8J,YAAY,aAAa5J,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,SAAS,CAACE,YAAY,gBAAgBC,GAAG,CAAC,MAAQL,EAAI+J,aAAa,CAAC/J,EAAIuB,GAAG,QAAQrB,EAAG,SAAS,CAACE,YAAY,iBAAiBC,GAAG,CAAC,MAAQL,EAAIgK,oBAAoB,CAAChK,EAAIuB,GAAG,kBAAkBvB,EAAIiC,KAAMjC,EAAIiK,iBAAkB/J,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACJ,EAAIuB,GAAG,aAAarB,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,SAAS,CAACE,YAAY,iBAAiBC,GAAG,CAAC,MAAQ,SAASe,GAAQpB,EAAIiK,kBAAmB,CAAK,IAAI,CAACjK,EAAIuB,GAAG,QAAQrB,EAAG,SAAS,CAACE,YAAY,aAAaC,GAAG,CAAC,MAAQL,EAAIkK,eAAe,CAAClK,EAAIuB,GAAG,cAAcvB,EAAIiC,MAC7pH,EACIiB,EAAkB,CAAC,WAAY,IAAIlD,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,KAAK,CAACF,EAAIuB,GAAG,WAC/H,EAAE,WAAY,IAAIvB,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACF,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACJ,EAAIuB,GAAG,WAAWrB,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACJ,EAAIuB,GAAG,SAASrB,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACJ,EAAIuB,GAAG,QAAQrB,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACJ,EAAIuB,GAAG,UAAUrB,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACJ,EAAIuB,GAAG,QAAQrB,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACJ,EAAIuB,GAAG,UACpc,EAAE,WAAY,IAAIvB,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACF,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACJ,EAAIuB,GAAG,WAAWrB,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACJ,EAAIuB,GAAG,UAAUrB,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACJ,EAAIuB,GAAG,QAAQrB,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACJ,EAAIuB,GAAG,SAASrB,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACJ,EAAIuB,GAAG,UACxY,GCg1BA,GACAT,KAAA,cACAqJ,MAAA,CACA9B,QAAA,CACArG,KAAAoI,QACAC,SAAA,GAEA3B,OAAA,CACA1G,KAAAsI,OACAD,QAAAA,KAAA,KAEA7B,sBAAA,CACAxG,KAAAuI,SAGApH,OACA,OACAqH,gBAAA,GACAhC,sBAAA,WACAU,iBAAA,EACAuB,eAAA,EACAR,kBAAA,EACA3B,eAAA,CACA,CAAAG,MAAA,OAAAzH,MAAA,YAAA0J,KAAA,OACA,CAAAjC,MAAA,KAAAzH,MAAA,WAAA0J,KAAA,MACA,CAAAjC,MAAA,KAAAzH,MAAA,aAAA0J,KAAA,MACA,CAAAjC,MAAA,KAAAzH,MAAA,YAAA0J,KAAA,OAEAC,oBAAA,CACA,CAAAlC,MAAA,MAAAzH,MAAA,GACA,CAAAyH,MAAA,MAAAzH,MAAA,GACA,CAAAyH,MAAA,MAAAzH,MAAA,GACA,CAAAyH,MAAA,MAAAzH,MAAA,GACA,CAAAyH,MAAA,OAAAzH,MAAA,IACA,CAAAyH,MAAA,OAAAzH,MAAA,KAEA4J,mBAAA,CACA,CAAAnC,MAAA,KAAAzH,MAAA,GACA,CAAAyH,MAAA,KAAAzH,MAAA,GACA,CAAAyH,MAAA,KAAAzH,MAAA,GACA,CAAAyH,MAAA,KAAAzH,MAAA,GACA,CAAAyH,MAAA,KAAAzH,MAAA,GACA,CAAAyH,MAAA,KAAAzH,MAAA,IAEA6J,oBAAA,CACA,CAAApC,MAAA,KAAAzH,MAAA,GACA,CAAAyH,MAAA,KAAAzH,MAAA,GACA,CAAAyH,MAAA,KAAAzH,MAAA,GACA,CAAAyH,MAAA,KAAAzH,MAAA,GACA,CAAAyH,MAAA,KAAAzH,MAAA,GACA,CAAAyH,MAAA,KAAAzH,MAAA,GACA,CAAAyH,MAAA,KAAAzH,MAAA,GACA,CAAAyH,MAAA,KAAAzH,MAAA,IAEA8J,qBAAA,CACA,CAAArC,MAAA,KAAAzH,MAAA,GACA,CAAAyH,MAAA,KAAAzH,MAAA,GACA,CAAAyH,MAAA,KAAAzH,MAAA,IAGA,EACA+J,SAAA,CACAlB,yBACA,YAAArB,uBACA,gBAEA,OADA,KAAAsB,WAAA,KAAApB,OAAA,aACA,KAAAiC,oBACA,eAEA,OADA,KAAAb,WAAA,KAAApB,OAAA,YACA,KAAAkC,mBACA,iBAEA,OADA,KAAAd,WAAA,KAAApB,OAAA,cACA,KAAAmC,oBACA,gBAEA,OADA,KAAAf,WAAA,KAAApB,OAAA,aACA,KAAAoC,qBACA,QACA,YAAAF,mBAEA,EACAd,aACA,MAAAkB,EAAA,KAAAtC,OAAA,KAAAF,wBAAA,EACA,OAAAwC,EAAA,KAAA9B,kBAAA+B,QAAA,EACA,EACAjC,YACA,YAAAR,uBACA,4BACA,0BACA,4BACA,2BACA,iBAEA,EACA0C,YACA,YAAA1C,uBACA,gBACA,WACA,eACA,UACA,iBACA,UACA,gBACA,UACA,QACA,WAEA,GAEA2C,MAAA,CACA3C,wBACA,KAAAU,iBAAA,KAAAW,uBAAA,IAAA7I,OAAA,CACA,EACAkI,mBACA,KAAAY,WAAA,KAAApB,OAAA,KAAAF,uBAAA,KAAAU,gBACA,EACAA,iBAAA,CACAkC,QAAAC,GACA,KAAAC,MAAA,eAAAD,EAAA,KAAAH,UACA,EACAK,WAAA,GAEAzB,WAAA,CACAsB,QAAAC,GACA,KAAAC,MAAA,gBAAAD,EACA,EACAE,WAAA,IAGAjH,QAAA,CACAyF,aACA,KAAAuB,MAAA,QACA,EACAtB,oBACA,KAAAC,kBAAA,CACA,EACAC,eACA,KAAAD,kBAAA,EACA,KAAAqB,MAAA,kBAEA,MAAAE,EAAA,CACAC,SAAA,KAAA/C,OAAAzE,GACAyH,WAAA,KAAAhD,OAAA5H,KACA6K,cAAA,KAAAnD,sBACAoD,SAAA,KAAA1C,iBACAuB,cAAA,KAAAA,cACAO,MAAA,KAAAlB,WACA+B,MAAA,CACAC,SAAA,KAAApD,OAAA5H,KACAiL,KAAA,KAAArD,OAAAqD,KACAhD,WAAA,KAAAL,OAAAK,WACAiD,UAAA,KAAAtD,OAAAsD,WAAA,EACAC,OAAA,KAAAvD,OAAAuD,OACArD,YAAA,KAAAF,OAAAE,cAIA,KAAA0C,MAAA,gBAAAE,GACA,KAAAzB,YACA,ICn/BoQ,I,UCShQ3B,GAAY,OACd,EACArI,EACAmD,GACA,EACA,KACA,WACA,MAIF,EAAekF,EAAiB,O,oECpBhC,IAAIrI,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACgM,YAAY,CAAC,cAAc,OAAO,CAAElM,EAAImM,eAAgBjM,EAAG,oBAAoB,CAACM,MAAM,CAAC,QAAUR,EAAIoM,oBAAoB,KAAO,UAAU,SAAW,KAAM/L,GAAG,CAAC,MAAQ,SAASe,GAAQpB,EAAImM,gBAAiB,CAAK,KAAKnM,EAAIiC,KAAK/B,EAAG,MAAM,CAACgM,YAAY,CAAC,MAAQ,QAAQ,CAAChM,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgBC,GAAG,CAAC,MAAQL,EAAIqM,yBAAyB,CAACnM,EAAG,OAAO,CAACE,YAAY,gBAAgB,CAACJ,EAAIuB,GAAG,QAAQrB,EAAG,IAAI,CAACE,YAAY,cAAcqB,MAAM,CAAE,WAAczB,EAAIsM,kBAAmB,CAACtM,EAAIuB,GAAGvB,EAAIwB,GAAGxB,EAAIsM,gBAAkB,IAAM,UAAUpM,EAAG,aAAa,CAACM,MAAM,CAAC,KAAO,SAASH,GAAG,CAAC,eAAeL,EAAIuM,YAAY,MAAQvM,EAAIwM,MAAM,cAAcxM,EAAIyM,WAAW,eAAezM,EAAI0M,YAAY,MAAQ1M,EAAI2M,MAAM,cAAc3M,EAAI4M,aAAa,CAAE5M,EAAIsM,gBAAiBpM,EAAG,MAAM,CAAC2B,IAAI,gBAAgBzB,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIuB,GAAG,UAAUrB,EAAG,MAAM,CAACE,YAAY,iCAAiC,CAACF,EAAG,QAAQ,CAACE,YAAY,cAAc,CAACF,EAAG,QAAQ,CAACW,WAAW,CAAC,CAACC,KAAK,QAAQC,QAAQ,UAAUC,MAAOhB,EAAI6M,UAAW3L,WAAW,cAAcV,MAAM,CAAC,KAAO,QAAQ,MAAQ,YAAY,KAAO,WAAW0B,SAAS,CAAC,QAAUlC,EAAI8M,GAAG9M,EAAI6M,UAAU,cAAcxM,GAAG,CAAC,OAAS,SAASe,GAAQpB,EAAI6M,UAAU,WAAW,KAAK3M,EAAG,OAAO,CAACE,YAAY,eAAeF,EAAG,OAAO,CAACE,YAAY,cAAc,CAACJ,EAAIuB,GAAG,UAAUrB,EAAG,QAAQ,CAACE,YAAY,cAAc,CAACF,EAAG,QAAQ,CAACW,WAAW,CAAC,CAACC,KAAK,QAAQC,QAAQ,UAAUC,MAAOhB,EAAI6M,UAAW3L,WAAW,cAAcV,MAAM,CAAC,KAAO,QAAQ,MAAQ,WAAW,KAAO,WAAW0B,SAAS,CAAC,QAAUlC,EAAI8M,GAAG9M,EAAI6M,UAAU,aAAaxM,GAAG,CAAC,OAAS,SAASe,GAAQpB,EAAI6M,UAAU,UAAU,KAAK3M,EAAG,OAAO,CAACE,YAAY,eAAeF,EAAG,OAAO,CAACE,YAAY,cAAc,CAACJ,EAAIuB,GAAG,UAAUrB,EAAG,QAAQ,CAACE,YAAY,cAAc,CAACF,EAAG,QAAQ,CAACW,WAAW,CAAC,CAACC,KAAK,QAAQC,QAAQ,UAAUC,MAAOhB,EAAI6M,UAAW3L,WAAW,cAAcV,MAAM,CAAC,KAAO,QAAQ,MAAQ,aAAa,KAAO,WAAW0B,SAAS,CAAC,QAAUlC,EAAI8M,GAAG9M,EAAI6M,UAAU,eAAexM,GAAG,CAAC,OAAS,SAASe,GAAQpB,EAAI6M,UAAU,YAAY,KAAK3M,EAAG,OAAO,CAACE,YAAY,eAAeF,EAAG,OAAO,CAACE,YAAY,cAAc,CAACJ,EAAIuB,GAAG,UAAUrB,EAAG,QAAQ,CAACE,YAAY,cAAc,CAACF,EAAG,QAAQ,CAACW,WAAW,CAAC,CAACC,KAAK,QAAQC,QAAQ,UAAUC,MAAOhB,EAAI6M,UAAW3L,WAAW,cAAcV,MAAM,CAAC,KAAO,QAAQ,MAAQ,YAAY,KAAO,WAAW0B,SAAS,CAAC,QAAUlC,EAAI8M,GAAG9M,EAAI6M,UAAU,cAAcxM,GAAG,CAAC,OAAS,SAASe,GAAQpB,EAAI6M,UAAU,WAAW,KAAK3M,EAAG,OAAO,CAACE,YAAY,eAAeF,EAAG,OAAO,CAACE,YAAY,cAAc,CAACJ,EAAIuB,GAAG,cAAcrB,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIuB,GAAG,WAAWrB,EAAG,MAAM,CAACE,YAAY,iCAAiC,CAACF,EAAG,QAAQ,CAACE,YAAY,iBAAiB,CAACF,EAAG,QAAQ,CAACW,WAAW,CAAC,CAACC,KAAK,QAAQC,QAAQ,UAAUC,MAAOhB,EAAI+M,QAAQC,WAAY9L,WAAW,uBAAuBV,MAAM,CAAC,KAAO,YAAY0B,SAAS,CAAC,QAAUkH,MAAM6D,QAAQjN,EAAI+M,QAAQC,YAAYhN,EAAIkN,GAAGlN,EAAI+M,QAAQC,WAAW,OAAO,EAAGhN,EAAI+M,QAAQC,YAAa3M,GAAG,CAAC,OAAS,CAAC,SAASe,GAAQ,IAAI+L,EAAInN,EAAI+M,QAAQC,WAAWI,EAAKhM,EAAO2B,OAAOsK,IAAID,EAAKE,QAAuB,GAAGlE,MAAM6D,QAAQE,GAAK,CAAC,IAAII,EAAI,KAAKC,EAAIxN,EAAIkN,GAAGC,EAAII,GAAQH,EAAKE,QAASE,EAAI,GAAIxN,EAAIyN,KAAKzN,EAAI+M,QAAS,aAAcI,EAAIO,OAAO,CAACH,KAAaC,GAAK,GAAIxN,EAAIyN,KAAKzN,EAAI+M,QAAS,aAAcI,EAAInG,MAAM,EAAEwG,GAAKE,OAAOP,EAAInG,MAAMwG,EAAI,IAAM,MAAMxN,EAAIyN,KAAKzN,EAAI+M,QAAS,aAAcM,EAAK,EAAErN,EAAI2N,qBAAqBzN,EAAG,OAAO,CAACE,YAAY,iBAAiB,CAACJ,EAAIuB,GAAG,UAAUvB,EAAIS,GAAIT,EAAI4N,SAAS,SAASC,GAAQ,OAAO3N,EAAG,QAAQ,CAACiB,IAAI0M,EAAO5J,GAAG7D,YAAY,iBAAiB,CAACF,EAAG,QAAQ,CAACW,WAAW,CAAC,CAACC,KAAK,QAAQC,QAAQ,UAAUC,MAAOhB,EAAI+M,QAAQe,gBAAiB5M,WAAW,4BAA4BV,MAAM,CAAC,KAAO,YAAY0B,SAAS,CAAC,MAAQ2L,EAAO5J,GAAG,QAAUmF,MAAM6D,QAAQjN,EAAI+M,QAAQe,iBAAiB9N,EAAIkN,GAAGlN,EAAI+M,QAAQe,gBAAgBD,EAAO5J,KAAK,EAAGjE,EAAI+M,QAAQe,iBAAkBzN,GAAG,CAAC,OAAS,CAAC,SAASe,GAAQ,IAAI+L,EAAInN,EAAI+M,QAAQe,gBAAgBV,EAAKhM,EAAO2B,OAAOsK,IAAID,EAAKE,QAAuB,GAAGlE,MAAM6D,QAAQE,GAAK,CAAC,IAAII,EAAIM,EAAO5J,GAAGuJ,EAAIxN,EAAIkN,GAAGC,EAAII,GAAQH,EAAKE,QAASE,EAAI,GAAIxN,EAAIyN,KAAKzN,EAAI+M,QAAS,kBAAmBI,EAAIO,OAAO,CAACH,KAAaC,GAAK,GAAIxN,EAAIyN,KAAKzN,EAAI+M,QAAS,kBAAmBI,EAAInG,MAAM,EAAEwG,GAAKE,OAAOP,EAAInG,MAAMwG,EAAI,IAAM,MAAMxN,EAAIyN,KAAKzN,EAAI+M,QAAS,kBAAmBM,EAAK,EAAErN,EAAI+N,kBAAkB7N,EAAG,OAAO,CAACE,YAAY,iBAAiB,CAACJ,EAAIuB,GAAGvB,EAAIwB,GAAGqM,EAAO/M,UAAU,KAAI,KAAKZ,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIuB,GAAG,WAAWrB,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIuB,GAAG,YAAavB,EAAIgO,mBAAmBvJ,OAAS,EAAGvE,EAAG,MAAM,CAACE,YAAY,2BAA2B,CAACF,EAAG,QAAQ,CAACE,YAAY,iBAAiB,CAACF,EAAG,QAAQ,CAACW,WAAW,CAAC,CAACC,KAAK,QAAQC,QAAQ,UAAUC,MAAOhB,EAAI+M,QAAQkB,aAAc/M,WAAW,yBAAyBV,MAAM,CAAC,KAAO,YAAY0B,SAAS,CAAC,QAAUkH,MAAM6D,QAAQjN,EAAI+M,QAAQkB,cAAcjO,EAAIkN,GAAGlN,EAAI+M,QAAQkB,aAAa,OAAO,EAAGjO,EAAI+M,QAAQkB,cAAe5N,GAAG,CAAC,OAAS,CAAC,SAASe,GAAQ,IAAI+L,EAAInN,EAAI+M,QAAQkB,aAAab,EAAKhM,EAAO2B,OAAOsK,IAAID,EAAKE,QAAuB,GAAGlE,MAAM6D,QAAQE,GAAK,CAAC,IAAII,EAAI,KAAKC,EAAIxN,EAAIkN,GAAGC,EAAII,GAAQH,EAAKE,QAASE,EAAI,GAAIxN,EAAIyN,KAAKzN,EAAI+M,QAAS,eAAgBI,EAAIO,OAAO,CAACH,KAAaC,GAAK,GAAIxN,EAAIyN,KAAKzN,EAAI+M,QAAS,eAAgBI,EAAInG,MAAM,EAAEwG,GAAKE,OAAOP,EAAInG,MAAMwG,EAAI,IAAM,MAAMxN,EAAIyN,KAAKzN,EAAI+M,QAAS,eAAgBM,EAAK,EAAErN,EAAIkO,uBAAuBhO,EAAG,OAAO,CAACE,YAAY,iBAAiB,CAACJ,EAAIuB,GAAG,UAAUvB,EAAIS,GAAIT,EAAIgO,oBAAoB,SAASG,GAAK,OAAOjO,EAAG,QAAQ,CAACiB,IAAIgN,EAAIlK,GAAG7D,YAAY,iBAAiB,CAACF,EAAG,QAAQ,CAACW,WAAW,CAAC,CAACC,KAAK,QAAQC,QAAQ,UAAUC,MAAOhB,EAAI+M,QAAQqB,kBAAmBlN,WAAW,8BAA8BV,MAAM,CAAC,KAAO,YAAY0B,SAAS,CAAC,MAAQiM,EAAIlK,GAAG,QAAUmF,MAAM6D,QAAQjN,EAAI+M,QAAQqB,mBAAmBpO,EAAIkN,GAAGlN,EAAI+M,QAAQqB,kBAAkBD,EAAIlK,KAAK,EAAGjE,EAAI+M,QAAQqB,mBAAoB/N,GAAG,CAAC,OAAS,CAAC,SAASe,GAAQ,IAAI+L,EAAInN,EAAI+M,QAAQqB,kBAAkBhB,EAAKhM,EAAO2B,OAAOsK,IAAID,EAAKE,QAAuB,GAAGlE,MAAM6D,QAAQE,GAAK,CAAC,IAAII,EAAIY,EAAIlK,GAAGuJ,EAAIxN,EAAIkN,GAAGC,EAAII,GAAQH,EAAKE,QAASE,EAAI,GAAIxN,EAAIyN,KAAKzN,EAAI+M,QAAS,oBAAqBI,EAAIO,OAAO,CAACH,KAAaC,GAAK,GAAIxN,EAAIyN,KAAKzN,EAAI+M,QAAS,oBAAqBI,EAAInG,MAAM,EAAEwG,GAAKE,OAAOP,EAAInG,MAAMwG,EAAI,IAAM,MAAMxN,EAAIyN,KAAKzN,EAAI+M,QAAS,oBAAqBM,EAAK,EAAErN,EAAI+N,kBAAkB7N,EAAG,OAAO,CAACE,YAAY,iBAAiB,CAACJ,EAAIuB,GAAGvB,EAAIwB,GAAG2M,EAAIrN,UAAU,KAAI,GAAGZ,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACJ,EAAIuB,GAAG,2BAA2BrB,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIuB,GAAG,UAAUrB,EAAG,MAAM,CAACE,YAAY,iCAAiC,CAACF,EAAG,QAAQ,CAACE,YAAY,iBAAiB,CAACF,EAAG,QAAQ,CAACW,WAAW,CAAC,CAACC,KAAK,QAAQC,QAAQ,UAAUC,MAAOhB,EAAI+M,QAAQsB,eAAeC,YAAapN,WAAW,uCAAuCV,MAAM,CAAC,KAAO,YAAY0B,SAAS,CAAC,QAAUkH,MAAM6D,QAAQjN,EAAI+M,QAAQsB,eAAeC,aAAatO,EAAIkN,GAAGlN,EAAI+M,QAAQsB,eAAeC,YAAY,OAAO,EAAGtO,EAAI+M,QAAQsB,eAAeC,aAAcjO,GAAG,CAAC,OAAS,SAASe,GAAQ,IAAI+L,EAAInN,EAAI+M,QAAQsB,eAAeC,YAAYlB,EAAKhM,EAAO2B,OAAOsK,IAAID,EAAKE,QAAuB,GAAGlE,MAAM6D,QAAQE,GAAK,CAAC,IAAII,EAAI,KAAKC,EAAIxN,EAAIkN,GAAGC,EAAII,GAAQH,EAAKE,QAASE,EAAI,GAAIxN,EAAIyN,KAAKzN,EAAI+M,QAAQsB,eAAgB,cAAelB,EAAIO,OAAO,CAACH,KAAaC,GAAK,GAAIxN,EAAIyN,KAAKzN,EAAI+M,QAAQsB,eAAgB,cAAelB,EAAInG,MAAM,EAAEwG,GAAKE,OAAOP,EAAInG,MAAMwG,EAAI,IAAM,MAAMxN,EAAIyN,KAAKzN,EAAI+M,QAAQsB,eAAgB,cAAehB,EAAK,KAAKnN,EAAG,OAAO,CAACE,YAAY,iBAAiB,CAACJ,EAAIuB,GAAG,iBAAiBvB,EAAIiC,QAAQ,GAAIjC,EAAIuO,gBAAgB9J,OAAS,EAAGvE,EAAG,MAAM,CAACE,YAAY,gBAAgBJ,EAAIS,GAAIT,EAAIuO,iBAAiB,SAAS7F,GAAQ,OAAOxI,EAAG,MAAM,CAACiB,IAAIuH,EAAOzE,GAAG7D,YAAY,cAAcqB,MAAM,CAAE,sBAAuBzB,EAAIwO,gBAAkB9F,EAAOzE,IAAK5D,GAAG,CAAC,WAAa,SAASe,GAAQpB,EAAIwO,cAAgB9F,EAAOzE,EAAE,EAAE,WAAa,SAAS7C,GAAQpB,EAAIwO,cAAgB,IAAI,IAAI,CAACtO,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIuB,GAAGvB,EAAIwB,GAAGxB,EAAIyO,cAAc/F,EAAOmF,YAAY3N,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIuB,GAAG,IAAIvB,EAAIwB,GAAGkH,EAAO5H,MAAM,OAAOZ,EAAG,MAAM,CAACW,WAAW,CAAC,CAACC,KAAK,OAAOC,QAAQ,SAASC,MAAyB,cAAlBhB,EAAI6M,UAA2B3L,WAAW,8BAA8Bd,YAAY,wBAAwB,CAACF,EAAG,MAAM,CAACE,YAAY,SAAS,CAACF,EAAG,OAAO,CAACE,YAAY,YAAY,CAACJ,EAAIuB,GAAG,OAAOrB,EAAG,OAAO,CAACE,YAAY,UAAU,CAACJ,EAAIuB,GAAGvB,EAAIwB,GAAGkH,EAAOgG,cAAcxO,EAAG,OAAO,CAACE,YAAY,QAAQ,CAACJ,EAAIuB,GAAG,WAAWrB,EAAG,MAAM,CAACE,YAAY,gBAAgBqB,MAAMzB,EAAI2O,qBAAqBjG,EAAOkG,kBAAkB,CAAC5O,EAAIuB,GAAG,IAAIvB,EAAIwB,GAAGxB,EAAI6O,oBAAoBnG,EAAOkG,kBAAkB,SAAS1O,EAAG,MAAM,CAACW,WAAW,CAAC,CAACC,KAAK,OAAOC,QAAQ,SAASC,MAAyB,aAAlBhB,EAAI6M,UAA0B3L,WAAW,6BAA6Bd,YAAY,wBAAwB,CAACF,EAAG,MAAM,CAACE,YAAY,SAAS,CAACF,EAAG,OAAO,CAACE,YAAY,YAAY,CAACJ,EAAIuB,GAAG,OAAOrB,EAAG,OAAO,CAACE,YAAY,UAAU,CAACJ,EAAIuB,GAAGvB,EAAIwB,GAAGkH,EAAOoG,aAAa5O,EAAG,OAAO,CAACE,YAAY,QAAQ,CAACJ,EAAIuB,GAAG,UAAUrB,EAAG,MAAM,CAACE,YAAY,gBAAgBqB,MAAMzB,EAAI2O,qBAAqBjG,EAAOkG,kBAAkB,CAAC5O,EAAIuB,GAAG,IAAIvB,EAAIwB,GAAGxB,EAAI6O,oBAAoBnG,EAAOkG,kBAAkB,SAAS1O,EAAG,MAAM,CAACW,WAAW,CAAC,CAACC,KAAK,OAAOC,QAAQ,SAASC,MAAyB,eAAlBhB,EAAI6M,UAA4B3L,WAAW,+BAA+Bd,YAAY,wBAAwB,CAACF,EAAG,MAAM,CAACE,YAAY,SAAS,CAACF,EAAG,OAAO,CAACE,YAAY,YAAY,CAACJ,EAAIuB,GAAG,OAAOrB,EAAG,OAAO,CAACE,YAAY,UAAU,CAACJ,EAAIuB,GAAGvB,EAAIwB,GAAGkH,EAAOqG,eAAe7O,EAAG,OAAO,CAACE,YAAY,QAAQ,CAACJ,EAAIuB,GAAG,UAAUrB,EAAG,MAAM,CAACE,YAAY,gBAAgBqB,MAAMzB,EAAI2O,qBAAqBjG,EAAOkG,kBAAkB,CAAC5O,EAAIuB,GAAG,IAAIvB,EAAIwB,GAAGxB,EAAI6O,oBAAoBnG,EAAOkG,kBAAkB,SAAS1O,EAAG,MAAM,CAACW,WAAW,CAAC,CAACC,KAAK,OAAOC,QAAQ,SAASC,MAAyB,cAAlBhB,EAAI6M,UAA2B3L,WAAW,8BAA8Bd,YAAY,wBAAwB,CAACF,EAAG,MAAM,CAACE,YAAY,SAAS,CAACF,EAAG,OAAO,CAACE,YAAY,YAAY,CAACJ,EAAIuB,GAAG,OAAOrB,EAAG,OAAO,CAACE,YAAY,UAAU,CAACJ,EAAIuB,GAAGvB,EAAIwB,GAAGkH,EAAOsG,cAAc9O,EAAG,OAAO,CAACE,YAAY,QAAQ,CAACJ,EAAIuB,GAAG,UAAUrB,EAAG,MAAM,CAACE,YAAY,gBAAgBqB,MAAMzB,EAAI2O,qBAAqBjG,EAAOkG,kBAAkB,CAAC5O,EAAIuB,GAAG,IAAIvB,EAAIwB,GAAGxB,EAAI6O,oBAAoBnG,EAAOkG,kBAAkB,SAAS1O,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIuB,GAAG,UAAUrB,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIuB,GAAGvB,EAAIwB,GAAGkH,EAAOC,yBAAyBzI,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIuB,GAAG,YAAYrB,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIuB,GAAGvB,EAAIwB,GAAGkH,EAAOE,kBAAkB1I,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIuB,GAAG,YAAYrB,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIuB,GAAGvB,EAAIwB,GAAGkH,EAAOG,uBAAuB3I,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIuB,GAAG,aAAarB,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIuB,GAAGvB,EAAIwB,GAAGkH,EAAOK,iBAAiB7I,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIuB,GAAG,YAAYrB,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIuB,GAAGvB,EAAIwB,GAAGkH,EAAOsD,WAAa,UAAU9L,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIuB,GAAG,YAAYrB,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIuB,GAAGvB,EAAIwB,GAAGkH,EAAOI,yBAAyB5I,EAAG,SAAS,CAACE,YAAY,aAAaqB,MAAM,CACt5W,SAAuC,IAA3BiH,EAAOkG,gBACnB,qBAAsB5O,EAAIwO,gBAAkB9F,EAAOzE,IACnD5D,GAAG,CAAC,MAAQ,SAASe,GAAQsH,EAAOkG,gBAAkB,GAAI5O,EAAIiP,iBAAwB,IAAI,CAACjP,EAAIuB,GAAG,aAAa,IAAG,GAAGrB,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,qBAAqBF,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACJ,EAAIuB,GAAG,gBAAgBrB,EAAG,eAAe,CAACM,MAAM,CAAC,QAAUR,EAAIkP,WAAW,OAASlP,EAAImP,SAAS,sBAAwBnP,EAAIwI,uBAAuBnI,GAAG,CAAC,eAAiB,SAASe,GAAQ,OAAOpB,EAAIoP,OAAOpP,EAAImP,SAAS,EAAE,gBAAgBnP,EAAIqP,WAAW,eAAerP,EAAIsP,WAAW,MAAQtP,EAAIuP,oBAAoBrP,EAAG,WAAW,EAC1jB,EACIgD,EAAkB,G,sEC2PtB,GACApC,KAAA,cACA0O,WAAA,CAAAC,OAAA,IAAAC,YAAA,aAAAC,OAAA,IAAAC,kBAAAA,EAAAA,GACAzM,OACA,OACA0M,UAAA,KACA7E,MAAA,KAEAxC,sBAAA,YACA2G,SAAA,GACAhD,gBAAA,EACAC,oBAAA,GACA8C,YAAA,EACArC,UAAA,YAEAP,iBAAA,EAGAkC,cAAA,KAGAZ,QAAA,CACA,CAAA3J,GAAA,OAAAnD,KAAA,QACA,CAAAmD,GAAA,OAAAnD,KAAA,QACA,CAAAmD,GAAA,OAAAnD,KAAA,SAIAmN,aAAA,CACA,CAAAhK,GAAA,kBAAAnD,KAAA,mBACA,CAAAmD,GAAA,gBAAAnD,KAAA,iBACA,CAAAmD,GAAA,mBAAAnD,KAAA,oBACA,CAAAmD,GAAA,oBAAAnD,KAAA,mBACA,CAAAmD,GAAA,kBAAAnD,KAAA,mBACA,CAAAmD,GAAA,kBAAAnD,KAAA,mBACA,CAAAmD,GAAA,kBAAAnD,KAAA,oBAIAgP,eAAA,CACAC,UAAA,YACAC,SAAA,WACAC,YAAA,eAIAlD,QAAA,CACAC,YAAA,EACAc,gBAAA,GACAnC,cAAA,SACAsC,cAAA,EACAG,kBAAA,GACAC,eAAA,CACAC,aAAA,IAKA4B,WAAA,CACA,CACAjM,GAAA,EACA6H,SAAA,yBACAqE,WAAA,kBACAC,SAAA,EACAC,UAAA,GACAtE,KAAA,IACAhD,WAAA,GACAiD,UAAA,KACAC,OAAA,IACAqE,aAAA,MACAC,OAAA,cACAC,SAAA,aAEA,CACAvM,GAAA,EACA6H,SAAA,yBACAqE,WAAA,kBACAC,SAAA,EACAC,UAAA,GACAtE,KAAA,GACAhD,WAAA,GACAiD,UAAA,KACAC,OAAA,IACAqE,aAAA,MACAC,OAAA,WACAC,SAAA,aAEA,CACAvM,GAAA,EACA6H,SAAA,yBACAqE,WAAA,kBACAC,SAAA,EACAC,UAAA,GACAtE,KAAA,GACAhD,WAAA,GACAiD,UAAA,KACAC,OAAA,IACAqE,aAAA,MACAC,OAAA,YACAC,SAAA,aAEA,CACAvM,GAAA,EACA6H,SAAA,yBACAqE,WAAA,kBACAC,SAAA,EACAC,UAAA,GACAtE,KAAA,IACAhD,WAAA,IACAiD,UAAA,IACAC,OAAA,KACAqE,aAAA,MACAC,OAAA,YACAC,SAAA,eAEA,CACAvM,GAAA,EACA6H,SAAA,0BACAqE,WAAA,mBACAC,SAAA,EACAC,UAAA,GACAtE,KAAA,GACAhD,WAAA,GACAiD,UAAA,KACAC,OAAA,IACAqE,aAAA,MACAC,OAAA,WACAC,SAAA,aAEA,CACAvM,GAAA,EACA6H,SAAA,uBACAqE,WAAA,gBACAC,SAAA,EACAC,UAAA,GACAtE,KAAA,GACAhD,WAAA,GACAiD,UAAA,KACAC,OAAA,IACAqE,aAAA,MACAC,OAAA,cACAC,SAAA,cAIA,EACAzF,SAAA,CAEAiD,qBAEA,YAAAjB,QAAAe,gBAAArJ,OACA,SAGA,MAAAgM,EAAA,KAAAP,WACArJ,QAAA6B,GAAA,KAAAqE,QAAAe,gBAAA4C,SAAAhI,EAAAmF,UACApE,KAAAf,GAAAA,EAAA5H,OAGA6P,EAAA,QAAAC,IAAAH,IAGA,YAAAxC,aAAApH,QAAAsH,GAAAwC,EAAAD,SAAAvC,EAAArN,OACA,EAGAyN,kBAOA,OALA,KAAAxB,QAAAkB,cAAA,SAAAlB,QAAAqB,kBAAA3J,QAAA,KAAAuJ,mBAAAvJ,OAAA,IACA,KAAAsI,QAAAqB,kBAAA,KAAAJ,mBAAAvE,KAAA0E,GAAAA,EAAAlK,MAIA,SAAA8I,QAAAe,gBAAArJ,QAAA,SAAAsI,QAAAqB,kBAAA3J,OACA,GAGA,KAAAyL,WAAArJ,QAAA6B,KAEA,KAAAqE,QAAAe,gBAAA4C,SAAAhI,EAAAmF,YAKA,KAAAd,QAAAqB,kBAAAsC,SAAAhI,EAAA5H,SAKA,KAAAiM,QAAAsB,eAAAC,cASA,GAEAhK,QAAA,CACAiL,mBACA,KAAAL,YAAA,CACA,EAEAI,WAAAjE,GACA,KAAAwE,UAAAxE,CACA,EAEAgE,WAAAhE,GACA,KAAAL,MAAAK,CACA,EACA,wBAEA,KAAAwF,EAAAA,EAAAA,MAGA,OAFA,KAAAC,QAAA/L,KAAA,eACA,KAAAgM,OAAAC,QAAA,cAKA,IACA,MAAAC,QAAAC,EAAAA,EAAAA,IAAA,0BACA,SAAAD,EAAA9N,KAAAgO,KAAA,CACA,MAAAC,EAAAH,EAAA9N,KAAAA,KAAAiO,OAGA,OAAAA,EAUA,OATA,KAAAhF,oBAAA,uBACA,KAAAD,gBAAA,OAEA1E,YAAA,KACA,KAAAqJ,QAAA/L,KAAA,CACAsM,KAAA,YACAC,MAAA,CAAAC,UAAA,iBACA,GACA,KAKA,KAAAT,QAAA/L,KAAA,WACA,CACA,OAAAuC,GACA,KAAAyJ,OAAAzJ,MAAA,WAEA,CACA,EAEAkK,QAAAC,GAEA,GAAAA,EAAA7C,iBAAA,EACA,KAAAmC,OAAAzJ,MAAA,mBADA,CAKA,KAAAuJ,EAAAA,EAAAA,MAGA,OAFA,KAAAC,QAAA/L,KAAA,eACA,KAAAgM,OAAAC,QAAA,cAIA,KAAA7B,SAAAsC,EACA,KAAAjJ,sBAAA,KAAAqE,UACA,KAAAqC,YAAA,CAVA,CAWA,EAEAE,OAAA1G,GACA,IAAAgJ,EAAA,CACAzN,GAAAyE,EAAAzE,GACA+G,MAAA,GACAlK,KAAA4H,EAAA5H,KACAwB,KAAA,KAAAuN,WAEA6B,EAAA1G,MAAA,KAAAA,MACA0G,EAAApP,KAAA,KAAAuN,WACA8B,EAAAA,EAAAA,IAAA,6BAAAD,GAAAE,MAAAX,IACA,SAAAA,EAAA9N,KAAA0O,IAIA,SAAAZ,EAAA9N,KAAA0O,IAIA,KAAAd,OAAAC,QAAA,iBAHA,KAAAD,OAAAzJ,MAAA,cAJA,KAAAyJ,OAAAzJ,MAAA,aAOA,GAEA,EAEA+E,yBACA,KAAAC,iBAAA,KAAAA,eACA,EAGAC,YAAAuF,GACAA,EAAAC,MAAAC,OAAA,IACAF,EAAAC,MAAAE,QAAA,IACAH,EAAAC,MAAAG,SAAA,QACA,EAGA1F,MAAAsF,GACA,MAAAE,EAAAF,EAAAhK,aAEAqK,uBAAA,KACAL,EAAAC,MAAAC,OAAAA,EAAA,KACAF,EAAAC,MAAAE,QAAA,MAEA,EAGAxF,WAAAqF,GAEAA,EAAAC,MAAAC,OAAA,GACAF,EAAAC,MAAAG,SAAA,EACA,EAGAxF,YAAAoF,GAEAA,EAAAC,MAAAC,OAAAF,EAAAhK,aAAA,KACAgK,EAAAC,MAAAG,SAAA,QACA,EAGAvF,MAAAmF,GAEAA,EAAAM,aAGAD,uBAAA,KACAL,EAAAC,MAAAC,OAAA,IACAF,EAAAC,MAAAE,QAAA,MAEA,EAGArF,WAAAkF,GAEAA,EAAAC,MAAAC,OAAA,GACAF,EAAAC,MAAAG,SAAA,EACA,EAGAvE,mBACA,KAAAZ,QAAAC,WACA,KAAAD,QAAAe,gBAAA,KAAAF,QAAAnE,KAAAoE,GAAAA,EAAA/M,OAEA,KAAAiM,QAAAe,gBAAA,GAEA,KAAAC,eACA,EAGAG,qBACA,KAAAnB,QAAAkB,aACA,KAAAlB,QAAAqB,kBAAA,KAAAJ,mBAAAvE,KAAA0E,GAAAA,EAAAlK,KAEA,KAAA8I,QAAAqB,kBAAA,GAEA,KAAAL,eACA,EAGAA,gBAEA,KAAAhB,QAAAC,WAAA,KAAAD,QAAAe,gBAAArJ,SAAA,KAAAmJ,QAAAnJ,OAGA,KAAAI,WAAA,KACA,KAAAkI,QAAAkB,aAAA,KAAAD,mBAAAvJ,OAAA,GACA,KAAAsI,QAAAqB,kBAAA3J,SAAA,KAAAuJ,mBAAAvJ,MAAA,GAKA,EAGAgK,cAAA+B,GACA,MAAA3C,EAAA,KAAAD,QAAAyE,MAAAC,GAAAA,EAAArO,KAAAuM,IACA,OAAA3C,EAAAA,EAAA/M,KAAA,MACA,EAGA+N,oBAAA0B,GAQA,OAPAA,EAAA,GAAAA,GAAA,EACAA,EAAA,WACAA,EAAA,EACAA,EAAA,YACA,GAAAA,IACAA,EAAA,eAEAA,GACA,UAAAT,eAAAC,UACA,aACA,UAAAD,eAAAE,SACA,aACA,UAAAF,eAAAG,YACA,YACA,QACA,aAEA,EAGAtB,qBAAA4B,GAQA,OAPAA,EAAA,GAAAA,GAAA,EACAA,EAAA,WACAA,EAAA,EACAA,EAAA,YACA,GAAAA,IACAA,EAAA,eAEAA,GACA,UAAAT,eAAAC,UACA,yBACA,UAAAD,eAAAE,SACA,wBACA,UAAAF,eAAAG,YACA,2BACA,QACA,SAEA,EAGAsC,eAEA,IAAAb,GACAc,EAAAA,EAAAA,IAAA,oBAAAZ,MAAAa,IACA,KAAAvC,WAAAuC,EAAAtP,KAAAA,MACAuP,EAAAA,EAAAA,IAAA,4BAAAd,MAAAX,IACA,KAAAhD,aAAAgD,EAAA9N,KAAAA,KACA,QAAAwP,EAAA,EAAAA,EAAA,KAAA1E,aAAAxJ,OAAAkO,IACA,KAAA1E,aAAA0E,GAAA1O,GAAA,KAAAgK,aAAA0E,GAAA7R,MAEA4R,EAAAA,EAAAA,IAAA,+BAAAd,MAAA/D,IACA,KAAAD,QAAAC,EAAA1K,KAAAA,KACA,QAAAwP,EAAA,EAAAA,EAAA,KAAA/E,QAAAnJ,OAAAkO,IACA,KAAA/E,QAAA+E,GAAA1O,GAAA,KAAA2J,QAAA+E,GAAA9E,OACA,KAAAD,QAAA+E,GAAA7R,KAAA,KAAA8M,QAAA+E,GAAA9E,OAEA,KAAAF,mBACA,KAAAO,oBAAA,GACA,IAEAwD,EAAA,CACA9D,QAAA,KAAAb,QAAAe,gBACA8E,UAAA,KAAA7F,QAAAqB,kBACAzC,cAAA,KAAAoB,QAAApB,cACA,GAEA,GAEAkH,UAEA,KAAAN,eAEA,KAAA1N,WAAA,KACA,KAAAkI,QAAAqB,kBAAA,KAAAJ,mBAAAvE,KAAA0E,GAAAA,EAAAlK,IAAA,GAEA,EACAkH,MAAA,CAEA,2BACAC,UAEA,QAAA2B,QAAAkB,aACA,KAAAlB,QAAAqB,kBAAA,KAAAJ,mBAAAvE,KAAA0E,GAAAA,EAAAlK,SACA,CAEA,MAAA6O,EAAA,KAAA9E,mBAAAvE,KAAA0E,GAAAA,EAAAlK,KACA,KAAA8I,QAAAqB,kBAAA,KAAArB,QAAAqB,kBAAAvH,QAAA5C,GAAA6O,EAAApC,SAAAzM,IACA,CACA,EACAsH,WAAA,KC5tBoQ,I,UCQhQnD,GAAY,OACd,EACArI,EACAmD,GACA,EACA,KACA,WACA,MAIF,EAAekF,EAAiB,O", "sources": ["webpack://portal-ui/./src/components/common/mider/chatAi.vue", "webpack://portal-ui/src/components/common/mider/chatAi.vue", "webpack://portal-ui/./src/components/common/mider/chatAi.vue?09db", "webpack://portal-ui/./src/components/common/mider/chatAi.vue?3a6e", "webpack://portal-ui/./src/views/Product/OrderDetail.vue", "webpack://portal-ui/src/views/Product/OrderDetail.vue", "webpack://portal-ui/./src/views/Product/OrderDetail.vue?178e", "webpack://portal-ui/./src/views/Product/OrderDetail.vue?e571", "webpack://portal-ui/./src/views/Product/ProductView.vue", "webpack://portal-ui/src/views/Product/ProductView.vue", "webpack://portal-ui/./src/views/Product/ProductView.vue?487f", "webpack://portal-ui/./src/views/Product/ProductView.vue?68ff"], "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[_c('div',{staticClass:\"chat-container\"},[_c('div',{staticClass:\"question-carousel\",on:{\"mouseenter\":_vm.pauseCarousel,\"mouseleave\":_vm.resumeCarousel}},[_c('transition-group',{staticClass:\"carousel-wrapper\",attrs:{\"name\":\"slide\",\"tag\":\"div\"}},_vm._l((_vm.questions),function(question,index){return _c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.currentQuestionIndex === index),expression:\"currentQuestionIndex === index\"}],key:question,staticClass:\"question-item\",on:{\"click\":function($event){return _vm.sendCarouselQuestion(question)},\"mouseenter\":function($event){return _vm.witde(index)}}},[_vm._v(\" \"+_vm._s(question)+\" \")])}),0)],1),_c('div',{staticClass:\"chat-icon\",class:{ 'chat-icon-active': _vm.showChat },on:{\"click\":_vm.toggleChat}},[_c('i',{staticClass:\"fas fa-comment\"})])]),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.showChat),expression:\"showChat\"}],staticClass:\"chat-window\"},[_c('div',{staticClass:\"chat-header\"},[_vm._m(0),_c('div',{staticClass:\"chat-controls\"},[_c('i',{staticClass:\"fas fa-times\",on:{\"click\":_vm.toggleChat}})])]),_c('div',{ref:\"messagesContainer\",staticClass:\"chat-messages\"},[_vm._l((_vm.messages),function(message,index){return _c('div',{key:index,class:['message', message.type]},[(message.type === 'bot')?_c('div',{staticClass:\"avatar\"},[_c('i',{staticClass:\"fas fa-robot\"})]):_vm._e(),_c('div',{staticClass:\"message-content\"},[_c('div',{staticClass:\"message-text\",domProps:{\"innerHTML\":_vm._s(_vm.formatMessage(message.text))}}),_c('div',{staticClass:\"message-time\"},[_vm._v(_vm._s(_vm.formatTime(message.time)))])])])}),(_vm.loading)?_c('div',{staticClass:\"typing-indicator\"},[_c('div',{staticClass:\"dot\"}),_c('div',{staticClass:\"dot\"}),_c('div',{staticClass:\"dot\"})]):_vm._e()],2),_c('div',{staticClass:\"chat-input\"},[_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.userInput),expression:\"userInput\"}],attrs:{\"type\":\"text\",\"placeholder\":\"请输入您的问题...\",\"disabled\":_vm.loading},domProps:{\"value\":(_vm.userInput)},on:{\"keyup\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"enter\",13,$event.key,\"Enter\"))return null;return _vm.sendMessage.apply(null, arguments)},\"input\":function($event){if($event.target.composing)return;_vm.userInput=$event.target.value}}}),_c('button',{attrs:{\"disabled\":_vm.loading || !_vm.userInput.trim()},on:{\"click\":_vm.sendMessage}},[_c('i',{staticClass:\"fas fa-paper-plane\"})])])])])\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"chat-title\"},[_c('i',{staticClass:\"fas fa-robot\"}),_c('span',[_vm._v(\"智能客服\")])])\n}]\n\nexport { render, staticRenderFns }", "<template>\r\n    <div >\r\n<!--        问题轮播-->\r\n        <!-- 悬浮客服容器 -->\r\n        <div class=\"chat-container\">\r\n            <!-- 问题轮播区 -->\r\n            <div class=\"question-carousel\"\r\n                 @mouseenter=\"pauseCarousel\"\r\n                 @mouseleave=\"resumeCarousel\">\r\n                <transition-group name=\"slide\" tag=\"div\" class=\"carousel-wrapper\">\r\n                    <div v-for=\"(question, index) in questions\"\r\n                         :key=\"question\"\r\n                         class=\"question-item\"\r\n                         v-show=\"currentQuestionIndex === index\"\r\n                         @click=\"sendCarouselQuestion(question)\"\r\n                         @mouseenter=\"witde(index)\"\r\n                    >\r\n                        {{ question }}\r\n                    </div>\r\n                </transition-group>\r\n            </div>\r\n\r\n            <!-- 原有悬浮按钮 -->\r\n            <div class=\"chat-icon\"\r\n                 :class=\"{ 'chat-icon-active': showChat }\"\r\n                 @click=\"toggleChat\">\r\n                <i class=\"fas fa-comment\"></i>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- 聊天窗口 -->\r\n        <div class=\"chat-window\" v-show=\"showChat\">\r\n            <div class=\"chat-header\">\r\n                <div class=\"chat-title\">\r\n                    <i class=\"fas fa-robot\"></i>\r\n                    <span>智能客服</span>\r\n                </div>\r\n                <div class=\"chat-controls\">\r\n                    <i class=\"fas fa-times\" @click=\"toggleChat\"></i>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"chat-messages\" ref=\"messagesContainer\">\r\n                <div\r\n                        v-for=\"(message, index) in messages\"\r\n                        :key=\"index\"\r\n                        :class=\"['message', message.type]\"\r\n                >\r\n                    <div class=\"avatar\" v-if=\"message.type === 'bot'\">\r\n                        <i class=\"fas fa-robot\"></i>\r\n                    </div>\r\n                    <div class=\"message-content\">\r\n                        <div class=\"message-text\" v-html=\"formatMessage(message.text)\"></div>\r\n                        <div class=\"message-time\">{{ formatTime(message.time) }}</div>\r\n                    </div>\r\n                </div>\r\n                <div class=\"typing-indicator\" v-if=\"loading\">\r\n                    <div class=\"dot\"></div>\r\n                    <div class=\"dot\"></div>\r\n                    <div class=\"dot\"></div>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"chat-input\">\r\n                <input\r\n                        type=\"text\"\r\n                        v-model=\"userInput\"\r\n                        placeholder=\"请输入您的问题...\"\r\n                        @keyup.enter=\"sendMessage\"\r\n                        :disabled=\"loading\"\r\n                />\r\n                <button @click=\"sendMessage\" :disabled=\"loading || !userInput.trim()\">\r\n                    <i class=\"fas fa-paper-plane\"></i>\r\n                </button>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    export default {\r\n        name: 'chatAi',\r\n\r\n        data() {\r\n            return {\r\n                showChat: false,\r\n                userInput: '',\r\n                messages: [\r\n                    {\r\n                        type: 'bot',\r\n                        text: '您好！我是智能客服助手，有什么可以帮您？',\r\n                        time: new Date()\r\n                    }\r\n                ],\r\n                loading: false,\r\n                historyMessages:[],\r\n                questions: [\r\n                    \"如何租赁GPU算力？\",\r\n                    \"支持哪些支付方式？\",\r\n                    \"如何查看订单状态？\"\r\n                ],\r\n                currentQuestionIndex: 0,\r\n                carouselTimer: null,\r\n                carouselInterval: 3000,\r\n                isPaused: false\r\n            }\r\n        },\r\n        beforeDestroy() {\r\n            this.clearCarousel()\r\n        },\r\n        mounted() {\r\n            this.startCarousel();\r\n            // 导入 Font Awesome 图标库\r\n            if (!document.getElementById('font-awesome')) {\r\n                const link = document.createElement('link');\r\n                link.id = 'font-awesome';\r\n                link.rel = 'stylesheet';\r\n                link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css';\r\n                document.head.appendChild(link);\r\n            }\r\n        },\r\n\r\n        methods: {\r\n            witde(index){\r\n                this.currentQuestionIndex = index;\r\n                this.pauseCarousel();\r\n            },\r\n            startCarousel() {\r\n                let that = this\r\n                this.clearCarousel();\r\n                this.carouselTimer = setInterval(() => {\r\n                    if (!that.isPaused) {\r\n                        this.currentQuestionIndex =\r\n                            (this.currentQuestionIndex + 1) % this.questions.length\r\n                        console.log(\"数据\", this.currentQuestionIndex)\r\n                        console.log(\"ispasued\",that.isPaused)\r\n                    }\r\n                }, this.carouselInterval)\r\n            },\r\n            pauseCarousel() {\r\n                this.isPaused = true;\r\n            },\r\n            resumeCarousel() {\r\n                this.isPaused = false;\r\n            },\r\n            clearCarousel() {\r\n                if (this.carouselTimer) {\r\n                    clearInterval(this.carouselTimer);\r\n                    this.carouselTimer = null;\r\n                }\r\n            },\r\n            // 点击轮播问题自动提问\r\n            sendCarouselQuestion(question) {\r\n                this.userInput = question;\r\n                this.sendMessage();\r\n            },\r\n            toggleChat() {\r\n                this.showChat = !this.showChat;\r\n\r\n                if (this.showChat) {\r\n                    this.$nextTick(() => {\r\n                        this.scrollToBottom();\r\n                    });\r\n                }\r\n            },\r\n\r\n            async sendMessage() {\r\n                if (!this.userInput.trim() || this.loading) return;\r\n\r\n                // 添加用户消息\r\n                this.messages.push({\r\n                    type: 'user',\r\n                    text: this.userInput,\r\n                    time: new Date()\r\n                });\r\n\r\n                const userQuestion = this.userInput;\r\n                this.userInput = '';\r\n                this.loading = true;\r\n                //添加历史记录\r\n                this.historyMessages.push({\r\n                    role:'user',\r\n                    content:userQuestion,\r\n                })\r\n\r\n                // 构造请求体\r\n                const requestBody = {\r\n                    model: 'Qwen/QwQ-32B',\r\n                    messages: [{role:\"system\",content: \"你是天工开物算力租赁网站的客服，当我问到算力租赁相关问题的时候再进行回答，如果我的问题与算力无关则回答“小天也不知道怎么办了呢，不如我们来聊聊算力相关的话题吧！”,如果问题涉及到你是谁或者你好的这种礼貌性踊跃，可以进行礼貌性的回复\"},...this.historyMessages], // 携带上下文历史\r\n                    stream: true,\r\n                    options: {\r\n                        presence_penalty: 1.2,  // 重复内容惩罚（0-2）\r\n                        frequency_penalty: 1.5, // 高频词惩罚（0-2）\r\n                        // repeat_last_n: 64,      // 检查重复的上下文长度\r\n                        seed: 12345             // 固定随机种子\r\n                    }\r\n                };\r\n                // 滚动到底部\r\n                this.$nextTick(() => {\r\n                    this.scrollToBottom();\r\n                });\r\n\r\n                try {\r\n                    // 调用后端API获取回复\r\n                    // 替换为你的实际API\r\n                    const response = await fetch('https://api.siliconflow.cn/v1/chat/completions', {\r\n                        method: 'POST',\r\n                        headers: {\r\n                            Authorization: 'Bearer sk-yxjtnfarjndtcwbjwmscdejrgtlpaaebnfdxcfnhhuanbqty',\r\n                            'Content-Type': 'application/json',\r\n                        },\r\n                        body: JSON.stringify(requestBody)\r\n                    });\r\n\r\n                    // 处理流式数据\r\n                    const reader = response.body.getReader();\r\n                    const decoder = new TextDecoder();\r\n                    const aiResponseIndex = this.messages.push({\r\n                        type: 'bot',\r\n                        text: '',\r\n                        time:new Date()\r\n                    }) - 1;\r\n                    while (true) {\r\n                        const { done, value } = await reader.read();\r\n                        if (done) break;\r\n\r\n                        // 解析流式数据块（可能包含多个JSON对象）\r\n                        const chunk = decoder.decode(value);\r\n                        const lines = chunk.split('\\n').filter(line => line.trim());\r\n\r\n                        for (const line of lines) {\r\n                            try {\r\n                                const jsonString = line.slice(6).trim();\r\n                                if (jsonString === \"\" || jsonString === \"[DONE]\") continue;\r\n\r\n                                let data = JSON.parse(jsonString)\r\n                                if (data.choices) {\r\n                                    if (data.choices[0].delta.reasoning_content!=null){\r\n                                        continue\r\n                                    }\r\n                                    if (data.choices[0].delta.content == '\\n\\n'){\r\n                                        continue\r\n                                    }\r\n                                    this.messages[aiResponseIndex].text += data.choices[0].delta.content;\r\n                                }\r\n                            } catch (e) {\r\n                            }\r\n                        }\r\n                    }\r\n                    this.historyMessages.push({\r\n                        role:\"assistant\",\r\n                        content:this.messages[aiResponseIndex].text\r\n                    })\r\n\r\n                    // 添加机器人回复\r\n                    // this.messages.push({\r\n                    //     type: 'bot',\r\n                    //     text: response,\r\n                    //     time: new Date()\r\n                    // });\r\n                } catch (error) {\r\n\r\n                    // 添加错误消息\r\n                    this.messages.push({\r\n                        type: 'bot',\r\n                        text: '抱歉，系统暂时无法响应，请稍后再试。',\r\n                        time: new Date()\r\n                    });\r\n                } finally {\r\n                    this.loading = false;\r\n\r\n                    // 滚动到底部\r\n                    this.$nextTick(() => {\r\n                        this.scrollToBottom();\r\n                    });\r\n                }\r\n            },\r\n\r\n            // 模拟API调用，实际使用时替换为真实API\r\n            async callChatAPI(message) {\r\n                // 模拟网络延迟\r\n                await new Promise(resolve => setTimeout(resolve, 1000));\r\n\r\n                // TODO: 替换为实际的API调用\r\n                // const response = await fetch('YOUR_API_ENDPOINT', {\r\n                //   method: 'POST',\r\n                //   headers: {\r\n                //     'Content-Type': 'application/json',\r\n                //   },\r\n                //   body: JSON.stringify({ message }),\r\n                // });\r\n                // return await response.json();\r\n\r\n                // 模拟返回数据\r\n                return `感谢您的提问: \"${message}\"。这是一个模拟回复，请替换为真实API调用。`;\r\n            },\r\n\r\n            scrollToBottom() {\r\n                const container = this.$refs.messagesContainer;\r\n                container.scrollTop = container.scrollHeight;\r\n            },\r\n\r\n            formatTime(date) {\r\n                return new Date(date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\r\n            },\r\n\r\n            formatMessage(text) {\r\n                // 处理文本中的链接、表情等\r\n                return text\r\n                    .replace(/\\n/g, '<br>')\r\n                    .replace(/(https?:\\/\\/[^\\s]+)/g, '<a href=\"$1\" target=\"_blank\">$1</a>');\r\n            }\r\n        }\r\n    }\r\n</script>\r\n\r\n<style scoped>\r\n    /* 修正后的轮播样式 */\r\n    .chat-container {\r\n        position: fixed;\r\n        bottom: 20px;\r\n        right: 20px;\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: flex-end;\r\n        gap: 10px;\r\n        z-index: 1001;\r\n    }\r\n\r\n    .question-carousel {\r\n        left: 20px;\r\n        padding-bottom: 85px;\r\n        padding-top: 20px;\r\n        color: white;\r\n        min-width: 150px;\r\n        text-align: left;\r\n        cursor: pointer;\r\n        overflow: hidden;\r\n        position: relative;\r\n        height: 60px; /* 固定高度避免跳动 */\r\n    }\r\n\r\n    .carousel-wrapper {\r\n        position: relative;\r\n        height: 100%;\r\n    }\r\n\r\n    .question-item {\r\n        position: absolute;\r\n        border-radius: 20px 20px 20px 20px;\r\n        background-color: black;\r\n        width: 100%;\r\n        left: 0;\r\n        top: 50%;\r\n        transform: translateY(-50%);\r\n        padding: 10px 10px;\r\n        font-size: 14px;\r\n        opacity: 1;\r\n        transition: all 0.5s ease;\r\n    }\r\n\r\n    .question-item:hover {\r\n        color: #4286f4;\r\n    }\r\n\r\n    /* 过渡动画修正 */\r\n    .slide-enter-active,\r\n    .slide-leave-active {\r\n        transition: all 0.5s ease;\r\n    }\r\n    .slide-enter-from {\r\n        opacity: 0;\r\n        transform: translateY(20px) translateY(-50%);\r\n    }\r\n    .slide-leave-to {\r\n        opacity: 0;\r\n        transform: translateY(-20px) translateY(-50%);\r\n    }\r\n    .slide-enter-to,\r\n    .slide-leave-from {\r\n        opacity: 1;\r\n        transform: translateY(0) translateY(-50%);\r\n    }\r\n    .chat-icon {\r\n        position: fixed;\r\n        bottom: 20px;\r\n        right: 20px;\r\n        width: 60px;\r\n        height: 60px;\r\n        background-color: blue;\r\n        border-radius: 50%;\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        cursor: pointer;\r\n        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);\r\n        transition: all 0.3s ease;\r\n        z-index: 1001;\r\n    }\r\n\r\n    .chat-icon i {\r\n        color: white;\r\n        font-size: 24px;\r\n    }\r\n\r\n    .chat-icon:hover, .chat-icon-active {\r\n        background-color: #3367d6;\r\n        transform: scale(1.05);\r\n    }\r\n\r\n    .chat-window {\r\n        position: fixed;\r\n        bottom: 90px;\r\n        right: 20px;\r\n        width: 350px;\r\n        height: 500px;\r\n        background-color: #fff;\r\n        border-radius: 10px;\r\n        box-shadow: 0 5px 40px rgba(0, 0, 0, 0.16);\r\n        display: flex;\r\n        flex-direction: column;\r\n        overflow: hidden;\r\n        z-index: 1002;\r\n    }\r\n\r\n    .chat-header {\r\n        padding: 15px;\r\n        background-color: #4286f4;\r\n        color: white;\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n    }\r\n\r\n    .chat-title {\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 10px;\r\n        font-weight: bold;\r\n    }\r\n\r\n    .chat-controls i {\r\n        cursor: pointer;\r\n        font-size: 18px;\r\n    }\r\n\r\n    .chat-messages {\r\n        flex: 1;\r\n        padding: 15px;\r\n        overflow-y: auto;\r\n        background-color: #f5f5f5;\r\n    }\r\n\r\n    .message {\r\n        display: flex;\r\n        margin-bottom: 15px;\r\n        align-items: flex-start;\r\n    }\r\n\r\n    .message.user {\r\n        flex-direction: row-reverse;\r\n    }\r\n\r\n    .avatar {\r\n        width: 36px;\r\n        height: 36px;\r\n        border-radius: 50%;\r\n        background-color: #4286f4;\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        color: white;\r\n        margin-right: 10px;\r\n    }\r\n\r\n    .message-content {\r\n        max-width: 70%;\r\n    }\r\n\r\n    .message-text {\r\n        padding: 10px 15px;\r\n        border-radius: 18px;\r\n        margin-bottom: 5px;\r\n        word-break: break-word;\r\n    }\r\n\r\n    .message.bot .message-text {\r\n        background-color: white;\r\n        border: 1px solid #e0e0e0;\r\n    }\r\n\r\n    .message.user .message-text {\r\n        background-color: #4286f4;\r\n        color: white;\r\n        text-align: right;\r\n    }\r\n\r\n    .message-time {\r\n        font-size: 12px;\r\n        color: #999;\r\n        margin-left: 10px;\r\n    }\r\n\r\n    .message.user .message-time {\r\n        text-align: right;\r\n    }\r\n\r\n    .typing-indicator {\r\n        display: flex;\r\n        padding: 10px 15px;\r\n        background-color: white;\r\n        border-radius: 18px;\r\n        border: 1px solid #e0e0e0;\r\n        width: fit-content;\r\n        margin-bottom: 15px;\r\n    }\r\n\r\n    .dot {\r\n        width: 8px;\r\n        height: 8px;\r\n        background-color: #999;\r\n        border-radius: 50%;\r\n        margin: 0 2px;\r\n        animation: bounce 1.5s infinite;\r\n    }\r\n\r\n    .dot:nth-child(2) {\r\n        animation-delay: 0.2s;\r\n    }\r\n\r\n    .dot:nth-child(3) {\r\n        animation-delay: 0.4s;\r\n    }\r\n\r\n    @keyframes bounce {\r\n        0%, 60%, 100% {\r\n            transform: translateY(0);\r\n        }\r\n        30% {\r\n            transform: translateY(-4px);\r\n        }\r\n    }\r\n\r\n    .chat-input {\r\n        padding: 15px;\r\n        display: flex;\r\n        border-top: 1px solid #e0e0e0;\r\n        background-color: white;\r\n    }\r\n\r\n    .chat-input input {\r\n        flex: 1;\r\n        padding: 10px 15px;\r\n        border: 1px solid #e0e0e0;\r\n        border-radius: 20px;\r\n        font-size: 14px;\r\n        outline: none;\r\n    }\r\n\r\n    .chat-input button {\r\n        margin-left: 10px;\r\n        width: 40px;\r\n        height: 40px;\r\n        border-radius: 50%;\r\n        background-color: #4286f4;\r\n        color: white;\r\n        border: none;\r\n        cursor: pointer;\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n    }\r\n\r\n    .chat-input button:disabled {\r\n        background-color: #b3c9f4;\r\n        cursor: not-allowed;\r\n    }\r\n\r\n    .chat-input button i {\r\n        font-size: 16px;\r\n    }\r\n\r\n    /* 移动端适配 */\r\n    @media (max-width: 480px) {\r\n        .chat-window {\r\n            width: 100%;\r\n            height: 100%;\r\n            bottom: 0;\r\n            right: 0;\r\n            border-radius: 0;\r\n        }\r\n\r\n        .chat-icon {\r\n            bottom: 15px;\r\n            right: 15px;\r\n        }\r\n    }\r\n</style>", "import mod from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./chatAi.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./chatAi.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./chatAi.vue?vue&type=template&id=46c63c47&scoped=true&\"\nimport script from \"./chatAi.vue?vue&type=script&lang=js&\"\nexport * from \"./chatAi.vue?vue&type=script&lang=js&\"\nimport style0 from \"./chatAi.vue?vue&type=style&index=0&id=46c63c47&prod&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"46c63c47\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[(_vm.visible)?_c('div',{staticClass:\"modal-overlay\"},[_c('div',{staticClass:\"modal-container\"},[_vm._m(0),_c('div',{staticClass:\"modal-body\"},[_c('div',{staticClass:\"section-title\"},[_vm._v(\"计费方式\")]),_c('div',{staticClass:\"billing-tabs\"},_vm._l((_vm.billingOptions),function(option,index){return _c('div',{key:index,staticClass:\"billing-tab\",class:{ 'active': _vm.selectedBillingMethod === option.value },on:{\"click\":function($event){_vm.selectedBillingMethod = option.value}}},[_vm._v(\" \"+_vm._s(option.label)+\" \")])}),0),_c('div',{staticClass:\"section-title\"},[_vm._v(\"选择主机\")]),_c('div',{staticClass:\"specs-example-table\"},[_vm._m(1),_c('div',{staticClass:\"specs-example-row\"},[_c('div',{staticClass:\"specs-example-cell\"},[_vm._v(_vm._s(_vm.server.name))]),_c('div',{staticClass:\"specs-example-cell\"},[_vm._v(_vm._s(_vm.server.graphicsCardNumber)+\"卡\")]),_c('div',{staticClass:\"specs-example-cell\"},[_vm._v(_vm._s(_vm.server.videoMemory)+\"GB\")]),_c('div',{staticClass:\"specs-example-cell\"},[_vm._v(_vm._s(_vm.server.gpuNuclearNumber)+\"核\")]),_c('div',{staticClass:\"specs-example-cell\"},[_vm._v(_vm._s(_vm.server.internalMemory)+\"GB\")]),_c('div',{staticClass:\"specs-example-cell\"},[_vm._v(_vm._s(_vm.server.systemDisk)+\"GB\")])]),_c('div',{staticClass:\"server-card-footer\"},[_c('div',{staticClass:\"server-price\"},[_vm._v(\"¥ \"+_vm._s(_vm.server[_vm.selectedBillingMethod])),_c('span',{staticClass:\"spec-label\"},[_vm._v(\" \"+_vm._s(_vm.priceUnit))])])])]),_c('div',{staticClass:\"section-title\"},[_vm._v(\"实例规格\")]),_c('div',{staticClass:\"specs-example-table\"},[_vm._m(2),_c('div',{staticClass:\"specs-example-row\"},[_c('div',{staticClass:\"specs-example-cell\"},[_vm._v(_vm._s(_vm.server.name))]),_c('div',{staticClass:\"specs-example-cell\"},[_vm._v(_vm._s(_vm.server.gpuNuclearNumber)+\"核心\")]),_c('div',{staticClass:\"specs-example-cell\"},[_vm._v(_vm._s(_vm.server.internalMemory)+\"GB\")]),_c('div',{staticClass:\"specs-example-cell\"},[_vm._v(_vm._s(_vm.server.systemDisk)+\"GB\")]),_c('div',{staticClass:\"specs-example-cell\"},[_vm._v(\"免费\"+_vm._s(_vm.server.dataDisk)+\"GB SSD\")])])]),_c('div',{staticClass:\"rental-duration\"},[_c('div',{staticClass:\"duration-label\"},[_vm._v(\"租用时长：\")]),_c('div',{staticClass:\"duration-selector\"},[_c('select',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.selectedDuration),expression:\"selectedDuration\"}],staticClass:\"duration-select\",on:{\"change\":function($event){var $$selectedVal = Array.prototype.filter.call($event.target.options,function(o){return o.selected}).map(function(o){var val = \"_value\" in o ? o._value : o.value;return val}); _vm.selectedDuration=$event.target.multiple ? $$selectedVal : $$selectedVal[0]}}},_vm._l((_vm.currentDurationOptions),function(option,index){return _c('option',{key:index,domProps:{\"value\":option.value}},[_vm._v(\" \"+_vm._s(option.label)+\" \")])}),0)])]),_c('div',{staticClass:\"price-summary\"},[_c('div',{staticClass:\"price-label\"},[_vm._v(\"配置费用：\")]),_c('div',{staticClass:\"price-value\"},[_vm._v(\"¥ \"+_vm._s(_vm.totalPrice)+\" 元 \")])])]),_c('div',{staticClass:\"modal-footer\"},[_c('button',{staticClass:\"cancel-button\",on:{\"click\":_vm.closeModal}},[_vm._v(\"取消\")]),_c('button',{staticClass:\"confirm-button\",on:{\"click\":_vm.showConfirmDialog}},[_vm._v(\" 立即租赁 \")])])])]):_vm._e(),(_vm.showConfirmation)?_c('div',{staticClass:\"confirm-overlay\"},[_c('div',{staticClass:\"confirm-dialog\"},[_c('div',{staticClass:\"confirm-message\"},[_vm._v(\"是否确认订单？\")]),_c('div',{staticClass:\"confirm-footer\"},[_c('button',{staticClass:\"confirm-cancel\",on:{\"click\":function($event){_vm.showConfirmation = false}}},[_vm._v(\"取消\")]),_c('button',{staticClass:\"confirm-ok\",on:{\"click\":_vm.confirmOrder}},[_vm._v(\"确认\")])])])]):_vm._e()])\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"modal-header1\"},[_c('h2',[_vm._v(\"订单确认\")])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"specs-example-row\"},[_c('div',{staticClass:\"specs-example-cell\"},[_vm._v(\"GPU型号\")]),_c('div',{staticClass:\"specs-example-cell\"},[_vm._v(\"GPU\")]),_c('div',{staticClass:\"specs-example-cell\"},[_vm._v(\"显存\")]),_c('div',{staticClass:\"specs-example-cell\"},[_vm._v(\"vCPU\")]),_c('div',{staticClass:\"specs-example-cell\"},[_vm._v(\"内存\")]),_c('div',{staticClass:\"specs-example-cell\"},[_vm._v(\"系统盘\")])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"specs-example-row\"},[_c('div',{staticClass:\"specs-example-cell\"},[_vm._v(\"GPU型号\")]),_c('div',{staticClass:\"specs-example-cell\"},[_vm._v(\"vCPU\")]),_c('div',{staticClass:\"specs-example-cell\"},[_vm._v(\"内存\")]),_c('div',{staticClass:\"specs-example-cell\"},[_vm._v(\"系统盘\")]),_c('div',{staticClass:\"specs-example-cell\"},[_vm._v(\"数据盘\")])])\n}]\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"modal-overlay\" v-if=\"visible\">\r\n    <div class=\"modal-container\">\r\n      <div class=\"modal-header1\">\r\n        <h2>订单确认</h2>\r\n      </div>\r\n\r\n      <div class=\"modal-body\">\r\n        <!-- 计费方式 -->\r\n        <div class=\"section-title\">计费方式</div>\r\n        <div class=\"billing-tabs\">\r\n          <div\r\n              v-for=\"(option, index) in billingOptions\"\r\n              :key=\"index\"\r\n              class=\"billing-tab\"\r\n              :class=\"{ 'active': selectedBillingMethod === option.value }\"\r\n              @click=\"selectedBillingMethod = option.value\"\r\n          >\r\n            {{ option.label }}\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"section-title\">选择主机</div>\r\n        <div class=\"specs-example-table\">\r\n          <div class=\"specs-example-row\">\r\n            <div class=\"specs-example-cell\">GPU型号</div>\r\n            <div class=\"specs-example-cell\">GPU</div>\r\n            <div class=\"specs-example-cell\">显存</div>\r\n            <div class=\"specs-example-cell\">vCPU</div>\r\n            <div class=\"specs-example-cell\">内存</div>\r\n            <div class=\"specs-example-cell\">系统盘</div>\r\n          </div>\r\n          <div class=\"specs-example-row\">\r\n            <div class=\"specs-example-cell\">{{ server.name }}</div>\r\n            <div class=\"specs-example-cell\">{{ server.graphicsCardNumber }}卡</div>\r\n            <div class=\"specs-example-cell\">{{ server.videoMemory }}GB</div>\r\n            <div class=\"specs-example-cell\">{{ server.gpuNuclearNumber }}核</div>\r\n            <div class=\"specs-example-cell\">{{ server.internalMemory }}GB</div>\r\n            <div class=\"specs-example-cell\">{{ server.systemDisk }}GB</div>\r\n          </div>\r\n          <div class=\"server-card-footer\">\r\n            <div class=\"server-price\">¥ {{ server[selectedBillingMethod] }}<span class=\"spec-label\"> {{ priceUnit }}</span></div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 服务器配置规格展示 -->\r\n        <div class=\"section-title\">实例规格</div>\r\n        <div class=\"specs-example-table\">\r\n          <div class=\"specs-example-row\">\r\n            <div class=\"specs-example-cell\">GPU型号</div>\r\n            <div class=\"specs-example-cell\">vCPU</div>\r\n            <div class=\"specs-example-cell\">内存</div>\r\n            <div class=\"specs-example-cell\">系统盘</div>\r\n            <div class=\"specs-example-cell\">数据盘</div>\r\n          </div>\r\n          <div class=\"specs-example-row\">\r\n            <div class=\"specs-example-cell\">{{ server.name }}</div>\r\n            <div class=\"specs-example-cell\">{{ server.gpuNuclearNumber }}核心</div>\r\n            <div class=\"specs-example-cell\">{{ server.internalMemory }}GB</div>\r\n            <div class=\"specs-example-cell\">{{ server.systemDisk }}GB</div>\r\n            <div class=\"specs-example-cell\">免费{{ server.dataDisk }}GB SSD</div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 租用时长 -->\r\n        <div class=\"rental-duration\">\r\n          <div class=\"duration-label\">租用时长：</div>\r\n          <div class=\"duration-selector\">\r\n            <select v-model=\"selectedDuration\"  class=\"duration-select\">\r\n              <option\r\n                  v-for=\"(option, index) in currentDurationOptions\"\r\n                  :key=\"index\"\r\n                  :value=\"option.value\"\r\n              >\r\n                {{ option.label }}\r\n              </option>\r\n            </select>\r\n          </div>\r\n          <div class=\"duration-hint\" v-if=\"selectedBillingMethod === 'priceHour'\"></div>\r\n          <div class=\"duration-hint\" v-else-if=\"selectedBillingMethod === 'priceDay'\"></div>\r\n          <div class=\"duration-hint\" v-else-if=\"selectedBillingMethod === 'priceWeek'\"></div>\r\n          <div class=\"duration-hint\" v-else></div>\r\n        </div>\r\n\r\n        <!-- 配置费用 -->\r\n        <div class=\"price-summary\">\r\n          <div class=\"price-label\">配置费用：</div>\r\n          <div class=\"price-value\">¥ {{ totalPrice }} 元 </div>\r\n          <div class=\"details-link\" @click=\"showPriceDetails = true\">费用明细</div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"modal-footer\">\r\n        <button class=\"cancel-button\" @click=\"closeModal\">取消</button>\r\n        <button\r\n            class=\"confirm-button\"\r\n            @click=\"showConfirmDialog\"\r\n        >\r\n          立即租赁\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'OrderDetail',\r\n  props: {\r\n    visible: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    server: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    selectedBillingMethod: {\r\n      type:String\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      defaultDiskSize: 50,\r\n      selectedBillingMethod: 'priceDay',\r\n      selectedDuration: 1,\r\n      needExtraDisk: false,\r\n      showPriceDetails: false,\r\n      showConfirmation: false, // 控制二次确认弹窗显示\r\n      billingOptions: [\r\n        { label: '按量计费', value: 'priceHour', unit: '/小时' },\r\n        { label: '包日', value: 'priceDay', unit: '/日' },\r\n        { label: '包月', value: 'priceMouth', unit: '/月' },\r\n        { label: '包年', value: 'priceYear', unit: '/年' }\r\n      ],\r\n      durationOptionsHour: [\r\n        { label: '1小时', value: 1 },\r\n        { label: '2小时', value: 2 },\r\n        { label: '4小时', value: 4 },\r\n        { label: '8小时', value: 8 },\r\n        { label: '12小时', value: 12 },\r\n        { label: '24小时', value: 24 }\r\n      ],\r\n      durationOptionsDay: [\r\n        { label: '1天', value: 1 },\r\n        { label: '2天', value: 2 },\r\n        { label: '3天', value: 3 },\r\n        { label: '4天', value: 4 },\r\n        { label: '5天', value: 5 },\r\n        { label: '6天', value: 6 }\r\n      ],\r\n      durationOptionsWeek: [\r\n        { label: '1月', value: 1 },\r\n        { label: '2月', value: 2 },\r\n        { label: '3月', value: 3 },\r\n        { label: '4月', value: 4 },\r\n        { label: '5月', value: 5 },\r\n        { label: '6月', value: 6 },\r\n        { label: '7月', value: 7 },\r\n        { label: '8月', value: 8 }\r\n      ],\r\n      durationOptionsMonth: [\r\n        { label: '1年', value: 1 },\r\n        { label: '2年', value: 2 },\r\n        { label: '3年', value: 3 }\r\n      ]\r\n    };\r\n  },\r\n  created() {\r\n  },\r\n  computed: {\r\n    currentDurationOptions() {\r\n      switch (this.selectedBillingMethod) {\r\n        case 'priceHour':\r\n          this.totalPrice = this.server['priceHour']\r\n          return this.durationOptionsHour;\r\n        case 'priceDay':\r\n          this.totalPrice = this.server['priceDay']\r\n          return this.durationOptionsDay;\r\n        case 'priceMouth':\r\n          this.totalPrice = this.server['priceMouth']\r\n          return this.durationOptionsWeek;\r\n        case 'priceYear':\r\n          this.totalPrice = this.server['priceYear']\r\n          return this.durationOptionsMonth;\r\n        default:\r\n          return this.durationOptionsDay;\r\n      }\r\n    },\r\n    totalPrice() {\r\n      const price = this.server[this.selectedBillingMethod] || 0;\r\n      return (price * this.selectedDuration).toFixed(2);\r\n    },\r\n    priceUnit() {\r\n      switch (this.selectedBillingMethod) {\r\n        case 'priceHour': return '/小时';\r\n        case 'priceDay': return '/日';\r\n        case 'priceMouth': return '/月';\r\n        case 'priceYear': return '/年';\r\n        default: return '';\r\n      }\r\n    },\r\n    totalTime(){\r\n      switch (this.selectedBillingMethod) {\r\n        case 'priceHour':\r\n          return '小时';\r\n        case 'priceDay':\r\n          return '天'\r\n        case 'priceMouth':\r\n          return '月'\r\n        case 'priceYear':\r\n          return '年'\r\n        default:\r\n          return '小时'\r\n      }\r\n    },\r\n  },\r\n  watch: {\r\n    selectedBillingMethod() {\r\n      this.selectedDuration = this.currentDurationOptions[0]?.value || 1;\r\n    },\r\n    selectedDuration(){\r\n      this.totalPrice =this.server[this.selectedBillingMethod] * this.selectedDuration\r\n    },\r\n    selectedDuration:{\r\n      handler(newval){\r\n        this.$emit('time-updated',newval+this.totalTime)\r\n      },\r\n      immediate:true\r\n    },\r\n    totalPrice:{\r\n      handler(newval){\r\n        this.$emit('price-updated',newval);\r\n      },\r\n      immediate:true\r\n    },\r\n  },\r\n  methods: {\r\n    closeModal() {\r\n      this.$emit('close');\r\n    },\r\n    selectServer() {\r\n      // 保留方法，但不需要实际操作，因为只有一个服务器\r\n    },\r\n    showConfirmDialog() {\r\n      this.showConfirmation = true;\r\n    },\r\n    confirmOrder() {\r\n      this.showConfirmation = false;\r\n      this.$emit(\"orderSubmitted\");\r\n\r\n      const order = {\r\n        serverId: this.server.id,\r\n        serverName: this.server.name,\r\n        billingMethod: this.selectedBillingMethod,\r\n        duration: this.selectedDuration,\r\n        needExtraDisk: this.needExtraDisk,\r\n        price: this.totalPrice,\r\n        specs: {\r\n          gpuModel: this.server.name,\r\n          vcpu: this.server.vcpu,\r\n          systemDisk: this.server.systemDisk,\r\n          cloudDisk: this.server.cloudDisk || 0,\r\n          memory: this.server.memory,\r\n          videoMemory: this.server.videoMemory\r\n        }\r\n      };\r\n\r\n      this.$emit('order-success', order);\r\n      this.closeModal();\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.modal-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 1000;\r\n}\r\n\r\n.modal-container {\r\n  background-color: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\r\n  width: 1020px;\r\n  max-width: 95%;\r\n  max-height: 90vh;\r\n  overflow-y: auto;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.modal-header1 {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 10px 24px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.modal-header1 h2 {\r\n  margin: 0;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.close-button {\r\n  background: none;\r\n  width: 30px;\r\n  height: 55px;\r\n  border: none;\r\n  font-size: 24px;\r\n  cursor: pointer;\r\n  color: #999;\r\n  transition: color 0.2s;\r\n}\r\n\r\n.close-button:hover {\r\n  color: #333;\r\n}\r\n\r\n.modal-body {\r\n  margin-top: -4vh;\r\n  padding: 20px 24px;\r\n  flex-grow: 1;\r\n}\r\n\r\n.section-title {\r\n  font-size: 15px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-top: 24px;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.billing-tabs {\r\n  display: flex;\r\n  background-color: #f7f7f9;\r\n  border-radius: 8px;\r\n  font-size: 1.8vh;\r\n  padding: 4px;\r\n  width:50vh;\r\n  margin-bottom: 16px;\r\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.billing-tab {\r\n  flex: 1;\r\n  padding: 12px 16px;\r\n  text-align: center;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  font-weight: 500;\r\n  color: #666;\r\n  border-radius: 6px;\r\n}\r\n\r\n.billing-tab:hover {\r\n  color: #2196f3;\r\n  background-color: rgba(99, 102, 241, 0.05);\r\n}\r\n\r\n.billing-tab.active {\r\n  color: #fff;\r\n  background-color: #2196f3;\r\n  font-weight: 600;\r\n  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);\r\n}\r\n\r\n.server-card {\r\n  border: 1px solid #e8e8e8;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  margin-bottom: 20px;\r\n  transition: all 0.3s;\r\n  cursor: pointer;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.server-card:hover {\r\n  border-color: #2196f3;\r\n  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.15);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.server-card-header {\r\n  padding: 16px;\r\n  background-color: #f9f9f9;\r\n  display: flex;\r\n  align-items: center;\r\n  border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.server-radio {\r\n  margin-right: 12px;\r\n  width: 18px;\r\n  height: 18px;\r\n  accent-color: #2196f3;\r\n}\r\n\r\n.server-name {\r\n  font-weight: 600;\r\n  font-size: 15px;\r\n  color: #333;\r\n}\r\n\r\n.server-card-body {\r\n  padding: 16px;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 16px;\r\n  background-color: #fff;\r\n}\r\n\r\n.server-spec {\r\n  display: flex;\r\n  align-items: center;\r\n  flex: 1 0 18%;\r\n  min-width: 120px;\r\n}\r\n\r\n.spec-label {\r\n  color: #666;\r\n  margin-right: 8px;\r\n  font-size: 14px;\r\n}\r\n\r\n.spec-value {\r\n  font-weight: 500;\r\n  color: #333;\r\n  font-size: 14px;\r\n}\r\n\r\n.server-card-footer {\r\n  padding: 12px 16px;\r\n  height: 6vh;\r\n  background-color: #f9f9f9;\r\n  border-top: 1px solid #e8e8e8;\r\n  display: flex;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.server-price {\r\n  font-weight: 600;\r\n  color: #f43f5e;\r\n  font-size: 16px;\r\n}\r\n\r\n.specs-example {\r\n  background-color: #f9f9f9;\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.specs-example-table {\r\n  width: 100%;\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n  border: 1px solid #eaeaea;\r\n}\r\n\r\n.specs-example-row {\r\n  display: flex;\r\n}\r\n\r\n.specs-example-row:first-child {\r\n  background-color: #f3f4f6;\r\n  font-weight: 500;\r\n}\r\n\r\n.specs-example-row:last-child {\r\n  background-color: #fff;\r\n}\r\n\r\n.specs-example-cell {\r\n  padding: 10px 12px;\r\n  flex: 1;\r\n  border-right: 1px solid #eaeaea;\r\n  font-size: 13px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  text-align: center;\r\n}\r\n\r\n.specs-example-cell:last-child {\r\n  border-right: none;\r\n}\r\n\r\n.rental-duration {\r\n  margin-top: 2vh;\r\n  display: flex;\r\n  margin-left: 2vh;\r\n  align-items: center;\r\n  margin-bottom: -2vh;\r\n  flex-wrap: wrap;\r\n  gap: 10px;\r\n}\r\n\r\n.duration-label {\r\n  font-weight: 500;\r\n  color: #333;\r\n  margin-right: 12px;\r\n}\r\n\r\n.duration-selector {\r\n  position: relative;\r\n}\r\n\r\n.duration-select {\r\n  padding: 10px 32px 10px 12px;\r\n  border: 1px solid #ddd;\r\n  border-radius: 6px;\r\n  appearance: none;\r\n  background-color: white;\r\n  font-size: 14px;\r\n  color: #333;\r\n  cursor: pointer;\r\n  transition: all 0.2s;\r\n  min-width: 120px;\r\n}\r\n\r\n.duration-select:hover {\r\n  border-color: #2196f3;\r\n}\r\n\r\n.duration-select:focus {\r\n  outline: none;\r\n  border-color: #2196f3;\r\n  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.15);\r\n}\r\n\r\n.duration-selector::after {\r\n  content: \"▼\";\r\n  position: absolute;\r\n  right: 12px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  color: #666;\r\n  pointer-events: none;\r\n  font-size: 10px;\r\n}\r\n\r\n.duration-hint {\r\n  font-size: 12px;\r\n  color: #666;\r\n  margin-left: 8px;\r\n}\r\n\r\n.price-summary {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-top: 24px;\r\n  padding: 16px;\r\n  background-color: #f9f9f9;\r\n  border-radius: 8px;\r\n}\r\n\r\n.price-label {\r\n  font-weight: 500;\r\n  color: #333;\r\n  height: 4vh;\r\n  margin-right: 8px;\r\n}\r\n\r\n.price-value {\r\n  font-weight: 700;\r\n  color: #f43f5e;\r\n  font-size: 18px;\r\n  margin-right: auto;\r\n}\r\n\r\n.details-link {\r\n  color: #2196f3;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  text-decoration: underline;\r\n}\r\n\r\n.details-link:hover {\r\n  color: #2196f3;\r\n}\r\n\r\n.modal-footer {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  padding: 16px 24px;\r\n  border-top: 1px solid #f0f0f0;\r\n  gap: 12px;\r\n}\r\n\r\n.cancel-button {\r\n  padding: 10px 20px;\r\n  border: 1px solid #ddd;\r\n  background-color: white;\r\n  color: #666;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  transition: all 0.2s;\r\n}\r\n\r\n.cancel-button:hover {\r\n  background-color: #f3f4f6;\r\n  border-color: #ccc;\r\n}\r\n\r\n.confirm-button {\r\n  padding: 10px 24px;\r\n  border: none;\r\n  background-color: #2196f3;\r\n  color: white;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  transition: all 0.2s;\r\n  box-shadow: 0 2px 4px rgba(99, 102, 241, 0.2);\r\n}\r\n\r\n.confirm-button:hover {\r\n  background-color: #2196f3;\r\n  box-shadow: 0 4px 8px rgba(99, 102, 241, 0.3);\r\n}\r\n\r\n/* 二次确认弹窗样式 */\r\n.confirm-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 2000;\r\n}\r\n\r\n.confirm-dialog {\r\n  background-color: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\r\n  width: 400px;\r\n  max-width: 95%;\r\n  overflow: hidden;\r\n}\r\n\r\n.confirm-header {\r\n  padding: 16px 24px;\r\n  background-color: #f7f7f9;\r\n  border-bottom: 1px solid #eee;\r\n}\r\n\r\n.confirm-header h3 {\r\n  margin: 0;\r\n  font-size: 16px;\r\n  color: #333;\r\n}\r\n\r\n.confirm-body {\r\n  padding: 20px 24px;\r\n}\r\n\r\n.confirm-body p {\r\n  margin: 0 0 16px 0;\r\n  font-size: 14px;\r\n  color: #333;\r\n}\r\n\r\n.confirm-details {\r\n  background-color: #f9f9f9;\r\n  padding: 12px;\r\n  border-radius: 6px;\r\n  font-size: 13px;\r\n  line-height: 1.6;\r\n}\r\n\r\n.confirm-details div {\r\n  margin-bottom: 6px;\r\n}\r\n\r\n.confirm-details div:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.confirm-footer {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  padding: 16px 24px;\r\n  border-top: 1px solid #eee;\r\n  gap: 12px;\r\n}\r\n\r\n.confirm-cancel {\r\n  padding: 8px 16px;\r\n  border: 1px solid #ddd;\r\n  background-color: white;\r\n  color: #666;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  transition: all 0.2s;\r\n}\r\n\r\n.confirm-cancel:hover {\r\n  background-color: #f3f4f6;\r\n  border-color: #ccc;\r\n}\r\n\r\n.confirm-ok {\r\n  padding: 8px 16px;\r\n  border: none;\r\n  background-color: #2196f3;\r\n  color: white;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  transition: all 0.2s;\r\n}\r\n\r\n.confirm-ok:hover {\r\n  background-color: #2196f3;\r\n}\r\n</style>\r\n\r\n<template>\r\n  <div>\r\n    <div class=\"modal-overlay\" v-if=\"visible\">\r\n      <div class=\"modal-container\">\r\n        <div class=\"modal-header1\">\r\n          <h2>订单确认</h2>\r\n        </div>\r\n\r\n        <div class=\"modal-body\">\r\n          <!-- 计费方式 -->\r\n          <div class=\"section-title\">计费方式</div>\r\n          <div class=\"billing-tabs\">\r\n            <div\r\n                v-for=\"(option, index) in billingOptions\"\r\n                :key=\"index\"\r\n                class=\"billing-tab\"\r\n                :class=\"{ 'active': selectedBillingMethod === option.value }\"\r\n                @click=\"selectedBillingMethod = option.value\"\r\n            >\r\n              {{ option.label }}\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"section-title\">选择主机</div>\r\n          <div class=\"specs-example-table\">\r\n            <div class=\"specs-example-row\">\r\n              <div class=\"specs-example-cell\">GPU型号</div>\r\n              <div class=\"specs-example-cell\">GPU</div>\r\n              <div class=\"specs-example-cell\">显存</div>\r\n              <div class=\"specs-example-cell\">vCPU</div>\r\n              <div class=\"specs-example-cell\">内存</div>\r\n              <div class=\"specs-example-cell\">系统盘</div>\r\n            </div>\r\n            <div class=\"specs-example-row\">\r\n              <div class=\"specs-example-cell\">{{ server.name }}</div>\r\n              <div class=\"specs-example-cell\">{{ server.graphicsCardNumber }}卡</div>\r\n              <div class=\"specs-example-cell\">{{ server.videoMemory }}GB</div>\r\n              <div class=\"specs-example-cell\">{{ server.gpuNuclearNumber }}核</div>\r\n              <div class=\"specs-example-cell\">{{ server.internalMemory }}GB</div>\r\n              <div class=\"specs-example-cell\">{{ server.systemDisk }}GB</div>\r\n            </div>\r\n            <div class=\"server-card-footer\">\r\n              <div class=\"server-price\">¥ {{ server[selectedBillingMethod] }}<span class=\"spec-label\"> {{ priceUnit }}</span></div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 服务器配置规格展示 -->\r\n          <div class=\"section-title\">实例规格</div>\r\n          <div class=\"specs-example-table\">\r\n            <div class=\"specs-example-row\">\r\n              <div class=\"specs-example-cell\">GPU型号</div>\r\n              <div class=\"specs-example-cell\">vCPU</div>\r\n              <div class=\"specs-example-cell\">内存</div>\r\n              <div class=\"specs-example-cell\">系统盘</div>\r\n              <div class=\"specs-example-cell\">数据盘</div>\r\n            </div>\r\n            <div class=\"specs-example-row\">\r\n              <div class=\"specs-example-cell\">{{ server.name }}</div>\r\n              <div class=\"specs-example-cell\">{{ server.gpuNuclearNumber }}核心</div>\r\n              <div class=\"specs-example-cell\">{{ server.internalMemory }}GB</div>\r\n              <div class=\"specs-example-cell\">{{ server.systemDisk }}GB</div>\r\n              <div class=\"specs-example-cell\">免费{{ server.dataDisk }}GB SSD</div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 租用时长 -->\r\n          <div class=\"rental-duration\">\r\n            <div class=\"duration-label\">租用时长：</div>\r\n            <div class=\"duration-selector\">\r\n              <select v-model=\"selectedDuration\"  class=\"duration-select\">\r\n                <option\r\n                    v-for=\"(option, index) in currentDurationOptions\"\r\n                    :key=\"index\"\r\n                    :value=\"option.value\"\r\n                >\r\n                  {{ option.label }}\r\n                </option>\r\n              </select>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 配置费用 -->\r\n          <div class=\"price-summary\">\r\n            <div class=\"price-label\">配置费用：</div>\r\n            <div class=\"price-value\">¥ {{ totalPrice }} 元 </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"modal-footer\">\r\n          <button class=\"cancel-button\" @click=\"closeModal\">取消</button>\r\n          <button\r\n              class=\"confirm-button\"\r\n              @click=\"showConfirmDialog\"\r\n          >\r\n            立即租赁\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 简化的二次确认弹窗 -->\r\n    <div class=\"confirm-overlay\" v-if=\"showConfirmation\">\r\n      <div class=\"confirm-dialog\">\r\n        <div class=\"confirm-message\">是否确认订单？</div>\r\n        <div class=\"confirm-footer\">\r\n          <button class=\"confirm-cancel\" @click=\"showConfirmation = false\">取消</button>\r\n          <button class=\"confirm-ok\" @click=\"confirmOrder\">确认</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'OrderDetail',\r\n  props: {\r\n    visible: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    server: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    selectedBillingMethod: {\r\n      type:String\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      defaultDiskSize: 50,\r\n      selectedBillingMethod: 'priceDay',\r\n      selectedDuration: 1,\r\n      needExtraDisk: false,\r\n      showConfirmation: false,\r\n      billingOptions: [\r\n        { label: '按量计费', value: 'priceHour', unit: '/小时' },\r\n        { label: '包日', value: 'priceDay', unit: '/日' },\r\n        { label: '包月', value: 'priceMouth', unit: '/月' },\r\n        { label: '包年', value: 'priceYear', unit: '/年' }\r\n      ],\r\n      durationOptionsHour: [\r\n        { label: '1小时', value: 1 },\r\n        { label: '2小时', value: 2 },\r\n        { label: '4小时', value: 4 },\r\n        { label: '8小时', value: 8 },\r\n        { label: '12小时', value: 12 },\r\n        { label: '24小时', value: 24 }\r\n      ],\r\n      durationOptionsDay: [\r\n        { label: '1天', value: 1 },\r\n        { label: '2天', value: 2 },\r\n        { label: '3天', value: 3 },\r\n        { label: '4天', value: 4 },\r\n        { label: '5天', value: 5 },\r\n        { label: '6天', value: 6 }\r\n      ],\r\n      durationOptionsWeek: [\r\n        { label: '1月', value: 1 },\r\n        { label: '2月', value: 2 },\r\n        { label: '3月', value: 3 },\r\n        { label: '4月', value: 4 },\r\n        { label: '5月', value: 5 },\r\n        { label: '6月', value: 6 },\r\n        { label: '7月', value: 7 },\r\n        { label: '8月', value: 8 }\r\n      ],\r\n      durationOptionsMonth: [\r\n        { label: '1年', value: 1 },\r\n        { label: '2年', value: 2 },\r\n        { label: '3年', value: 3 }\r\n      ]\r\n    };\r\n  },\r\n  computed: {\r\n    currentDurationOptions() {\r\n      switch (this.selectedBillingMethod) {\r\n        case 'priceHour':\r\n          this.totalPrice = this.server['priceHour']\r\n          return this.durationOptionsHour;\r\n        case 'priceDay':\r\n          this.totalPrice = this.server['priceDay']\r\n          return this.durationOptionsDay;\r\n        case 'priceMouth':\r\n          this.totalPrice = this.server['priceMouth']\r\n          return this.durationOptionsWeek;\r\n        case 'priceYear':\r\n          this.totalPrice = this.server['priceYear']\r\n          return this.durationOptionsMonth;\r\n        default:\r\n          return this.durationOptionsDay;\r\n      }\r\n    },\r\n    totalPrice() {\r\n      const price = this.server[this.selectedBillingMethod] || 0;\r\n      return (price * this.selectedDuration).toFixed(2);\r\n    },\r\n    priceUnit() {\r\n      switch (this.selectedBillingMethod) {\r\n        case 'priceHour': return '/小时';\r\n        case 'priceDay': return '/日';\r\n        case 'priceMouth': return '/月';\r\n        case 'priceYear': return '/年';\r\n        default: return '';\r\n      }\r\n    },\r\n    totalTime(){\r\n      switch (this.selectedBillingMethod) {\r\n        case 'priceHour':\r\n          return '小时';\r\n        case 'priceDay':\r\n          return '天'\r\n        case 'priceMouth':\r\n          return '月'\r\n        case 'priceYear':\r\n          return '年'\r\n        default:\r\n          return '小时'\r\n      }\r\n    },\r\n  },\r\n  watch: {\r\n    selectedBillingMethod() {\r\n      this.selectedDuration = this.currentDurationOptions[0]?.value || 1;\r\n    },\r\n    selectedDuration(){\r\n      this.totalPrice =this.server[this.selectedBillingMethod] * this.selectedDuration\r\n    },\r\n    selectedDuration:{\r\n      handler(newval){\r\n        this.$emit('time-updated',newval+this.totalTime)\r\n      },\r\n      immediate:true\r\n    },\r\n    totalPrice:{\r\n      handler(newval){\r\n        this.$emit('price-updated',newval);\r\n      },\r\n      immediate:true\r\n    },\r\n  },\r\n  methods: {\r\n    closeModal() {\r\n      this.$emit('close');\r\n    },\r\n    showConfirmDialog() {\r\n      this.showConfirmation = true;\r\n    },\r\n    confirmOrder() {\r\n      this.showConfirmation = false;\r\n      this.$emit(\"orderSubmitted\");\r\n\r\n      const order = {\r\n        serverId: this.server.id,\r\n        serverName: this.server.name,\r\n        billingMethod: this.selectedBillingMethod,\r\n        duration: this.selectedDuration,\r\n        needExtraDisk: this.needExtraDisk,\r\n        price: this.totalPrice,\r\n        specs: {\r\n          gpuModel: this.server.name,\r\n          vcpu: this.server.vcpu,\r\n          systemDisk: this.server.systemDisk,\r\n          cloudDisk: this.server.cloudDisk || 0,\r\n          memory: this.server.memory,\r\n          videoMemory: this.server.videoMemory\r\n        }\r\n      };\r\n\r\n      this.$emit('order-success', order);\r\n      this.closeModal();\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.modal-overlay {\r\n  /*margin-left: 200px;*/\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 1000;\r\n}\r\n\r\n.modal-container {\r\n  background-color: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\r\n  width: 1020px;\r\n  max-width: 95%;\r\n  max-height: 90vh;\r\n  overflow-y: auto;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.modal-header1 {\r\n  display: flex;\r\n  /*height: 5vh;*/\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 10px 24px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.modal-header1 h2 {\r\n  margin: 0;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.close-button {\r\n  background: none;\r\n  width: 30px;\r\n  height: 55px;\r\n  border: none;\r\n  font-size: 24px;\r\n  cursor: pointer;\r\n  color: #999;\r\n  transition: color 0.2s;\r\n}\r\n\r\n.close-button:hover {\r\n  color: #333;\r\n}\r\n\r\n.modal-body {\r\n  margin-top: -4vh;\r\n  padding: 20px 24px;\r\n  flex-grow: 1;\r\n}\r\n\r\n.section-title {\r\n  font-size: 15px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-top: 24px;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n/* 优化的计费方式选项卡样式 */\r\n.billing-tabs {\r\n  display: flex;\r\n  background-color: #f7f7f9;\r\n  border-radius: 8px;\r\n  font-size: 1.8vh;\r\n  padding: 4px;\r\n  width:50vh;\r\n  /*higth:10px;*/\r\n  margin-bottom: 16px;\r\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.billing-tab {\r\n  flex: 1;\r\n  padding: 12px 16px;\r\n  text-align: center;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  font-weight: 500;\r\n  color: #666;\r\n  border-radius: 6px;\r\n}\r\n\r\n.billing-tab:hover {\r\n  color: #2196f3;\r\n  background-color: rgba(99, 102, 241, 0.05);\r\n}\r\n\r\n.billing-tab.active {\r\n  color: #fff;\r\n  background-color: #2196f3;\r\n  font-weight: 600;\r\n  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);\r\n}\r\n\r\n.billing-description {\r\n  font-size: 12px;\r\n  color: #666;\r\n  margin-top: 8px;\r\n  margin-bottom: 20px;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* 服务器卡片样式 */\r\n.server-card {\r\n  border: 1px solid #e8e8e8;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  margin-bottom: 20px;\r\n  transition: all 0.3s;\r\n  cursor: pointer;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.server-card:hover {\r\n  border-color: #2196f3;\r\n  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.15);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.server-card-header {\r\n  padding: 16px;\r\n  background-color: #f9f9f9;\r\n  display: flex;\r\n  align-items: center;\r\n  border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.server-radio {\r\n  margin-right: 12px;\r\n  width: 18px;\r\n  height: 18px;\r\n  accent-color: #2196f3;\r\n}\r\n\r\n.server-name {\r\n  font-weight: 600;\r\n  font-size: 15px;\r\n  color: #333;\r\n}\r\n\r\n.server-card-body {\r\n  padding: 16px;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 16px;\r\n  background-color: #fff;\r\n}\r\n\r\n.server-spec {\r\n  display: flex;\r\n  align-items: center;\r\n  flex: 1 0 18%;\r\n  min-width: 120px;\r\n}\r\n\r\n.spec-label {\r\n  color: #666;\r\n  margin-right: 8px;\r\n  font-size: 14px;\r\n}\r\n\r\n.spec-value {\r\n  font-weight: 500;\r\n  color: #333;\r\n  font-size: 14px;\r\n}\r\n\r\n.server-card-footer {\r\n  padding: 12px 16px;\r\n  height: 6vh;\r\n  background-color: #f9f9f9;\r\n  border-top: 1px solid #e8e8e8;\r\n  display: flex;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.server-price {\r\n  font-weight: 600;\r\n  color: #f43f5e;\r\n  font-size: 16px;\r\n}\r\n\r\n/* 数据盘选择样式 */\r\n.disk-options {\r\n  display: flex;\r\n  gap: 24px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.disk-option {\r\n  display: flex;\r\n  align-items: center;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  color: #333;\r\n}\r\n\r\n.disk-option input[type=\"checkbox\"] {\r\n  margin-right: 8px;\r\n  width: 16px;\r\n  height: 16px;\r\n  accent-color: #2196f3;\r\n}\r\n\r\n/* 规格展示表格样式 */\r\n.specs-example {\r\n  background-color: #f9f9f9;\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.specs-example-title {\r\n  font-weight: 600;\r\n  margin-bottom: 12px;\r\n  color: #333;\r\n  font-size: 14px;\r\n}\r\n\r\n.specs-example-table {\r\n  width: 100%;\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n  border: 1px solid #eaeaea;\r\n}\r\n\r\n.specs-example-row {\r\n  display: flex;\r\n}\r\n\r\n.specs-example-row:first-child {\r\n  background-color: #f3f4f6;\r\n  font-weight: 500;\r\n}\r\n\r\n.specs-example-row:last-child {\r\n  background-color: #fff;\r\n}\r\n\r\n.specs-example-cell {\r\n  padding: 10px 12px;\r\n  flex: 1;\r\n  border-right: 1px solid #eaeaea;\r\n  font-size: 13px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  text-align: center;\r\n}\r\n\r\n.specs-example-cell:last-child {\r\n  border-right: none;\r\n}\r\n\r\n/* 租用时长样式 */\r\n.rental-duration {\r\n  margin-top: 2vh;\r\n  display: flex;\r\n  margin-left: 2vh;\r\n  align-items: center;\r\n  margin-bottom: -2vh;\r\n  /*margin-bottom: 24px;*/\r\n  flex-wrap: wrap;\r\n  gap: 10px;\r\n}\r\n\r\n.duration-label {\r\n  font-weight: 500;\r\n  color: #333;\r\n  margin-right: 12px;\r\n}\r\n\r\n.duration-selector {\r\n  position: relative;\r\n}\r\n\r\n.duration-select {\r\n  padding: 10px 32px 10px 12px;\r\n  border: 1px solid #ddd;\r\n  border-radius: 6px;\r\n  appearance: none;\r\n  background-color: white;\r\n  font-size: 14px;\r\n  color: #333;\r\n  cursor: pointer;\r\n  transition: all 0.2s;\r\n  min-width: 120px;\r\n}\r\n\r\n.duration-select:hover {\r\n  border-color: #2196f3;\r\n}\r\n\r\n.duration-select:focus {\r\n  outline: none;\r\n  border-color: #2196f3;\r\n  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.15);\r\n}\r\n\r\n.duration-selector::after {\r\n  content: \"▼\";\r\n  position: absolute;\r\n  right: 12px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  color: #666;\r\n  pointer-events: none;\r\n  font-size: 10px;\r\n}\r\n\r\n.duration-hint {\r\n  font-size: 12px;\r\n  color: #666;\r\n  margin-left: 8px;\r\n}\r\n\r\n/* 价格总结样式 */\r\n.price-summary {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-top: 24px;\r\n  padding: 16px;\r\n  background-color: #f9f9f9;\r\n  border-radius: 8px;\r\n}\r\n\r\n.price-label {\r\n  font-weight: 500;\r\n  color: #333;\r\n  height: 4vh;\r\n  margin-right: 8px;\r\n}\r\n\r\n.price-value {\r\n  font-weight: 700;\r\n  color: #f43f5e;\r\n  font-size: 18px;\r\n  margin-right: auto;\r\n}\r\n\r\n.details-link {\r\n  color: #2196f3;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  text-decoration: underline;\r\n}\r\n\r\n.details-link:hover {\r\n  color: #2196f3;\r\n}\r\n\r\n/* 底部按钮样式 */\r\n.modal-footer {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  padding: 16px 24px;\r\n  border-top: 1px solid #f0f0f0;\r\n  gap: 12px;\r\n}\r\n\r\n.cancel-button {\r\n  padding: 10px 20px;\r\n  border: 1px solid #ddd;\r\n  background-color: white;\r\n  color: #666;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  transition: all 0.2s;\r\n}\r\n\r\n.cancel-button:hover {\r\n  background-color: #f3f4f6;\r\n  border-color: #ccc;\r\n}\r\n\r\n.confirm-button {\r\n  padding: 10px 24px;\r\n  border: none;\r\n  background-color: #2196f3;\r\n  color: white;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  transition: all 0.2s;\r\n  box-shadow: 0 2px 4px rgba(99, 102, 241, 0.2);\r\n}\r\n\r\n.confirm-button:hover {\r\n  background-color: #2196f3;\r\n  box-shadow: 0 4px 8px rgba(99, 102, 241, 0.3);\r\n}\r\n\r\n/* 简化后的二次确认弹窗样式 */\r\n.confirm-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 2000;\r\n}\r\n\r\n.confirm-dialog {\r\n  background-color: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\r\n  width: 300px;\r\n  padding: 20px;\r\n}\r\n\r\n.confirm-message {\r\n  margin-bottom: 20px;\r\n  text-align: center;\r\n  font-size: 16px;\r\n}\r\n\r\n.confirm-footer {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 16px;\r\n}\r\n\r\n.confirm-cancel {\r\n  padding: 8px 16px;\r\n  border: 1px solid #ddd;\r\n  background-color: white;\r\n  color: #666;\r\n  width: 100px;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  transition: all 0.2s;\r\n}\r\n\r\n.confirm-cancel:hover {\r\n  background-color: #f3f4f6;\r\n  border-color: #ccc;\r\n}\r\n\r\n.confirm-ok {\r\n  padding: 8px 16px;\r\n  border: none;\r\n  background-color: #2196f3;\r\n  color: white;\r\n  border-radius: 4px;\r\n  width: 100px;\r\n\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  transition: all 0.2s;\r\n}\r\n\r\n.confirm-ok:hover {\r\n  background-color: #2196f3;\r\n}\r\n</style>", "import mod from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./OrderDetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./OrderDetail.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./OrderDetail.vue?vue&type=template&id=50b2ba60&scoped=true&\"\nimport script from \"./OrderDetail.vue?vue&type=script&lang=js&\"\nexport * from \"./OrderDetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./OrderDetail.vue?vue&type=style&index=0&id=50b2ba60&prod&scoped=true&lang=css&\"\nimport style1 from \"./OrderDetail.vue?vue&type=style&index=1&id=50b2ba60&prod&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"50b2ba60\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticStyle:{\"margin-left\":\"7%\"}},[(_vm.showComingSoon)?_c('SlideNotification',{attrs:{\"message\":_vm.notificationMessage,\"type\":\"warning\",\"duration\":2000},on:{\"close\":function($event){_vm.showComingSoon = false}}}):_vm._e(),_c('div',{staticStyle:{\"width\":\"95%\"}},[_c('div',{staticClass:\"compute-market\"},[_c('div',{staticClass:\"filter-section\"},[_c('div',{staticClass:\"filter-header\",on:{\"click\":_vm.toggleFilterVisibility}},[_c('span',{staticClass:\"filter-title\"},[_vm._v(\"筛选\")]),_c('i',{staticClass:\"filter-icon\",class:{ 'collapsed': !_vm.isFilterVisible }},[_vm._v(_vm._s(_vm.isFilterVisible ? '▼' : '►'))])]),_c('transition',{attrs:{\"name\":\"slide\"},on:{\"before-enter\":_vm.beforeEnter,\"enter\":_vm.enter,\"after-enter\":_vm.afterEnter,\"before-leave\":_vm.beforeLeave,\"leave\":_vm.leave,\"after-leave\":_vm.afterLeave}},[(_vm.isFilterVisible)?_c('div',{ref:\"filterContent\",staticClass:\"filter-content\"},[_c('div',{staticClass:\"filter-row\"},[_c('div',{staticClass:\"filter-label\"},[_vm._v(\"计费模式\")]),_c('div',{staticClass:\"filter-options checkbox-group\"},[_c('label',{staticClass:\"radio-item\"},[_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.showindex),expression:\"showindex\"}],attrs:{\"type\":\"radio\",\"value\":\"priceHour\",\"name\":\"billing\"},domProps:{\"checked\":_vm._q(_vm.showindex,\"priceHour\")},on:{\"change\":function($event){_vm.showindex=\"priceHour\"}}}),_c('span',{staticClass:\"radio-item\"}),_c('span',{staticClass:\"radio-text\"},[_vm._v(\"按量\")])]),_c('label',{staticClass:\"radio-item\"},[_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.showindex),expression:\"showindex\"}],attrs:{\"type\":\"radio\",\"value\":\"priceDay\",\"name\":\"billing\"},domProps:{\"checked\":_vm._q(_vm.showindex,\"priceDay\")},on:{\"change\":function($event){_vm.showindex=\"priceDay\"}}}),_c('span',{staticClass:\"radio-item\"}),_c('span',{staticClass:\"radio-text\"},[_vm._v(\"包日\")])]),_c('label',{staticClass:\"radio-item\"},[_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.showindex),expression:\"showindex\"}],attrs:{\"type\":\"radio\",\"value\":\"priceMouth\",\"name\":\"billing\"},domProps:{\"checked\":_vm._q(_vm.showindex,\"priceMouth\")},on:{\"change\":function($event){_vm.showindex=\"priceMouth\"}}}),_c('span',{staticClass:\"radio-item\"}),_c('span',{staticClass:\"radio-text\"},[_vm._v(\"包月\")])]),_c('label',{staticClass:\"radio-item\"},[_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.showindex),expression:\"showindex\"}],attrs:{\"type\":\"radio\",\"value\":\"priceYear\",\"name\":\"billing\"},domProps:{\"checked\":_vm._q(_vm.showindex,\"priceYear\")},on:{\"change\":function($event){_vm.showindex=\"priceYear\"}}}),_c('span',{staticClass:\"radio-item\"}),_c('span',{staticClass:\"radio-text\"},[_vm._v(\"包年\")])])])]),_c('div',{staticClass:\"filter-row\"},[_c('div',{staticClass:\"filter-label\"},[_vm._v(\"选择可用区\")]),_c('div',{staticClass:\"filter-options checkbox-group\"},[_c('label',{staticClass:\"checkbox-item\"},[_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.filters.allRegions),expression:\"filters.allRegions\"}],attrs:{\"type\":\"checkbox\"},domProps:{\"checked\":Array.isArray(_vm.filters.allRegions)?_vm._i(_vm.filters.allRegions,null)>-1:(_vm.filters.allRegions)},on:{\"change\":[function($event){var $$a=_vm.filters.allRegions,$$el=$event.target,$$c=$$el.checked?(true):(false);if(Array.isArray($$a)){var $$v=null,$$i=_vm._i($$a,$$v);if($$el.checked){$$i<0&&(_vm.$set(_vm.filters, \"allRegions\", $$a.concat([$$v])))}else{$$i>-1&&(_vm.$set(_vm.filters, \"allRegions\", $$a.slice(0,$$i).concat($$a.slice($$i+1))))}}else{_vm.$set(_vm.filters, \"allRegions\", $$c)}},_vm.toggleAllRegions]}}),_c('span',{staticClass:\"checkbox-text\"},[_vm._v(\"全选\")])]),_vm._l((_vm.regions),function(region){return _c('label',{key:region.id,staticClass:\"checkbox-item\"},[_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.filters.selectedRegions),expression:\"filters.selectedRegions\"}],attrs:{\"type\":\"checkbox\"},domProps:{\"value\":region.id,\"checked\":Array.isArray(_vm.filters.selectedRegions)?_vm._i(_vm.filters.selectedRegions,region.id)>-1:(_vm.filters.selectedRegions)},on:{\"change\":[function($event){var $$a=_vm.filters.selectedRegions,$$el=$event.target,$$c=$$el.checked?(true):(false);if(Array.isArray($$a)){var $$v=region.id,$$i=_vm._i($$a,$$v);if($$el.checked){$$i<0&&(_vm.$set(_vm.filters, \"selectedRegions\", $$a.concat([$$v])))}else{$$i>-1&&(_vm.$set(_vm.filters, \"selectedRegions\", $$a.slice(0,$$i).concat($$a.slice($$i+1))))}}else{_vm.$set(_vm.filters, \"selectedRegions\", $$c)}},_vm.updateFilters]}}),_c('span',{staticClass:\"checkbox-text\"},[_vm._v(_vm._s(region.name))])])})],2)]),_c('div',{staticClass:\"filter-row\"},[_c('div',{staticClass:\"filter-label\"},[_vm._v(\"GPU型号\")]),_c('div',{staticClass:\"filter-options\"},[_c('div',{staticClass:\"gpu-brand\"},[_vm._v(\"NVIDIA\")]),(_vm.availableGpuModels.length > 0)?_c('div',{staticClass:\"checkbox-group gpu-list\"},[_c('label',{staticClass:\"checkbox-item\"},[_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.filters.allGpuModels),expression:\"filters.allGpuModels\"}],attrs:{\"type\":\"checkbox\"},domProps:{\"checked\":Array.isArray(_vm.filters.allGpuModels)?_vm._i(_vm.filters.allGpuModels,null)>-1:(_vm.filters.allGpuModels)},on:{\"change\":[function($event){var $$a=_vm.filters.allGpuModels,$$el=$event.target,$$c=$$el.checked?(true):(false);if(Array.isArray($$a)){var $$v=null,$$i=_vm._i($$a,$$v);if($$el.checked){$$i<0&&(_vm.$set(_vm.filters, \"allGpuModels\", $$a.concat([$$v])))}else{$$i>-1&&(_vm.$set(_vm.filters, \"allGpuModels\", $$a.slice(0,$$i).concat($$a.slice($$i+1))))}}else{_vm.$set(_vm.filters, \"allGpuModels\", $$c)}},_vm.toggleAllGpuModels]}}),_c('span',{staticClass:\"checkbox-text\"},[_vm._v(\"全选\")])]),_vm._l((_vm.availableGpuModels),function(gpu){return _c('label',{key:gpu.id,staticClass:\"checkbox-item\"},[_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.filters.selectedGpuModels),expression:\"filters.selectedGpuModels\"}],attrs:{\"type\":\"checkbox\"},domProps:{\"value\":gpu.id,\"checked\":Array.isArray(_vm.filters.selectedGpuModels)?_vm._i(_vm.filters.selectedGpuModels,gpu.id)>-1:(_vm.filters.selectedGpuModels)},on:{\"change\":[function($event){var $$a=_vm.filters.selectedGpuModels,$$el=$event.target,$$c=$$el.checked?(true):(false);if(Array.isArray($$a)){var $$v=gpu.id,$$i=_vm._i($$a,$$v);if($$el.checked){$$i<0&&(_vm.$set(_vm.filters, \"selectedGpuModels\", $$a.concat([$$v])))}else{$$i>-1&&(_vm.$set(_vm.filters, \"selectedGpuModels\", $$a.slice(0,$$i).concat($$a.slice($$i+1))))}}else{_vm.$set(_vm.filters, \"selectedGpuModels\", $$c)}},_vm.updateFilters]}}),_c('span',{staticClass:\"checkbox-text\"},[_vm._v(_vm._s(gpu.name))])])})],2):_c('div',{staticClass:\"no-options-message\"},[_vm._v(\" 当前筛选条件下无可用GPU型号 \")])])]),_c('div',{staticClass:\"filter-row\"},[_c('div',{staticClass:\"filter-label\"},[_vm._v(\"使用场景\")]),_c('div',{staticClass:\"filter-options checkbox-group\"},[_c('label',{staticClass:\"checkbox-item\"},[_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.filters.usageScenarios.development),expression:\"filters.usageScenarios.development\"}],attrs:{\"type\":\"checkbox\"},domProps:{\"checked\":Array.isArray(_vm.filters.usageScenarios.development)?_vm._i(_vm.filters.usageScenarios.development,null)>-1:(_vm.filters.usageScenarios.development)},on:{\"change\":function($event){var $$a=_vm.filters.usageScenarios.development,$$el=$event.target,$$c=$$el.checked?(true):(false);if(Array.isArray($$a)){var $$v=null,$$i=_vm._i($$a,$$v);if($$el.checked){$$i<0&&(_vm.$set(_vm.filters.usageScenarios, \"development\", $$a.concat([$$v])))}else{$$i>-1&&(_vm.$set(_vm.filters.usageScenarios, \"development\", $$a.slice(0,$$i).concat($$a.slice($$i+1))))}}else{_vm.$set(_vm.filters.usageScenarios, \"development\", $$c)}}}}),_c('span',{staticClass:\"checkbox-text\"},[_vm._v(\"开发机\")])])])])]):_vm._e()])],1),(_vm.filteredServers.length > 0)?_c('div',{staticClass:\"servers-grid\"},_vm._l((_vm.filteredServers),function(server){return _c('div',{key:server.id,staticClass:\"server-card\",class:{ 'server-card-hovered': _vm.hoveredServer === server.id },on:{\"mouseenter\":function($event){_vm.hoveredServer = server.id},\"mouseleave\":function($event){_vm.hoveredServer = null}}},[_c('div',{staticClass:\"region-tag\"},[_vm._v(_vm._s(_vm.getRegionName(server.region)))]),_c('div',{staticClass:\"server-title\"},[_vm._v(\" \"+_vm._s(server.name)+\" \")]),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.showindex === 'priceHour'),expression:\"showindex === 'priceHour'\"}],staticClass:\"server-price-section\"},[_c('div',{staticClass:\"price\"},[_c('span',{staticClass:\"currency\"},[_vm._v(\"¥\")]),_c('span',{staticClass:\"amount\"},[_vm._v(_vm._s(server.priceHour))]),_c('span',{staticClass:\"unit\"},[_vm._v(\"/小时\")])]),_c('div',{staticClass:\"server-status\",class:_vm.getServerStatusClass(server.inventoryNumber)},[_vm._v(\" \"+_vm._s(_vm.getServerStatusText(server.inventoryNumber))+\" \")])]),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.showindex === 'priceDay'),expression:\"showindex === 'priceDay'\"}],staticClass:\"server-price-section\"},[_c('div',{staticClass:\"price\"},[_c('span',{staticClass:\"currency\"},[_vm._v(\"¥\")]),_c('span',{staticClass:\"amount\"},[_vm._v(_vm._s(server.priceDay))]),_c('span',{staticClass:\"unit\"},[_vm._v(\"/天\")])]),_c('div',{staticClass:\"server-status\",class:_vm.getServerStatusClass(server.inventoryNumber)},[_vm._v(\" \"+_vm._s(_vm.getServerStatusText(server.inventoryNumber))+\" \")])]),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.showindex === 'priceMouth'),expression:\"showindex === 'priceMouth'\"}],staticClass:\"server-price-section\"},[_c('div',{staticClass:\"price\"},[_c('span',{staticClass:\"currency\"},[_vm._v(\"¥\")]),_c('span',{staticClass:\"amount\"},[_vm._v(_vm._s(server.priceMouth))]),_c('span',{staticClass:\"unit\"},[_vm._v(\"/月\")])]),_c('div',{staticClass:\"server-status\",class:_vm.getServerStatusClass(server.inventoryNumber)},[_vm._v(\" \"+_vm._s(_vm.getServerStatusText(server.inventoryNumber))+\" \")])]),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.showindex === 'priceYear'),expression:\"showindex === 'priceYear'\"}],staticClass:\"server-price-section\"},[_c('div',{staticClass:\"price\"},[_c('span',{staticClass:\"currency\"},[_vm._v(\"¥\")]),_c('span',{staticClass:\"amount\"},[_vm._v(_vm._s(server.priceYear))]),_c('span',{staticClass:\"unit\"},[_vm._v(\"/年\")])]),_c('div',{staticClass:\"server-status\",class:_vm.getServerStatusClass(server.inventoryNumber)},[_vm._v(\" \"+_vm._s(_vm.getServerStatusText(server.inventoryNumber))+\" \")])]),_c('div',{staticClass:\"server-specs\"},[_c('div',{staticClass:\"specs-grid\"},[_c('div',{staticClass:\"spec-item\"},[_c('div',{staticClass:\"spec-label\"},[_vm._v(\"显卡数量\")]),_c('div',{staticClass:\"spec-value\"},[_vm._v(_vm._s(server.graphicsCardNumber))])]),_c('div',{staticClass:\"spec-item\"},[_c('div',{staticClass:\"spec-label\"},[_vm._v(\"显存(GB)\")]),_c('div',{staticClass:\"spec-value\"},[_vm._v(_vm._s(server.videoMemory))])]),_c('div',{staticClass:\"spec-item\"},[_c('div',{staticClass:\"spec-label\"},[_vm._v(\"VCPU核数\")]),_c('div',{staticClass:\"spec-value\"},[_vm._v(_vm._s(server.gpuNuclearNumber))])]),_c('div',{staticClass:\"spec-item\"},[_c('div',{staticClass:\"spec-label\"},[_vm._v(\"系统盘(GB)\")]),_c('div',{staticClass:\"spec-value\"},[_vm._v(_vm._s(server.systemDisk))])]),_c('div',{staticClass:\"spec-item\"},[_c('div',{staticClass:\"spec-label\"},[_vm._v(\"云盘(GB)\")]),_c('div',{staticClass:\"spec-value\"},[_vm._v(_vm._s(server.cloudDisk || '-'))])]),_c('div',{staticClass:\"spec-item\"},[_c('div',{staticClass:\"spec-label\"},[_vm._v(\"内存(GB)\")]),_c('div',{staticClass:\"spec-value\"},[_vm._v(_vm._s(server.internalMemory))])])])]),_c('button',{staticClass:\"buy-button\",class:{\n    'disabled': server.inventoryNumber === 0,\n    'buy-button-hovered': _vm.hoveredServer === server.id\n  },on:{\"click\":function($event){server.inventoryNumber > 0 ? _vm.directToConsole() : null}}},[_vm._v(\" 立即租赁 \")])])}),0):_c('div',{staticClass:\"empty-state\"},[_c('div',{staticClass:\"empty-state-icon\"}),_c('div',{staticClass:\"empty-state-text\"},[_vm._v(\"暂无数据\")])])])]),_c('order-detail',{attrs:{\"visible\":_vm.showDetail,\"server\":_vm.serverss,\"selectedBillingMethod\":_vm.selectedBillingMethod},on:{\"orderSubmitted\":function($event){return _vm.buyGpu(_vm.serverss)},\"price-updated\":_vm.orderPirce,\"time-updated\":_vm.orderTimes,\"close\":_vm.closeOrderDetail}}),_c('chatAi')],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div style=\"margin-left: 7%\">\r\n    <SlideNotification\r\n        v-if=\"showComingSoon\"\r\n        :message=\"notificationMessage\"\r\n        type=\"warning\"\r\n        :duration=\"2000\"\r\n        @close=\"showComingSoon = false\"\r\n    />\r\n    <div style=\"width: 95%\">\r\n      <!-- 导航占位符 -->\r\n      <div class=\"compute-market\">\r\n        <!-- 筛选条件区域 -->\r\n        <div class=\"filter-section\">\r\n          <div class=\"filter-header\" @click=\"toggleFilterVisibility\">\r\n            <span class=\"filter-title\">筛选</span>\r\n            <i class=\"filter-icon\" :class=\"{ 'collapsed': !isFilterVisible }\">{{ isFilterVisible ? '▼' : '►' }}</i>\r\n          </div>\r\n\r\n          <!-- 改进的过渡动画 -->\r\n          <transition\r\n              name=\"slide\"\r\n              @before-enter=\"beforeEnter\"\r\n              @enter=\"enter\"\r\n              @after-enter=\"afterEnter\"\r\n              @before-leave=\"beforeLeave\"\r\n              @leave=\"leave\"\r\n              @after-leave=\"afterLeave\">\r\n            <div v-if=\"isFilterVisible\" class=\"filter-content\" ref=\"filterContent\">\r\n              <!-- 内容保持不变 -->\r\n              <!-- 计费模式 - 改为单选按钮 -->\r\n              <div class=\"filter-row\">\r\n                <div class=\"filter-label\">计费模式</div>\r\n                <div class=\"filter-options checkbox-group\">\r\n                  <label class=\"radio-item\">\r\n                    <input type=\"radio\" v-model=\"showindex\" value=\"priceHour\" name=\"billing\">\r\n                    <span class=\"radio-item\"></span>\r\n                    <span class=\"radio-text\">按量</span>\r\n                  </label>\r\n                  <label class=\"radio-item\">\r\n                    <input type=\"radio\" v-model=\"showindex\" value=\"priceDay\" name=\"billing\">\r\n                    <span class=\"radio-item\"></span>\r\n                    <span class=\"radio-text\">包日</span>\r\n                  </label>\r\n                  <label class=\"radio-item\">\r\n                    <input type=\"radio\" v-model=\"showindex\" value=\"priceMouth\" name=\"billing\">\r\n                    <span class=\"radio-item\"></span>\r\n                    <span class=\"radio-text\">包月</span>\r\n                  </label>\r\n                  <label class=\"radio-item\">\r\n                    <input type=\"radio\" v-model=\"showindex\" value=\"priceYear\" name=\"billing\">\r\n                    <span class=\"radio-item\"></span>\r\n                    <span class=\"radio-text\">包年</span>\r\n                  </label>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 可用区筛选 -->\r\n              <div class=\"filter-row\">\r\n                <div class=\"filter-label\">选择可用区</div>\r\n                <div class=\"filter-options checkbox-group\">\r\n                  <label class=\"checkbox-item\">\r\n                    <input type=\"checkbox\" v-model=\"filters.allRegions\" @change=\"toggleAllRegions\">\r\n                    <span class=\"checkbox-text\">全选</span>\r\n                  </label>\r\n                  <label class=\"checkbox-item\" v-for=\"region in regions\" :key=\"region.id\">\r\n                    <input type=\"checkbox\" v-model=\"filters.selectedRegions\" :value=\"region.id\" @change=\"updateFilters\">\r\n                    <span class=\"checkbox-text\">{{ region.name }}</span>\r\n                  </label>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- GPU型号 -->\r\n              <div class=\"filter-row\">\r\n                <div class=\"filter-label\">GPU型号</div>\r\n                <div class=\"filter-options\">\r\n                  <div class=\"gpu-brand\">NVIDIA</div>\r\n                  <div v-if=\"availableGpuModels.length > 0\" class=\"checkbox-group gpu-list\">\r\n                    <label class=\"checkbox-item\">\r\n                      <input type=\"checkbox\" v-model=\"filters.allGpuModels\" @change=\"toggleAllGpuModels\">\r\n                      <span class=\"checkbox-text\">全选</span>\r\n                    </label>\r\n                    <label class=\"checkbox-item\" v-for=\"gpu in availableGpuModels\" :key=\"gpu.id\">\r\n                      <input type=\"checkbox\" v-model=\"filters.selectedGpuModels\" :value=\"gpu.id\" @change=\"updateFilters\">\r\n                      <span class=\"checkbox-text\">{{ gpu.name }}</span>\r\n                    </label>\r\n                  </div>\r\n                  <div v-else class=\"no-options-message\">\r\n                    当前筛选条件下无可用GPU型号\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 使用场景 -->\r\n              <div class=\"filter-row\">\r\n                <div class=\"filter-label\">使用场景</div>\r\n                <div class=\"filter-options checkbox-group\">\r\n                  <label class=\"checkbox-item\">\r\n                    <input type=\"checkbox\" v-model=\"filters.usageScenarios.development\">\r\n                    <span class=\"checkbox-text\">开发机</span>\r\n                  </label>\r\n                  <!-- 更多场景可以在这里添加 -->\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </transition>\r\n        </div>\r\n\r\n        <!-- 服务器列表 -->\r\n        <div v-if=\"filteredServers.length > 0\" class=\"servers-grid\">\r\n          <div\r\n              class=\"server-card\"\r\n              v-for=\"server in filteredServers\"\r\n              :key=\"server.id\"\r\n              @mouseenter=\"hoveredServer = server.id\"\r\n              @mouseleave=\"hoveredServer = null\"\r\n              :class=\"{ 'server-card-hovered': hoveredServer === server.id }\"\r\n          >\r\n            <!-- 区域标识放在右上角 -->\r\n            <div class=\"region-tag\">{{ getRegionName(server.region) }}</div>\r\n\r\n            <!-- 服务器标题 -->\r\n            <div class=\"server-title\">\r\n              {{ server.name }}\r\n            </div>\r\n\r\n            <!-- 价格和状态 -->\r\n            <div class=\"server-price-section\" v-show=\"showindex === 'priceHour'\">\r\n              <div class=\"price\">\r\n                <span class=\"currency\">¥</span>\r\n                <span class=\"amount\">{{ server.priceHour }}</span>\r\n                <span class=\"unit\">/小时</span>\r\n              </div>\r\n              <!-- 使用动态生成的状态文本和CSS类 -->\r\n              <div class=\"server-status\" :class=\"getServerStatusClass(server.inventoryNumber)\">\r\n                {{ getServerStatusText(server.inventoryNumber) }}\r\n              </div>\r\n            </div>\r\n            <div class=\"server-price-section\" v-show=\"showindex === 'priceDay'\">\r\n              <div class=\"price\">\r\n                <span class=\"currency\">¥</span>\r\n                <span class=\"amount\">{{ server.priceDay }}</span>\r\n                <span class=\"unit\">/天</span>\r\n              </div>\r\n              <!-- 使用动态生成的状态文本和CSS类 -->\r\n              <div class=\"server-status\" :class=\"getServerStatusClass(server.inventoryNumber)\">\r\n                {{ getServerStatusText(server.inventoryNumber) }}\r\n              </div>\r\n            </div>\r\n            <div class=\"server-price-section\" v-show=\"showindex === 'priceMouth'\">\r\n              <div class=\"price\">\r\n                <span class=\"currency\">¥</span>\r\n                <span class=\"amount\">{{ server.priceMouth }}</span>\r\n                <span class=\"unit\">/月</span>\r\n              </div>\r\n              <!-- 使用动态生成的状态文本和CSS类 -->\r\n              <div class=\"server-status\" :class=\"getServerStatusClass(server.inventoryNumber)\">\r\n                {{ getServerStatusText(server.inventoryNumber) }}\r\n              </div>\r\n            </div>\r\n            <div class=\"server-price-section\" v-show=\"showindex === 'priceYear'\">\r\n              <div class=\"price\">\r\n                <span class=\"currency\">¥</span>\r\n                <span class=\"amount\">{{ server.priceYear }}</span>\r\n                <span class=\"unit\">/年</span>\r\n              </div>\r\n              <!-- 使用动态生成的状态文本和CSS类 -->\r\n              <div class=\"server-status\" :class=\"getServerStatusClass(server.inventoryNumber)\">\r\n                {{ getServerStatusText(server.inventoryNumber) }}\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 服务器规格 - 修改为垂直对齐的布局 -->\r\n            <div class=\"server-specs\">\r\n              <div class=\"specs-grid\">\r\n                <div class=\"spec-item\">\r\n                  <div class=\"spec-label\">显卡数量</div>\r\n                  <div class=\"spec-value\">{{ server.graphicsCardNumber }}</div>\r\n                </div>\r\n\r\n                <div class=\"spec-item\">\r\n                  <div class=\"spec-label\">显存(GB)</div>\r\n                  <div class=\"spec-value\">{{ server.videoMemory }}</div>\r\n                </div>\r\n\r\n                <div class=\"spec-item\">\r\n                  <div class=\"spec-label\">VCPU核数</div>\r\n                  <div class=\"spec-value\">{{ server.gpuNuclearNumber }}</div>\r\n                </div>\r\n\r\n                <div class=\"spec-item\">\r\n                  <div class=\"spec-label\">系统盘(GB)</div>\r\n                  <div class=\"spec-value\">{{ server.systemDisk }}</div>\r\n                </div>\r\n\r\n                <div class=\"spec-item\">\r\n                  <div class=\"spec-label\">云盘(GB)</div>\r\n                  <div class=\"spec-value\">{{ server.cloudDisk || '-' }}</div>\r\n                </div>\r\n\r\n                <div class=\"spec-item\">\r\n                  <div class=\"spec-label\">内存(GB)</div>\r\n                  <div class=\"spec-value\">{{ server.internalMemory }}</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 购买按钮状态根据服务器状态动态调整 -->\r\n            <!--            <button-->\r\n            <!--                class=\"buy-button\"-->\r\n            <!--                :class=\"{-->\r\n            <!--      'disabled': server.inventoryNumber === 0,-->\r\n            <!--      'buy-button-hovered': hoveredServer === server.id-->\r\n            <!--    }\"-->\r\n            <!--                @click=\"server.inventoryNumber > 0 ? orderOK(server) : null\"-->\r\n            <!--            >-->\r\n            <!--              立即租赁-->\r\n            <!--            </button>-->\r\n\r\n            <button\r\n                class=\"buy-button\"\r\n                :class=\"{\r\n      'disabled': server.inventoryNumber === 0,\r\n      'buy-button-hovered': hoveredServer === server.id\r\n    }\"\r\n                @click=\"server.inventoryNumber > 0 ? directToConsole() : null\"\r\n            >\r\n              立即租赁\r\n            </button>\r\n\r\n          </div>\r\n        </div>\r\n        <!-- 空状态提示 -->\r\n        <div v-else class=\"empty-state\">\r\n          <div class=\"empty-state-icon\">\r\n            <!--            <img src=\"/path/to/empty-state-icon.png\" alt=\"暂无数据\" />-->\r\n          </div>\r\n          <div class=\"empty-state-text\">暂无数据</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <order-detail :visible=\"showDetail\" :server=\"serverss\" :selectedBillingMethod=\"selectedBillingMethod\" @orderSubmitted=\"buyGpu(serverss)\" @price-updated=\"orderPirce\" @time-updated=\"orderTimes\" @close=\"closeOrderDetail\"></order-detail>\r\n    <chatAi></chatAi>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Layout from \"@/components/common/Layout-header\";\r\nimport chatAi from \"@/components/common/mider/chatAi\";\r\nimport {postAnyData, getNotAuth, postNotAuth,getAnyData,postJsonData} from \"@/api/login\";\r\nimport {formatDateTime} from \"@/utils/component\"\r\nimport {getToken} from '@/utils/auth'\r\nimport orderDetail from \"@/views/Product/OrderDetail\";\r\nimport SlideNotification from '@/components/common/header/SlideNotification.vue';\r\n\r\n\r\nexport default {\r\n  name: \"ProductView\",\r\n  components: { Layout,orderDetail,chatAi,SlideNotification },\r\n  data() {\r\n    return {\r\n      orderTime:null,\r\n      price:null,\r\n      //记录当前用户选择计费方式\r\n      selectedBillingMethod:'priceHour',\r\n      serverss: {},\r\n      showComingSoon: false,\r\n      notificationMessage: \"\",\r\n      showDetail:false,\r\n      showindex:'priceHour',\r\n      // 筛选区域可见性\r\n      isFilterVisible: true,\r\n\r\n      // 当前悬浮的服务器ID\r\n      hoveredServer: null,\r\n\r\n      // 区域数据\r\n      regions: [\r\n        { id: '宁夏-B', name: '宁夏-B' },\r\n        { id: '四川-A', name: '四川-A' },\r\n        { id: '广东-B', name: '广东-B' }\r\n      ],\r\n\r\n      // GPU型号数据 - 作为所有可能的GPU型号库\r\n      allGpuModels: [\r\n        { id: 'a100-80g-nvlink', name: 'A100-80G NVLink' },\r\n        { id: 'a800-80g-pcie', name: 'A800-80G PCIe' },\r\n        { id: 'rtx4090-24g-pcie', name: 'RTX4090-24G PCIe' },\r\n        { id: 'a100-80g-nvlink-2', name: 'A100-80G NVLink' },\r\n        { id: 'h100-80g-nvlink', name: 'H100-80G NVLink' },\r\n        { id: 'a800-80g-nvlink', name: 'A800-80G NVLink' },\r\n        { id: 'h800-80g-nvlink', name: 'H800-80G NVLink' }\r\n      ],\r\n\r\n      // 服务器状态类型定义\r\n      serverStatuses: {\r\n        AVAILABLE: 'available',     // 资源充足\r\n        SHORTAGE: 'shortage',       // 资源紧张\r\n        UNAVAILABLE: 'unavailable'  // 已售罄\r\n      },\r\n\r\n      // 筛选条件 - 改为单选的计费模式\r\n      filters: {\r\n        allRegions: true,\r\n        selectedRegions: [],\r\n        billingMethod: 'hourly', // 改为单个字符串值\r\n        allGpuModels: true,\r\n        selectedGpuModels: [],\r\n        usageScenarios: {\r\n          development: true\r\n        }\r\n      },\r\n\r\n      // 服务器数据库 - 模拟从API获取的数据\r\n      allServers: [\r\n        {\r\n          id: 1,\r\n          gpuModel: 'NVIDIA A100-80G NVLink',\r\n          gpuModelId: 'a100-80g-nvlink',\r\n          gpuCount: 8,\r\n          gpuMemory: 80,\r\n          vcpu: 112,\r\n          systemDisk: 50,\r\n          cloudDisk: null,\r\n          memory: 912,\r\n          pricePerHour: 69.76,\r\n          status: 'unavailable',  // 这里不再硬编码，而是从后端获取\r\n          regionId: 'ningxia-b'\r\n        },\r\n        {\r\n          id: 2,\r\n          gpuModel: 'NVIDIA A100-80G NVLink',\r\n          gpuModelId: 'a100-80g-nvlink',\r\n          gpuCount: 4,\r\n          gpuMemory: 80,\r\n          vcpu: 56,\r\n          systemDisk: 50,\r\n          cloudDisk: null,\r\n          memory: 456,\r\n          pricePerHour: 34.88,\r\n          status: 'shortage',  // 示例：资源紧张状态\r\n          regionId: 'ningxia-b'\r\n        },\r\n        {\r\n          id: 3,\r\n          gpuModel: 'NVIDIA A100-80G NVLink',\r\n          gpuModelId: 'a100-80g-nvlink',\r\n          gpuCount: 2,\r\n          gpuMemory: 80,\r\n          vcpu: 28,\r\n          systemDisk: 50,\r\n          cloudDisk: null,\r\n          memory: 228,\r\n          pricePerHour: 17.44,\r\n          status: 'available',\r\n          regionId: 'ningxia-b'\r\n        },\r\n        {\r\n          id: 4,\r\n          gpuModel: 'NVIDIA H100-80G NVLink',\r\n          gpuModelId: 'h100-80g-nvlink',\r\n          gpuCount: 8,\r\n          gpuMemory: 80,\r\n          vcpu: 128,\r\n          systemDisk: 100,\r\n          cloudDisk: 500,\r\n          memory: 1024,\r\n          pricePerHour: 99.88,\r\n          status: 'available',\r\n          regionId: 'guangdong-b'\r\n        },\r\n        {\r\n          id: 5,\r\n          gpuModel: 'NVIDIA RTX4090-24G PCIe',\r\n          gpuModelId: 'rtx4090-24g-pcie',\r\n          gpuCount: 4,\r\n          gpuMemory: 24,\r\n          vcpu: 48,\r\n          systemDisk: 50,\r\n          cloudDisk: null,\r\n          memory: 256,\r\n          pricePerHour: 18.56,\r\n          status: 'shortage',\r\n          regionId: 'sichuan-a'\r\n        },\r\n        {\r\n          id: 6,\r\n          gpuModel: 'NVIDIA A800-80G PCIe',\r\n          gpuModelId: 'a800-80g-pcie',\r\n          gpuCount: 4,\r\n          gpuMemory: 80,\r\n          vcpu: 56,\r\n          systemDisk: 50,\r\n          cloudDisk: null,\r\n          memory: 456,\r\n          pricePerHour: 32.64,\r\n          status: 'unavailable',\r\n          regionId: 'sichuan-a'\r\n        }\r\n      ]\r\n    };\r\n  },\r\n  computed: {\r\n    // 根据所选区域动态获取可用的GPU型号\r\n    availableGpuModels() {\r\n      // 确保有选择的区域\r\n      if (this.filters.selectedRegions.length === 0) {\r\n        return [];\r\n      }\r\n      // 根据所选区域筛选出可用的GPU型号ID\r\n      const availableGpuIds = this.allServers\r\n          .filter(server => this.filters.selectedRegions.includes(server.region))\r\n          .map(server => server.name);\r\n\r\n      // 使用Set去重\r\n      const uniqueGpuIds = [...new Set(availableGpuIds)];\r\n\r\n      // 返回可用的GPU型号完整信息\r\n      return this.allGpuModels.filter(gpu => uniqueGpuIds.includes(gpu.name));\r\n    },\r\n\r\n    // 根据筛选条件过滤服务器\r\n    filteredServers() {\r\n      // 如果首次加载，设置默认选中所有可用的GPU型号\r\n      if (this.filters.allGpuModels && this.filters.selectedGpuModels.length === 0 && this.availableGpuModels.length > 0) {\r\n        this.filters.selectedGpuModels = this.availableGpuModels.map(gpu => gpu.id);\r\n      }\r\n\r\n      // 如果没有选择可用区或GPU型号，则不显示服务器\r\n      if (this.filters.selectedRegions.length === 0 || this.filters.selectedGpuModels.length === 0) {\r\n        return [];\r\n      }\r\n\r\n      return this.allServers.filter(server => {\r\n        // 区域筛选\r\n        if (!this.filters.selectedRegions.includes(server.region)) {\r\n          return false;\r\n        }\r\n\r\n        // GPU型号筛选\r\n        if (!this.filters.selectedGpuModels.includes(server.name)) {\r\n          return false;\r\n        }\r\n\r\n        // 使用场景筛选\r\n        if (!this.filters.usageScenarios.development) {\r\n          return false;\r\n        }\r\n\r\n        // 所有服务器都支持所有计费方式，此处无需进行过滤\r\n        // 保留此注释作为业务逻辑说明\r\n\r\n        return true;\r\n      });\r\n    }\r\n  },\r\n  methods: {\r\n    closeOrderDetail(){\r\n      this.showDetail = false\r\n    },\r\n    //接收订单时间\r\n    orderTimes(newval){\r\n      this.orderTime = newval\r\n    },\r\n    //接收订单价格\r\n    orderPirce(newval){\r\n      this.price = newval\r\n    },\r\n    async directToConsole() {\r\n      // 检查登录状态\r\n      if (!getToken()) {\r\n        this.$router.push('/login');\r\n        this.$toast.success(\"请先登录后再进行购买\");\r\n        return;\r\n      }\r\n\r\n      // 获取用户实名信息\r\n      try {\r\n        const res = await postAnyData(\"/logout/cilent/getInfo\");\r\n        if (res.data.code === 200) {\r\n          const isReal = res.data.data.isReal;\r\n\r\n          // 未实名认证处理\r\n          if (isReal !== 1) {\r\n            this.notificationMessage = \"请先完成实名认证，正在为您跳转到对应页面\";\r\n            this.showComingSoon = true;\r\n\r\n            setTimeout(() => {\r\n              this.$router.push({\r\n                path: '/personal',\r\n                query: { activeTab: 'verification' }\r\n              });\r\n            }, 2000);\r\n            return;\r\n          }\r\n\r\n          // 已实名直接跳转\r\n          this.$router.push('/console');\r\n        }\r\n      } catch (error) {\r\n        this.$toast.error(\"获取用户信息失败\");\r\n        // console.error(error);\r\n      }\r\n    },\r\n    //购买Gpu\r\n    orderOK(serverinfo) {\r\n      // 再次检查库存，防止通过其他方式触发\r\n      if (serverinfo.inventoryNumber <= 0) {\r\n        this.$toast.error(\"该资源已售罄，无法购买\");\r\n        return;\r\n      }\r\n\r\n      if(!getToken()) {\r\n        this.$router.push('/login');\r\n        this.$toast.success(\"请先登录后再进行购买\");\r\n        return;\r\n      }\r\n\r\n      this.serverss = serverinfo;\r\n      this.selectedBillingMethod = this.showindex;\r\n      this.showDetail = true;\r\n    },\r\n\r\n    buyGpu(server){\r\n      let params = {\r\n        id:server.id,\r\n        price:'',\r\n        name:server.name,\r\n        time:this.orderTime\r\n      }\r\n      params.price = this.price\r\n      params.time = this.orderTime\r\n      postJsonData(\"/system/parameter/decrease\",params).then(res =>{\r\n        if (res.data.msg === '余额不足'){\r\n          this.$toast.error(\"余额不足,请前往充值\")\r\n          return\r\n        }\r\n        if (res.data.msg === '库存不足'){\r\n          this.$toast.error(\"库存不足，请稍后再试\")\r\n          return;\r\n        }\r\n        this.$toast.success(\"租赁成功，请前往控制台使用\")\r\n      })\r\n    },\r\n    // 切换筛选区域的可见性\r\n    toggleFilterVisibility() {\r\n      this.isFilterVisible = !this.isFilterVisible;\r\n    },\r\n\r\n    // 动画钩子函数 - 进入动画开始前\r\n    beforeEnter(el) {\r\n      el.style.height = '0';\r\n      el.style.opacity = '0';\r\n      el.style.overflow = 'hidden';\r\n    },\r\n\r\n    // 进入动画中\r\n    enter(el) {\r\n      const height = el.scrollHeight;\r\n      // 使用 requestAnimationFrame 确保过渡平滑\r\n      requestAnimationFrame(() => {\r\n        el.style.height = height + 'px';\r\n        el.style.opacity = '1';\r\n      });\r\n    },\r\n\r\n    // 进入动画结束\r\n    afterEnter(el) {\r\n      // 移除高度限制，允许内容自然流动\r\n      el.style.height = '';\r\n      el.style.overflow = '';\r\n    },\r\n\r\n    // 离开动画开始前\r\n    beforeLeave(el) {\r\n      // 设置具体高度，为动画做准备\r\n      el.style.height = el.scrollHeight + 'px';\r\n      el.style.overflow = 'hidden';\r\n    },\r\n\r\n    // 离开动画中\r\n    leave(el) {\r\n      // 强制浏览器重排以确保动画正确开始\r\n      void el.offsetHeight;\r\n\r\n      // 使用 requestAnimationFrame 确保过渡平滑\r\n      requestAnimationFrame(() => {\r\n        el.style.height = '0';\r\n        el.style.opacity = '0';\r\n      });\r\n    },\r\n\r\n    // 离开动画结束\r\n    afterLeave(el) {\r\n      // 清理样式\r\n      el.style.height = '';\r\n      el.style.overflow = '';\r\n    },\r\n\r\n    // 全选/取消全选区域\r\n    toggleAllRegions() {\r\n      if (this.filters.allRegions) {\r\n        this.filters.selectedRegions = this.regions.map(region => region.name);\r\n      } else {\r\n        this.filters.selectedRegions = [];\r\n      }\r\n      this.updateFilters();\r\n    },\r\n\r\n    // 全选/取消全选GPU型号\r\n    toggleAllGpuModels() {\r\n      if (this.filters.allGpuModels) {\r\n        this.filters.selectedGpuModels = this.availableGpuModels.map(gpu => gpu.id);\r\n      } else {\r\n        this.filters.selectedGpuModels = [];\r\n      }\r\n      this.updateFilters();\r\n    },\r\n\r\n    // 更新筛选条件，重新加载GPU型号和服务器数据\r\n    updateFilters() {\r\n      // 同步全选状态\r\n      this.filters.allRegions = this.filters.selectedRegions.length === this.regions.length;\r\n\r\n      // 确保availableGpuModels更新后再检查全选状态\r\n      this.$nextTick(() => {\r\n        this.filters.allGpuModels = this.availableGpuModels.length > 0 &&\r\n            this.filters.selectedGpuModels.length === this.availableGpuModels.length;\r\n      });\r\n\r\n      // 在实际应用中，这里应该调用API重新获取数据\r\n      // this.fetchServers();\r\n    },\r\n\r\n    // 根据区域ID获取区域名称\r\n    getRegionName(regionId) {\r\n      const region = this.regions.find(r => r.id === regionId);\r\n      return region ? region.name : '未知区域';\r\n    },\r\n\r\n    // 获取服务器状态显示文本\r\n    getServerStatusText(status) {\r\n      if (status>0 && status<=3){\r\n        status = 'shortage'\r\n      }else if(status>3){\r\n        status = 'available'\r\n      }else if (status ==0){\r\n        status = 'unavailable'\r\n      }\r\n      switch(status) {\r\n        case this.serverStatuses.AVAILABLE:\r\n          return '资源充足';\r\n        case this.serverStatuses.SHORTAGE:\r\n          return '资源紧张';\r\n        case this.serverStatuses.UNAVAILABLE:\r\n          return '已售罄';\r\n        default:\r\n          return '未知状态';\r\n      }\r\n    },\r\n\r\n    // 获取服务器状态样式类\r\n    getServerStatusClass(status) {\r\n      if (status>0 && status<=3){\r\n        status = 'shortage'\r\n      }else if(status>3){\r\n        status = 'available'\r\n      }else if (status ==0){\r\n        status = 'unavailable'\r\n      }\r\n      switch(status) {\r\n        case this.serverStatuses.AVAILABLE:\r\n          return 'server-available';\r\n        case this.serverStatuses.SHORTAGE:\r\n          return 'server-shortage';\r\n        case this.serverStatuses.UNAVAILABLE:\r\n          return 'server-unavailable';\r\n        default:\r\n          return '';\r\n      }\r\n    },\r\n\r\n    // 从API获取服务器数据\r\n    fetchServers() {\r\n      //加载数据\r\n      let params;\r\n      getNotAuth(\"/auth/getGpuList\").then(respose =>{\r\n        this.allServers = respose.data.data;\r\n        postNotAuth(\"/system/parameter/getGpu\").then(res =>{\r\n          this.allGpuModels = res.data.data;\r\n          for (let i =0;i<this.allGpuModels.length;i++){\r\n            this.allGpuModels[i].id = this.allGpuModels[i].name\r\n          }\r\n          postNotAuth(\"/system/parameter/getRegion\").then(region =>{\r\n            this.regions = region.data.data\r\n            for (let i =0;i<this.regions.length;i++){\r\n              this.regions[i].id = this.regions[i].region\r\n              this.regions[i].name = this.regions[i].region\r\n            }\r\n            this.toggleAllRegions()\r\n            this.toggleAllGpuModels()\r\n          })\r\n        })\r\n        params = {\r\n          regions: this.filters.selectedRegions,\r\n          gpuModels: this.filters.selectedGpuModels,\r\n          billingMethod: this.filters.billingMethod\r\n        };\r\n      })\r\n    }\r\n  },\r\n  created() {\r\n    // 在组件创建时获取服务器数据\r\n    this.fetchServers();\r\n    // 初始化选中所有可用的GPU型号\r\n    this.$nextTick(() => {\r\n      this.filters.selectedGpuModels = this.availableGpuModels.map(gpu => gpu.id);\r\n    });\r\n  },\r\n  watch: {\r\n    // 监听区域选择变化，同步更新GPU型号选择\r\n    'filters.selectedRegions': {\r\n      handler() {\r\n        // 当选择的区域变化时，使用新的可用GPU型号列表更新选中的GPU型号\r\n        if (this.filters.allGpuModels) {\r\n          this.filters.selectedGpuModels = this.availableGpuModels.map(gpu => gpu.id);\r\n        } else {\r\n          // 仅保留在新的可用列表中的GPU型号\r\n          const availableIds = this.availableGpuModels.map(gpu => gpu.id);\r\n          this.filters.selectedGpuModels = this.filters.selectedGpuModels.filter(id => availableIds.includes(id));\r\n        }\r\n      },\r\n      immediate: true\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 基础样式 */\r\n.compute-market {\r\n  font-family: Arial, sans-serif;\r\n  max-width: 2560px;\r\n  margin: 0 auto;\r\n  padding: 20px;\r\n  color: #333;\r\n}\r\n\r\n/* 筛选区域样式 */\r\n.filter-section {\r\n  background-color: #fff;\r\n  border-radius: 4px;\r\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\r\n  margin-bottom: 20px;\r\n  padding: 8px 16px;\r\n  border: 1px solid #f0f0f0;\r\n}\r\n\r\n.filter-header {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 6px 0;\r\n  cursor: pointer;\r\n  user-select: none;\r\n  min-height: 32px;\r\n}\r\n\r\n.filter-title {\r\n  font-size: 15px;\r\n  font-weight: 400;\r\n  color: #333;\r\n}\r\n\r\n.filter-icon {\r\n  color: #409eff;\r\n  margin-left: 8px;\r\n  font-size: 12px;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.filter-icon.collapsed {\r\n  transform: rotate(-90deg);\r\n}\r\n\r\n/* 改进过渡动画效果 */\r\n.slide-enter-active,\r\n.slide-leave-active {\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n  max-height: 800px;\r\n  overflow: hidden;\r\n}\r\n\r\n.slide-enter-from,\r\n.slide-leave-to {\r\n  max-height: 0;\r\n  opacity: 0;\r\n  overflow: hidden;\r\n}\r\n\r\n.filter-content {\r\n  overflow: hidden;\r\n}\r\n\r\n.filter-row {\r\n  display: flex;\r\n  padding: 10px 0;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  flex-direction: column;\r\n}\r\n\r\n.filter-row:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.filter-label {\r\n  width: 100%;\r\n  color: #333;\r\n  font-size: 13px;\r\n  font-weight: 400;\r\n  padding-top: 2px;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.filter-options {\r\n  flex: 1;\r\n}\r\n\r\n.checkbox-group, .radio-group {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 12px;\r\n}\r\n\r\n.checkbox-item, .radio-item {\r\n  display: flex;\r\n  align-items: center;\r\n  cursor: pointer;\r\n  min-width: 2px;\r\n}\r\n\r\n.checkbox-item input[type=\"checkbox\"], .radio-item input[type=\"radio\"] {\r\n  margin-right: 5px;\r\n  width: 14px;\r\n  height: 14px;\r\n  accent-color: #2196f3;\r\n  cursor: pointer;\r\n}\r\n\r\n.checkbox-text, .radio-text {\r\n  font-size: 12px;\r\n  font-weight: 400;\r\n  margin-bottom: -5px;\r\n}\r\n\r\n.gpu-brand {\r\n  font-size: 13px;\r\n  font-weight: 400;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.gpu-list {\r\n  margin-top: 6px;\r\n}\r\n\r\n/* 服务器卡片网格 - 响应式布局 */\r\n.servers-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\r\n  gap: 15px;\r\n}\r\n\r\n/* 服务器卡片样式 */\r\n.server-card {\r\n  background-color: #fff;\r\n  border-radius: 4px;\r\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\r\n  padding: 16px;\r\n  border: 1px solid #f0f0f0;\r\n  transition: all 0.2s ease;\r\n  position: relative;\r\n}\r\n\r\n.server-card-hovered {\r\n  border-color: #409eff;\r\n  box-shadow: 0 2px 8px rgba(128, 65, 255, 0.2);\r\n}\r\n\r\n/* 区域标签 */\r\n.region-tag {\r\n  position: absolute;\r\n  top: 16px;\r\n  right: 16px;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  color: #666;\r\n}\r\n\r\n.server-title {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  margin-bottom: 12px;\r\n  height: 3em;\r\n  color: #333;\r\n  padding-right: 60px;\r\n}\r\n\r\n.server-price-section {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.price {\r\n  display: flex;\r\n  align-items: baseline;\r\n}\r\n\r\n.currency {\r\n  font-size: 16px;\r\n  color: #2196f3;\r\n  font-weight: 500;\r\n}\r\n\r\n.amount {\r\n  font-size: 26px;\r\n  font-weight: bold;\r\n  color: #2196f3;\r\n  margin: 0 2px;\r\n}\r\n\r\n.unit {\r\n  font-size: 14px;\r\n  color: #2196f3;\r\n}\r\n\r\n.server-status {\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n}\r\n\r\n/* 资源充足状态 */\r\n.server-available {\r\n  color: #52c41a;\r\n  background-color: #f6ffed;\r\n  border: 1px solid #b7eb8f;\r\n}\r\n\r\n/* 资源紧张状态 */\r\n.server-shortage {\r\n  color: #fa8c16;\r\n  background-color: #fff7e6;\r\n  border: 1px solid #ffd591;\r\n}\r\n\r\n/* 已售罄状态 */\r\n.server-unavailable {\r\n  color: #333;\r\n  background-color: #f5f5f5;\r\n  border: 1px solid #d9d9d9;\r\n}\r\n\r\n/* 服务器规格 - 响应式网格布局 */\r\n.server-specs {\r\n  background-color: #f9f9f9;\r\n  border-radius: 4px;\r\n  padding: 12px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.specs-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(3, 1fr);\r\n  gap: 12px;\r\n}\r\n\r\n.spec-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  text-align: center;\r\n}\r\n\r\n.spec-label {\r\n  font-size: 12px;\r\n  color: #999;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.spec-value {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  color: #333;\r\n}\r\n\r\n/* 购买按钮 */\r\n.buy-button {\r\n  width: 100%;\r\n  padding: 10px 0;\r\n  background-color: white;\r\n  color: #333;\r\n  border: 1px solid #d9d9d9;\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n  cursor: pointer;\r\n  text-align: center;\r\n  transition: all 0.2s;\r\n}\r\n\r\n.buy-button.disabled {\r\n  background-color: #f5f5f5;\r\n  color: #999;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.buy-button-hovered:not(.disabled) {\r\n  background-color: #2196f3;\r\n  color: white;\r\n  border-color: #2196f3;\r\n}\r\n\r\n/* 空状态提示 */\r\n.empty-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 60px 0;\r\n  color: #999;\r\n}\r\n\r\n.empty-state-icon {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.empty-state-icon img {\r\n  width: 80px;\r\n  height: 80px;\r\n  opacity: 0.6;\r\n}\r\n\r\n.empty-state-text {\r\n  font-size: 16px;\r\n  color: #999;\r\n}\r\n\r\n/* 移动端适配 */\r\n@media (max-width: 768px) {\r\n  .compute-market {\r\n    padding: 10px;\r\n  }\r\n\r\n  .filter-section {\r\n    padding: 6px 12px;\r\n  }\r\n\r\n  .filter-row {\r\n    padding: 8px 0;\r\n  }\r\n\r\n  .checkbox-group, .radio-group {\r\n    gap: 8px;\r\n  }\r\n\r\n  .radio-item {\r\n    margin-right: -15px; /* 移动端减小间距 */\r\n  }\r\n\r\n  .radio-item input[type=\"radio\"] {\r\n    margin-right: -5px; /* 移动端进一步减小间距 */\r\n  }\r\n\r\n  .radio-text {\r\n    font-size: 11px; /* 移动端减小字体 */\r\n    margin-left: -30px; /* 移动端减小间距 */\r\n  }\r\n  .checkbox-item {\r\n    min-width: 70px;\r\n  }\r\n\r\n  .servers-grid {\r\n    grid-template-columns: 1fr;\r\n    gap: 12px;\r\n  }\r\n\r\n  .server-card {\r\n    padding: 12px;\r\n  }\r\n\r\n  .specs-grid {\r\n    grid-template-columns: repeat(2, 1fr);\r\n  }\r\n\r\n  .price .amount {\r\n    font-size: 22px;\r\n  }\r\n\r\n  .price .unit {\r\n    font-size: 12px;\r\n  }\r\n\r\n  .server-title {\r\n    font-size: 15px;\r\n  }\r\n\r\n  .buy-button {\r\n    padding: 8px 0;\r\n    font-size: 13px;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .filter-row {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .filter-label {\r\n    margin-bottom: 3px;\r\n  }\r\n\r\n  .checkbox-group, .radio-group {\r\n    gap: 6px;\r\n  }\r\n\r\n  .checkbox-item, .radio-item {\r\n    min-width: 60px;\r\n  }\r\n\r\n  .specs-grid {\r\n    grid-template-columns: 1fr 1fr;\r\n  }\r\n\r\n  .price .amount {\r\n    font-size: 20px;\r\n  }\r\n\r\n  .server-status {\r\n    font-size: 11px;\r\n    padding: 3px 6px;\r\n  }\r\n\r\n  .empty-state-icon img {\r\n    width: 60px;\r\n    height: 60px;\r\n  }\r\n\r\n  .empty-state-text {\r\n    font-size: 14px;\r\n  }\r\n}\r\n\r\n/* 调整左侧边距在移动端 */\r\n@media (max-width: 768px) {\r\n  div[style^=\"margin-left\"] {\r\n    margin-left: 0 !important;\r\n  }\r\n\r\n  div[style^=\"width\"] {\r\n    width: 100% !important;\r\n  }\r\n}\r\n/* 调整左侧边距在安卓显示器端 */\r\n@media (min-width: 1440px) {\r\n  div[style^=\"margin-left\"] {\r\n    margin-left: 3.5% !important;\r\n  }\r\n\r\n  div[style^=\"width\"] {\r\n    width: 96% !important;\r\n  }\r\n}\r\n\r\n</style>", "import mod from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./ProductView.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./ProductView.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./ProductView.vue?vue&type=template&id=4dbfce5b&scoped=true&\"\nimport script from \"./ProductView.vue?vue&type=script&lang=js&\"\nexport * from \"./ProductView.vue?vue&type=script&lang=js&\"\nimport style0 from \"./ProductView.vue?vue&type=style&index=0&id=4dbfce5b&prod&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4dbfce5b\",\n  null\n  \n)\n\nexport default component.exports"], "names": ["render", "_vm", "this", "_c", "_self", "staticClass", "on", "pauseCarousel", "resumeCarousel", "attrs", "_l", "questions", "question", "index", "directives", "name", "rawName", "value", "currentQuestionIndex", "expression", "key", "$event", "sendCarouselQuestion", "witde", "_v", "_s", "class", "showChat", "toggleChat", "_m", "ref", "messages", "message", "type", "_e", "domProps", "formatMessage", "text", "formatTime", "time", "loading", "userInput", "indexOf", "_k", "keyCode", "sendMessage", "apply", "arguments", "target", "composing", "trim", "staticRenderFns", "data", "Date", "historyMessages", "carouselTimer", "carouselI<PERSON>val", "isPaused", "<PERSON><PERSON><PERSON><PERSON>", "clearCarousel", "mounted", "startCarousel", "document", "getElementById", "link", "createElement", "id", "rel", "href", "head", "append<PERSON><PERSON><PERSON>", "methods", "that", "setInterval", "length", "console", "log", "clearInterval", "$nextTick", "scrollToBottom", "push", "userQuestion", "role", "content", "requestBody", "model", "stream", "options", "presence_penalty", "frequency_penalty", "seed", "response", "fetch", "method", "headers", "Authorization", "body", "JSON", "stringify", "reader", "<PERSON><PERSON><PERSON><PERSON>", "decoder", "TextDecoder", "aiResponseIndex", "done", "read", "chunk", "decode", "lines", "split", "filter", "line", "jsonString", "slice", "parse", "choices", "delta", "reasoning_content", "e", "error", "Promise", "resolve", "setTimeout", "container", "$refs", "messagesContainer", "scrollTop", "scrollHeight", "date", "toLocaleTimeString", "hour", "minute", "replace", "component", "visible", "billingOptions", "option", "selectedBillingMethod", "label", "server", "graphicsCardNumber", "videoMemory", "gpuNuclearNumber", "internalMemory", "systemDisk", "priceUnit", "dataDisk", "selectedDuration", "$$selectedVal", "Array", "prototype", "call", "o", "selected", "map", "val", "_value", "multiple", "currentDurationOptions", "totalPrice", "closeModal", "showConfirmDialog", "showConfirmation", "confirmOrder", "props", "Boolean", "default", "Object", "String", "defaultDiskSize", "needExtraDisk", "unit", "durationOptionsHour", "durationOptionsDay", "durationOptionsWeek", "durationOptionsMonth", "computed", "price", "toFixed", "totalTime", "watch", "handler", "newval", "$emit", "immediate", "order", "serverId", "serverName", "billingMethod", "duration", "specs", "gpuModel", "vcpu", "cloudDisk", "memory", "staticStyle", "showComingSoon", "notificationMessage", "toggleFilterVisibility", "isFilterVisible", "beforeEnter", "enter", "afterEnter", "beforeLeave", "leave", "afterLeave", "showindex", "_q", "filters", "allRegions", "isArray", "_i", "$$a", "$$el", "$$c", "checked", "$$v", "$$i", "$set", "concat", "toggleAllRegions", "regions", "region", "selectedRegions", "updateFilters", "availableGpuModels", "allGpuModels", "toggleAllGpuModels", "gpu", "selectedGpuModels", "usageScenarios", "development", "filteredServers", "hoveredServer", "getRegionName", "priceHour", "getServerStatusClass", "inventoryNumber", "getServerStatusText", "priceDay", "priceMouth", "priceYear", "directToConsole", "showDetail", "serverss", "buyGpu", "orderPirce", "orderTimes", "closeOrderDetail", "components", "Layout", "orderDetail", "chatAi", "SlideNotification", "orderTime", "serverStatuses", "AVAILABLE", "SHORTAGE", "UNAVAILABLE", "allServers", "gpuModelId", "gpuCount", "gpuMemory", "pricePerHour", "status", "regionId", "availableGpuIds", "includes", "uniqueGpuIds", "Set", "getToken", "$router", "$toast", "success", "res", "postAnyData", "code", "isReal", "path", "query", "activeTab", "orderOK", "serverinfo", "params", "postJsonData", "then", "msg", "el", "style", "height", "opacity", "overflow", "requestAnimationFrame", "offsetHeight", "find", "r", "fetchServers", "getNotAuth", "respose", "postNotAuth", "i", "gpuModels", "created", "availableIds"], "sourceRoot": ""}