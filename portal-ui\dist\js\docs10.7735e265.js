"use strict";(self["webpackChunkportal_ui"]=self["webpackChunkportal_ui"]||[]).push([[1447],{7631:function(t,o,n){n.r(o);var e=new URL(n(1404),n.b),u=new URL(n(4117),n.b),r=new URL(n(3683),n.b),f=new URL(n(7065),n.b),s=new URL(n(5114),n.b),a='<h1 id="容器化部署-funasr">容器化部署 FunASR</h1> <blockquote> <p>此镜像提供了标准化的<strong>API 接口</strong>，让您能够便捷地通过 <strong>API 调用方式</strong>访问和使用所有功能。</p> </blockquote> <p><font style="color:#020817">本指南详细阐述了在天工开物平台上，高效部署与使用 FunASR API 项目的技术方案。FunASR 是一个基本的语音识别工具包，提供多种功能，包括语音识别（ASR）、语音活动检测（VAD）、标点符号恢复、语言模型、说话人验证、说话人分类和多说话者 ASR。</font></p> <h2 id="1在天工开物上运行-funasr-api">1.在天工开物上运行 FunASR API</h2> <p><font style="color:#020817">天工开物平台提供预构建的 FunASR API 容器镜像，用户无需本地复杂环境配置，可快速完成部署并启用服务。以下是详细部署步骤：</font></p> <h3 id="11-创建部署服务">1.1 创建部署服务</h3> <p><font style="color:#020817">登录<a href="https://tiangongkaiwu.top/portal/#/console"><font style="color:#06c">天工开物控制台</font></a>，在控制台首页点击“弹性部署服务”进入管理页面。</font></p> <p><img src="'+e+'" alt=""></p> <h3 id="12-选择-gpu-型号">1.2 选择 GPU 型号</h3> <p><font style="color:#020817">根据实际需求选择 GPU 型号：</font></p> <p><font style="color:#020817">初次使用或调试阶段，推荐配置单张 NVIDIA RTX 4090 GPU</font></p> <p><img src="'+u+'" alt=""></p> <h3 id="13-选择预制镜像">1.3 选择预制镜像</h3> <p><font style="color:#020817">在“服务配置”模块切换至“预制服务”选项卡，选择 FunASR API 官方镜像。</font></p> <p><img src="'+r+'" alt=""></p> <h3 id="14-部署并访问服务">1.4 部署并访问服务</h3> <p><font style="color:#020817">点击“部署服务”，平台将自动拉取镜像并启动容器。</font></p> <p><img src="'+f+'" alt=""></p> <p><font style="color:#020817">部署完成后，在“快捷访问”中复制端口为 10095 的公网访问链接，后续是通过该地址调用 API 服务。</font></p> <h2 id="2-快速上手">2. 快速上手</h2> <p><font style="color:#020817">系统架构图：</font></p> <p><img src="'+s+'" alt=""></p> <p><font style="color:#020817">通信协议：</font></p> <p><font style="color:#020817">使用 WebSocket 协议进行通信，消息格式：</font></p> <ul> <li>配置参数：<font style="color:#2f8ef4;background-color:#eff0f0">JSON</font> 格式</li> <li>音频数据：<font style="color:#2f8ef4;background-color:#eff0f0">bytes</font> 格式</li> </ul> <h3 id="21-客户端向服务端发送数据">2.1 客户端向服务端发送数据</h3> <h4 id="211-首次通信---发送配置参数">2.1.1 首次通信 - 发送配置参数</h4> <pre><code class="language-python">{\n    &quot;chunk_size&quot;: [5, 10, 5],\n    &quot;wav_name&quot;: &quot;h5&quot;,\n    &quot;is_speaking&quot;: true,\n    &quot;wav_format&quot;: &quot;pcm&quot;,\n    &quot;chunk_interval&quot;: 10,\n    &quot;itn&quot;: true,\n    &quot;mode&quot;: &quot;2pass&quot;,\n    &quot;hotwords&quot;: &quot;{\\&quot;阿里巴巴\\&quot;:20,\\&quot;hello world\\&quot;:40}&quot;\n}\n</code></pre> <p><font style="color:#020817">参数说明：</font></p> <table> <thead> <tr> <th>参数</th> <th>类型</th> <th>说明</th> </tr> </thead> <tbody><tr> <td><font style="color:#2f8ef4;background-color:#eff0f0">wav_name</font></td> <td>string</td> <td>音频文件名</td> </tr> <tr> <td><font style="color:#2f8ef4;background-color:#eff0f0">wav_format</font></td> <td>string</td> <td>音视频文件后缀名，只支持 pcm 音频流</td> </tr> <tr> <td><font style="color:#2f8ef4;background-color:#eff0f0">is_speaking</font></td> <td>boolean</td> <td>断句尾点标识</td> </tr> <tr> <td><font style="color:#2f8ef4;background-color:#eff0f0">chunk_size</font></td> <td>array</td> <td>流式模型延迟配置 <font style="color:#2f8ef4;background-color:#eff0f0">[5,10,5] </font>表示当前音频 600ms，回看 300ms，预看 300ms</td> </tr> <tr> <td><font style="color:#2f8ef4;background-color:#eff0f0">chunk_interval</font></td> <td>number</td> <td>块间隔时间</td> </tr> <tr> <td><font style="color:#2f8ef4;background-color:#eff0f0">itn</font></td> <td>boolean</td> <td>是否使用逆文本标准化，默认 true</td> </tr> <tr> <td><font style="color:#2f8ef4;background-color:#eff0f0">mode</font></td> <td>string</td> <td>模型模式，支持：<font style="color:#2f8ef4;background-color:#eff0f0">2pass</font>（默认）、<font style="color:#2f8ef4;background-color:#eff0f0">online</font>、<font style="color:#2f8ef4;background-color:#eff0f0">offline</font></td> </tr> <tr> <td><font style="color:#2f8ef4;background-color:#eff0f0">hotwords</font></td> <td>string</td> <td>热词配置，JSON 字符串格式</td> </tr> </tbody></table> <h4 id="212-发送音频数据">2.1.2 发送音频数据</h4> <p><font style="color:#020817">直接将音频数据（移除头部信息）以 bytes 格式发送，支持 8000Hz 采样率。</font></p> <h4 id="213-发送结束标志">2.1.3 发送结束标志</h4> <pre><code class="language-python">{\n    &quot;is_speaking&quot;: false\n}\n</code></pre> <h3 id="22-服务端向客户端发送数据">2.2 服务端向客户端发送数据</h3> <p><font style="color:#020817">识别中结果：</font></p> <pre><code class="language-python">{\n    &quot;is_final&quot;: false,\n    &quot;mode&quot;: &quot;2pass-online&quot;,\n    &quot;text&quot;: &quot;阿里&quot;,\n    &quot;wav_name&quot;: &quot;h5&quot;\n}\n</code></pre> <p><font style="color:#020817">识别结束结果：</font></p> <pre><code class="language-python">{\n    &quot;is_final&quot;: false,\n    &quot;mode&quot;: &quot;2pass-offline&quot;,\n    &quot;stamp_sents&quot;: [\n        {\n            &quot;end&quot;: 4385,\n            &quot;punc&quot;: &quot;&quot;,\n            &quot;start&quot;: 820,\n            &quot;text_seg&quot;: &quot;阿 里 开 源 的 语 音 识 别 真 是 太 牛 了&quot;\n        }\n    ],\n    &quot;text&quot;: &quot;阿里开源的语音识别真是太牛了&quot;,\n    &quot;timestamp&quot;: &quot;[[820,1060],[1060,1319],[1319,1480],[1480,1760],[1760,2220],[2220,2420],[2420,2659],[2659,2820],[2820,3300],[3300,3480],[3480,3659],[3659,3820],[3820,4060],[4060,4385]]&quot;,\n    &quot;wav_name&quot;: &quot;h5&quot;\n}\n</code></pre> <p><font style="color:#020817">返回参数说明：</font></p> <table> <thead> <tr> <th>参数</th> <th>说明</th> </tr> </thead> <tbody><tr> <td><font style="color:#2f8ef4;background-color:#eff0f0">mode</font></td> <td>推理模式：<font style="color:#2f8ef4;background-color:#eff0f0">2pass-online</font>（实时识别）、<font style="color:#2f8ef4;background-color:#eff0f0">2pass-offline</font>（2 遍修正识别）</td> </tr> <tr> <td><font style="color:#2f8ef4;background-color:#eff0f0">wav_name</font></td> <td>音频文件名</td> </tr> <tr> <td><font style="color:#2f8ef4;background-color:#eff0f0">text</font></td> <td>语音识别输出文本</td> </tr> <tr> <td><font style="color:#2f8ef4;background-color:#eff0f0">is_final</font></td> <td>是否为最终结果</td> </tr> <tr> <td><font style="color:#2f8ef4;background-color:#eff0f0">stamp_sents</font></td> <td>句子分割信息</td> </tr> <tr> <td><font style="color:#2f8ef4;background-color:#eff0f0">timestamp</font></td> <td>时间戳信息</td> </tr> </tbody></table> <h3 id="23-模型模式说明">2.3 模型模式说明</h3> <p><font style="color:#020817">2pass：默认模式，实时语音识别 + 句尾离线模型纠错（准确度更高）</font></p> <p><font style="color:#020817">online：实时语音识别</font></p> <p><font style="color:#020817">offline：一句话识别</font></p> <h3 id="24-逆文本标准化（itn）">2.4 逆文本标准化（ITN）</h3> <p><font style="color:#020817">ITN 将标准化文本转换为更自然的书面格式：</font></p> <p><font style="color:#2f8ef4;background-color:#eff0f0">&quot;一二三&quot; </font>→ <font style="color:#2f8ef4;background-color:#eff0f0">&quot;123&quot;</font></p> <p><font style="color:#2f8ef4;background-color:#eff0f0">&quot;二零二四年七月二十五日&quot;</font> → <font style="color:#2f8ef4;background-color:#eff0f0">&quot;2024年7月25日&quot;</font></p> <p><font style="color:#2f8ef4;background-color:#eff0f0">&quot;五千七百六十三&quot;</font>→ <font style="color:#2f8ef4;background-color:#eff0f0">&quot;5763&quot;</font></p> <h2 id="3python-调用-api-功能示例">3.Python 调用 API 功能示例</h2> <p><font style="color:#020817">基本依赖：</font></p> <pre><code class="language-powershell">pip install websockets\npip install pyaudio  # 可选，用于实时录音\n</code></pre> <p><font style="color:#020817">核心文件：</font></p> <pre><code class="language-powershell">python test_connection.py          # 基础连接测试\npython quick_demo.py              # 最简API演示\npython simple_websocket_test.py   # 综合功能测试\npython simple_test.py             # 虚拟音频测试\npython simple_audio_converter.py  # 音频格式转换\npython funasr_client_no_audio.py  # 音频文件识别\npython recognize_audio.py         # 识别示例\n</code></pre> <h3 id="31-基础连接测试">3.1 基础连接测试</h3> <p><font style="color:#020817">代码示例：</font></p> <pre><code class="language-python">import asyncio\nimport websockets\nimport json\nimport logging\nlogging.basicConfig(level=logging.INFO, format=&#39;%(asctime)s - %(levelname)s - %(message)s&#39;)\nlogger = logging.getLogger(__name__)\nasync def test_connection():\n    server_url = &quot;wss://d07040947-funasr-online-server-1978-wta6gctb-10095.550c.cloud/&quot;\n    try:\n        logger.info(f&quot;正在连接到：{server_url}&quot;)\n        async with websockets.connect(server_url) as websocket:\n            logger.info(&quot;✅ WebSocket 连接成功！&quot;)\n            # 发送测试配置\n            test_config = {\n                &quot;chunk_size&quot;: [5, 10, 5],\n                &quot;wav_name&quot;: &quot;connection_test&quot;,\n                &quot;is_speaking&quot;: True,\n                &quot;wav_format&quot;: &quot;pcm&quot;,\n                &quot;chunk_interval&quot;: 10,\n                &quot;itn&quot;: True,\n                &quot;mode&quot;: &quot;2pass&quot;\n            }\n            logger.info(&quot;📤 发送测试配置...&quot;)\n            await websocket.send(json.dumps(test_config, ensure_ascii=False))\n            logger.info(&quot;✅ 配置发送成功！&quot;)\n            # 发送结束信号\n            end_signal = {&quot;is_speaking&quot;: False}\n            await websocket.send(json.dumps(end_signal))\n            logger.info(&quot;✅ 结束信号发送成功！&quot;)\n            # 尝试接收响应\n            try:\n                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)\n                logger.info(f&quot;📥 收到服务器响应：{response}&quot;)\n            except asyncio.TimeoutError:\n                logger.info(&quot;⏰ 未在 5 秒内收到响应（这是正常的，因为没有发送音频数据）&quot;)\n            logger.info(&quot;🎉 连接测试完成！&quot;)\n    except Exception as e:\n        logger.error(f&quot;❌ 连接测试失败：{e}&quot;)\nif __name__ == &quot;__main__&quot;:\n    logger.info(&quot;🚀 开始 FunASR WebSocket API 连接测试&quot;)\n    asyncio.run(test_connection())\n</code></pre> <p><font style="color:#020817">运行输出：</font></p> <pre><code class="language-python">2025-07-03 11:37:28,981 - INFO - 🚀 开始 FunASR WebSocket API 连接测试\n2025-07-03 11:37:28,982 - INFO - 正在连接到：wss://d07011926-funasr-online-server-318-wjqawusl-10095.550c.cloud/\n2025-07-03 11:37:29,436 - INFO - ✅ WebSocket 连接成功！\n2025-07-03 11:37:29,436 - INFO - 📤 发送测试配置...\n2025-07-03 11:37:29,437 - INFO - ✅ 配置发送成功！\n2025-07-03 11:37:29,437 - INFO - ✅ 结束信号发送成功！\n2025-07-03 11:37:29,516 - INFO - 📥 收到服务器响应：{&quot;is_final&quot;:true,&quot;text&quot;:&quot;&quot;,&quot;wav_name&quot;:&quot;connection_test&quot;}\n2025-07-03 11:37:29,516 - INFO - 🎉 连接测试完成！\n</code></pre> <h3 id="32-音频文件识别示例">3.2 音频文件识别示例</h3> <p><font style="color:#020817">代码示例：</font></p> <pre><code class="language-python">import asyncio\nfrom funasr_client_no_audio import FunASRClientNoAudio\nasync def recognize_converted_audio():\n    &quot;&quot;&quot;识别转换后的音频文件&quot;&quot;&quot;\n    audio_file = &quot;output_20250625084849_0_converted_8k.wav&quot;\n    print(f&quot;🎙️ 开始识别音频文件：{audio_file}&quot;)\n    print(&quot;=&quot; * 60)\n    client = FunASRClientNoAudio()\n    try:\n        # 连接服务器\n        print(&quot;📡 正在连接 FunASR 服务器...&quot;)\n        if not await client.connect():\n            return\n        # 发送配置 - 使用 offline 模式处理长音频\n        print(&quot;📤 发送配置参数...&quot;)\n        await client.send_config(\n            mode=&quot;offline&quot;,  # 离线模式更适合长音频\n            itn=True,        # 启用逆文本标准化\n            hotwords=&#39;{&quot;语音识别&quot;:20,&quot;人工智能&quot;:25,&quot;技术&quot;:15}&#39;  # 添加热词\n        )\n        # 创建结果监听任务\n        print(&quot;👂 开始监听识别结果...&quot;)\n        results = []\n        def collect_results(result):\n            &quot;&quot;&quot;收集识别结果&quot;&quot;&quot;\n            results.append(result)\n            print(f&quot;📨 收到结果：{result.get(&#39;text&#39;, &#39;&#39;)}&quot;)\n            if result.get(&#39;timestamp&#39;):\n                print(f&quot;   时间戳：{result[&#39;timestamp&#39;]}&quot;)\n        listen_task = asyncio.create_task(client.listen_for_results(collect_results))\n        # 发送音频文件\n        print(&quot;📁 开始发送音频文件...&quot;)\n        await client.send_audio_file(audio_file)\n        # 发送结束标志\n        print(&quot;📤 发送识别结束标志...&quot;)\n        await client.send_end_flag()\n        # 等待识别完成\n        print(&quot;⏳ 等待识别完成...&quot;)\n        await asyncio.sleep(60)\n        # 取消监听任务\n        listen_task.cancel()\n        # 输出最终结果\n        print(&quot;\\n&quot; + &quot;=&quot; * 60)\n        print(&quot;🎯 识别结果汇总：&quot;)\n        print(&quot;=&quot; * 60)\n        if results:\n            for i, result in enumerate(results[:5], 1):  # 显示前 5 个结果\n                text = result.get(&#39;text&#39;, &#39;&#39;)\n                mode = result.get(&#39;mode&#39;, &#39;unknown&#39;)\n                print(f&quot;\\n结果 {i}:&quot;)\n                print(f&quot;  模式：{mode}&quot;)\n                print(f&quot;  文本：{text}&quot;)\n                if result.get(&#39;timestamp&#39;):\n                    print(f&quot;  时间戳：{result[&#39;timestamp&#39;]}&quot;)\n        else:\n            print(&quot;❌ 没有收到识别结果&quot;)\n    except Exception as e:\n        print(f&quot;❌ 识别过程出现错误：{e}&quot;)\n    finally:\n        await client.close()\n        print(&quot;\\n🔒 连接已关闭&quot;)\nif __name__ == &quot;__main__&quot;:\n    asyncio.run(recognize_converted_audio())\n</code></pre> <p><font style="color:#020817">运行输出：</font></p> <pre><code class="language-python">🎙️ 开始识别音频文件：output_20250625084849_0_converted_8k.wav\n============================================================\n📡 正在连接 FunASR 服务器...\n✅ 成功连接到 FunASR 服务器\n📤 发送配置参数...\n📤 发送配置：{&#39;chunk_size&#39;: [5, 10, 5], &#39;wav_name&#39;: &#39;python_client_no_audio&#39;, &#39;is_speaking&#39;: True, &#39;wav_format&#39;: &#39;pcm&#39;, &#39;chunk_interval&#39;: 10, &#39;itn&#39;: True, &#39;mode&#39;: &#39;offline&#39;, &#39;hotwords&#39;: &#39;{&quot;语音识别&quot;:20,&quot;人工智能&quot;:25,&quot;技术&quot;:15}&#39;}\n👂 开始监听识别结果...\n📁 开始发送音频文件...\n📊 音频信息：1 声道，8000Hz, 1665892 帧\n📨 收到结果：看 aslow 的 night\n时间戳：[[5070,5330],[5330,5570],[5570,5710],[5710,5950]]\n📨 收到结果：你你妈\n时间戳：[[8109,8310],[8310,8490],[8490,8650]]\n📨 收到结果：我来听听他\n时间戳：[[9410,9490],[9490,9590],[9590,9670],[9670,9790],[9790,9995]]\n📤 音频文件发送完成\n📤 发送识别结束标志...\n⏳ 等待识别完成...\n    ============================================================\n    🎯 识别结果汇总：\n    ============================================================\n    结果 1:\n    模式：2pass-offline\n    文本：看 aslow 的 night\n    时间戳：[[5070,5330],[5330,5570],[5570,5710],[5710,5950]]\n    结果 2:\n    模式：2pass-offline\n    文本：你你妈\n    时间戳：[[8109,8310],[8310,8490],[8490,8650]]\n    结果 3:\n    模式：2pass-offline\n    文本：我来听听他\n    时间戳：[[9410,9490],[9490,9590],[9590,9670],[9670,9790],[9790,9995]]\n    🔒 连接已关闭\n</code></pre> <h3 id="33-音频格式转换示例">3.3 音频格式转换示例</h3> <p><font style="color:#020817">代码示例：</font></p> <pre><code class="language-python">import struct\nimport wave\nimport math\ndef convert_ieee_float_to_pcm(input_file, output_file=None, target_rate=8000):\n    &quot;&quot;&quot;将 IEEE Float WAV 文件转换为 PCM 格式&quot;&quot;&quot;\n    if output_file is None:\n        input_name = input_file.rsplit(&#39;.&#39;, 1)[0]\n        output_file = f&quot;{input_name}_converted_8k.wav&quot;\n    try:\n        print(f&quot;📁 读取音频文件：{input_file}&quot;)\n        # 读取 IEEE Float 数据\n        audio_data, sample_rate, channels = read_ieee_float_wav(input_file)\n        print(&quot;🔄 开始转换...&quot;)\n        # 转换为单声道\n        if channels &gt; 1:\n            print(&quot;🔄 转换为单声道...&quot;)\n            mono_data = convert_to_mono(audio_data, channels)\n        else:\n            mono_data = list(audio_data)\n        # 重采样\n        if sample_rate != target_rate:\n            print(f&quot;🔄 重采样：{sample_rate} Hz -&gt; {target_rate} Hz&quot;)\n            resampled_data = simple_resample(mono_data, sample_rate, target_rate)\n        else:\n            resampled_data = mono_data\n        # 标准化音频\n        print(&quot;🔄 标准化音频...&quot;)\n        normalized_data = normalize_audio(resampled_data, 0.95)\n        # 转换为 16 位 PCM\n        print(&quot;🔄 转换为 16 位 PCM...&quot;)\n        pcm_data = []\n        for sample in normalized_data:\n            pcm_value = int(max(-32768, min(32767, sample * 32767)))\n            pcm_data.append(pcm_value)\n        # 保存为 WAV 文件\n        print(f&quot;💾 保存转换后的音频：{output_file}&quot;)\n        with wave.open(output_file, &#39;wb&#39;) as wf:\n            wf.setnchannels(1)  # 单声道\n            wf.setsampwidth(2)  # 16 位 = 2 字节\n            wf.setframerate(target_rate)\n            # 将 PCM 数据转换为 bytes\n            pcm_bytes = b&#39;&#39;.join(struct.pack(&#39;&lt;h&#39;, sample) for sample in pcm_data)\n            wf.writeframes(pcm_bytes)\n        print(&quot;✅ 音频转换完成！&quot;)\n        print(f&quot;📊 转换后信息：&quot;)\n        print(f&quot;   采样率：{target_rate} Hz&quot;)\n        print(f&quot;   声道数：1 (单声道)&quot;)\n        print(f&quot;   位深：16 bit&quot;)\n        print(f&quot;   时长：{len(pcm_data) / target_rate:.2f} 秒&quot;)\n        print(f&quot;   文件大小：{len(pcm_data) * 2 / 1024 / 1024:.2f} MB&quot;)\n        return output_file\n    except Exception as e:\n        print(f&quot;❌ 音频转换失败：{e}&quot;)\n        return None\nif __name__ == &quot;__main__&quot;:\n    convert_ieee_float_to_pcm(&quot;input.wav&quot;, &quot;output.wav&quot;, 8000)\n</code></pre> <p><font style="color:#020817">运行输出：</font></p> <pre><code class="language-python">$ python simple_audio_converter.py output_20250625084849_0.wav test_converted.wav\n📁 读取音频文件: output_20250625084849_0.wav\n📊 原始音频信息:\n采样率: 48000 Hz\n声道数: 2\n位深: 32 bit\n格式: IEEE Float (3)\n数据大小: 79962840 bytes\n时长: 208.24 秒\n样本数: 19990710\n🔄 开始转换...\n🔄 转换为单声道...\n🔄 重采样: 48000 Hz -&gt; 8000 Hz\n🔄 标准化音频...\n🔄 转换为16位PCM...\n💾 保存转换后的音频: test_converted.wav\n✅ 音频转换完成!\n📊 转换后信息:\n采样率: 8000 Hz\n声道数: 1 (单声道)\n位深: 16 bit\n时长: 208.24 秒\n文件大小: 3.18 MB\n🔍 验证音频文件: test_converted.wav\n声道数: 1\n采样率: 8000 Hz\n采样位深: 16 bit\n总帧数: 1665892\n时长: 208.24 秒\n压缩类型: NONE\n✅ 音频格式完全符合FunASR要求!\n</code></pre> <h3 id="34-热词功能测试示例">3.4 热词功能测试示例</h3> <p><font style="color:#020817">代码示例：</font></p> <pre><code class="language-python">import asyncio\nimport websockets\nimport json\nasync def test_hotwords():\n    &quot;&quot;&quot;测试热词功能&quot;&quot;&quot;\n    uri = &quot;wss://d07011926-funasr-online-server-318-wjqawusl-10095.550c.cloud/&quot;\n    # 定义热词\n    hotwords = {\n        &quot;阿里巴巴&quot;: 25,\n        &quot;人工智能&quot;: 30,\n        &quot;语音识别&quot;: 28,\n        &quot;机器学习&quot;: 22\n    }\n    print(&quot;🔥 测试热词功能&quot;)\n    print(&quot;🎯 设置的热词及权重：&quot;)\n    for word, weight in hotwords.items():\n        print(f&quot;   {word}: {weight}&quot;)\n    try:\n        async with websockets.connect(uri) as websocket:\n            print(&quot;✅ 连接成功！&quot;)\n            # 发送带热词的配置\n            config = {\n                &quot;chunk_size&quot;: [5, 10, 5],\n                &quot;wav_name&quot;: &quot;hotwords_test&quot;,\n                &quot;is_speaking&quot;: True,\n                &quot;wav_format&quot;: &quot;pcm&quot;,\n                &quot;chunk_interval&quot;: 10,\n                &quot;itn&quot;: True,\n                &quot;mode&quot;: &quot;2pass&quot;,\n                &quot;hotwords&quot;: json.dumps(hotwords, ensure_ascii=False)\n            }\n            await websocket.send(json.dumps(config, ensure_ascii=False))\n            print(&quot;✅ 热词配置发送成功！&quot;)\n            # 发送结束信号\n            await websocket.send(json.dumps({&quot;is_speaking&quot;: False}))\n            # 接收响应\n            response = await websocket.recv()\n            result = json.loads(response)\n            print(f&quot;📥 收到响应：{result}&quot;)\n    except Exception as e:\n        print(f&quot;❌ 热词测试失败：{e}&quot;)\nasyncio.run(test_hotwords())\n</code></pre> <p><font style="color:#020817">运行输出：</font></p> <pre><code class="language-python">🔥 测试热词功能\n🎯 设置的热词及权重：\n阿里巴巴：25\n人工智能：30\n语音识别：28\n机器学习：22\n✅ 连接成功！\n✅ 热词配置发送成功！\n📥 收到响应：{&#39;is_final&#39;: True, &#39;text&#39;: &#39;&#39;, &#39;wav_name&#39;: &#39;hotwords_test&#39;}\n</code></pre> <h2 id="4-api-调用配置说明">4. API 调用配置说明</h2> <p><font style="color:#020817">基础配置参数：</font></p> <pre><code class="language-python">{\n    &quot;chunk_size&quot;: [5, 10, 5],        // 延迟配置：[3,6,3] 低延迟 [8,15,8] 高精度\n    &quot;wav_name&quot;: &quot;test&quot;,              // 音频标识\n    &quot;is_speaking&quot;: true,             // 说话状态\n    &quot;wav_format&quot;: &quot;pcm&quot;,             // 音频格式\n    &quot;chunk_interval&quot;: 10,            // 块间隔\n    &quot;itn&quot;: true,                     // 逆文本标准化\n    &quot;mode&quot;: &quot;2pass&quot;,                 // 识别模式\n    &quot;hotwords&quot;: &quot;{}&quot;                 // 热词配置\n}\n</code></pre> <p><font style="color:#020817">识别模式选择：</font></p> <ul> <li><font style="color:#2f8ef4;background-color:#eff0f0">online</font>: 实时模式，低延迟，适合对话</li> <li><font style="color:#2f8ef4;background-color:#eff0f0">offline</font>: 离线模式，高精度，适合文件转写</li> <li><font style="color:#2f8ef4;background-color:#eff0f0">2pass</font>: 双通道模式，平衡延迟和精度</li> </ul> <p><font style="color:#020817">热词配置示例：</font></p> <pre><code class="language-python">{\n    &quot;阿里巴巴&quot;: 25,\n    &quot;人工智能&quot;: 30,\n    &quot;语音识别&quot;: 28,\n    &quot;机器学习&quot;: 22\n}\n</code></pre> <p><font style="color:#020817">音频格式转换：如果音频不符合要求（8000Hz、单声道、16 位 PCM），使用转换器：</font></p> <pre><code class="language-powershell">python simple_audio_converter.py input.wav output.wav 8000\n</code></pre> <p><br><br> <font style="color:#b2b2b2">最后更新于: 2025/7/4 14:39</font></p> ';o["default"]=a},3683:function(t,o,n){t.exports=n.p+"img/funasr3.a2137132.png"},7065:function(t,o,n){t.exports=n.p+"img/funasr4.f3076758.png"},5114:function(t,o,n){t.exports=n.p+"img/funasr5.2a5810fd.png"},1404:function(t,o,n){t.exports=n.p+"img/universal1.9a1b3f4b.png"},4117:function(t,o,n){t.exports=n.p+"img/universal2.4306636e.png"}}]);
//# sourceMappingURL=docs10.7735e265.js.map