# <font style="color:rgb(2, 8, 23);">容器化部署 Ollama+Qwen3+Open WebUI</font>
<font style="color:rgb(2, 8, 23);">本指南全面介绍了在天工开物平台上部署 Ollama 与 Qwen3 大语言模型，通过 WebUI 访问的完整解决方案。该方案不仅提供了详实的部署流程，还提供了如何制作镜像的方案。</font>

> <font style="color:rgb(31, 41, 55);">此镜像提供了标准化的API 接口，同时提供了 WebUI 的方式与大模型进行交互。</font>
>

## **<font style="color:rgb(2, 8, 23);">1、部署服务</font>**
<font style="color:rgb(2, 8, 23);">点击这里【</font>[<font style="color:#2F8EF4;">新增部署任务</font>](https://tiangongkaiwu.top/#/console)<font style="color:rgb(2, 8, 23);">】，登录后根据页面提示进行部署。【选择 GPU 型号】-->在【服务配置】中选择【预制镜像】-->勾选【服务协议】-->点击【部署服务】-->部署完成，等待镜像拉取！</font>

### <font style="color:rgb(2, 8, 23);">1.1 访问</font>[天工开物控制台](https://tiangongkaiwu.top/#/console)<font style="color:rgb(2, 8, 23);">，点击【新增部署任务】。</font>
![](./imgs/Ollama+Qwen1.png)

### <font style="color:rgb(2, 8, 23);">1.2 选择设备</font>
<font style="color:rgb(2, 8, 23);">基于自身需要进行配置，参考配置为单卡 4090 和 1 个节点（初次使用进行调试）。</font>

![](./imgs/Ollama+Qwen2.png)

### <font style="color:rgb(2, 8, 23);">1.3 选择相应预制镜像</font>
<font style="color:rgb(2, 8, 23);">这里选择</font><font style="color:rgb(2, 8, 23);"> </font>**<font style="color:rgb(2, 8, 23);">Open Web UI + Qwen3 30B，</font>**<font style="color:rgb(2, 8, 23);">点击【部署服务】。</font>

![](./imgs/ollama-qwen-webui3.png)

### <font style="color:rgb(2, 8, 23);">1.4 耐心等待节点拉取镜像并启动</font>
![](./imgs/ollama-qwen-webui4.png)

### <font style="color:rgb(2, 8, 23);">1.5 部署完成</font>
<font style="color:rgb(2, 8, 23);">在部署完成页面，能看到两个访问链接，</font>

+ <font style="color:rgb(2, 8, 23);">11434 对应的链接就是 Ollama 服务的 API 访问地址。将这个 API 地址【复制】下来，就可以在任何支持 Ollama 协议的应用程序中使用。</font>
+ <font style="color:rgb(2, 8, 23);">8080 对应的链接就是 Open WebUI 的访问地址</font>

![](./imgs/ollama-qwen-webui5.png)

<font style="color:rgb(103, 103, 108);">请耐心一点哦～ 模型镜像会比较大，</font>**<font style="color:rgb(103, 103, 108);">qwen3:30b-a3b 镜像本身 20G+，打包之后大约 30G+，</font>**<font style="color:rgb(103, 103, 108);"> 拉取镜像会需要一段时间</font>

### <font style="color:rgb(2, 8, 23);">1.6 如何切换模型</font>
<font style="color:rgb(2, 8, 23);">通过修改 Docker 镜像的环境变量，您可以轻松切换不同的 AI 模型。</font>

<font style="color:rgb(2, 8, 23);">找到任务列表，选择刚刚创建的任务，点击跳转到详情页面</font>

![](./imgs/ollama-qwen-webui6.png)

1. <font style="color:rgb(2, 8, 23);">进入环境变量设置页面</font>
2. <font style="color:rgb(2, 8, 23);">找到输入框，输入以下格式的环境变量</font>

<font style="color:rgb(2, 8, 23);">DEFAULT_MODEL=模型名称</font>

```plain
例如，要切换到Qwen 3.0 32B模型，输入：
DEFAULT_MODEL=qwen3:32b
```

1. <font style="color:rgb(2, 8, 23);">点击【保存】按钮</font>
2. <font style="color:rgb(2, 8, 23);">点击【</font>**<font style="color:rgb(2, 8, 23);">应用修改】</font>**<font style="color:rgb(2, 8, 23);">按钮使新设置</font>**<font style="color:rgb(2, 8, 23);">生效</font>**

<font style="color:rgb(2, 8, 23);">重要提示：</font>

+ <font style="color:rgb(2, 8, 23);">环境变量修改后必须点击【应用修改】按钮才能生效</font>
+ <font style="color:rgb(2, 8, 23);">可用的模型名称请参考 </font>[<font style="color:#2F8EF4;">Ollama官方模型库</font>](https://ollama.com/library)
+ <font style="color:rgb(2, 8, 23);">确保输入的模型名称格式正确，通常为 模型系列:参数规模 的形式</font>

![](./imgs/ollama-qwen-webui7.png)

## <font style="color:rgb(2, 8, 23);">2、开始使用 Open WebUI</font>
### <font style="color:rgb(2, 8, 23);">2.1 访问开始页面</font>
<font style="color:rgb(2, 8, 23);">访问 8080 端口对应的链接，会出现以下界面，说明 Open WebUI 已经成功部署。点击【开始使用】</font>

![](./imgs/ollama-qwen-webui8.png)

### <font style="color:rgb(2, 8, 23);">2.2 设置管理员账号</font>
<font style="color:rgb(2, 8, 23);">设置管理员账号和密码，点击【创建管理员账号】</font>

![](./imgs/ollama-qwen-webui9.png)

### <font style="color:rgb(2, 8, 23);">2.3 开始 AI 对话</font>
<font style="color:rgb(2, 8, 23);">创建管理员账号之后会自动登录，默认会选中镜像中的模型</font><font style="color:rgb(2, 8, 23);"> </font>**<font style="color:rgb(2, 8, 23);">qwen3:30b-a3b</font>**<font style="color:rgb(2, 8, 23);">，就是制作镜像时设置的镜像名称。</font>

<font style="color:rgb(2, 8, 23);">现在一起就绪，开始与 AI 的对话之旅吧~</font>

![](./imgs/ollama-qwen-webui10.png)

## <font style="color:rgb(2, 8, 23);">3、 模型速度测试</font>
<font style="color:rgb(2, 8, 23);">qwen3 部署完成了，速度怎么样呢？点击 </font>[<font style="color:#2F8EF4;">LM Speed</font>](https://lmspeed.net/zh-CN)<font style="color:rgb(2, 8, 23);"> 测试一下速度吧～</font>

![](./imgs/ollama-qwen-webui11.png)

## **<font style="color:rgb(2, 8, 23);">4、</font>****<font style="color:rgb(2, 8, 23);">本地</font>****<font style="color:rgb(2, 8, 23);">打包 Ollama 和 Open WebUI 镜像</font>**
<font style="color:rgb(103, 103, 108);">温馨提示，如果你只希望使用我们默认的镜像，那么下面的内容您无需关注。</font>

### <font style="color:rgb(2, 8, 23);">4.1 clone 项目</font>
```bash
git clone https://github.com/slmnb-lab/llm-deployment.git
```

### <font style="color:rgb(2, 8, 23);">4.2 修改镜像仓库地址名</font>
<font style="color:rgb(103, 103, 108);">开始之前需要在天工开物中创建一个镜像仓库，镜像仓库名称为 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">{yourusername}</font><font style="color:rgb(103, 103, 108);">，镜像标签为 </font><font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">30b-a3b</font><font style="color:rgb(103, 103, 108);">。访问这里 </font>[<font style="color:#2F8EF4;">初始化镜像仓库</font>](https://tiangongkaiwu.top/#/console)。<font style="color:#2F8EF4;background-color:rgba(142, 150, 170, 0.14);">harbor.suanleme.cn/{yourusername}/ollama-webui-qwen3:30b-a3b</font><font style="color:rgb(103, 103, 108);">是您创建的镜像仓库地址，这个参数在部署服务的时候会用到，记得替换成你的镜像仓库地址。</font>

```yaml
services:
  ollama-webui-qwen3:
    image: harbor.suanleme.cn/{yourusername}/ollama-webui-qwen3:30b-a3b  
    build: .
    labels:
      - suanleme_0.http.port=11434                   # 这里是 ollama 运行的端口，不要修改
      - suanleme_0.http.prefix=ollama-webui-qwen3     # 这里是发布的回传域名
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    ports:
      - "11434:11434"                        # 这里是ollama运行的端口，不要修改
      - "8080:8080"                          # 这里是open webui运行的端口，不需要修改
```

### <font style="color:rgb(2, 8, 23);">4.3 运行打包脚本</font>
<font style="color:rgb(2, 8, 23);">执行成功之后，会在本地生成镜像</font>

```bash
cd llm-deployment/ollama-webui   # 进入 ollama 目录
docker compose build       # 打包镜像
```

## **<font style="color:rgb(2, 8, 23);">5、镜像上传</font>**
<font style="color:rgb(2, 8, 23);">将打包的镜像上传到天工开物</font><font style="color:rgb(2, 8, 23);">的镜像仓库</font>

### <font style="color:rgb(2, 8, 23);">5.1 登录镜像仓库</font>
<font style="color:rgb(2, 8, 23);">username 需要替换为自己的天工开物【</font>**<font style="color:rgb(2, 8, 23);">镜像仓库】</font>**<font style="color:rgb(2, 8, 23);">的【</font>**<font style="color:rgb(2, 8, 23);">仓库账号】</font>**<font style="color:rgb(2, 8, 23);">！</font>

<font style="color:rgb(2, 8, 23);">输入密码需要输入初始化镜像仓库时设置的密码</font>

```bash
### harbor.suanleme.cn 是固定值，{yourusername}需要替换为自己的镜像仓库的用户名！
docker login harbor.suanleme.cn --username={yourusername}

## 输入密码  镜像仓库的密码！
*******
```

### <font style="color:rgb(2, 8, 23);">5.2 上传镜像</font>
<font style="color:rgb(2, 8, 23);">执行以下代码，进行镜像上传</font>

```bash
## 为新生成的镜像打上标签
docker tag harbor.suanleme.cn/{yourusername}/ollama-webui-qwen3:30b-a3b harbor.suanleme.cn/{yourusername}/ollama-webui-qwen3:30b-a3b

## 上传镜像
docker push harbor.suanleme.cn/{yourusername}/ollama-webui-qwen3:30b-a3b
```

<font style="color:rgb(103, 103, 108);">备注：镜像比较大，如果推送失败了，多试几次就好了。</font><font style="color:rgb(103, 103, 108);">😊</font>

<br><br>
<font style="color:rgba(178,178,178,1);">最后更新于: 2025/6/12 15:10</font>